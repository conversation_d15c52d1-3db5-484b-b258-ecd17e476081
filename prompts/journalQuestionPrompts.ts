/**
 * Specialized prompts for AI journal question generation
 * Optimized for Indonesian language and mental health context
 */

import type { AIGenerationType } from '@/types/journal';

export interface PromptTemplate {
  systemPrompt: string;
  userPrompt: string;
  examples?: string[];
}

/**
 * Base system prompt for journal question generation
 */
const BASE_SYSTEM_PROMPT = `<PERSON><PERSON> adalah asisten AI yang ahli dalam membuat pertanyaan reflektif untuk jurnal pribadi dalam bahasa Indonesia. 

KONTEKS:
- Kamu membantu remaja Indonesia dalam perjalanan kesehatan mental mereka
- Pertanyaan harus mendorong refleksi diri yang sehat dan konstruktif
- <PERSON><PERSON><PERSON> bahasa yang hangat, personal, dan tidak menghakimi
- Hindari pertanyaan yang terlalu invasif atau memicu trauma
- Fokus pada pertumbuhan pribadi, kesadaran diri, dan kesejahteraan emosional

PRINSIP UTAMA:
1. Gunakan bahasa Indonesia yang natural dan ramah
2. Pertanyaan harus open-ended dan mendorong eksplorasi
3. Hindari pertanyaan yang bisa memicu respons negatif atau berbahaya
4. Sesuaikan dengan konteks kesehatan mental remaja
5. Pertanyaan harus praktis dan dapat dijawab dalam konteks harian

TONE: Empati, mendukung, tidak menghakimi, seperti teman yang peduli`;

/**
 * Generate follow-up questions based on existing question
 */
export const FOLLOW_UP_PROMPT_TEMPLATE: PromptTemplate = {
  systemPrompt: `${BASE_SYSTEM_PROMPT}

TUGAS KHUSUS: Buat pertanyaan lanjutan yang menggali lebih dalam dari pertanyaan dasar yang diberikan.

KARAKTERISTIK PERTANYAAN LANJUTAN:
- Membantu pengguna mengeksplorasi perasaan atau pengalaman lebih detail
- Mendorong refleksi tentang penyebab, dampak, atau makna dari jawaban sebelumnya
- Membantu mengidentifikasi pola atau pembelajaran
- Menghubungkan pengalaman dengan pertumbuhan pribadi`,

  userPrompt: `Berdasarkan pertanyaan jurnal ini: "{baseQuestion}"

Buatlah {count} pertanyaan lanjutan yang:
1. Menggali lebih dalam topik yang sama
2. Membantu pengguna memahami perasaan atau pengalaman mereka
3. Mendorong refleksi konstruktif
4. Menggunakan bahasa yang natural dan ramah

Konteks tambahan: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering.`,

  examples: [
    'Apa yang membuat momen itu terasa istimewa bagimu?',
    'Bagaimana perasaan itu mempengaruhi cara kamu melihat hari ini?',
    'Apa yang bisa kamu pelajari dari pengalaman tersebut?',
  ],
};

/**
 * Generate alternative phrasings of existing questions
 */
export const ALTERNATIVE_PROMPT_TEMPLATE: PromptTemplate = {
  systemPrompt: `${BASE_SYSTEM_PROMPT}

TUGAS KHUSUS: Buat variasi pertanyaan yang memiliki tujuan sama dengan pertanyaan asli tapi dengan kata-kata yang berbeda.

KARAKTERISTIK PERTANYAAN ALTERNATIF:
- Mempertahankan maksud dan tujuan pertanyaan asli
- Menggunakan sudut pandang atau framing yang berbeda
- Tetap mudah dipahami dan dijawab
- Memberikan variasi untuk menghindari kebosanan`,

  userPrompt: `Berdasarkan pertanyaan jurnal ini: "{baseQuestion}"

Buatlah {count} variasi pertanyaan yang:
1. Memiliki tujuan refleksi yang sama
2. Menggunakan kata-kata atau framing yang berbeda
3. Tetap mudah dipahami dan dijawab
4. Memberikan perspektif segar pada topik yang sama

Konteks tambahan: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering.`,

  examples: [
    'Hal apa yang paling berkesan hari ini?',
    'Momen apa yang membuatmu tersenyum hari ini?',
    'Apa yang membuatmu merasa bersyukur saat ini?',
  ],
};

/**
 * Generate related questions on similar themes
 */
export const RELATED_PROMPT_TEMPLATE: PromptTemplate = {
  systemPrompt: `${BASE_SYSTEM_PROMPT}

TUGAS KHUSUS: Buat pertanyaan baru yang berkaitan dengan tema atau topik dari pertanyaan asli.

KARAKTERISTIK PERTANYAAN TERKAIT:
- Mengeksplorasi aspek berbeda dari tema yang sama
- Memperluas cakupan refleksi pada area terkait
- Membantu pengguna melihat koneksi antar pengalaman
- Mendorong pemahaman holistik tentang diri`,

  userPrompt: `Berdasarkan pertanyaan jurnal ini: "{baseQuestion}"

Buatlah {count} pertanyaan terkait yang:
1. Mengeksplorasi tema atau topik yang berkaitan
2. Memperluas area refleksi ke aspek terkait
3. Membantu pengguna melihat koneksi antar pengalaman
4. Tetap relevan dengan konteks kesehatan mental remaja

Konteks tambahan: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering.`,

  examples: [
    'Bagaimana hubungan dengan orang terdekat mempengaruhi perasaanmu hari ini?',
    'Apa yang membuatmu merasa paling nyaman dengan dirimu sendiri?',
    'Kegiatan apa yang membuatmu merasa paling energik?',
  ],
};

/**
 * Get prompt template based on generation type
 */
export function getPromptTemplate(type: AIGenerationType): PromptTemplate {
  switch (type) {
    case 'follow_up':
      return FOLLOW_UP_PROMPT_TEMPLATE;
    case 'alternative':
      return ALTERNATIVE_PROMPT_TEMPLATE;
    case 'related':
      return RELATED_PROMPT_TEMPLATE;
    default:
      throw new Error(`Unknown generation type: ${type}`);
  }
}

/**
 * Build complete prompt for AI generation
 */
export function buildPrompt(
  type: AIGenerationType,
  baseQuestion: string,
  options: {
    count?: number;
    context?: string;
    tone?: 'casual' | 'formal' | 'empathetic';
    userPreferences?: Record<string, any>;
  } = {}
): { systemPrompt: string; userPrompt: string } {
  const template = getPromptTemplate(type);
  const count = options.count || 3;
  const context = options.context || 'Tidak ada konteks tambahan';

  // Customize system prompt based on tone preference
  let systemPrompt = template.systemPrompt;
  if (options.tone === 'casual') {
    systemPrompt += '\n\nGUNAKAN TONE: Santai dan bersahabat, seperti ngobrol dengan teman dekat.';
  } else if (options.tone === 'formal') {
    systemPrompt += '\n\nGUNAKAN TONE: Lebih formal tapi tetap hangat dan mendukung.';
  } else if (options.tone === 'empathetic') {
    systemPrompt += '\n\nGUNAKAN TONE: Sangat empati dan penuh perhatian, fokus pada dukungan emosional.';
  }

  // Build user prompt with variables
  const userPrompt = template.userPrompt
    .replace('{baseQuestion}', baseQuestion)
    .replace('{count}', count.toString())
    .replace('{context}', context);

  return { systemPrompt, userPrompt };
}

/**
 * Validate generated questions for safety and quality
 */
export function validateGeneratedQuestions(questions: string[]): {
  valid: string[];
  invalid: string[];
  reasons: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];
  const reasons: string[] = [];

  const dangerousPatterns = [
    /bunuh diri|suicide/i,
    /menyakiti diri|self.?harm/i,
    /mati|death/i,
    /putus asa|hopeless/i,
    /tidak berguna|worthless/i,
  ];

  const qualityPatterns = [
    /^.{10,200}$/, // Length check: 10-200 characters
    /\?$/, // Should end with question mark
    /^[A-Z]/, // Should start with capital letter
  ];

  questions.forEach((question, index) => {
    const trimmed = question.trim();
    
    // Check for dangerous content
    const hasDangerousContent = dangerousPatterns.some(pattern => pattern.test(trimmed));
    if (hasDangerousContent) {
      invalid.push(trimmed);
      reasons.push(`Question ${index + 1}: Contains potentially harmful content`);
      return;
    }

    // Check quality patterns
    const passesQuality = qualityPatterns.every(pattern => pattern.test(trimmed));
    if (!passesQuality) {
      invalid.push(trimmed);
      reasons.push(`Question ${index + 1}: Does not meet quality standards`);
      return;
    }

    valid.push(trimmed);
  });

  return { valid, invalid, reasons };
}
