# Dynamic Navbar Color Implementation

This document describes the implementation of dynamic navbar colors that change based on the current page's theme.

## Overview

The navbar (TabBar) now dynamically changes its background color and icon colors to match the main color theme of the currently active page.

## Implementation Details

### 1. Theme System (`constants/themes.ts`)

Created a centralized theme system that defines color schemes for each page:

- **Chat Page**: Gold/Yellow theme (`#B5A136`, `#FDF8E8`)
- **Home Page**: Blue theme (`#2B7EFF`, `#F0F8FF`)
- **Mood Page**: Green theme (`#4EAF64`, `#F0FDF4`)
- **Journal Page**: Pink theme (`#DD76B9`, `#FDF2F8`)
- **Mic Page**: Blue theme (default)

Each theme includes:
- `primary`: Main color for active elements
- `background`: Light tint for navbar background
- `secondary`: Secondary accent color
- `light`: Very light tint for subtle backgrounds

### 2. Theme Hook (`hooks/usePageTheme.ts`)

Created a custom React hook that:
- Takes the current route name or navigation state
- Returns the appropriate theme colors
- Uses memoization for performance
- Provides fallback to default theme

### 3. Updated TabBar Component (`components/TabBar.tsx`)

Modified the TabBar to:
- Use the theme hook to get current page colors
- Apply dynamic background color based on active route
- Use page-specific primary color for active icons
- Use neutral gray for inactive icons
- Maintain smooth transitions between pages

## Color Mapping

| Page | Primary Color | Background Color | Theme |
|------|---------------|------------------|-------|
| Chat | `#B5A136` | `#FDF8E8` | Gold/Yellow |
| Home | `#2B7EFF` | `#F0F8FF` | Blue |
| Mood | `#4EAF64` | `#F0FDF4` | Green |
| Journal | `#DD76B9` | `#FDF2F8` | Pink |
| Mic | `#2B7EFF` | `#F0F8FF` | Blue (default) |

## Usage

The system works automatically - no additional configuration needed. When users navigate between pages, the navbar will automatically update its colors to match the page theme.

## Benefits

1. **Visual Cohesion**: Navbar integrates seamlessly with each page's design
2. **User Experience**: Clear visual indication of current page context
3. **Maintainability**: Centralized theme system for easy updates
4. **Scalability**: Easy to add new pages with custom themes
5. **Performance**: Optimized with memoization

## Future Enhancements

- Add smooth color transition animations
- Support for dark mode themes
- User-customizable color schemes
- Accessibility improvements for color contrast
