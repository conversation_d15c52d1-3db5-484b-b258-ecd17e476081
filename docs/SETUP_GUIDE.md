# Authentication Setup Guide

This guide will walk you through setting up Google Sign-in authentication with Supa<PERSON> for your Expo React Native app.

## Prerequisites

- A Supabase account and project
- A Google Cloud Console account
- Basic knowledge of React Native and Expo

## Step 1: Create Supabase Project

1. Go to [https://supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be fully initialized
4. Go to **Settings** → **API**
5. Copy your **Project URL** and **anon public key**

## Step 2: Configure Environment Variables

Create a `.env` file in your project root and add your Supabase credentials:

```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

**Important Notes:**
- Replace `your-project-ref` with your actual Supabase project reference
- Replace `your_anon_key_here` with your actual anon key
- Restart your development server after changing environment variables

## Step 3: Setup Google OAuth in Google Cloud Console

### 3.1 Create OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **Google+ API** (or **Google Identity API**)
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**
5. Select **Web application** as the application type

### 3.2 Configure Authorized JavaScript Origins

Add the following URLs to **Authorized JavaScript origins**:

```
https://your-project-ref.supabase.co
http://localhost:8081
https://your-deployment-url.com
```

### 3.3 Configure Authorized Redirect URIs

Add the following URLs to **Authorized redirect URIs**:

```
https://your-project-ref.supabase.co/auth/v1/callback
http://localhost:8081
https://your-deployment-url.com
```

### 3.4 Get Your Credentials

1. Copy your **Client ID** and **Client Secret**
2. You'll need these for the Supabase configuration

## Step 4: Enable Supabase Authentication

### 4.1 Configure Google Provider

1. In your Supabase project, go to **Authentication** → **Providers**
2. Find **Google** and click **Enable**
3. Enter your Google **Client ID** and **Client Secret**
4. Save the configuration

### 4.2 Configure Site URL and Redirect URLs

1. Go to **Authentication** → **URL Configuration**
2. Set **Site URL** to your deployment URL (e.g., `https://your-app.netlify.app`)
3. Add the following to **Redirect URLs**:
   - `http://localhost:8081` (for development)
   - `https://your-deployment-url.com` (for production)

## Step 5: Test Your Setup

### 5.1 Local Testing

1. Start your development server: `npm run dev`
2. Try signing in with Google
3. Check the browser console for any errors
4. Verify that authentication state persists on page refresh

### 5.2 Production Testing

1. Deploy your app to your hosting provider
2. Update Google OAuth URLs with your production domain
3. Update Supabase redirect URLs with your production domain
4. Test authentication on the live site

## Troubleshooting

### Common Issues

**"Invalid redirect URI"**
- Ensure all URLs in Google Cloud Console match exactly
- No trailing slashes in URLs
- URLs must use HTTPS in production

**"Invalid client"**
- Check that your Google Client ID and Secret are correct in Supabase
- Ensure the Google+ API is enabled in Google Cloud Console

**Authentication not persisting**
- Check that your environment variables are correctly set
- Verify that the Supabase client is properly configured

**CORS errors**
- Ensure your domain is added to Supabase's allowed origins
- Check that your Site URL is correctly configured in Supabase

### Debug Steps

1. Check browser console for error messages
2. Verify environment variables are loaded: `console.log(process.env.EXPO_PUBLIC_SUPABASE_URL)`
3. Test Supabase connection in the browser network tab
4. Ensure all URLs match exactly between Google Cloud Console and Supabase

## Important Notes

- **URL Matching**: All URLs must match exactly between Google Cloud Console and Supabase (no trailing slashes)
- **HTTPS Required**: Production URLs must use HTTPS
- **Environment Variables**: Restart your development server after changing `.env` files
- **Testing**: Always test locally before deploying
- **Updates**: Update Google OAuth URLs after deployment to a new domain

## Security Considerations

- Never commit your `.env` file to version control
- Use different Google OAuth credentials for development and production
- Regularly rotate your API keys and secrets
- Monitor authentication logs in both Google Cloud Console and Supabase

## Support

If you encounter issues:

1. Check the [Supabase documentation](https://supabase.com/docs/guides/auth/social-login/auth-google)
2. Review the [Google OAuth documentation](https://developers.google.com/identity/protocols/oauth2)
3. Check the browser console and network tab for error details
4. Verify all configuration steps have been completed correctly