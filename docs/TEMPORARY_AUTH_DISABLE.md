# Authentication Feature Control

This document explains how to control Google sign-in and user registration features via environment variables.

## Current Status

- ❌ **Google Sign-in**: Disabled by default (hidden button)
- ❌ **User Registration**: Disabled by default (hidden button)
- ✅ **Email/Password Sign-in**: Always available for existing users

## How It Works

The implementation uses environment variables that are read in `lib/env.ts`:

```typescript
export const AUTH_CONFIG = {
  googleSigninEnabled: getBooleanEnvVar('EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED', false),
  signupEnabled: getBooleanEnvVar('EXPO_PUBLIC_SIGNUP_ENABLED', false),
};
```

**Default Values**: Both features are disabled by default (`false`) for security.

## Files Modified

1. **`lib/authConfig.ts`** - Feature flag configuration
2. **`app/(auth)/index.tsx`** - UI conditional rendering
3. **`context/AuthContext.tsx`** - Backend function protection

## How to Re-enable

### Environment Variables

Add these environment variables to your `.env` file or deployment configuration:

#### Option 1: Enable Both Features

```bash
EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED=true
EXPO_PUBLIC_SIGNUP_ENABLED=true
```

#### Option 2: Enable Only Google Sign-in

```bash
EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED=true
EXPO_PUBLIC_SIGNUP_ENABLED=false
```

#### Option 3: Enable Only Sign-up

```bash
EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED=false
EXPO_PUBLIC_SIGNUP_ENABLED=true
```

### For Development

Create or update your `.env` file in the project root:

```bash
# Authentication Features
EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED=true
EXPO_PUBLIC_SIGNUP_ENABLED=true
```

### For Production

Set these environment variables in your deployment platform:
- Vercel: Project Settings → Environment Variables
- Netlify: Site Settings → Environment Variables
- Railway: Project → Variables
- EAS Build: Use `eas.json` or EAS Secrets

## User Experience

With both features disabled:
- Users see only the email/password sign-in form
- No Google sign-in button
- No "Buat Akun" button
- Clean, simplified interface
- Existing users can still sign in normally

## Rollback Instructions

To completely remove this feature flag system and restore original functionality:

1. Remove environment variable imports from `lib/env.ts`
2. Remove `lib/authConfig.ts`
3. Remove imports from `app/(auth)/index.tsx` and `context/AuthContext.tsx`
4. Remove conditional rendering from `app/(auth)/index.tsx`
5. Remove feature flag checks from `context/AuthContext.tsx`

## Environment Variable Reference

| Variable | Default | Description |
|----------|---------|-------------|
| `EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED` | `false` | Controls Google OAuth sign-in button visibility |
| `EXPO_PUBLIC_SIGNUP_ENABLED` | `false` | Controls user registration button visibility |

## Notes

- ✅ All backend functionality remains intact
- ✅ No database changes required
- ✅ Easy to toggle via environment variables
- ✅ No impact on existing user sessions
- ✅ Secure by default (features disabled)
- ✅ Works across all deployment environments
