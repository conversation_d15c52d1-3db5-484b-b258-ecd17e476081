# Storage Adapter Fix for Expo Web

## Problem

When running `npm run dev` with Expo web, the application was crashing with:

```
ReferenceError: window is not defined
    at getValue (/node_modules/@react-native-async-storage/async-storage/lib/commonjs/AsyncStorage.js:63:52)
```

This error occurred because:
1. `@react-native-async-storage/async-storage` tries to access `window.localStorage` during initialization
2. During Expo web build/SSR process, the `window` object is not available in the Node.js environment
3. Supabase auth initialization triggers AsyncStorage during module loading

## Solution

Created a custom storage adapter (`lib/storage.ts`) that:
- ✅ Uses `localStorage` for web environments
- ✅ Uses `AsyncStorage` for React Native environments  
- ✅ Handles SSR/build-time scenarios gracefully
- ✅ Provides proper error handling and fallbacks

## Implementation

### 1. Custom Storage Adapter (`lib/storage.ts`)

```typescript
export class SupabaseStorage implements StorageAdapter {
  async getItem(key: string): Promise<string | null> {
    if (this.isWeb()) {
      return localStorage.getItem(key);
    }
    const AsyncStorage = await this.getAsyncStorage();
    return AsyncStorage ? await AsyncStorage.getItem(key) : null;
  }
  
  // Similar implementations for setItem and removeItem
}
```

### 2. Updated Supabase Configuration (`lib/supabase.ts`)

```typescript
import { supabaseStorage } from './storage';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: supabaseStorage, // Use custom adapter instead of AsyncStorage
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
```

## Benefits

1. **Cross-platform compatibility**: Works seamlessly on web, iOS, and Android
2. **Build-time safety**: No more "window is not defined" errors during builds
3. **Graceful fallbacks**: Handles environments where storage is not available
4. **Maintains functionality**: All Supabase features continue to work as expected
5. **Future-proof**: Compatible with any Expo/React Native web setup

## Testing

Created test utilities to verify the implementation:
- `utils/testStorage.ts` - Comprehensive storage adapter tests
- `components/StorageTest.tsx` - Visual test component for debugging

## Files Modified

- ✅ `lib/storage.ts` - New custom storage adapter
- ✅ `lib/supabase.ts` - Updated to use custom storage
- ✅ `utils/testStorage.ts` - Test utilities
- ✅ `components/StorageTest.tsx` - Test component

## Verification

Run the development server to verify the fix:

```bash
npm run dev
```

The application should now start without the "window is not defined" error.

## Debug Information

The storage adapter logs useful information:
- Storage type being used (localStorage vs AsyncStorage)
- Storage availability status
- Any errors that occur during storage operations

Check the console for messages like:
```
[Supabase] Using storage type: localStorage
[SupabaseStorage] Storage available: true
```

## Alternative Solutions Considered

1. **Conditional Supabase Initialization** - More complex, requires changing all Supabase usage
2. **Lazy Loading Pattern** - Requires significant architectural changes
3. **Platform-specific Builds** - Would complicate the build process

The custom storage adapter was chosen as the most robust and maintainable solution.

## Troubleshooting

If you encounter issues:

1. **Check console logs** for storage-related messages
2. **Verify environment variables** are properly set
3. **Test storage functionality** using the provided test utilities
4. **Ensure dependencies** are properly installed:
   ```bash
   npm install @supabase/supabase-js @react-native-async-storage/async-storage react-native-url-polyfill
   ```

## References

- [Supabase React Native Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-expo-react-native)
- [Expo Web Documentation](https://docs.expo.dev/guides/using-supabase/)
- [AsyncStorage Web Compatibility Issues](https://github.com/react-native-async-storage/async-storage/issues/1096)
