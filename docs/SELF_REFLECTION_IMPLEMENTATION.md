# Self-Reflection Check-In Implementation

## Overview

The Self-Reflection Check-In system is an extensible, configuration-driven feature that allows users to complete emotional wellness assessments. The system supports dynamic question management, flexible scoring, and risk assessment protocols.

## Architecture

### Database Schema

The system uses a flexible database schema that supports:
- **Question Sets**: Versioned collections of questions with A/B testing support
- **Questions**: Dynamic question definitions with multiple types and scoring methods
- **User Sessions**: Flexible response storage with calculated scores and risk levels
- **Risk Assessment Rules**: Configurable risk evaluation and response protocols

### Key Components

1. **QuestionConfigService**: Manages dynamic loading of question configurations
2. **ScoringEngine**: Handles flexible scoring calculations and risk assessment
3. **SelfReflectionService**: Manages user session persistence and retrieval
4. **SelfReflectionCheckIn**: Main orchestrator component for the user flow

## Features

### ✅ Current Implementation

- **5 Initial Questions**: Emotional state, energy level, concerns, support preference, safety check
- **Dynamic Question Loading**: Questions loaded from database configuration
- **Flexible Scoring**: Multiple scoring methods (direct, weighted, conditional)
- **Risk Assessment**: 4-level risk classification (Green, Yellow, Red, Emergency)
- **Safety Protocols**: Crisis intervention for high-risk responses
- **Indonesian Language**: Full Indonesian language support
- **Privacy Protection**: Responses not stored permanently
- **Responsive Design**: Works on all screen sizes
- **Integration**: Seamless integration with existing onboarding flow

### 🚀 Future Extensibility

- **Zero-Deployment Updates**: Add questions via database without code changes
- **Multiple Question Types**: Support for scale, boolean, text input, multi-select
- **A/B Testing**: Multiple question set versions
- **Multi-Language**: Ready for additional language support
- **Advanced Scoring**: Conditional logic and weighted scoring
- **Analytics Ready**: Rich data structure for insights

## Question Types Supported

### Current
- **Multiple Choice**: Options with emoji, text, and scoring

### Future Ready
- **Scale**: 1-10 sliders for intensity ratings
- **Boolean**: Yes/No questions
- **Text Input**: Optional elaboration fields
- **Multi-Select**: Multiple option selection
- **Conditional**: Smart question flows based on previous answers

## Risk Assessment

### Risk Levels
- **🟢 Green (0-4 points)**: Stable emotional condition
- **🟡 Yellow (5-7 points)**: Mild emotional distress
- **🔴 Red (8-9 points)**: Significant distress
- **🚨 Emergency (10+ points)**: Crisis protocol activation

### Safety Protocols
- Immediate crisis resources for emergency levels
- Professional help recommendations
- Safety tips and coping strategies
- Integration with Indonesian mental health services

## File Structure

```
📁 Self-Reflection System:
├── supabase/migrations/
│   ├── 20250703000000_create_flexible_self_reflection_system.sql
│   └── 20250703000001_seed_initial_questions.sql
├── types/selfReflection.ts
├── lib/
│   ├── questionConfigService.ts
│   ├── scoringEngine.ts
│   └── selfReflectionService.ts
├── components/
│   ├── SelfReflectionCheckIn.tsx
│   └── selfReflection/
│       ├── MultipleChoiceQuestion.tsx
│       ├── SafetyProtocol.tsx
│       └── ResultsDisplay.tsx
├── app/(onboarding)/self-reflection.tsx
├── styles/SelfReflectionStyles.ts
└── __tests__/selfReflection.test.ts
```

## Usage

### Basic Implementation
```typescript
import { SelfReflectionCheckIn } from '@/components/SelfReflectionCheckIn';

<SelfReflectionCheckIn
  onComplete={(session) => {
    // Handle completion
    console.log('Risk level:', session.riskLevel);
    console.log('AI tone:', session.aiTonePreference);
  }}
  onSkip={() => {
    // Handle skip
  }}
  language="id"
/>
```

### Adding New Questions

1. **Via Database** (Recommended):
```sql
INSERT INTO questions (
  question_set_id,
  question_key,
  question_text,
  question_type,
  options,
  scoring_config,
  order_index
) VALUES (
  'question-set-id',
  'new_question',
  '{"id": "Pertanyaan baru?"}',
  'multiple_choice',
  '[{"id": "option1", "text": {"id": "Opsi 1"}, "score": 1}]',
  '{"method": "direct", "maxScore": 3}',
  6
);
```

2. **Via Configuration Service**:
```typescript
await QuestionConfigService.createQuestion({
  questionKey: 'new_question',
  questionText: { id: 'Pertanyaan baru?' },
  questionType: 'multiple_choice',
  // ... other config
});
```

## Integration Points

### Authentication Flow
- Integrated with existing AuthContext
- Tracks completion status separately from onboarding
- Supports skip functionality

### AI Chat Integration
- Risk level passed to chat service for tone adjustment
- Support preference guides conversation style
- Safety protocols can trigger specialized responses

### Analytics & Insights
- Rich session data for user insights
- Risk level trends over time
- Support preference patterns

## Testing

Run tests with:
```bash
npm test __tests__/selfReflection.test.ts
```

Test the fixes with:
```bash
npx ts-node scripts/testSelfReflectionFixes.ts
```

Tests cover:
- Scoring engine calculations
- Risk level determination
- Question validation
- Configuration validation
- Import error fixes
- Authentication timing

## Deployment

1. **Apply Database Migrations**:
```bash
npx supabase db push
```

2. **Test Fixes**:
```bash
npx ts-node scripts/testSelfReflectionFixes.ts
```

3. **Verify Question Loading**:
```typescript
import { QuestionConfigService } from '@/lib/questionConfigService';
const questionSet = await QuestionConfigService.getActiveQuestionSet('id');
console.log('Questions loaded:', questionSet.questions.length);
```

4. **Test Complete Flow**:
- Navigate to onboarding
- Complete self-reflection
- Verify risk assessment
- Check AI tone adjustment

## Troubleshooting

### Common Issues

**PGRST116 Error**:
- Ensure user is authenticated before loading questions
- Check RLS policies are correctly applied
- Verify database has active question sets

**Import Errors**:
- Error classes should be imported as classes, not types
- Check TypeScript compilation

**Authentication Timing**:
- Component waits for user authentication before loading questions
- Clear error messages for authentication issues

## Privacy & Security

- **No Permanent Storage**: Responses used only for session
- **RLS Policies**: Database-level security for user data
- **Anonymized Analytics**: Risk levels tracked without personal details
- **Crisis Resources**: Professional help information readily available

## Maintenance

### Adding Question Types
1. Create new component in `components/selfReflection/questionTypes/`
2. Add to question renderer factory
3. Update TypeScript types
4. Add scoring logic to ScoringEngine

### Updating Risk Assessment
1. Modify risk assessment rules in database
2. Update actions and thresholds as needed
3. Test with various score combinations

### Performance Optimization
- Question sets are cached for 5 minutes
- Lazy loading of question components
- Optimized database queries with indexes

## Support

For questions or issues:
1. Check the test files for usage examples
2. Review the TypeScript types for API documentation
3. Examine the database schema for data structure
4. Test with the provided mock data

This implementation provides a solid foundation for emotional wellness assessment while maintaining flexibility for future enhancements and requirements.
