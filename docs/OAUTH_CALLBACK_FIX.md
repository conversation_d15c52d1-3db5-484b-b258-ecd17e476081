# OAuth Callback Deep Link Fix

## Issue Resolved
**Problem**: After Google OAuth login, users were redirected to auth screen instead of self-reflection due to incomplete OAuth flow completion.

## Root Cause Analysis
The deep link handler was **incorrectly ignoring** OAuth callback codes instead of processing them to complete the authentication flow.

### Previous (Incorrect) Implementation
```typescript
// For Convex Auth, skip OAuth callback processing
// Let Convex handle OAuth callbacks natively
if (url.includes('code=') || url.includes('access_token=')) {
  console.log('OAuth callback detected - letting Convex Auth handle natively');
  return; // ❌ This was the problem!
}
```

### Correct Convex Auth OAuth Flow
According to Convex Auth documentation, the proper React Native/Expo OAuth flow is:

1. **OAuth starts**: User clicks sign-in → opens Google OAuth
2. **Google redirects to Convex**: `https://disciplined-butterfly-269.convex.cloud/api/auth/callback/google?code=...`
3. **Convex processes & redirects back**: Convex handles OAuth and redirects to app
4. **A<PERSON> receives deep link**: `http://localhost:8082/?code=14141860` ✅ **This is expected!**
5. **App MUST complete sign-in**: Extract code and call `signIn("google", { code })`

## Changes Made

### 1. Added Required Import
**File**: `layouts/ConvexLayout.tsx`

```typescript
import { ConvexAuthProvider, useAuthActions } from "@convex-dev/auth/react";
```

### 2. Added useAuthActions Hook
**In RootNavigator component**:

```typescript
function RootNavigator() {
  const {
    user,
    loading,
    userStatusLoading,
    connectionError,
    onboardingCompleted,
    selfReflectionCompleted,
    selfReflectionSkipped,
    retryConnection
  } = useConvexAuthContext();
  
  const { signIn } = useAuthActions(); // ← Added this
```

### 3. Fixed Deep Link Handler
**Replaced the incorrect OAuth handling**:

```typescript
useEffect(() => {
  // Handle deep links for OAuth callback
  const handleDeepLink = async (url: string) => {
    console.log('Deep link received:', url);

    // Extract OAuth code and complete sign-in
    if (url.includes('code=')) {
      try {
        const urlObj = new URL(url);
        const code = urlObj.searchParams.get('code');
        
        if (code) {
          console.log('🔐 [OAuth] Completing sign-in with code:', code);
          // Complete the OAuth flow
          await signIn("google", { code });
          console.log('🔐 [OAuth] Sign-in completed successfully');
        } else {
          console.warn('🔐 [OAuth] No code found in URL:', url);
        }
      } catch (error) {
        console.error('🔐 [OAuth] Sign-in completion failed:', error);
      }
      return;
    }

    // Handle other deep links
    if (url.includes('temani://')) {
      console.log('Non-OAuth deep link:', url);
      // Handle other deep links here
    }
  };

  // Listen for deep links
  const subscription = Linking.addEventListener('url', ({ url }) => {
    handleDeepLink(url);
  });

  // Handle initial URL if app was opened via deep link
  Linking.getInitialURL().then((url) => {
    if (url) {
      handleDeepLink(url);
    }
  });

  return () => {
    subscription?.remove();
  };
}, [signIn]); // ← Added signIn dependency
```

## Expected Results

### Before Fix:
```
1. Deep link received: http://localhost:8082/?code=14141860
2. OAuth callback detected - letting Convex Auth handle natively
3. Return early (no processing) ❌
4. Authentication state remains unstable
5. User redirected to auth instead of self-reflection
```

### After Fix:
```
1. Deep link received: http://localhost:8082/?code=14141860
2. Extract code: 14141860
3. Call signIn("google", { code }) ✅
4. Authentication completes successfully
5. User state becomes stable: user: true
6. Direct navigation to self-reflection ✅
```

## Key Insight from Research

The Convex Auth documentation explicitly shows this pattern for React Native:
```tsx
const result = await openAuthSessionAsync(redirect!.toString(), redirectTo);
if (result.type === "success") {
  const { url } = result;
  const code = new URL(url).searchParams.get("code")!;
  await signIn("github", { code }); // ← This is what we were missing!
}
```

**The deep link with the code parameter is the final step that completes the OAuth flow!** By ignoring it, we never completed the authentication, causing:
- ✅ Google Console configured correctly
- ✅ Convex processes the OAuth callback  
- ✅ Deep link received as expected
- ❌ But authentication never completed because we ignored the code

## Success Indicators

- ✅ No more "OAuth callback detected - letting Convex Auth handle natively" logs
- ✅ New logs: "🔐 [OAuth] Completing sign-in with code: XXXXX"
- ✅ New logs: "🔐 [OAuth] Sign-in completed successfully"
- ✅ Stable authentication state after OAuth
- ✅ Direct navigation to self-reflection screen
- ✅ No redirect to auth screen after Google login

## Files Modified
- `layouts/ConvexLayout.tsx` - Added useAuthActions import and fixed deep link OAuth handling

The fix properly completes the Convex Auth OAuth flow by processing the authorization code received via deep link, resolving the authentication state instability and routing issues.
