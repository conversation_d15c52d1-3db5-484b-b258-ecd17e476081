# Voice Transcription Integration

This document describes the ElevenLabs Conversational AI transcription integration implemented in the Temani app.

## Overview

The voice transcription feature captures real-time conversations between users and the AI agent, providing:
- Real-time transcription display
- Console logging for debugging and monitoring
- Temporary session-based conversation storage
- **Note: Voice conversations are NOT automatically saved to chat history**

## Architecture

### Components

1. **useVoiceTranscript Hook** (`hooks/useVoiceTranscript.ts`)
   - Manages transcript state and message processing
   - Handles ElevenLabs message parsing
   - Integrates with chat service for persistence

2. **TranscriptView Component** (`components/TranscriptView.tsx`)
   - Displays real-time transcription
   - Provides save/clear functionality
   - Animated slide-up interface

3. **Enhanced Chat Service** (`lib/chatService.ts`)
   - Added `sendVoiceMessage()` method
   - Support for transcription field in messages
   - Voice message metadata handling

### Database Schema

The `messages` table includes:
- `transcription` (TEXT): Stores voice transcriptions
- `metadata` (JSONB): Includes `isVoiceMessage` flag

## Usage

### Basic Integration

```typescript
import { useVoiceTranscript } from '@/hooks/useVoiceTranscript';
import { TranscriptView } from '@/components/TranscriptView';

const transcript = useVoiceTranscript();

// Start a new voice session (creates new thread)
onConnect: async () => {
  await transcript.startNewVoiceSession();
},

// In ElevenLabs onMessage callback
onMessage: (message) => {
  transcript.handleVoiceMessage(message);
}

// In render
<TranscriptView
  messages={transcript.messages}
  isVisible={transcript.isVisible}
  loading={transcript.loading}
  error={transcript.error}
  onClose={transcript.hideTranscript}
  onSaveToChat={handleSaveTranscript}
  onClear={handleClearTranscript}
/>
```

### Message Processing

The system processes ElevenLabs messages with the following structure:

```typescript
interface ElevenLabsMessage {
  id?: string;
  message?: string;
  text?: string;
  content?: string;
  source?: 'ai' | 'user';
  role?: 'assistant' | 'user';
  sender?: 'ai' | 'user';
  type?: 'agent_response' | 'user_input';
  isFinal?: boolean;
}
```

### Features

1. **Real-time Transcription**
   - Tentative messages shown with dashed borders
   - Final messages replace tentative ones
   - Auto-scroll to latest message

2. **Message Types**
   - User voice input (with transcription)
   - AI agent responses
   - Tentative vs final message states

3. **Persistence**
   - Automatic saving to chat database
   - Thread creation for voice conversations
   - Integration with existing chat history

4. **UI/UX**
   - Slide-up animation
   - Gradient header matching app design
   - Save/clear/close actions
   - Error handling and loading states

## Implementation Details

### Message Flow

1. User speaks → ElevenLabs processes → `onMessage` callback
2. `handleVoiceMessage()` parses and categorizes message
3. Tentative messages shown immediately in UI
4. Final messages replace tentative and are logged to console
5. AI responses processed, displayed, and logged
6. **No automatic database saving** - transcripts exist only during session

### Error Handling

- Network errors during save operations
- Invalid message formats
- Database connection issues
- User authentication problems

### Performance Considerations

- Debounced tentative message updates
- Efficient message deduplication
- Lazy loading of chat history
- Memory cleanup on component unmount

## Console Logging

Voice transcriptions are logged to the console for debugging and monitoring purposes:

### Log Format

```
🎤 [TRANSCRIPTION] Voice message received: { timestamp, type, hasUserTranscript, hasAgentResponse, isFinal, threadId }
🎤 [TRANSCRIPTION] 👤 USER: { text, isFinal, timestamp }
🎤 [TRANSCRIPTION] 🤖 AI: { text, timestamp }
🎤 [TRANSCRIPTION] 📝 Final message processed: { timestamp, sender, content, contentLength, messageId, sessionId }
🎤 [TRANSCRIPTION] 💬 Complete message: { sender, text, timestamp }
```

### Log Levels

- **Voice message received**: Raw message from ElevenLabs
- **USER/AI**: Parsed transcription content
- **Final message processed**: Completed transcription with metadata
- **Complete message**: Human-readable format

## Configuration

### Environment Variables

Required for ElevenLabs integration:
- `EXPO_PUBLIC_ELEVENLABS_AGENT_ID`

### Database Setup

The transcription column is added via migration:
```sql
ALTER TABLE messages ADD COLUMN transcription text;
```

## Testing

### Manual Testing

1. Start voice conversation
2. Speak to test user transcription
3. Wait for AI response
4. Check transcript view shows both messages
5. Save to chat and verify in chat history
6. Test clear functionality

### Edge Cases

- Empty messages
- Network interruptions
- Rapid message sequences
- Long conversations
- Permission denials

## Future Enhancements

1. **Export Options**
   - PDF export
   - Email sharing
   - Text file download

2. **Search and Filter**
   - Search within transcripts
   - Filter by date/speaker
   - Conversation summaries

3. **Voice Analysis**
   - Sentiment analysis
   - Speaking time metrics
   - Conversation insights

4. **Accessibility**
   - Screen reader support
   - Voice commands
   - High contrast mode

## Troubleshooting

### Common Issues

1. **Messages not appearing**
   - Check ElevenLabs agent configuration
   - Verify onMessage callback integration
   - Check console for parsing errors

2. **Save failures**
   - Verify user authentication
   - Check database connection
   - Review Supabase RLS policies

3. **UI not showing**
   - Check transcript.isVisible state
   - Verify TranscriptView props
   - Check for z-index conflicts

### Debug Logging

Enable debug logging by checking console for:
- `[VoiceTranscript]` prefixed messages
- ElevenLabs message structures
- Database save operations
