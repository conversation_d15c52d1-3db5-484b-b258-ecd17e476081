# Google Authentication Setup Guide

This guide will help you properly configure and test Google Authentication in the Temani app.

## 🚨 Important: Cannot Test in Expo Go

Google OAuth with custom URL schemes **cannot be tested in Expo Go**. You must create a development build to test authentication.

## 📋 Prerequisites

1. Google Cloud Console project with OAuth 2.0 credentials
2. Supabase project with Google OAuth provider enabled
3. Development build environment set up
4. Required dependencies installed (see Installation section)

## 🔧 Configuration Steps

### 1. Install Required Dependencies

First, install the necessary dependencies for optimal Google Auth performance:

```bash
# Install required dependencies
npm run install:deps

# Or manually:
npx expo install @react-native-async-storage/async-storage expo-auth-session
```

### 2. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to **APIs & Services** → **Credentials**
4. Create or edit your OAuth 2.0 Client ID

#### Authorized JavaScript Origins
Add these URLs:
```
https://your-project-ref.supabase.co
http://localhost:8081
https://your-deployment-url.com
```

#### Authorized Redirect URIs
Add these URLs:
```
https://your-project-ref.supabase.co/auth/v1/callback
http://localhost:8081
https://your-deployment-url.com
```

### 3. Supabase Configuration

1. Go to your Supabase project dashboard
2. Navigate to **Authentication** → **Providers**
3. Enable **Google** provider
4. Enter your Google **Client ID** and **Client Secret**

#### URL Configuration
1. Go to **Authentication** → **URL Configuration**
2. Set **Site URL** to your deployment URL
3. Add redirect URLs:
   - `http://localhost:8081` (development)
   - `https://your-deployment-url.com` (production)
   - `temani://auth/callback` (mobile app)

### 4. Environment Variables

Ensure your `.env.local` file contains:
```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

## 🏗️ Development Build Setup

### Option 1: Local Development Build

#### iOS
```bash
# Install dependencies
npm install

# Create iOS development build
npx expo run:ios
```

#### Android
```bash
# Install dependencies
npm install

# Create Android development build
npx expo run:android
```

### Option 2: EAS Development Build

#### Build for iOS
```bash
# Install EAS CLI if not already installed
npm install -g @expo/eas-cli

# Login to Expo
eas login

# Build development client for iOS
eas build --profile development --platform ios
```

#### Build for Android
```bash
# Build development client for Android
eas build --profile development --platform android
```

## 🧪 Testing Google OAuth

### 1. Install Development Build
- Install the development build on your physical device or simulator
- The app icon should show "Development" overlay

### 2. Test OAuth Flow
1. Open the app
2. Navigate to the login screen
3. Tap "Lanjutkan dengan Google"
4. Complete Google sign-in in the browser
5. App should redirect back and show success message
6. You should be logged into the main app

### 3. Debugging

#### Check Logs
```bash
# View logs during testing
npx expo start --dev-client
```

#### Common Issues

**Issue**: "Invalid redirect URI"
- **Solution**: Verify Supabase redirect URLs include `temani://auth/callback`

**Issue**: "OAuth callback not working"
- **Solution**: Ensure URL scheme in app.json matches AuthContext (`temani`)

**Issue**: "App doesn't open after Google sign-in"
- **Solution**: Check that the app is installed and URL scheme is registered

## 🔍 Verification Checklist

- [ ] Google Cloud Console has correct redirect URIs
- [ ] Supabase Google provider is enabled with correct credentials
- [ ] Supabase redirect URLs include mobile scheme
- [ ] Environment variables are set correctly
- [ ] Development build is installed (not Expo Go)
- [ ] URL scheme matches between app.json and AuthContext
- [ ] OAuth callback route exists and handles tokens

## 🚀 Production Deployment

### Web Deployment
1. Update Supabase redirect URLs with production domain
2. Update Google Cloud Console with production URLs
3. Deploy to your hosting platform

### Mobile App Store
1. Create production builds with EAS
2. Submit to App Store/Play Store
3. Ensure production OAuth credentials are configured

## 📱 URL Scheme Details

The app uses the URL scheme `temani://` for deep linking:
- OAuth callback: `temani://auth/callback`
- Password reset: `temani://reset-password`

This scheme is defined in:
- `app.json` → `expo.scheme`
- `context/AuthContext.tsx` → redirect URLs
- `eas.json` → bundle identifiers

## 🆘 Troubleshooting

### OAuth Not Working
1. Check browser network tab for redirect URLs
2. Verify Supabase logs for authentication attempts
3. Ensure development build (not Expo Go) is being used
4. Check that URL scheme is properly registered

### App Not Opening After Sign-in
1. Verify the app is installed on the device
2. Check that URL scheme matches exactly
3. Test deep linking manually: `npx uri-scheme open temani://auth/callback --ios`

### Session Not Persisting
1. Check Supabase client configuration
2. Verify token handling in callback screen
3. Ensure AuthContext is properly updating state

## 📞 Support

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify all configuration steps are completed
3. Test with a simple deep link first
4. Ensure you're using a development build, not Expo Go
