import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, Image, ScrollView, Platform, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import StandardHeader from '@/components/StandardHeader';
import { HEADER_CONFIGS } from '@/styles/HeaderStyles';
import { useJournalCalendar } from '@/hooks/useJournalCalendar';

import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { useLoadingTimeout } from '@/hooks/useLoadingTimeout';
import { JournalErrorBoundary } from '@/components/JournalErrorBoundary';
import { runFullDebug } from '@/debug/journal-debug';
import {
  useJournalHistory,
  useTodayEntries,
  useJournalQuestions,
  useUserJournalFields,
  useCreateJournalEntry,
  useUpdateJournalEntry,

} from '@/hooks/useJournalQuery';

import { CustomFieldManagerModal } from '@/components/journal/CustomFieldManagerModal';
import CollapsibleCalendar from '@/components/CollapsibleCalendar';
import { UnifiedJournalCard } from '@/components/journal/UnifiedJournalCard';
import type { JournalEntryWithQuestion } from '@/types/journal';



// Loading component for journal cards
const JournalCardSkeleton: React.FC = () => (
  <View style={[styles.card, { opacity: 0.6 }]}>
    <View style={[styles.cardQuestion, { backgroundColor: '#F0F0F0', height: 20, borderRadius: 4 }]} />
    <View style={styles.inputContainer}>
      <View style={[styles.input, { backgroundColor: '#F8F8F8', minHeight: 80 }]} />
      <View style={[styles.checkButton, { backgroundColor: '#F0F0F0' }]} />
    </View>
  </View>
);

// Error component for journal
const JournalError: React.FC<{ message: string; onRetry: () => void }> = ({ message, onRetry }) => (
  <View style={styles.errorContainer}>
    <Ionicons name="alert-circle" size={48} color="#FF6B6B" />
    <Text style={styles.errorText}>{message}</Text>
    <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
      <Text style={styles.retryButtonText}>Coba Lagi</Text>
    </TouchableOpacity>
  </View>
);

// Journal history card component
interface JournalHistoryCardProps {
  entry: JournalEntryWithQuestion;
}

const JournalHistoryCard: React.FC<JournalHistoryCardProps> = ({ entry }) => {
  return (
    <View style={styles.historyCard}>
      <Text style={styles.historyCardQuestion}>{entry.question.question_text}</Text>
      <Text style={styles.historyCardAnswer}>{entry.answer}</Text>
    </View>
  );
};

// Journal history view component with integrated calendar
const JournalHistoryView: React.FC = () => {
  const { user } = useUniversalAuth();

  // Use new TanStack Query hook for journal history
  const {
    data: historyGroups = [],
    isLoading: loading,
    error,
    refetch
  } = useJournalHistory();

  // For now, we'll disable pagination until we implement infinite queries
  const hasMore = false;
  const loadMore = () => {};
  const {
    currentMonth,
    selectedDate,
    entryDates,
    entriesForDate,
    loading: calendarLoading,
    error: calendarError,
    navigateMonth,
    selectDate,
    goToToday,

  } = useJournalCalendar();

  const [isDateFilterActive, setIsDateFilterActive] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Use loading timeout hook
  const { hasTimedOut: loadingTimeout, reset: resetTimeout } = useLoadingTimeout(loading, {
    timeout: 15000, // 15 seconds
    onTimeout: () => {
      console.warn('[JournalHistoryView] Loading timed out after 15 seconds');
    }
  });

  // Load history when component mounts (only if not already loaded)
  React.useEffect(() => {
    console.log('[JournalHistoryView] useEffect triggered:', {
      historyGroupsLength: historyGroups.length,
      loading,
      retryCount,
      hasUser: !!user,
      userId: user?.id || 'no-user-id',
      hasInitialized
    });

    // TanStack Query handles initialization automatically, but we can trigger refetch if needed
    if (historyGroups.length === 0 && !loading && !hasInitialized && user?.id) {
      console.log('[JournalHistoryView] Initializing - calling refetch');
      setHasInitialized(true);
      refetch();
    }
  }, [historyGroups.length, loading, hasInitialized, user?.id, refetch, retryCount, user]);

  const handleRetry = () => {
    console.log('[JournalHistoryView] Retry button pressed, count:', retryCount + 1);
    setRetryCount(prev => prev + 1);
    setHasInitialized(false); // Reset initialization flag
    resetTimeout();
    refetch();
  };

  const handleDateSelect = (date: string) => {
    selectDate(date);
    setIsDateFilterActive(true);
  };

  const handleShowAllEntries = () => {
    setIsDateFilterActive(false);
    selectDate(null);
  };

  // Show loading state for initial load
  if (loading && historyGroups.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#DD76B9" />
        <Text style={styles.loadingText}>
          {loadingTimeout ? 'Loading memakan waktu lama...' : 'Memuat riwayat journal...'}
        </Text>
        {/* Debug info */}
        {__DEV__ && (
          <View style={styles.debugContainer}>
            <Text style={styles.debugText}>Debug Info:</Text>
            <Text style={styles.debugText}>User ID: {user?.id || 'No user'}</Text>
            <Text style={styles.debugText}>Loading: {loading ? 'Yes' : 'No'}</Text>
            <Text style={styles.debugText}>History Groups: {historyGroups.length}</Text>
            <Text style={styles.debugText}>Error: {error ? String(error) : 'None'}</Text>
            <Text style={styles.debugText}>Retry Count: {retryCount}</Text>
          </View>
        )}
        {loadingTimeout && (
          <>
            <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
              <Text style={styles.retryButtonText}>Coba Lagi</Text>
            </TouchableOpacity>
            {__DEV__ && (
              <TouchableOpacity
                style={[styles.retryButton, { backgroundColor: '#4CAF50', marginTop: 8 }]}
                onPress={() => runFullDebug()}
              >
                <Text style={styles.retryButtonText}>Run Debug</Text>
              </TouchableOpacity>
            )}
          </>
        )}
        {retryCount > 0 && (
          <Text style={styles.retryCountText}>Percobaan ke-{retryCount + 1}</Text>
        )}
      </View>
    );
  }

  // Show error state for initial load
  if (error && historyGroups.length === 0) {
    return (
      <JournalError
        message={error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat riwayat'}
        onRetry={() => refetch()}
      />
    );
  }

  return (
    <View style={styles.historyContainer}>
      {/* Collapsible Calendar - always show */}
      <CollapsibleCalendar
        currentMonth={currentMonth}
        selectedDate={selectedDate}
        entryDates={entryDates}
        entriesForDate={entriesForDate}
        loading={calendarLoading}
        error={calendarError}
        onDateSelect={handleDateSelect}
        onNavigateMonth={navigateMonth}
        onGoToToday={goToToday}
        onShowAllEntries={handleShowAllEntries}
        isDateFilterActive={isDateFilterActive}
      />

      {/* History Content - only show when not filtering by date */}
      {!isDateFilterActive && (
        <>
          {historyGroups.length === 0 ? (
            // Show empty state when no history entries
            <View style={styles.emptyContainer}>
              <Ionicons name="book-outline" size={64} color="#C5C5C5" />
              <Text style={styles.emptyTitle}>Belum Ada Riwayat</Text>
              <Text style={styles.emptySubtitle}>Mulai menulis journal untuk melihat riwayat Anda</Text>
            </View>
          ) : (
            // Show history entries
            <ScrollView
              style={styles.scrollView}
              contentContainerStyle={styles.historyContentContainer}
              onScroll={({ nativeEvent }) => {
                const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
                const paddingToBottom = 20;
                if (layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom) {
                  if (hasMore && !loading) {
                    loadMore();
                  }
                }
              }}
              scrollEventThrottle={400}
            >
              {historyGroups.map((dateGroup, dateIndex) => (
                <View key={`date-${dateIndex}`} style={styles.dateGroup}>
                  <Text style={styles.dateHeader}>{dateGroup.date}</Text>
                  {dateGroup.entries.map((entry, entryIndex) => (
                    <JournalHistoryCard
                      key={`entry-${entry.id}`}
                      entry={entry}
                    />
                  ))}
                </View>
              ))}

              {loading && historyGroups.length > 0 && (
                <View style={styles.loadMoreContainer}>
                  <ActivityIndicator size="small" color="#DD76B9" />
                  <Text style={styles.loadMoreText}>Memuat lebih banyak...</Text>
                </View>
              )}

              {error && historyGroups.length > 0 && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>
                    {error instanceof Error ? error.message : 'Terjadi kesalahan'}
                  </Text>
                  <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
                    <Text style={styles.retryButtonText}>Coba Lagi</Text>
                  </TouchableOpacity>
                </View>
              )}
            </ScrollView>
          )}
        </>
      )}
    </View>
  );
};

// Journal today view component
const JournalTodayView: React.FC = () => {
  const [showFieldManager, setShowFieldManager] = useState(false);

  // Use new TanStack Query hooks for user fields (system + custom)
  const {
    data: userFields,
    isLoading: fieldsLoading,
    error: fieldsError,
    refetch: refetchFields
  } = useUserJournalFields();

  // Keep system questions for backward compatibility
  const {
    data: systemQuestions,
    isLoading: questionsLoading,
    error: questionsError,
    refetch: refetchQuestions
  } = useJournalQuestions();

  const {
    data: entries,
    isLoading: entriesLoading,
    error: entriesError,
    refetch: refetchEntries
  } = useTodayEntries();

  const createEntryMutation = useCreateJournalEntry();
  const updateEntryMutation = useUpdateJournalEntry();

  // Use userFields as primary source, fallback to systemQuestions for compatibility
  const fieldsToRender = userFields || systemQuestions || [];
  const isLoading = fieldsLoading || questionsLoading || entriesLoading;
  const hasError = fieldsError || questionsError || entriesError;

  const handleSave = async (fieldId: string, answer: string) => {
    const existingEntry = entries?.find(entry => entry.question_id === fieldId);

    if (existingEntry) {
      await updateEntryMutation.mutateAsync({ entryId: existingEntry.id, answer });
    } else {
      await createEntryMutation.mutateAsync({ question_id: fieldId, answer });
    }
  };

  const handleFieldManagerOpen = () => {
    setShowFieldManager(true);
  };

  if (isLoading) {
    return (
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <Image
          source={require('@/assets/images/journal.png')}
          style={styles.illustration}
          resizeMode="contain"
        />
        <JournalCardSkeleton />
        <JournalCardSkeleton />
      </ScrollView>
    );
  }

  if (hasError) {
    const errorMessage = fieldsError?.message || questionsError?.message || entriesError?.message || 'Terjadi kesalahan';
    return (
      <JournalError
        message={errorMessage}
        onRetry={() => {
          refetchFields();
          refetchEntries();
          refetchQuestions();
        }}
      />
    );
  }

  return (
    <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
      {/* Journal illustration */}
      <Image
        source={require('@/assets/images/journal.png')}
        style={styles.illustration}
        resizeMode="contain"
      />

      {/* Journal fields (system + custom) */}
      {fieldsToRender?.map((field: any) => {
        const existingEntry = entries?.find(entry => entry.question_id === field.id);
        return (
          <UnifiedJournalCard
            key={field.id}
            field={field}
            existingEntry={existingEntry}
            onSave={handleSave}
            showAIButton={true}
            autoSave={true}
            showSaveButton={true}
          />
        );
      })}

      {/* Enhanced Field Management Button - Moved to Bottom */}
      <TouchableOpacity
        style={styles.enhancedManageFieldsButton}
        onPress={handleFieldManagerOpen}
        accessibilityLabel="Kelola dan tambah field jurnal kustom"
        accessibilityHint="Buka pengaturan untuk mengelola field jurnal kustom"
      >
        <LinearGradient
          colors={['#FFD572', '#FEBD38']}
          style={styles.buttonGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <Ionicons name="add-circle-outline" size={22} color="#454459" />
          <Text style={styles.enhancedManageFieldsText}>Kelola Field Kustom</Text>
        </LinearGradient>
      </TouchableOpacity>

      {/* Custom Field Manager Modal */}
      <CustomFieldManagerModal
        visible={showFieldManager}
        onClose={() => setShowFieldManager(false)}
        onFieldSelect={(field) => {
          // Handle field selection if needed
          console.log('Field selected:', field);
        }}
        showAIGenerator={true}
      />
    </ScrollView>
  );
};

export default function JournalScreen() {
  const [activeTab, setActiveTab] = useState<'journal' | 'history'>('journal');

  const handleTabChange = (tab: 'journal' | 'history') => {
    setActiveTab(tab);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <LinearGradient
        colors={['#FFE7F6', '#FFFFFF']}
        locations={[0, 0.58]}
        style={styles.backgroundGradient}
      />

      {/* Top navigation */}
      <StandardHeader
        title={activeTab === 'journal' ? HEADER_CONFIGS.journal.title : HEADER_CONFIGS.journalHistory.title}
        iconName={HEADER_CONFIGS.journal.iconName}
        iconColor={HEADER_CONFIGS.journal.iconColor}
      />
      
      {/* Tab selector */}
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'journal' && styles.activeTab]} 
          onPress={() => handleTabChange('journal')}
        >
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Ionicons 
              name="create" 
              size={16} 
              color={activeTab === 'journal' ? "#DD76B9" : "#FFFFFF"} 
              style={{marginRight: 4}} 
            />
            <Text style={[styles.tabText, activeTab === 'journal' && styles.activeTabText]}>
              Journal
            </Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => handleTabChange('history')}
        >
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Ionicons
              name="time"
              size={16}
              color={activeTab === 'history' ? "#DD76B9" : "#FFFFFF"}
              style={{marginRight: 4}}
            />
            <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
              Riwayat
            </Text>
          </View>
        </TouchableOpacity>

      </View>
      
      {/* Render active view based on tab */}
      {activeTab === 'journal' ? (
        <JournalTodayView />
      ) : (
        <JournalErrorBoundary
          onError={(error, errorInfo) => {
            console.error('[Journal] Error in history view:', error, errorInfo);
          }}
        >
          <JournalHistoryView />
        </JournalErrorBoundary>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },

  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginTop: 10,
    backgroundColor: '#DD76B9',
    borderRadius: 30,
    padding: 4,
    height: 44,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
    height: 36,
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  activeTabText: {
    color: '#DD76B9',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    alignItems: 'center',
  },
  historyContentContainer: {
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  illustration: {
    width: 250,
    height: 250,
    marginBottom: 20,
  },
  card: {
    backgroundColor: '#FFEBF9',
    borderRadius: 24,
    padding: 16,
    marginBottom: 16,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  cardQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4285F4',
    marginBottom: 12,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderRadius: 24,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  input: {
    flex: 1,
    minHeight: 40,
    fontSize: 16,
    color: '#9C2B6C', // Dark pink color for input text
    paddingVertical: 4,
  },
  checkButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#DD76B9',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
    marginLeft: 8,
  },
  enhancedManageFieldsButton: {
    marginTop: 24,
    marginBottom: 20,
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  enhancedManageFieldsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#454459',
    marginLeft: 10,
    letterSpacing: 0.3,
  },
  dateGroup: {
    marginBottom: 24,
  },
  dateHeader: {
    fontSize: 16,
    fontWeight: '700',
    color: '#5B3E31',
    marginBottom: 12,
  },
  historyCard: {
    backgroundColor: '#FFF',
    borderRadius: 24,
    padding: 16,
    marginBottom: 12,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  historyCardQuestion: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0056A8',
    marginBottom: 8,
  },
  historyCardAnswer: {
    fontSize: 14,
    color: '#4B3425',
    lineHeight: 20,
  },
  // Loading states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadMoreText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  // Error states
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#DD76B9',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  retryCountText: {
    fontSize: 12,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  debugContainer: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    marginHorizontal: 20,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  // Empty states
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  // History view styles
  historyContainer: {
    flex: 1,
  },
});
