import { Tabs } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import TabBar from '@/components/TabBar';
import { ConnectionErrorBanner } from '@/components/ConnectionErrorBanner';

export default function TabLayout() {
  const { user, loading, userStatusLoading, selfReflectionCompleted, selfReflectionSkipped } = useUniversalAuth();

  // Don't do any routing in tab layout - let root layout handle it
  // This prevents routing conflicts and loops

  // Show loading screen while checking authentication and user status
  if (loading || userStatusLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#A08CFB" />
        <Text style={styles.loadingText}>
          {loading ? 'Loading...' : 'Loading user data...'}
        </Text>
      </View>
    );
  }

  // Don't render anything if user is not authenticated or hasn't completed/skipped self-reflection
  if (!user || (!selfReflectionCompleted && !selfReflectionSkipped)) {
    return null;
  }

  return (
    <View style={{ flex: 1 }}>
      <ConnectionErrorBanner />
      <Tabs
      screenOptions={{
        headerShown: false,
      }}
      tabBar={(props) => <TabBar {...props} />}
    >
      <Tabs.Screen
        name="chat"
        options={{
          title: 'Chat',
        }}
      />
      <Tabs.Screen
        name="appointment"
        options={{
          title: 'Appointment',
        }}
      />
      <Tabs.Screen
        name="index"
        options={{
          title: 'Beranda',
        }}
      />
      <Tabs.Screen
        name="mood"
        options={{
          title: 'Mood',
        }}
      />
      <Tabs.Screen
        name="journal"
        options={{
          title: 'Journal',
        }}
      />
    </Tabs>
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FEFEFE',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
});