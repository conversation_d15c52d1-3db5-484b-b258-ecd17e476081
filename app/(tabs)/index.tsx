import React from 'react';
import { router, useFocusEffect } from 'expo-router';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  Image,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { useJournal } from '@/context/JournalContext';
import { useTodayMoodEntry, useCurrentWeekMood } from '@/hooks/useMoodQuery';
import { EnhancedCTAButton } from '@/components/mood/EnhancedCTAButton';
import { MOOD_ASSET_MAP, DAYS_OF_WEEK } from '@/types/mood';

// Helper function to format date in Indonesian
const formatDateIndonesian = (date: Date): string => {
  const days = ['Minggu', 'Senin', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'];
  const months = [
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'September', '<PERSON><PERSON><PERSON>', 'November', 'Desember'
  ];

  const dayName = days[date.getDay()];
  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  return `${dayName}, ${day}/${month.slice(0, 3)}/${year}`;
};

export default function HomeScreen() {
  const { user } = useUniversalAuth();
  const userName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'Pengguna';
  const { todayEntries, todayLoading: entriesLoading, refreshTodayEntries } = useJournal();

  // Fetch today's mood data for CTA state detection
  const {
    data: todayMoodData,
    isLoading: isLoadingTodayMoodData
  } = useTodayMoodEntry();

  // Fetch weekly mood data for mood tracker section
  const {
    data: weeklyMoodData,
    isLoading: isLoadingWeeklyMood,
    error: weeklyMoodError
  } = useCurrentWeekMood();

  // Detect if user has complete mood data for today (same logic as mood tab)
  const hasCompleteDataToday = user?.id && todayMoodData && (
    todayMoodData.mood !== null &&
    todayMoodData.physicalHealth !== null &&
    todayMoodData.sleepQuality !== null &&
    todayMoodData.dailyFeeling !== null
  );

  const todayDate = formatDateIndonesian(new Date());

  // Refresh data when screen comes into focus (e.g., navigating back from journal page)
  useFocusEffect(
    React.useCallback(() => {
      refreshTodayEntries();
    }, [refreshTodayEntries])
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      
      {/* Background decoration - same as auth pages */}
      <Image 
        source={require('../../assets/images/background-decoration.png')} 
        style={styles.backgroundDecoration} 
        resizeMode="cover"
      />
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header with greeting and settings */}
        <View style={styles.header}>
          <Text style={styles.greeting}>Hai, {userName}!</Text>
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => router.push('/(settings)')}
            activeOpacity={0.7}
          >
            <Ionicons name="settings-outline" size={24} color="#1C1C1E" />
          </TouchableOpacity>
        </View>

        {/* Feature Cards Grid */}
        <View style={styles.featureCardsContainer}>
          {/* Chat Temani Card - Yellow */}
          <TouchableOpacity
            style={[styles.featureCard, styles.chatCard]}
            activeOpacity={0.7}
            onPress={() => router.push('/chat')}
          >
            <View style={styles.iconContainer}>
              <Image
                source={require('../../assets/images/chat-robot.png')}
                style={styles.cardIcon}
                resizeMode="contain"
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.cardTitle}>Chat</Text>
              <Text style={styles.cardSubtitle}>Temani</Text>
              <View style={[styles.cardAccent, styles.chatAccent]} />
            </View>
          </TouchableOpacity>
          
          {/* Ngobrol Temani Card - Blue */}
          <TouchableOpacity
            style={[styles.featureCard, styles.ngobrolCard]}
            activeOpacity={0.7}
            onPress={() => router.push('/chat?mode=voice')}
          >
            <View style={styles.iconContainer}>
              <Image
                source={require('../../assets/images/voice-phone.png')}
                style={styles.cardIcon}
                resizeMode="contain"
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.cardTitle}>Ngobrol</Text>
              <Text style={styles.cardSubtitle}>Temani</Text>
              <View style={[styles.cardAccent, styles.ngobrolAccent]} />
            </View>
          </TouchableOpacity>

          {/* Cek Mood Kamu Card - Green */}
          <TouchableOpacity
            style={[styles.featureCard, styles.moodCard]}
            activeOpacity={0.7}
            onPress={() => router.push('/mood')}
          >
            <View style={styles.iconContainer}>
              <Image
                source={require('../../assets/images/mood-check.png')}
                style={styles.cardIcon}
                resizeMode="contain"
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.cardTitle}>Cek Mood</Text>
              <Text style={styles.cardSubtitle}>Kamu</Text>
              <View style={[styles.cardAccent, styles.moodAccent]} />
            </View>
          </TouchableOpacity>
          
          {/* Isi Journal Kamu Card - Pink */}
          <TouchableOpacity
            style={[styles.featureCard, styles.journalCard]}
            activeOpacity={0.7}
            onPress={() => router.push('/journal')}
          >
            <View style={styles.iconContainer}>
              <Image
                source={require('../../assets/images/journal.png')}
                style={styles.cardIcon}
                resizeMode="contain"
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.cardTitle}>Isi Journal</Text>
              <Text style={styles.cardSubtitle}>Kamu</Text>
              <View style={[styles.cardAccent, styles.journalAccent]} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Enhanced CTA Button for Mood Tracking */}
        <View style={styles.ctaContainer}>
          <EnhancedCTAButton
            onPress={() => router.push('/mood?openTracker=true')}
            hasDataToday={hasCompleteDataToday || false}
            isLoading={isLoadingTodayMoodData || false}
          />
        </View>

        {/* Mood Tracker */}
        <TouchableOpacity
          style={styles.moodTrackerContainer}
          activeOpacity={0.7}
          onPress={() => router.push('/mood')}
        >
          <View style={styles.moodTrackerHeader}>
            <Text style={styles.moodTrackerTitle}>Mood kamu minggu ini</Text>
            <Ionicons name="chevron-forward" size={24} color="#000000" />
          </View>

          {weeklyMoodError && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Gagal memuat data mood</Text>
            </View>
          )}
          
          <View style={styles.moodIconsContainer}>
            {DAYS_OF_WEEK.map((day) => {
              const moodLevel = user?.id ? weeklyMoodData?.[day]?.mood : null;
              return (
                <View key={day} style={styles.dayMoodContainer}>
                  <View style={styles.moodIconCircle}>
                    {!user?.id ? (
                      <View style={styles.emptyMoodIcon}>
                        <Text style={styles.emptyMoodText}>?</Text>
                      </View>
                    ) : isLoadingWeeklyMood ? (
                      <View style={styles.loadingMoodIcon}>
                        <Text style={styles.loadingText}>•</Text>
                      </View>
                    ) : moodLevel ? (
                      <Image
                        source={MOOD_ASSET_MAP[moodLevel]}
                        style={styles.moodIcon}
                        resizeMode="contain"
                      />
                    ) : (
                      <View style={styles.emptyMoodIcon}>
                        <Text style={styles.emptyMoodText}>?</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.dayLabel}>{day}</Text>
                </View>
              );
            })}
          </View>
        </TouchableOpacity>
        
        {/* Journal Entry Section - Outer Container */}
        <View style={styles.journalOuterContainer}>
          <TouchableOpacity
            style={styles.journalEntryHeader}
            onPress={() => router.push('/journal')}
            activeOpacity={0.7}
          >
            <Text style={styles.journalEntryTitle}>Isi Jurnal Kamu</Text>
            <Ionicons name="chevron-forward" size={24} color="#000000" />
          </TouchableOpacity>
 
          {/* Journal Entry Inner Container with Gray Background */}
          <TouchableOpacity
            style={styles.journalEntryContainer}
            activeOpacity={0.7}
            onPress={() => router.push('/journal')}
          >
            <Text style={styles.journalEntryDate}>{todayDate}</Text>

            {entriesLoading ? (
              <View style={styles.journalLoadingContainer}>
                <ActivityIndicator size="small" color="#007A89" />
                <Text style={styles.journalLoadingText}>Memuat journal hari ini...</Text>
              </View>
            ) : todayEntries.length > 0 ? (
              todayEntries.map((entry) => (
                <View key={entry.id} style={styles.journalQuestionContainer}>
                  <Text style={styles.journalQuestion}>{entry.question.question_text}</Text>
                  <View style={styles.journalResponseContainer}>
                    <Text style={styles.journalResponse} numberOfLines={2}>
                      {entry.answer}
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.journalEmptyContainer}>
                <Ionicons name="create-outline" size={32} color="#007A89" />
                <Text style={styles.journalEmptyText}>Belum ada journal hari ini</Text>
                <Text style={styles.journalEmptySubtext}>Tap untuk mulai menulis</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
        
        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

// New styles for the redesigned homepage
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundDecoration: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  scrollContent: {
    paddingBottom: 20, // Reduced padding now that we don't have custom bottom nav
    paddingTop: 16,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1C1C1E',
  },
  featureCardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  featureCard: {
    width: '44%',
    aspectRatio: 3/4,
    borderTopRightRadius: 63,
    borderTopLeftRadius: 23,
    borderBottomLeftRadius: 23,
    borderBottomRightRadius: 23,
    padding: 16,
    marginBottom: 24,
    position: 'relative',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  chatCard: {
    backgroundColor: '#FFF8E1',
  },
  ngobrolCard: {
    backgroundColor: '#E3F2FD',
  },
  moodCard: {
    backgroundColor: '#E8F5E9',
  },
  journalCard: {
    backgroundColor: '#FCE4EC',
  },
  iconContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardIcon: {
    width: '80%',
    height: '80%',
  },
  textContainer: {
    width: '100%',
    marginTop: 'auto',
    paddingHorizontal: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'left',
    lineHeight: 24,
  },
  cardSubtitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'left',
    lineHeight: 24,
  },
  cardAccent: {
    width: '50%',
    height: 4,
    borderRadius: 2,
    marginTop: 8,
    marginBottom: 8,
  },
  chatAccent: {
    backgroundColor: '#FFD54F',
  },
  ngobrolAccent: {
    backgroundColor: '#90CAF9',
  },
  moodAccent: {
    backgroundColor: '#81C784',
  },
  journalAccent: {
    backgroundColor: '#F48FB1',
  },
  // Mood Tracker Styles
  moodTrackerContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 20,
    margin: 24,
    marginTop: 10,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  moodTrackerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  moodTrackerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
  },
  moodIconsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayMoodContainer: {
    alignItems: 'center',
    width: 40,
  },
  moodIconCircle: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  moodIcon: {
    width: 24,
    height: 24,
  },
  loadingMoodIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  emptyMoodIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
  },
  emptyMoodText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '600',
  },
  dayLabel: {
    fontSize: 12,
    color: '#4B4B4B',
    textAlign: 'center',
  },
  // Journal Entry Styles
  journalOuterContainer: {
    backgroundColor: '#F8F8F8',
    margin: 24,
    marginTop: 10,
    marginBottom: 10,
    padding: 20,
    borderRadius: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  journalEntryContainer: {
    backgroundColor: '#D9D9D9',
    borderRadius: 20,
    marginTop: 10,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  journalEntryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  journalEntryTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
  },
  journalEntryDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  journalQuestionContainer: {
    marginBottom: 10,
  },
  journalQuestion: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007A89',
    marginBottom: 5,
  },
  journalResponseContainer: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#007A89',
    borderRadius: 20,
    padding: 8,
    paddingVertical: 10,
  },
  journalResponse: {
    fontSize: 11,
    color: '#007A89',
    lineHeight: 15,
    fontWeight: '500',
  },
  questionSection: {
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 24,
    paddingVertical: 24,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  questionContainer: {
    marginBottom: 24,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  responseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  responseText: {
    fontSize: 14,
    color: '#3A3A3C',
    lineHeight: 20,
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  navButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 50,
  },
  activeNavButton: {
    borderTopWidth: 2,
    borderTopColor: '#007AFF',
  },
  // Journal loading and empty states
  journalLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  journalLoadingText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  journalEmptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  journalEmptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007A89',
    marginTop: 8,
  },
  journalEmptySubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  ctaContainer: {
    marginHorizontal: 24,
    marginVertical: 16,
  },
  errorContainer: {
    padding: 12,
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#DC2626',
    textAlign: 'center',
  },
});