import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import StandardHeader from '@/components/StandardHeader';
import { HEADER_CONFIGS } from '@/styles/HeaderStyles';
import {
  DailyMoodData,
  DEFAULT_WEEKLY_MOOD_DATA
} from '../../types/mood';
import { DailyMoodTracker } from '../../components/mood/DailyMoodTracker';
import { MoodTrackingView } from '../../components/mood/MoodTrackingView';
import { MoodAnalyticsView } from '../../components/mood/MoodAnalyticsView';
import { useCurrentWeekMood, useSaveWeeklyMood, useMoodInitialization, useMoodSyncStatus } from '../../hooks/useMoodQuery';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { useUserTimezone } from '@/hooks/useUserProfile';
import { getCurrentDayOfWeek, getCurrentWeekStartInTimezone, debugTimezoneInfo } from '@/lib/timezoneUtils';
import { router, useLocalSearchParams } from 'expo-router';

export default function MoodScreen() {
  const { user } = useUniversalAuth();
  const { openTracker } = useLocalSearchParams();

  // State for tracking whether to show daily or weekly view
  const [showDailyTracker, setShowDailyTracker] = useState(false);

  // State for analytics mode (similar to voiceMode in chat)
  const [analyticsMode, setAnalyticsMode] = useState(false);

  // Get user timezone for timezone-aware calculations
  const { data: userTimezone } = useUserTimezone();

  // Get current day of week (timezone-aware)
  const [currentDay, setCurrentDay] = useState<DayOfWeek>('Sen');

  // Initialize mood service (migration and sync)
  const { isLoading: isInitializing } = useMoodInitialization();

  // Check sync status
  const { data: syncStatus } = useMoodSyncStatus();



  // Fetch current week's mood data from Supabase
  const {
    data: weeklyData,
    isLoading,
    error,
    refetch
  } = useCurrentWeekMood();

  // Mutation for saving weekly mood data
  const saveWeeklyMoodMutation = useSaveWeeklyMood();

  // Helper function to get Monday of current week (timezone-aware)
  const getCurrentWeekStart = useCallback(() => {
    const timezone = userTimezone || 'Asia/Jakarta'; // Fallback to default timezone
    return getCurrentWeekStartInTimezone(timezone);
  }, [userTimezone]);

  // Get current day on component mount (timezone-aware)
  useEffect(() => {
    // Always set current day, even if userTimezone is undefined (will use default)
    const timezone = userTimezone || 'Asia/Jakarta'; // Fallback to default timezone

    // Debug timezone information
    debugTimezoneInfo(timezone);

    const currentDayInTimezone = getCurrentDayOfWeek(timezone);
    console.log('[MoodScreen] Setting current day to:', currentDayInTimezone, 'for timezone:', timezone, 'userTimezone:', userTimezone);
    setCurrentDay(currentDayInTimezone);
  }, [userTimezone]);

  // Handle direct navigation to daily tracker from home page CTA
  useEffect(() => {
    if (openTracker === 'true') {
      setShowDailyTracker(true);
      // Clear the parameter to prevent reopening on subsequent renders
      router.replace('/mood');
    }
  }, [openTracker]);

  // Note: Weekly view is read-only. All editing is done through DailyMoodTracker.
  // These update functions are removed since weekly view components have readOnly={true}
  
  // Handle completing the daily tracker
  const handleDailyTrackerComplete = useCallback((data: DailyMoodData) => {
    console.log('[MoodScreen] Starting daily tracker completion process');
    console.log('[MoodScreen] Received data:', JSON.stringify(data, null, 2));
    console.log('[MoodScreen] Current day:', currentDay);
    console.log('[MoodScreen] User ID:', user?.id);
    console.log('[MoodScreen] Weekly data exists:', !!weeklyData);

    if (!weeklyData || !user?.id) {
      console.error('[MoodScreen] Missing required data - weeklyData:', !!weeklyData, 'user.id:', !!user?.id);
      return;
    }

    // Update the data for today
    const updatedWeeklyData = {
      ...weeklyData,
      [currentDay]: data
    };

    console.log('[MoodScreen] Updated weekly data:', JSON.stringify(updatedWeeklyData, null, 2));

    // Save to Supabase with correct week start (Monday of current week)
    const weekStart = getCurrentWeekStart();
    console.log('[MoodScreen] Week start calculated:', weekStart);

    saveWeeklyMoodMutation.mutate({
      weekStart,
      weeklyData: updatedWeeklyData
    }, {
      onSuccess: () => {
        console.log('[MoodScreen] Mood data saved successfully');
        // Close the daily tracker after successful save
        setShowDailyTracker(false);
      },
      onError: (error) => {
        console.error('[MoodScreen] Failed to save mood data:', error);
        // TODO: Add user-friendly error message
      }
    });
  }, [currentDay, weeklyData, user?.id, saveWeeklyMoodMutation, getCurrentWeekStart]);
  
  // Handle canceling the daily tracker
  const handleDailyTrackerCancel = useCallback(() => {
    setShowDailyTracker(false);
  }, []);

  // Navigate to analytics mode (internal switching)
  const handleNavigateToAnalytics = useCallback(() => {
    setAnalyticsMode(true);
  }, []);

  // Handle back from analytics mode
  const handleBackFromAnalytics = useCallback(() => {
    setAnalyticsMode(false);
  }, []);

  // Handle mode switching (similar to chat/call pattern)
  const handleModeSwitch = useCallback((mode: 'tracking' | 'analytics') => {
    setAnalyticsMode(mode === 'analytics');
  }, []);



  // Determine which data to use: Supabase data or fallback to empty data
  const displayData = user?.id ? weeklyData : DEFAULT_WEEKLY_MOOD_DATA;
  const isLoadingData = user?.id ? (isInitializing || isLoading) : false;
  const hasError = user?.id ? error : null;

  // Extract today's data from weekly data
  const todayData = weeklyData?.[currentDay] || null;

  // Detect if user has mood data for today (for CTA state)
  // Only check fields used in current 4-step DailyMoodTracker flow
  const hasDataToday = user?.id && todayData && (
    todayData.mood !== null &&
    todayData.physicalHealth !== null &&
    todayData.sleepQuality !== null &&
    todayData.dailyFeeling !== null
  );

  // Render weekly view or daily tracker based on state
  if (showDailyTracker) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <LinearGradient
          colors={['#C6FFDD', '#FFFFFF']}
          locations={[0, 0.29]}
          style={styles.backgroundGradient}
        />

        {/* Daily Tracker */}
        <DailyMoodTracker
          initialData={displayData?.[currentDay] || DEFAULT_WEEKLY_MOOD_DATA[currentDay]}
          onComplete={handleDailyTrackerComplete}
          onCancel={handleDailyTrackerCancel}
          currentDay={currentDay}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <LinearGradient
        colors={['#C6FFDD', '#FFFFFF']}
        locations={[0, 0.29]}
        style={styles.backgroundGradient}
      />

      {/* Top navigation */}
      <StandardHeader
        title={analyticsMode ? "Mood Analytics" : HEADER_CONFIGS.mood.title}
        iconName={analyticsMode ? "analytics" : HEADER_CONFIGS.mood.iconName}
        iconColor={analyticsMode ? "#4EAF64" : HEADER_CONFIGS.mood.iconColor}
        backgroundColor={analyticsMode ? 'rgba(78, 175, 100, 0.1)' : 'transparent'}
        leftElement={analyticsMode ? (
          <TouchableOpacity onPress={handleBackFromAnalytics} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#4EAF64" />
          </TouchableOpacity>
        ) : undefined}
        rightElement={undefined}
      />

      {/* Mode Switching Tabs (similar to chat/call) */}
      <View style={styles.moodTabContainer}>
        <TouchableOpacity
          style={[styles.moodTab, !analyticsMode && styles.activeMoodTab]}
          onPress={() => handleModeSwitch('tracking')}
        >
          <View style={styles.moodTabContent}>
            <Ionicons
              name="happy"
              size={16}
              color={!analyticsMode ? "#4EAF64" : "#6B7280"}
              style={styles.moodTabIcon}
            />
            <Text style={[
              styles.moodTabText,
              !analyticsMode && styles.activeMoodTabText
            ]}>
              Tracking
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.moodTab, analyticsMode && styles.activeMoodTab]}
          onPress={() => handleModeSwitch('analytics')}
        >
          <View style={styles.moodTabContent}>
            <Ionicons
              name="analytics"
              size={16}
              color={analyticsMode ? "#4EAF64" : "#6B7280"}
              style={styles.moodTabIcon}
            />
            <Text style={[
              styles.moodTabText,
              analyticsMode && styles.activeMoodTabText
            ]}>
              Analytics
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Sync Status Indicator */}
      {syncStatus?.needsSync && (
        <View style={styles.syncStatusContainer}>
          <Ionicons name="cloud-upload-outline" size={16} color="#F59E0B" />
          <Text style={styles.syncStatusText}>
            {syncStatus.pendingEntries} entries waiting to sync
          </Text>
        </View>
      )}

      {/* Loading State */}
      {isLoadingData && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4EAF64" />
          <Text style={styles.loadingText}>
            {isInitializing ? 'Initializing mood tracker...' : 'Loading your mood data...'}
          </Text>
        </View>
      )}

      {/* Error State */}
      {hasError && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
          <Text style={styles.errorTitle}>Unable to load mood data</Text>
          <Text style={styles.errorMessage}>
            {hasError.message || 'Please check your connection and try again'}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Main Content */}
      {!isLoadingData && !hasError && (
        analyticsMode ? (
          <MoodAnalyticsView />
        ) : (
          <MoodTrackingView
            displayData={displayData || DEFAULT_WEEKLY_MOOD_DATA}
            isLoadingData={isLoadingData}
            hasError={hasError}
            refetch={refetch}
            onOpenDailyTracker={() => setShowDailyTracker(true)}
            hasDataToday={hasDataToday || false}
            isLoadingTodayData={isLoading || false}
          />
        )
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },

  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },
  dailyTrackerButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 32,
  },
  dailyTrackerCTA: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  ctaContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  ctaIcon: {
    marginRight: 12,
  },
  ctaTextContainer: {
    flex: 1,
  },
  ctaTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#5B3E31',
  },
  ctaSubtitle: {
    fontSize: 13,
    color: '#6B7280',
    marginTop: 2,
  },
  sectionContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2B7EFF',
    marginBottom: 20,
    textAlign: 'left',
  },
  placeholderText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Development panel styles
  devPanel: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  devPanelTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  devButtonsContainer: {
    flexDirection: 'row',
  },
  devButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  devButtonActive: {
    backgroundColor: '#4EAF64',
    borderColor: '#4EAF64',
  },
  devButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  devButtonTextActive: {
    color: '#FFFFFF',
  },

  // Mode Switching Tabs (similar to chat/call)
  moodTabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    margin: 16,
    marginBottom: 8,
    padding: 4,
  },
  moodTab: {
    flex: 1,
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  activeMoodTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  moodTabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  moodTabIcon: {
    marginRight: 4,
  },
  moodTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeMoodTabText: {
    color: '#4EAF64',
  },

  // Sync Status
  syncStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#FFFBEB',
    borderBottomWidth: 1,
    borderBottomColor: '#FED7AA',
  },
  syncStatusText: {
    marginLeft: 6,
    fontSize: 12,
    color: '#92400E',
    fontWeight: '500',
  },

  // Loading and Error States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorTitle: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: '#EF4444',
    textAlign: 'center',
  },
  errorMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  retryButton: {
    marginTop: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#4EAF64',
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
});
