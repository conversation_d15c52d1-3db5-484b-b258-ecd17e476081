import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
  SafeAreaView,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { useVoiceMode } from '@/context/VoiceModeContext';
import { ChatMessage } from '@/components/ChatMessage';
import { ChatInput } from '@/components/ChatInput';
import { TypingIndicator } from '@/components/TypingIndicator';
import { ThreadManagementModal } from '@/components/ThreadManagementModal';
import StandardHeader from '@/components/StandardHeader';
import { HEADER_CONFIGS } from '@/styles/HeaderStyles';
import { chatService } from '@/lib/chatService';
import { chatStyles } from '@/styles/ChatStyles';
import type { Message, Thread, ChatState } from '@/types/chat';
import * as Clipboard from 'expo-clipboard';
import { Feather, Ionicons } from '@expo/vector-icons';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { LinearGradient } from 'expo-linear-gradient';
import VoiceContainer from '@/components/VoiceInterface/VoiceContainer';

interface UserActionFeedback {
  type: 'success' | 'error';
  message: string;
}

export default function ChatScreen() {
  const { user } = useUniversalAuth();
  const params = useLocalSearchParams();
  const [state, setState] = useState<ChatState>({
    messages: [],
    loading: true,
    error: null,
    isTyping: false,
    hasMore: true,
  });
  const [currentThread, setCurrentThread] = useState<Thread | null>(null);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [showThreadManagementModal, setShowThreadManagementModal] = useState(false);
  const [userActionFeedback, setUserActionFeedback] = useState<UserActionFeedback | null>(null);
  const { voiceMode, setVoiceMode } = useVoiceMode(); // Voice mode from context
  const flatListRef = useRef<FlatList>(null);
  const [page, setPage] = useState(0);
  const PAGE_SIZE = 20;
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  // Handle voice mode parameter from URL
  useEffect(() => {
    if (params.mode === 'voice') {
      setVoiceMode(true);
      // Clear the parameter to avoid re-triggering
      router.replace('/chat');
    }
  }, [params.mode, setVoiceMode]);

  // Auto-clear user action feedback after 3 seconds
  useEffect(() => {
    if (userActionFeedback) {
      const timer = setTimeout(() => {
        setUserActionFeedback(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [userActionFeedback]);

  const initializeChat = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Get all user threads
      const userThreads = await chatService.getUserThreads(user!.id);
      setThreads(userThreads);

      let thread = userThreads[0]; // Get most recent thread

      if (!thread) {
        // Create new thread if none exists
        thread = await chatService.createThread(user!.id, 'Percakapan dengan Temani');
        setThreads([thread]);
      }

      setCurrentThread(thread);

      // Load initial messages
      const messages = await chatService.getMessages(thread.id, PAGE_SIZE, 0);
      
      setState(prev => ({
        ...prev,
        messages,
        loading: false,
        hasMore: messages.length === PAGE_SIZE,
      }));

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);

    } catch (error) {
      console.error('Chat initialization error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Gagal memuat percakapan. Silakan coba lagi.',
      }));
    }
  }, [user]);

  // Initialize chat
  useEffect(() => {
    if (!user) return;

    initializeChat();
    
    // Cleanup subscription on unmount
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [user, initializeChat]);

  // Subscribe to real-time messages
  useEffect(() => {
    if (!currentThread) return;

    // Cleanup previous subscription
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
    }

    // Create new subscription
    subscriptionRef.current = chatService.subscribeToMessages(
      currentThread.id,
      // Handle new messages (INSERT events)
      (newMessage: Message) => {
        setState(prev => ({
          ...prev,
          messages: [...prev.messages, newMessage],
        }));
        
        // Auto-scroll to bottom for new messages
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      },
      // Handle message updates (UPDATE events)
      (updatedMessage: Message) => {
        setState(prev => ({
          ...prev,
          messages: prev.messages.map(msg => 
            msg.message_id === updatedMessage.message_id 
              ? { ...msg, ...updatedMessage }
              : msg
          ),
        }));
      }
    );

    // Cleanup on dependency change
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [currentThread]);

  const loadMoreMessages = async () => {
    if (!currentThread || !state.hasMore || state.loading) return;

    try {
      const nextPage = page + 1;
      const olderMessages = await chatService.getMessages(
        currentThread.id,
        PAGE_SIZE,
        nextPage * PAGE_SIZE
      );

      if (olderMessages.length > 0) {
        setState(prev => ({
          ...prev,
          messages: [...olderMessages, ...prev.messages],
          hasMore: olderMessages.length === PAGE_SIZE,
        }));
        setPage(nextPage);
      } else {
        setState(prev => ({ ...prev, hasMore: false }));
      }
    } catch (error) {
      console.error('Load more messages error:', error);
    }
  };

  const handleCreateNewThread = async () => {
    if (!user) return;

    try {
      const newThread = await chatService.createThread(user.id, 'Percakapan Baru');
      
      // Update threads list
      setThreads(prev => [newThread, ...prev]);
      
      // Switch to new thread
      setCurrentThread(newThread);
      
      // Clear messages
      setState(prev => ({
        ...prev,
        messages: [],
        error: null,
      }));
      
      setPage(0);
      
      // Show success feedback
      setUserActionFeedback({
        type: 'success',
        message: 'Percakapan baru berhasil dibuat'
      });
    } catch (error) {
      console.error('Create thread error:', error);
      setUserActionFeedback({
        type: 'error',
        message: 'Gagal membuat percakapan baru'
      });
    }
  };

  const handleSwitchThread = async (thread: Thread) => {
    if (thread.id === currentThread?.id) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      setCurrentThread(thread);
      
      // Load messages for selected thread
      const messages = await chatService.getMessages(thread.id, PAGE_SIZE, 0);
      
      setState(prev => ({
        ...prev,
        messages,
        loading: false,
        hasMore: messages.length === PAGE_SIZE,
      }));
      
      setPage(0);
      
      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);
      
    } catch (error) {
      console.error('Switch thread error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Gagal memuat percakapan.',
      }));
    }
  };

  const handleDeleteThread = async (threadId: string) => {
    try {
      await chatService.deleteThread(threadId);
      
      // Update threads list
      const updatedThreads = threads.filter(t => t.id !== threadId);
      setThreads(updatedThreads);
      
      // If deleted thread was current, switch to another or create new
      if (currentThread?.id === threadId) {
        if (updatedThreads.length > 0) {
          handleSwitchThread(updatedThreads[0]);
        } else {
          handleCreateNewThread();
        }
      }
      
      // Show success feedback
      setUserActionFeedback({
        type: 'success',
        message: 'Percakapan berhasil dihapus'
      });
    } catch (error) {
      console.error('Delete thread error:', error);
      setUserActionFeedback({
        type: 'error',
        message: 'Gagal menghapus percakapan'
      });
    }
  };

  const handleRenameThread = async (threadId: string, newTitle: string) => {
    try {
      const updatedThread = await chatService.updateThreadTitle(threadId, newTitle);
      
      // Update threads list
      setThreads(prev => 
        prev.map(t => t.id === threadId ? updatedThread : t)
      );
      
      // Update current thread if it's the one being renamed
      if (currentThread?.id === threadId) {
        setCurrentThread(updatedThread);
      }
      
      // Show success feedback
      setUserActionFeedback({
        type: 'success',
        message: 'Nama percakapan berhasil diubah'
      });
    } catch (error) {
      console.error('Rename thread error:', error);
      setUserActionFeedback({
        type: 'error',
        message: 'Gagal mengubah nama percakapan'
      });
    }
  };

  const handleSendMessage = async (content: string) => {
    if (!currentThread || !user) return;

    try {
      setState(prev => ({ ...prev, error: null }));

      // Send user message
      const userMessage = await chatService.sendMessage(
        currentThread.id,
        content,
        'user'
      );

      // Update message status to delivered after a short delay
      setTimeout(async () => {
        try {
          await chatService.updateMessageStatus(userMessage.message_id, 'delivered');
        } catch (error) {
          console.error('Failed to update message status:', error);
        }
      }, 1000);

      // Show typing indicator
      setState(prev => ({ ...prev, isTyping: true }));

      // Get AI response
      try {
        const aiResponse = await chatService.getAIResponse(
          content,
          user.id,
          state.messages.slice(-10) // Last 10 messages for context
        );

        // Send AI message
        await chatService.sendMessage(
          currentThread.id,
          aiResponse,
          'ai'
        );

        // Mark user message as read after AI responds
        setTimeout(async () => {
          try {
            await chatService.updateMessageStatus(userMessage.message_id, 'read');
          } catch (error) {
            console.error('Failed to update message status to read:', error);
          }
        }, 500);

      } catch (aiError) {
        console.error('AI response error:', aiError);
        
        // Send fallback message
        await chatService.sendMessage(
          currentThread.id,
          'Maaf, saya sedang mengalami kesulitan teknis. Namun, saya di sini untuk mendengarkan Anda. Bisakah Anda mencoba lagi dalam beberapa saat?',
          'ai'
        );
      }

    } catch (error) {
      console.error('Send message error:', error);
      setState(prev => ({
        ...prev,
        error: 'Gagal mengirim pesan. Silakan coba lagi.',
      }));
    } finally {
      setState(prev => ({ ...prev, isTyping: false }));
    }
  };

  const handleCopyMessage = async (content: string) => {
    try {
      if (Platform.OS === 'web') {
        await navigator.clipboard.writeText(content);
      } else {
        await Clipboard.setStringAsync(content);
      }
      
      // Show success feedback
      setUserActionFeedback({
        type: 'success',
        message: 'Pesan berhasil disalin ke clipboard'
      });
    } catch (error) {
      console.error('Copy error:', error);
      setUserActionFeedback({
        type: 'error',
        message: 'Gagal menyalin pesan'
      });
    }
  };

  const handleRetry = () => {
    setState(prev => ({ ...prev, error: null }));
    initializeChat();
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <ChatMessage message={item} onCopy={handleCopyMessage} />
  );

  // Voice mode toggle handlers


  const handleBackToChat = () => {
    setVoiceMode(false);
  };

  // Tab management uses current route instead of local state
  const handleTabChange = (tab: string) => {
    if (tab === 'call') {
      setVoiceMode(true);
    }
    // No need to handle 'chat' case since we're already on the chat screen
  };

  const renderHeader = () => (
    <>
      {/* Top navigation */}
      <StandardHeader
        title={voiceMode ? HEADER_CONFIGS.chatVoice.title : HEADER_CONFIGS.chat.title}
        iconName={voiceMode ? HEADER_CONFIGS.chatVoice.iconName : HEADER_CONFIGS.chat.iconName}
        iconColor={voiceMode ? HEADER_CONFIGS.chatVoice.iconColor : HEADER_CONFIGS.chat.iconColor}
        backgroundColor={voiceMode ? 'rgba(135, 196, 229, 0.1)' : 'transparent'}
        rightElement={
          <TouchableOpacity
            style={{ width: 40, height: 40, alignItems: 'center', justifyContent: 'center' }}
            onPress={() => setShowThreadManagementModal(true)}
          >
            <Feather name="list" size={20} color={voiceMode ? "#4A55A2" : "#B5A136"} />
          </TouchableOpacity>
        }
      />
      
      {/* Tab selector */}
      <View style={[
        chatStyles.tabContainer,
        voiceMode && { backgroundColor: '#87C4E5' }
      ]}>
        <TouchableOpacity
          style={[
            chatStyles.tab,
            !voiceMode && chatStyles.activeTab
          ]}
          onPress={() => handleTabChange('chat')}
        >
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Ionicons
              name="chatbubble"
              size={16}
              color={!voiceMode ? "#B5A136" : "#FFFFFF"}
              style={{marginRight: 4}}
            />
            <Text style={[
              chatStyles.tabText,
              !voiceMode && chatStyles.activeTabText
            ]}>Chat</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            chatStyles.tab,
            voiceMode && chatStyles.activeTab
          ]}
          onPress={() => handleTabChange('call')}
        >
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Ionicons
              name="call"
              size={16}
              color={voiceMode ? "#4A55A2" : "#FFFFFF"}
              style={{marginRight: 4}}
            />
            <Text style={[
              chatStyles.tabText,
              voiceMode && chatStyles.activeTabText
            ]}>Call</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Voice Chat Section - Now navigates to mic.tsx instead */}

      {/* Voice chat section replaces thread management button */}
    </>
  );


  const renderUserActionFeedback = () => {
    if (!userActionFeedback) return null;

    return (
      <View style={[
        chatStyles.userFeedbackContainer,
        userActionFeedback.type === 'success' 
          ? chatStyles.userFeedbackSuccess 
          : chatStyles.userFeedbackError
      ]}>
        <Feather 
          name={userActionFeedback.type === 'success' ? 'check-circle' : 'x-circle'} 
          size={16} 
          color={userActionFeedback.type === 'success' ? '#10B981' : '#EF4444'} 
        />
        <Text style={[
          chatStyles.userFeedbackText,
          userActionFeedback.type === 'success' 
            ? chatStyles.userFeedbackTextSuccess 
            : chatStyles.userFeedbackTextError
        ]}>
          {userActionFeedback.message}
        </Text>
      </View>
    );
  };

  const renderError = () => (
    <View style={chatStyles.errorContainer}>
      <Text style={chatStyles.errorText}>{state.error}</Text>
      <TouchableOpacity style={chatStyles.retryButton} onPress={handleRetry}>
        <Text style={chatStyles.retryButtonText}>Coba Lagi</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={chatStyles.emptyState}>
      <Text style={chatStyles.emptyStateText}>
        Selamat datang di Chat Temani! 👋
      </Text>
      <Text style={chatStyles.emptyStateSubtext}>
        Saya di sini untuk mendengarkan dan memberikan dukungan emosional. 
        Mulai percakapan dengan mengetik pesan di bawah atau gunakan fitur panggilan suara.
      </Text>
    </View>
  );

  if (state.loading && state.messages.length === 0) {
    return (
      <SafeAreaView style={chatStyles.container}>
        <LinearGradient
          colors={['#F6EBB1', '#FFFFFF']}
          locations={[0, 0.29]}
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          }}
        />
        {renderHeader()}
        <View style={chatStyles.loadingContainer}>
          <ActivityIndicator size="large" color="#2B7EFF" />
          <Text style={chatStyles.loadingText}>Memuat percakapan...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Conditional rendering based on voice mode
  if (voiceMode) {
    return <VoiceContainer onBackToChat={handleBackToChat} />;
  }

  return (
    <SafeAreaView style={chatStyles.container}>
      <LinearGradient
        colors={['#F6EBB1', '#FFFFFF']}
        locations={[0, 0.29]}
        style={chatStyles.backgroundGradient}
      />
      {renderHeader()}
      {renderUserActionFeedback()}
      
      <View style={chatStyles.messagesContainer}>
        {state.error && renderError()}
        
        <FlatList
          ref={flatListRef}
          data={state.messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.message_id}
          contentContainerStyle={[
            chatStyles.messagesList,
            state.messages.length === 0 && { flex: 1 }
          ]}
          onEndReached={loadMoreMessages}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
            autoscrollToTopThreshold: 10,
          }}
        />
        
        {state.isTyping && <TypingIndicator />}
      </View>

      <ChatInput
        onSend={handleSendMessage}
        disabled={state.isTyping}
        placeholder="Ceritakan apa yang Anda rasakan..."
      />

      <ThreadManagementModal
        isVisible={showThreadManagementModal}
        threads={threads}
        currentThreadId={currentThread?.id}
        onSelectThread={handleSwitchThread}
        onCreateNewThread={handleCreateNewThread}
        onDeleteThread={handleDeleteThread}
        onRenameThread={handleRenameThread}
        onClose={() => setShowThreadManagementModal(false)}
      />
    </SafeAreaView>
  );
}