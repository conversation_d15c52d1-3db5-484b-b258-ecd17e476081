import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';

import AppointmentCalendar from '@/components/AppointmentCalendar';
import AppointmentTimeSlots from '@/components/AppointmentTimeSlots';
import StandardHeader from '@/components/StandardHeader';
import { HEADER_CONFIGS } from '@/styles/HeaderStyles';
import { useAppointmentCalendar } from '@/hooks/useAppointmentCalendar';
import { ComingSoonOverlay } from '@/components/ComingSoonOverlay';

export default function AppointmentScreen() {
  const [selectedTime, setSelectedTime] = useState<string | null>(null);

  const {
    currentMonth,
    selectedDate,
    availableDates,
    bookedDates,
    availableTimeSlots,
    loading,
    navigateMonth,
    selectDate,
    goToToday,
  } = useAppointmentCalendar();

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  const handleBookAppointment = () => {
    if (selectedDate && selectedTime) {
      Alert.alert(
        'Konfirmasi Jadwal',
        `Apakah Anda yakin ingin menjadwalkan konsultasi pada ${selectedDate} pukul ${selectedTime}?`,
        [
          {
            text: 'Batal',
            style: 'cancel',
          },
          {
            text: 'Ya, Jadwalkan',
            onPress: () => {
              // TODO: Implement appointment booking logic
              Alert.alert('Berhasil', `Konsultasi berhasil dijadwalkan untuk ${selectedDate} pukul ${selectedTime}`);
              setSelectedTime(null);
            },
          },
        ]
      );
    } else {
      Alert.alert('Perhatian', 'Silakan pilih tanggal dan waktu terlebih dahulu');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#F6EBB1', '#FFFFFF']}
        locations={[0, 0.29]}
        style={styles.backgroundGradient}
      />

      {/* Header - Standardized */}
      <StandardHeader
        title={HEADER_CONFIGS.appointment.title}
        iconName={HEADER_CONFIGS.appointment.iconName}
        iconColor={HEADER_CONFIGS.appointment.iconColor}
      />

      <View style={styles.contentWrapper}>
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Calendar Component */}
          <AppointmentCalendar
            currentMonth={currentMonth}
            selectedDate={selectedDate}
            availableDates={availableDates}
            bookedDates={bookedDates}
            loading={loading}
            onDateSelect={selectDate}
            onNavigateMonth={navigateMonth}
            onGoToToday={goToToday}
          />

          {/* Time Slots Component */}
          <AppointmentTimeSlots
            selectedDate={selectedDate}
            selectedTime={selectedTime}
            availableTimeSlots={availableTimeSlots}
            loading={loading}
            onTimeSelect={handleTimeSelect}
          />

          {/* Book Button */}
          <View style={styles.bookButtonContainer}>
            <TouchableOpacity
              style={[
                styles.bookButton,
                (!selectedDate || !selectedTime) && styles.bookButtonDisabled
              ]}
              onPress={handleBookAppointment}
              disabled={!selectedDate || !selectedTime}
            >
              <LinearGradient
                colors={['#FFD572', '#FEBD38']}
                style={styles.bookButtonGradient}
              >
                <Text style={styles.bookButtonText}>Jadwalkan Konsultasi</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Coming Soon Overlay */}
        <ComingSoonOverlay
          title="Sistem Booking Segera Hadir! 📅"
          message="Kami lagi setup sesuatu yang keren banget buat kamu! Nanti kamu bisa booking konsultasi dengan super mudah. Gak sabar pengen tunjukin apa yang udah kami kerjain!"
          icon="calendar-outline"
          visible={true}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEFEFE',
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  contentWrapper: {
    flex: 1,
    position: 'relative',
  },
  content: {
    flex: 1,
    paddingTop: 16,
    paddingBottom: 32,
  },
  bookButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  bookButton: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  bookButtonDisabled: {
    opacity: 0.5,
  },
  bookButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  bookButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#454459',
  },

});
