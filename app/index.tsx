import { useEffect, useState, useRef } from 'react';
import { Redirect, useRouter } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { View, ActivityIndicator, Text } from 'react-native';

export default function IndexScreen() {
  const { user, loading, userStatusLoading, onboardingCompleted, selfReflectionCompleted, selfReflectionSkipped } = useUniversalAuth();
  const router = useRouter();
  const [hasNavigated, setHasNavigated] = useState(false);
  const previousUserRef = useRef(user);

  console.log('🏠 [IndexScreen] Current state:', {
    user: !!user,
    userId: user?._id,
    loading,
    userStatusLoading,
    onboardingCompleted,
    selfReflectionCompleted,
    selfReflectionSkipped,
    hasNavigated,
    previousUser: !!previousUserRef.current,
    timestamp: new Date().toISOString()
  });

  // Reset navigation flag when user state changes (unauthenticated ↔ authenticated)
  useEffect(() => {
    const userChanged = !!user !== !!previousUserRef.current;

    if (userChanged) {
      console.log('🔄 [IndexScreen] User state transition detected:', {
        previousUser: !!previousUserRef.current,
        currentUser: !!user,
        resettingNavigation: true,
        timestamp: new Date().toISOString()
      });

      setHasNavigated(false);
      previousUserRef.current = user;
    }
  }, [user]);

  // Re-evaluate routing when auth state changes
  useEffect(() => {
    console.log('🔄 [IndexScreen] useEffect triggered with state:', {
      loading,
      userStatusLoading,
      hasNavigated,
      user: !!user,
      onboardingCompleted,
      selfReflectionCompleted,
      selfReflectionSkipped,
      timestamp: new Date().toISOString()
    });

    if (loading) {
      console.log('⏳ [IndexScreen] Waiting for auth loading to complete...');
      return;
    }

    if (hasNavigated) {
      console.log('🔄 [IndexScreen] Already navigated for current state, skipping...');
      return;
    }

    console.log('🎯 [IndexScreen] Evaluating routing decision for current state...');

    // Authenticated users who completed self-reflection → main app
    if (user && (selfReflectionCompleted || selfReflectionSkipped)) {
      console.log('🎯 [IndexScreen] AUTHENTICATED + COMPLETED → navigating to (tabs)', {
        userId: user._id,
        selfReflectionCompleted,
        selfReflectionSkipped
      });
      setHasNavigated(true);
      router.replace('/(tabs)');
      return;
    }

    // Authenticated users who need self-reflection → onboarding for self-reflection
    if (user && !selfReflectionCompleted && !selfReflectionSkipped) {
      console.log('🎯 [IndexScreen] AUTHENTICATED + NEEDS_REFLECTION → navigating to (onboarding)', {
        userId: user._id,
        selfReflectionCompleted,
        selfReflectionSkipped
      });
      setHasNavigated(true);
      router.replace('/(onboarding)');
      return;
    }

    // Unauthenticated users who completed onboarding → auth
    if (!user && onboardingCompleted) {
      console.log('🎯 [IndexScreen] UNAUTHENTICATED + RETURNING → navigating to (auth)', {
        onboardingCompleted
      });
      setHasNavigated(true);
      router.replace('/(auth)');
      return;
    }

    // New users → onboarding
    if (!user && !onboardingCompleted) {
      console.log('🎯 [IndexScreen] UNAUTHENTICATED + NEW → navigating to (onboarding)', {
        onboardingCompleted
      });
      setHasNavigated(true);
      router.replace('/(onboarding)');
      return;
    }
  }, [user, loading, userStatusLoading, onboardingCompleted, selfReflectionCompleted, selfReflectionSkipped, hasNavigated, router]);

  // Show loading while auth state is being determined
  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        <ActivityIndicator size="large" color="#A08CFB" />
        <Text style={{ marginTop: 10, color: '#454459' }}>Loading auth state...</Text>
      </View>
    );
  }

  // Show loading while waiting for navigation
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
      <ActivityIndicator size="large" color="#A08CFB" />
      <Text style={{ marginTop: 10, color: '#454459' }}>Determining route...</Text>
    </View>
  );
}
