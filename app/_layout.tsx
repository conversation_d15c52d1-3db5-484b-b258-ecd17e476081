import React from 'react';
import { AUTH_CONFIG } from '@/lib/env';

// Import both layout implementations
import SupabaseRootLayout from '@/layouts/SupabaseLayout';
import ConvexRootLayout from '@/layouts/ConvexLayout';

/**
 * Feature flag-based layout selector
 * Uses Convex Auth if enabled, otherwise falls back to Supabase Auth
 */
export default function RootLayout() {
  console.log('Auth system selection:', {
    useConvexAuth: AUTH_CONFIG.useConvexAuth,
    timestamp: new Date().toISOString()
  });

  // Choose authentication system based on feature flag
  if (AUTH_CONFIG.useConvexAuth) {
    return <ConvexRootLayout />;
  } else {
    return <SupabaseRootLayout />;
  }
}