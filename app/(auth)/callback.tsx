import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { supabase } from '@/lib/supabase';
import { authStyles } from '@/styles/AuthStyles';

export default function AuthCallbackScreen() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Memproses autentikasi...');
  const { user } = useUniversalAuth();
  const params = useLocalSearchParams();

  useEffect(() => {
    handleAuthCallback();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // ConvexLayout will handle navigation automatically after authentication
    // No manual navigation needed here
    if (user && status === 'success') {
      // Just update the message, let ConvexLayout handle routing
      setMessage('Autentikasi berhasil! Mengarahkan...');
    }
  }, [user, status]);

  const handleAuthCallback = async () => {
    try {
      // Extract tokens from URL parameters
      const { access_token, refresh_token, error, error_description } = params;

      if (error) {
        console.error('OAuth error:', error, error_description);
        setStatus('error');

        // Handle specific OAuth error types
        let errorMessage = 'Terjadi kesalahan saat masuk dengan Google';
        if (error === 'access_denied') {
          errorMessage = 'Akses ditolak. Silakan coba lagi dan berikan izin yang diperlukan.';
        } else if (error === 'invalid_request') {
          errorMessage = 'Permintaan tidak valid. Silakan coba lagi.';
        } else if (error_description) {
          errorMessage = error_description as string;
        }

        setMessage(errorMessage);

        // Redirect back to auth screen after showing error
        setTimeout(() => {
          router.replace('/(auth)');
        }, 3000);
        return;
      }

      if (access_token && refresh_token) {
        // Validate token format before attempting to set session
        if (typeof access_token !== 'string' || typeof refresh_token !== 'string') {
          setStatus('error');
          setMessage('Format token tidak valid. Silakan coba lagi.');
          setTimeout(() => router.replace('/(auth)'), 3000);
          return;
        }

        // Set the session with the tokens
        const { data, error: sessionError } = await supabase.auth.setSession({
          access_token: access_token,
          refresh_token: refresh_token,
        });

        if (sessionError) {
          console.error('Session error:', sessionError);
          setStatus('error');

          // Handle specific session errors
          let sessionErrorMessage = 'Gagal membuat sesi. Silakan coba lagi.';
          if (sessionError.message.includes('Invalid token')) {
            sessionErrorMessage = 'Token tidak valid. Silakan masuk ulang.';
          } else if (sessionError.message.includes('Token expired')) {
            sessionErrorMessage = 'Token telah kedaluwarsa. Silakan masuk ulang.';
          }

          setMessage(sessionErrorMessage);
          setTimeout(() => router.replace('/(auth)'), 3000);
          return;
        }

        if (data.user) {
          setStatus('success');
          setMessage('Berhasil masuk! Mengarahkan ke aplikasi...');
          // The useEffect above will handle the redirect
        } else {
          setStatus('error');
          setMessage('Tidak dapat memverifikasi pengguna. Silakan coba lagi.');
          setTimeout(() => router.replace('/(auth)'), 3000);
        }
      } else {
        // No tokens found, might be an error or incomplete callback
        console.warn('No tokens found in callback URL', { params });
        setStatus('error');
        setMessage('Data autentikasi tidak lengkap. Silakan coba lagi.');
        setTimeout(() => router.replace('/(auth)'), 3000);
      }
    } catch (error) {
      console.error('Callback handling error:', error);
      setStatus('error');
      setMessage('Terjadi kesalahan tak terduga. Silakan coba lagi.');
      
      setTimeout(() => {
        router.replace('/(auth)');
      }, 3000);
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return '#4f46e5';
      case 'success':
        return '#10b981';
      case 'error':
        return '#ef4444';
      default:
        return '#4f46e5';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <ActivityIndicator size="large" color={getStatusColor()} />;
      case 'success':
        return (
          <View style={[authStyles.statusIcon, { backgroundColor: getStatusColor() }]}>
            <Text style={authStyles.statusIconText}>✓</Text>
          </View>
        );
      case 'error':
        return (
          <View style={[authStyles.statusIcon, { backgroundColor: getStatusColor() }]}>
            <Text style={authStyles.statusIconText}>✗</Text>
          </View>
        );
      default:
        return <ActivityIndicator size="large" color={getStatusColor()} />;
    }
  };

  return (
    <SafeAreaView style={authStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      <View style={authStyles.callbackContainer}>
        <View style={authStyles.callbackContent}>
          {getStatusIcon()}
          
          <Text style={[authStyles.callbackTitle, { color: getStatusColor() }]}>
            {status === 'loading' && 'Memproses...'}
            {status === 'success' && 'Berhasil!'}
            {status === 'error' && 'Terjadi Kesalahan'}
          </Text>
          
          <Text style={authStyles.callbackMessage}>
            {message}
          </Text>
          
          {status === 'loading' && (
            <Text style={authStyles.callbackSubtext}>
              Mohon tunggu sebentar
            </Text>
          )}
          
          {status === 'success' && (
            <Text style={authStyles.callbackSubtext}>
              Anda akan diarahkan ke aplikasi
            </Text>
          )}
          
          {status === 'error' && (
            <Text style={authStyles.callbackSubtext}>
              Anda akan diarahkan kembali ke halaman masuk
            </Text>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}
