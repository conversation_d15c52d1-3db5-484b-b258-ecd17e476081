import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { Feather } from '@expo/vector-icons';
import { authStyles } from '@/styles/AuthStyles';

export default function RegisterScreen() {
  const [displayName, setDisplayName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [displayNameError, setDisplayNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  
  const { signUpWithEmail, loading, error, clearError } = useUniversalAuth();
  
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  const validatePassword = (password: string) => {
    return password.length >= 6;
  };
  
  const validateDisplayName = (name: string) => {
    return name.trim().length >= 2;
  };
  
  const validateForm = () => {
    let isValid = true;
    
    // Clear all errors first
    setDisplayNameError('');
    setEmailError('');
    setPasswordError('');
    setConfirmPasswordError('');
    clearError();
    
    // Validate display name
    if (!validateDisplayName(displayName)) {
      setDisplayNameError('Nama panggilan minimal 2 karakter');
      isValid = false;
    }
    
    // Validate email
    if (!validateEmail(email)) {
      setEmailError('Mohon masukkan alamat email yang valid');
      isValid = false;
    }
    
    // Validate password
    if (!validatePassword(password)) {
      setPasswordError('Kata sandi minimal 6 karakter');
      isValid = false;
    }
    
    // Validate password confirmation
    if (password !== confirmPassword) {
      setConfirmPasswordError('Konfirmasi kata sandi tidak cocok');
      isValid = false;
    }
    
    return isValid;
  };
  
  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      // Pass the display name as parameter
      await signUpWithEmail(email, password, displayName);

      // Show success message - ConvexLayout will handle navigation automatically
      Alert.alert(
        'Pendaftaran Berhasil',
        'Akun Anda telah berhasil dibuat! Kami akan mengarahkan Anda ke langkah selanjutnya.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Registration error:', error);
    }
  };
  
  const handleBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={authStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {/* Background decorative elements */}
      <Image 
        source={require('../../assets/images/background-decoration.png')} 
        style={authStyles.backgroundDecoration} 
        resizeMode="cover"
      />
      
      <KeyboardAvoidingView 
        style={{flex: 1}} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={authStyles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Form Title */}
          <Text style={authStyles.formTitle}>Buat Akun</Text>
          
          {/* Registration Form */}
          <View style={authStyles.formContainer}>
            {/* Error Display */}
            {error && (
              <View style={authStyles.errorContainer}>
                <Text style={authStyles.errorText}>{error}</Text>
              </View>
            )}

            {/* Display Name Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Nama Panggilan</Text>
              <View style={[authStyles.inputContainer, displayNameError ? authStyles.inputError : null]}>
                <Feather name="user" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="masukkan nama panggilan kamu"
                  placeholderTextColor="#AAB2BF"
                  value={displayName}
                  onChangeText={(text) => {
                    setDisplayName(text);
                    if (displayNameError) setDisplayNameError('');
                  }}
                  autoCorrect={false}
                />
              </View>
              {displayNameError ? <Text style={authStyles.fieldError}>{displayNameError}</Text> : null}
            </View>

            {/* Email Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Email</Text>
              <View style={[authStyles.inputContainer, emailError ? authStyles.inputError : null]}>
                <Feather name="mail" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="<EMAIL>"
                  placeholderTextColor="#AAB2BF"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) setEmailError('');
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
              {emailError ? <Text style={authStyles.fieldError}>{emailError}</Text> : null}
            </View>

            {/* Password Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Password</Text>
              <View style={[authStyles.inputContainer, passwordError ? authStyles.inputError : null]}>
                <Feather name="lock" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="******"
                  placeholderTextColor="#AAB2BF"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (passwordError) setPasswordError('');
                  }}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={authStyles.eyeIcon}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Feather name={showPassword ? "eye-off" : "eye"} size={20} color="#777777" />
                </TouchableOpacity>
              </View>
              {passwordError ? <Text style={authStyles.fieldError}>{passwordError}</Text> : null}
            </View>

            {/* Confirm Password Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Konfirmasi Password</Text>
              <View style={[authStyles.inputContainer, confirmPasswordError ? authStyles.inputError : null]}>
                <Feather name="lock" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="******"
                  placeholderTextColor="#AAB2BF"
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text);
                    if (confirmPasswordError) setConfirmPasswordError('');
                  }}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={authStyles.eyeIcon}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Feather name={showConfirmPassword ? "eye-off" : "eye"} size={20} color="#777777" />
                </TouchableOpacity>
              </View>
              {confirmPasswordError ? <Text style={authStyles.fieldError}>{confirmPasswordError}</Text> : null}
            </View>

            {/* Button Row */}
            <View style={authStyles.buttonContainer}>
              <TouchableOpacity 
                style={authStyles.backButton}
                onPress={handleBack}
                disabled={loading}
              >
                <Feather name="arrow-left" size={24} color="#1C1C1E" />
              </TouchableOpacity>

              <TouchableOpacity
                style={authStyles.saveButton}
                onPress={handleRegister}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={authStyles.saveButtonText}>Buat Akun</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
