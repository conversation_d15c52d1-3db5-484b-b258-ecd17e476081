import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { Feather } from '@expo/vector-icons';
import { authStyles } from '@/styles/AuthStyles';
import AuthFlowTester from '@/components/AuthFlowTester';
import DebugEnvVars from '@/components/DebugEnvVars';
import { isGoogleSigninEnabled, isSignupEnabled } from '@/lib/authConfig';
import { addBreadcrumb } from '@/lib/sentryConfig';
import * as Sentry from '@sentry/react-native';
import { SentryTestButton } from '@/components/SentryTestButton';

export default function AuthScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const { signInWithGoogle, signInWithEmail, loading, error, clearError } = useUniversalAuth();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string) => {
    return password.length >= 6;
  };

  const handleEmailAuth = async () => {
    setEmailError('');
    setPasswordError('');
    clearError();

    // Add breadcrumb for auth attempt
    addBreadcrumb('Email authentication attempt', 'auth', { method: 'email' });

    if (!validateEmail(email)) {
      setEmailError('Mohon masukkan alamat email yang valid');
      addBreadcrumb('Email validation failed', 'validation', { field: 'email' });
      return;
    }

    if (!validatePassword(password)) {
      setPasswordError('Kata sandi minimal 6 karakter');
      addBreadcrumb('Password validation failed', 'validation', { field: 'password' });
      return;
    }

    // Use modern Sentry API with callback pattern for better reliability
    try {
      await Sentry.startSpan({ name: 'auth.email_signin', op: 'auth' }, async () => {
        await signInWithEmail(email, password);
      });
      addBreadcrumb('Email authentication successful', 'auth', { method: 'email' });
    } catch (error) {
      addBreadcrumb('Email authentication failed', 'auth', {
        method: 'email',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('Auth error:', error);
    }
  };

  const handleGoogleSignIn = async () => {
    clearError();

    // Add breadcrumb for Google auth attempt
    addBreadcrumb('Google authentication attempt', 'auth', { method: 'google' });

    // Use modern Sentry API with callback pattern for better reliability
    try {
      await Sentry.startSpan({ name: 'auth.google_signin', op: 'auth' }, async () => {
        await signInWithGoogle();
      });
      addBreadcrumb('Google authentication successful', 'auth', { method: 'google' });
    } catch (error) {
      addBreadcrumb('Google authentication failed', 'auth', {
        method: 'google',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('Google sign-in error:', error);
    }
  };

  return (
    <SafeAreaView style={authStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {/* Background decorative elements */}
      <Image 
        source={require('../../assets/images/background-decoration.png')} 
        style={authStyles.backgroundDecoration} 
        resizeMode="cover"
      />
      
      <KeyboardAvoidingView 
        style={{flex: 1}} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={authStyles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Hero Section with greeting and flower hand illustration */}
          <View style={authStyles.heroSection}>
            <Text style={authStyles.greeting}>Halo!</Text>
            <Image
              source={require('../../assets/images/hand-flower.png')}
              style={authStyles.heroImage}
            />
          </View>

          {/* Auth Form */}
          <View style={authStyles.formContainer}>
            {/* Error Display */}
            {error && (
              <View style={authStyles.errorContainer}>
                <Text style={authStyles.errorText}>{error}</Text>
              </View>
            )}

            {/* Email Input */}
            <View style={[authStyles.inputContainer, emailError ? authStyles.inputError : null]}>
              <Feather name="mail" size={20} style={authStyles.inputIcon} />
              <TextInput
                style={authStyles.input}
                placeholder="Email"
                placeholderTextColor="#AAB2BF"
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  if (emailError) setEmailError('');
                }}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            {emailError ? <Text style={authStyles.fieldError}>{emailError}</Text> : null}

            {/* Password Input */}
            <View style={[authStyles.inputContainer, passwordError ? authStyles.inputError : null]}>
              <Feather name="lock" size={20} style={authStyles.inputIcon} />
              <TextInput
                style={authStyles.input}
                placeholder="Password"
                placeholderTextColor="#AAB2BF"
                value={password}
                onChangeText={(text) => {
                  setPassword(text);
                  if (passwordError) setPasswordError('');
                }}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={authStyles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Feather name={showPassword ? "eye-off" : "eye"} size={20} color="#777777" />
              </TouchableOpacity>
            </View>
            {passwordError ? <Text style={authStyles.fieldError}>{passwordError}</Text> : null}
            
            {/* Forgot Password Link */}
            <TouchableOpacity 
              style={authStyles.forgotPasswordContainer}
              onPress={() => {
                clearError();
                router.push('/(auth)/forgot-password');
              }}
            >
              <Text style={authStyles.forgotPasswordText}>Lupa Password?</Text>
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              style={[authStyles.authButton, loading ? authStyles.authButtonDisabled : null]}
              onPress={handleEmailAuth}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={authStyles.authButtonText}>Masuk</Text>
              )}
            </TouchableOpacity>

            {/* Google Sign In Button - Temporarily Disabled */}
            {isGoogleSigninEnabled() && (
              <>
                <TouchableOpacity
                  style={[authStyles.googleButton, loading ? authStyles.googleButtonDisabled : null]}
                  onPress={handleGoogleSignIn}
                  disabled={loading}
                  activeOpacity={0.8}
                >
                  <Image
                    source={{ uri: 'https://developers.google.com/identity/images/g-logo.png' }}
                    style={authStyles.googleIcon}
                  />
                  <Text style={authStyles.googleButtonText}>Lanjutkan dengan Google</Text>
                </TouchableOpacity>

                {/* Divider */}
                <View style={authStyles.divider}>
                  <View style={authStyles.dividerLine} />
                  <Text style={authStyles.dividerText}>Atau</Text>
                  <View style={authStyles.dividerLine} />
                </View>
              </>
            )}

            {/* Create Account Button - Temporarily Disabled */}
            {isSignupEnabled() && (
              <TouchableOpacity
                style={authStyles.createAccountButton}
                onPress={() => {
                  clearError();
                  router.push('/(auth)/register');
                }}
                disabled={loading}
              >
                <Text style={authStyles.createAccountButtonText}>Buat Akun</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Debug Environment Variables */}
          <DebugEnvVars />

          {/* Sentry Test Button (Development Only) */}
          <SentryTestButton />

          {/* Temporary Auth Flow Tester */}
          <AuthFlowTester />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}