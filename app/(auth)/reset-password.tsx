import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { Feather } from '@expo/vector-icons';
import { authStyles } from '@/styles/AuthStyles';
import { validatePassword, getPasswordStrengthDescription, generateSecurePassword } from '@/utils/passwordSecurity';

export default function ResetPasswordScreen() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  
  const { updatePassword, loading, error, clearError } = useUniversalAuth();
  const params = useLocalSearchParams();

  // In a real implementation, you'd handle the token from the URL
  useEffect(() => {
    // Check if we have a token in the params (this would come from the URL)
    const token = params.token;
    if (!token) {
      Alert.alert(
        "Error",
        "Link reset password tidak valid atau telah kadaluarsa.",
        [{ text: "OK", onPress: () => router.push('/(auth)') }]
      );
    }
  }, [params]);

  const validatePasswordStrength = (password: string) => {
    const validation = validatePassword(password);
    return validation;
  };

  const validateForm = () => {
    let isValid = true;

    // Clear errors first
    setPasswordError('');
    setConfirmPasswordError('');

    // Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      setPasswordError(passwordValidation.errors[0] || 'Kata sandi tidak memenuhi kriteria keamanan');
      isValid = false;
    } else if (passwordValidation.score < 60) {
      setPasswordError('Kata sandi terlalu lemah. Gunakan kombinasi huruf besar, kecil, angka, dan karakter khusus');
      isValid = false;
    }

    // Validate confirm password
    if (password !== confirmPassword) {
      setConfirmPasswordError('Konfirmasi kata sandi tidak cocok');
      isValid = false;
    }

    return isValid;
  };

  const handleResetPassword = async () => {
    clearError();
    
    if (!validateForm()) return;
    
    try {
      await updatePassword(password);
      
      Alert.alert(
        "Berhasil",
        "Kata sandi Anda telah berhasil diperbarui",
        [{ text: "OK", onPress: () => router.push('/(auth)') }]
      );
    } catch (error) {
      console.error('Password update error:', error);
    }
  };

  return (
    <SafeAreaView style={authStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {/* Background decorative elements */}
      <Image 
        source={require('../../assets/images/background-decoration.png')} 
        style={authStyles.backgroundDecoration} 
        resizeMode="cover"
      />
      
      <KeyboardAvoidingView 
        style={{flex: 1}} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={authStyles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <View style={authStyles.heroSection}>
            <Text style={authStyles.formTitle}>Reset Password</Text>
            <Text style={authStyles.formSubtitle}>
              Buat kata sandi baru untuk akun Anda
            </Text>
          </View>

          {/* Form Container */}
          <View style={authStyles.formContainer}>
            {/* Error Display */}
            {error && (
              <View style={authStyles.errorContainer}>
                <Text style={authStyles.errorText}>{error}</Text>
              </View>
            )}

            {/* New Password Input */}
            <View style={[authStyles.inputContainer, passwordError ? authStyles.inputError : null]}>
              <Feather name="lock" size={20} style={authStyles.inputIcon} />
              <TextInput
                style={authStyles.input}
                placeholder="Kata Sandi Baru"
                placeholderTextColor="#AAB2BF"
                value={password}
                onChangeText={(text) => {
                  setPassword(text);
                  if (passwordError) setPasswordError('');
                }}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={authStyles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Feather name={showPassword ? "eye-off" : "eye"} size={20} color="#777777" />
              </TouchableOpacity>
            </View>
            {passwordError ? <Text style={authStyles.fieldError}>{passwordError}</Text> : null}

            {/* Confirm Password Input */}
            <View style={[authStyles.inputContainer, confirmPasswordError ? authStyles.inputError : null]}>
              <Feather name="lock" size={20} style={authStyles.inputIcon} />
              <TextInput
                style={authStyles.input}
                placeholder="Konfirmasi Kata Sandi"
                placeholderTextColor="#AAB2BF"
                value={confirmPassword}
                onChangeText={(text) => {
                  setConfirmPassword(text);
                  if (confirmPasswordError) setConfirmPasswordError('');
                }}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={authStyles.eyeIcon}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Feather name={showConfirmPassword ? "eye-off" : "eye"} size={20} color="#777777" />
              </TouchableOpacity>
            </View>
            {confirmPasswordError ? <Text style={authStyles.fieldError}>{confirmPasswordError}</Text> : null}

            {/* Reset Button */}
            <TouchableOpacity
              style={[authStyles.authButton, loading ? authStyles.authButtonDisabled : null]}
              onPress={handleResetPassword}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={authStyles.authButtonText}>Perbarui Password</Text>
              )}
            </TouchableOpacity>

            {/* Back to Login Button */}
            <TouchableOpacity
              style={authStyles.createAccountButton}
              onPress={() => {
                clearError();
                router.push('/(auth)');
              }}
              disabled={loading}
            >
              <Text style={authStyles.createAccountButtonText}>Kembali ke Login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
