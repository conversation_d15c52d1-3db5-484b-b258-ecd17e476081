import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image,
  SafeAreaView,
  StatusBar,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { Feather } from '@expo/vector-icons';
import { authStyles } from '@/styles/AuthStyles';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [resetSent, setResetSent] = useState(false);
  
  const { resetPassword, loading, error, clearError } = useUniversalAuth();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleResetPassword = async () => {
    setEmailError('');
    clearError();

    if (!validateEmail(email)) {
      setEmailError('Mohon masukkan alamat email yang valid');
      return;
    }

    try {
      await resetPassword(email);
      setResetSent(true);
      Alert.alert(
        "Email Terkirim",
        "Instruksi untuk reset password telah dikirim ke email Anda.",
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error('Password reset error:', error);
    }
  };

  return (
    <SafeAreaView style={authStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {/* Background decorative elements */}
      <Image 
        source={require('../../assets/images/background-decoration.png')} 
        style={authStyles.backgroundDecoration} 
        resizeMode="cover"
      />
      
      <KeyboardAvoidingView 
        style={{flex: 1}} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={authStyles.scrollContent}
          showsVerticalScrollIndicator={false}
        >


          {/* Form Title */}
          <Text style={authStyles.formTitle}>Lupa Password</Text>
          <Text style={authStyles.formSubtitle}>
            Masukkan email Anda untuk menerima tautan reset password
          </Text>

          {/* Form Container */}
          <View style={authStyles.formContainer}>
            {/* Error Display */}
            {error && (
              <View style={authStyles.errorContainer}>
                <Text style={authStyles.errorText}>{error}</Text>
              </View>
            )}



            {/* Success Display */}
            {resetSent && (
              <View style={authStyles.successContainer}>
                <Text style={authStyles.successText}>
                  Link reset password telah dikirim ke email Anda.
                </Text>
              </View>
            )}

            {/* Email Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Email</Text>
              <View style={[authStyles.inputContainer, emailError ? authStyles.inputError : null]}>
                <Feather name="mail" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="<EMAIL>"
                  placeholderTextColor="#AAB2BF"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) setEmailError('');
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
              {emailError ? <Text style={authStyles.fieldError}>{emailError}</Text> : null}
            </View>



            {/* Button Row */}
            <View style={authStyles.buttonContainer}>
              <TouchableOpacity 
                style={authStyles.backButton}
                onPress={() => router.back()}
                disabled={loading}
              >
                <Feather name="arrow-left" size={24} color="#1C1C1E" />
              </TouchableOpacity>

              <TouchableOpacity
                style={authStyles.saveButton}
                onPress={handleResetPassword}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={authStyles.saveButtonText}>Kirim Link Reset</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
