import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { Feather } from '@expo/vector-icons';
import { authStyles } from '@/styles/AuthStyles';

export default function ChangePasswordScreen() {
  const { updatePassword, loading, error, clearError } = useUniversalAuth();
  
  // Form state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // Visibility state
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Validation errors
  const [currentPasswordError, setCurrentPasswordError] = useState('');
  const [newPasswordError, setNewPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  const validatePassword = (password: string) => {
    return password.length >= 6;
  };

  const validateForm = () => {
    let isValid = true;
    
    // Clear all errors first
    setCurrentPasswordError('');
    setNewPasswordError('');
    setConfirmPasswordError('');
    clearError();
    
    // Validate current password
    if (!currentPassword.trim()) {
      setCurrentPasswordError('Kata sandi saat ini wajib diisi');
      isValid = false;
    }
    
    // Validate new password
    if (!validatePassword(newPassword)) {
      setNewPasswordError('Kata sandi baru minimal 6 karakter');
      isValid = false;
    }
    
    // Validate confirm password
    if (newPassword !== confirmPassword) {
      setConfirmPasswordError('Konfirmasi kata sandi tidak cocok');
      isValid = false;
    }
    
    return isValid;
  };

  const handleChangePassword = async () => {
    if (!validateForm()) return;
    
    try {
      await updatePassword(newPassword);
      
      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      Alert.alert(
        'Berhasil',
        'Kata sandi Anda telah berhasil diperbarui',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Password update error:', error);
      Alert.alert(
        'Gagal',
        'Terjadi kesalahan saat memperbarui kata sandi. Silakan coba lagi.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <SafeAreaView style={authStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Background decorative elements */}
      <Image 
        source={require('../../assets/images/background-decoration.png')} 
        style={authStyles.backgroundDecoration} 
        resizeMode="cover"
      />
      
      <KeyboardAvoidingView 
        style={{flex: 1}} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={authStyles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <View style={authStyles.heroSection}>
            <Text style={authStyles.formTitle}>Ubah Kata Sandi</Text>
            <Text style={authStyles.formSubtitle}>
              Perbarui kata sandi akun Anda untuk keamanan yang lebih baik
            </Text>
          </View>

          {/* Error Display */}
          {error && (
            <View style={authStyles.errorContainer}>
              <Text style={authStyles.errorText}>{error}</Text>
            </View>
          )}

          <View style={authStyles.formContainer}>
            {/* Current Password Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Kata Sandi Saat Ini</Text>
              <View style={[authStyles.inputContainer, currentPasswordError ? authStyles.inputError : null]}>
                <Feather name="lock" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="Masukkan kata sandi saat ini"
                  placeholderTextColor="#AAB2BF"
                  value={currentPassword}
                  onChangeText={(text) => {
                    setCurrentPassword(text);
                    if (currentPasswordError) setCurrentPasswordError('');
                  }}
                  secureTextEntry={!showCurrentPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={authStyles.eyeIcon}
                  onPress={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  <Feather name={showCurrentPassword ? "eye-off" : "eye"} size={20} color="#777777" />
                </TouchableOpacity>
              </View>
              {currentPasswordError ? <Text style={authStyles.fieldError}>{currentPasswordError}</Text> : null}
            </View>

            {/* New Password Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Kata Sandi Baru</Text>
              <View style={[authStyles.inputContainer, newPasswordError ? authStyles.inputError : null]}>
                <Feather name="lock" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="Masukkan kata sandi baru"
                  placeholderTextColor="#AAB2BF"
                  value={newPassword}
                  onChangeText={(text) => {
                    setNewPassword(text);
                    if (newPasswordError) setNewPasswordError('');
                  }}
                  secureTextEntry={!showNewPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={authStyles.eyeIcon}
                  onPress={() => setShowNewPassword(!showNewPassword)}
                >
                  <Feather name={showNewPassword ? "eye-off" : "eye"} size={20} color="#777777" />
                </TouchableOpacity>
              </View>
              {newPasswordError ? <Text style={authStyles.fieldError}>{newPasswordError}</Text> : null}
            </View>

            {/* Confirm New Password Input */}
            <View style={authStyles.formField}>
              <Text style={authStyles.fieldLabel}>Konfirmasi Kata Sandi Baru</Text>
              <View style={[authStyles.inputContainer, confirmPasswordError ? authStyles.inputError : null]}>
                <Feather name="lock" size={20} style={authStyles.inputIcon} />
                <TextInput
                  style={authStyles.input}
                  placeholder="Konfirmasi kata sandi baru"
                  placeholderTextColor="#AAB2BF"
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text);
                    if (confirmPasswordError) setConfirmPasswordError('');
                  }}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={authStyles.eyeIcon}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Feather name={showConfirmPassword ? "eye-off" : "eye"} size={20} color="#777777" />
                </TouchableOpacity>
              </View>
              {confirmPasswordError ? <Text style={authStyles.fieldError}>{confirmPasswordError}</Text> : null}
            </View>

            {/* Button Row */}
            <View style={authStyles.buttonContainer}>
              <TouchableOpacity 
                style={authStyles.backButton}
                onPress={() => router.back()}
                disabled={loading}
              >
                <Feather name="arrow-left" size={24} color="#1C1C1E" />
              </TouchableOpacity>

              <TouchableOpacity
                style={[authStyles.saveButton, loading ? authStyles.authButtonDisabled : null]}
                onPress={handleChangePassword}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={authStyles.saveButtonText}>Perbarui Kata Sandi</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
