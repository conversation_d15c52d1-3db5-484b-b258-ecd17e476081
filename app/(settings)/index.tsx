import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ConfirmationModal } from '@/components/ConfirmationModal';

export default function SettingsScreen() {
  const { signOut } = useUniversalAuth();

  // Logout confirmation state
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace('/(auth)');
      setShowLogoutConfirmation(false);
    } catch (error) {
      console.error('Error signing out:', error);
      setShowLogoutConfirmation(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Background gradient */}
      <LinearGradient
        colors={['#E8F4FD', '#FFFFFF']}
        locations={[0, 0.4]}
        style={styles.backgroundGradient}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Ionicons name="settings" size={20} color="#2B7EFF" style={styles.headerIcon} />
          <Text style={styles.headerTitle}>Pengaturan</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Akun</Text>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="person-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Profil</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="notifications-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Notifikasi</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="lock-closed-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Privasi</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        {/* Security Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Keamanan</Text>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => router.push('/(settings)/change-password')}
          >
            <Ionicons name="key-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Ubah Kata Sandi</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferensi</Text>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="color-palette-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Tampilan</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="language-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Bahasa</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dukungan</Text>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="help-circle-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Bantuan & Dukungan</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="information-circle-outline" size={22} color="#1C1C1E" />
            <Text style={styles.menuItemText}>Tentang</Text>
            <Ionicons name="chevron-forward-outline" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.signOutButton}
          onPress={() => setShowLogoutConfirmation(true)}
        >
          <Text style={styles.signOutButtonText}>Keluar</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Logout Confirmation Modal */}
      <ConfirmationModal
        isVisible={showLogoutConfirmation}
        title="Konfirmasi Keluar"
        message="Apakah Anda yakin ingin keluar dari akun?"
        confirmText="Keluar"
        cancelText="Batal"
        confirmButtonColor="#FF3B30"
        onConfirm={handleSignOut}
        onCancel={() => setShowLogoutConfirmation(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#5B3E31',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#2B7EFF',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  menuItemText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1C1C1E',
  },
  signOutButton: {
    marginTop: 40,
    marginBottom: 24,
    backgroundColor: '#FF3B30',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  signOutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
