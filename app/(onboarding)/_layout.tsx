import { Stack, useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

export default function OnboardingLayout() {
  const { user, onboardingCompleted, loading } = useUniversalAuth();
  const router = useRouter();
  const segments = useSegments();

  console.log('🔄 [OnboardingLayout] Current state:', {
    user: !!user,
    onboardingCompleted,
    loading,
    segments,
    timestamp: new Date().toISOString()
  });

  useEffect(() => {
    if (loading) return; // Wait for auth to load

    // If user is not authenticated and has completed onboarding, redirect to auth
    if (!user && onboardingCompleted) {
      console.log('🔄 [OnboardingLayout] Redirecting to auth - onboarding already completed');
      router.replace('/(auth)');
      return;
    }

    // If user is authenticated and needs self-reflection, ensure we're on self-reflection
    if (user && segments[1] !== 'self-reflection') {
      console.log('🔄 [OnboardingLayout] Redirecting to self-reflection');
      router.replace('/(onboarding)/self-reflection');
      return;
    }
  }, [user, onboardingCompleted, loading, segments]);

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="self-reflection" />
    </Stack>
  );
}