/**
 * Self-Reflection Onboarding Screen
 * Integrates the self-reflection check-in into the onboarding flow
 */

import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { SelfReflectionCheckIn } from '@/components/SelfReflectionCheckIn';

import type { SelfReflectionSession } from '@/types/selfReflection';

export default function SelfReflectionOnboardingScreen() {
  const { completeSelfReflection, skipSelfReflection, user, loading } = useUniversalAuth();

  console.log('SelfReflectionOnboardingScreen mounted:', { user: !!user, userId: user?.id });

  // No manual navigation needed - protected routes handle access control

  const handleComplete = async (session: SelfReflectionSession) => {
    try {
      console.log('Self-reflection completed:', {
        userId: user?.id,
        sessionId: session.id,
        riskLevel: session.riskLevel,
        aiTonePreference: session.aiTonePreference,
        supportPreference: session.supportPreference,
      });

      // Session is already saved and completion status already updated
      // by SelfReflectionCheckIn component, just need to update AuthContext
      await completeSelfReflection();

      console.log('Self-reflection completed - ConvexLayout will handle navigation');
      // Don't navigate manually - let ConvexLayout handle routing based on state change
    } catch (error) {
      console.error('Error completing self-reflection:', error);
      // Don't navigate manually even on error - let ConvexLayout handle it
    }
  };

  const handleSkip = () => {
    console.log('🔄 Skip self-reflection clicked - NOT marking as completed');

    // Call skip function (doesn't update completion status)
    skipSelfReflection();

    // Don't navigate manually - let ConvexLayout handle routing based on state change
    console.log('✅ Self-reflection skipped - ConvexLayout will handle navigation');
  };

  // Simple loading state - protected routes ensure user is authenticated
  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#FEFEFE' }}>
        <ActivityIndicator size="large" color="#A08CFB" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#6B7280' }}>
          Loading...
        </Text>
      </View>
    );
  }

  return (
    <SelfReflectionCheckIn
      onComplete={handleComplete}
      onSkip={handleSkip}
      language="id"
    />
  );
}
