import React, { useState, useEffect } from 'react';
import { View, Text, Image, StatusBar, Pressable, useWindowDimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { onboardingStyles } from '@/styles/OnboardingStyles';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

import { ConvexAuthTest } from '@/components/ConvexAuthTest';

export default function OnboardingScreen() {
  const [ctaPressed, setCtaPressed] = useState(false);
  const { width, height } = useWindowDimensions();
  const { completeOnboarding, user, selfReflectionCompleted, selfReflectionSkipped } = useUniversalAuth();

  // Redirect authenticated users to self-reflection
  useEffect(() => {
    if (user && !selfReflectionCompleted && !selfReflectionSkipped) {
      console.log('🎯 [OnboardingIndex] Authenticated user detected → redirecting to self-reflection');
      router.replace('/(onboarding)/self-reflection');
    }
  }, [user, selfReflectionCompleted, selfReflectionSkipped]);

  const handleGetStarted = async () => {
    // Complete onboarding - protected routes will automatically navigate to auth
    console.log('🚀 [Onboarding] Button clicked - completing onboarding');
    try {
      await completeOnboarding();
      console.log('✅ [Onboarding] Onboarding completed successfully - protected routes should navigate to auth');
    } catch (error) {
      console.error('❌ [Onboarding] Failed to complete onboarding:', error);
    }
  };
  
  // Adjust styles based on screen dimensions
  const dynamicStyles = {
    heroHeight: height < 700 ? 320 : 420,
    titleSize: width < 350 ? 28 : 32,
    subtitleSize: width < 350 ? 13 : 14,
    pillPadding: width < 350 ? 10 : 16, // Smaller horizontal padding for pills
    pillFontSize: width < 350 ? 10 : 12, // Smaller font size for pills
  };

  return (
    <SafeAreaView style={onboardingStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FEFEFE" />
      
      {/* Background image */}
      <View style={onboardingStyles.backgroundContainer}>
        <Image
          source={require('../../assets/images/background-decoration.png')}
          style={onboardingStyles.backgroundImage}
          resizeMode="cover"
        />
      </View>
      
      <View style={onboardingStyles.mainContainer}>
        {/* Hero Section - Full width image */}
        <View style={[onboardingStyles.heroSection, { height: dynamicStyles.heroHeight }]}>
          <Image
            source={require('../../assets/images/onboarding-hero.png')}
            style={onboardingStyles.heroImage}
            resizeMode="contain"
          />
        </View>

        {/* Unified Content Container - Title, Feature Tags, and Button */}
        <View style={onboardingStyles.unifiedContentContainer}>
          {/* Title and Subtitle */}
          <View style={onboardingStyles.textContainer}>
            <Text style={[onboardingStyles.title, { fontSize: dynamicStyles.titleSize }]}>TEMANI</Text>
            
            <Text style={[onboardingStyles.subtitle, { fontSize: dynamicStyles.subtitleSize }]}>
              Temani Selalu Ada, Tempat Kamu Bercerita.
            </Text>
          </View>

          {/* Feature Tags Container - All Pills in a single responsive container */}
          <View style={onboardingStyles.responsiveFeaturesContainer}>
            <View style={[onboardingStyles.featureTag, { paddingHorizontal: dynamicStyles.pillPadding }]}>
              <Text style={[onboardingStyles.featureTagText, { fontSize: dynamicStyles.pillFontSize }]}>
                AI chatbot & voice agent 24/7
              </Text>
            </View>
            
            <View style={[onboardingStyles.featureTag, { paddingHorizontal: dynamicStyles.pillPadding }]}>
              <Text style={[onboardingStyles.featureTagText, { fontSize: dynamicStyles.pillFontSize }]}>
                Privasi terjaga
              </Text>
            </View>
            
            <View style={[onboardingStyles.featureTag, { paddingHorizontal: dynamicStyles.pillPadding }]}>
              <Text style={[onboardingStyles.featureTagText, { fontSize: dynamicStyles.pillFontSize }]}>
                Akses ke psikolog profesional
              </Text>
            </View>
          </View>

          {/* Call to Action Button */}
          <Pressable
            style={[
              onboardingStyles.ctaButton,
              ctaPressed && onboardingStyles.ctaButtonPressed
            ]}
            onPress={handleGetStarted}
            onPressIn={() => setCtaPressed(true)}
            onPressOut={() => setCtaPressed(false)}
          >
            <Text style={onboardingStyles.ctaButtonText}>Yuk Curhat</Text>
          </Pressable>

          {/* Temporary Convex Auth Test Component */}
          <View style={{ marginTop: 20, padding: 10, backgroundColor: '#f0f0f0', borderRadius: 10 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, textAlign: 'center' }}>
              🧪 Convex Auth Test (Development Only)
            </Text>
            <ConvexAuthTest />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}