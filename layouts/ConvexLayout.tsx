import React, { useEffect } from 'react';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Linking, View, Text, ActivityIndicator, Platform } from 'react-native';
import { QueryClientProvider } from '@tanstack/react-query';
import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient, Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import * as SecureStore from "expo-secure-store";
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { ConvexAuthProvider as CustomConvexAuthProvider, useConvexAuthContext } from '@/context/ConvexAuthContext';
import { VoiceModeProvider } from '@/context/VoiceModeContext';
import { JournalProvider } from '@/context/JournalContext';
import { queryClient, setupQueryPersistence } from '@/lib/queryClient';
import { useMobileOptimizations } from '@/hooks/useAppFocus';
import * as SplashScreen from 'expo-splash-screen';
import * as Sentry from '@sentry/react-native';
import { initializeSentry } from '@/lib/sentryConfig';
import { CONVEX_CONFIG } from '@/lib/env';

// Initialize Sentry with enhanced privacy-first configuration
initializeSentry();

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

declare global {
  interface Window {
    frameworkReady?: () => void;
  }
}

const convex = new ConvexReactClient(CONVEX_CONFIG.url, {
  unsavedChangesWarning: false,
});

const secureStorage = {
  getItem: SecureStore.getItemAsync,
  setItem: SecureStore.setItemAsync,
  removeItem: SecureStore.deleteItemAsync,
};

function RootNavigator() {
  const {
    user,
    loading,
    userStatusLoading,
    connectionError,
    onboardingCompleted,
    selfReflectionCompleted,
    selfReflectionSkipped,
    completeOnboarding,
    retryConnection
  } = useConvexAuthContext();

  console.log('Protected Routes Navigation State:', {
    user: !!user,
    userId: user?._id,
    loading,
    userStatusLoading,
    onboardingCompleted,
    selfReflectionCompleted,
    selfReflectionSkipped,
    routingDecision: 'INDEX_SCREEN → handles routing decisions',
    timestamp: new Date().toISOString()
  });

  // Simple splash screen management - hide when auth state is determined
  React.useEffect(() => {
    if (!loading && !userStatusLoading) {
      SplashScreen.hideAsync();
    }
  }, [loading, userStatusLoading]);

  useEffect(() => {
    // Handle deep links for OAuth callback
    const handleDeepLink = (url: string) => {
      console.log('Deep link received:', url);

      // Skip web OAuth URLs - let Convex handle them automatically
      if (Platform.OS === 'web' && url.includes('#access_token=')) {
        console.log('Web OAuth URL detected, letting Convex handle automatically');
        return;
      }

      // Check if it's a mobile OAuth callback
      if (url.includes('temani://auth/callback')) {
        // Extract query parameters from the URL
        const urlObj = new URL(url);
        const params = new URLSearchParams(urlObj.search || urlObj.hash.substring(1));

        // Navigate to callback screen with parameters
        const queryString = params.toString();
        router.push(`/(auth)/callback?${queryString}` as any);
        return;
      }

      // Handle other deep links as needed
    };

    // Listen for deep links
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    // Handle initial URL if app was opened via deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => {
      subscription?.remove();
    };
  }, []);

  // Auto-complete onboarding for authenticated users
  React.useEffect(() => {
    if (user && !onboardingCompleted && !loading && !userStatusLoading) {
      console.log('Auto-completing onboarding for authenticated user');
      completeOnboarding();
    }
  }, [user, onboardingCompleted, loading, userStatusLoading, completeOnboarding]);

  // Show loading screen while determining initial route
  if (loading || userStatusLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#FEFEFE' }}>
        <ActivityIndicator size="large" color="#A08CFB" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#6B7280' }}>
          {loading ? 'Loading...' : 'Loading user data...'}
        </Text>
        {connectionError && (
          <View style={{ marginTop: 20, paddingHorizontal: 20, alignItems: 'center' }}>
            <Text style={{ fontSize: 14, color: '#EF4444', textAlign: 'center', marginBottom: 10 }}>
              {connectionError}
            </Text>
            <Text
              style={{ fontSize: 14, color: '#A08CFB', textDecorationLine: 'underline' }}
              onPress={retryConnection}
            >
              Tap to retry
            </Text>
          </View>
        )}

      </View>
    );
  }

  // Simplified routing - let index screen handle initial routing decisions
  return (
    <Stack screenOptions={{ headerShown: false }}>
      {/* Index screen handles initial routing */}
      <Stack.Screen name="index" options={{ headerShown: false }} />

      {/* All users have access to onboarding and auth */}
      <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
      <Stack.Screen name="(auth)" options={{ headerShown: false }} />

      {/* Authenticated users who completed self-reflection */}
      <Stack.Protected guard={user && (selfReflectionCompleted || selfReflectionSkipped)}>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      </Stack.Protected>

      {/* Always accessible screens */}
      <Stack.Screen name="+not-found" />
    </Stack>
  );
}

// Alternative implementation using Convex Auth components (for future use)
// To use this approach, replace RootNavigator with ConvexAuthNavigator in the main export
// This follows Convex Auth best practices for conditional rendering
function ConvexAuthNavigator() {
  const { selfReflectionCompleted, selfReflectionSkipped } = useConvexAuthContext();

  return (
    <>
      <AuthLoading>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#A08CFB" />
          <Text style={{ marginTop: 16, color: '#666' }}>Loading...</Text>
        </View>
      </AuthLoading>

      <Unauthenticated>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
          <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        </Stack>
      </Unauthenticated>

      <Authenticated>
        {selfReflectionCompleted || selfReflectionSkipped ? (
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          </Stack>
        ) : (
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="(onboarding)/self-reflection" options={{ headerShown: false }} />
          </Stack>
        )}
      </Authenticated>
    </>
  );
}

function QueryProvider({ children }: { children: React.ReactNode }) {
  // Setup mobile optimizations for TanStack Query
  useMobileOptimizations();

  // Setup query persistence on mount
  useEffect(() => {
    setupQueryPersistence();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

export default Sentry.wrap(function ConvexRootLayout() {
  useFrameworkReady();

  return (
    <ConvexAuthProvider
      client={convex}
      storage={
        Platform.OS === "android" || Platform.OS === "ios"
          ? secureStorage
          : undefined
      }
    >
      <QueryProvider>
        <CustomConvexAuthProvider>
          <VoiceModeProvider>
            <JournalProvider>
              <RootNavigator />
              <StatusBar style="auto" />
            </JournalProvider>
          </VoiceModeProvider>
        </CustomConvexAuthProvider>
      </QueryProvider>
    </ConvexAuthProvider>
  );
});
