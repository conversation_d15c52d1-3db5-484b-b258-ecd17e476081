import React, { useEffect } from 'react';
import { Stack , router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Linking, View, Text, ActivityIndicator, Platform } from 'react-native';
import { QueryClientProvider } from '@tanstack/react-query';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { AuthProvider, useAuth } from '@/context/AuthContext';
import { VoiceModeProvider } from '@/context/VoiceModeContext';
import { JournalProvider } from '@/context/JournalContext';
import { queryClient, setupQueryPersistence } from '@/lib/queryClient';
import { useMobileOptimizations } from '@/hooks/useAppFocus';
import * as SplashScreen from 'expo-splash-screen';
import * as Sentry from '@sentry/react-native';
import { initializeSentry } from '@/lib/sentryConfig';

// Initialize Sentry with enhanced privacy-first configuration
initializeSentry();

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

declare global {
  interface Window {
    frameworkReady?: () => void;
  }
}

function RootNavigator() {
  const { user, loading, userStatusLoading, onboardingCompleted, selfReflectionCompleted, selfReflectionSkipped } = useAuth();
  const [hasNavigated, setHasNavigated] = React.useState(false);

  // Reset navigation flag when user changes (sign in/out)
  React.useEffect(() => {
    setHasNavigated(false);
  }, [user?.id]);

  useEffect(() => {
    // Handle deep links for OAuth callback
    const handleDeepLink = (url: string) => {
      console.log('Deep link received:', url);

      // Skip web OAuth URLs - let Supabase handle them automatically
      if (Platform.OS === 'web' && url.includes('#access_token=')) {
        console.log('Web OAuth URL detected, letting Supabase handle automatically');
        return;
      }

      // Check if it's a mobile OAuth callback
      if (url.includes('temani://auth/callback')) {
        // Extract query parameters from the URL
        const urlObj = new URL(url);
        const params = new URLSearchParams(urlObj.search || urlObj.hash.substring(1));

        // Navigate to callback screen with parameters
        const queryString = params.toString();
        router.push(`/(auth)/callback?${queryString}` as any);
        return;
      }

      // Handle other deep links as needed
    };

    // Listen for deep links
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    // Handle initial URL if app was opened via deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    return () => {
      subscription?.remove();
    };
  }, []);

  useEffect(() => {
    // Wait for both auth loading and user status loading to complete
    if (!loading && !userStatusLoading && !hasNavigated) {
      // Hide splash screen once auth state is determined
      SplashScreen.hideAsync();

      // Handle routing based on auth state, onboarding, and self-reflection status
      console.log('Supabase Auth Routing decision:', {
        user: !!user,
        userId: user?.id,
        loading,
        userStatusLoading,
        onboardingCompleted,
        selfReflectionCompleted,
        selfReflectionSkipped,
        hasNavigated,
        timestamp: new Date().toISOString()
      });

      if (user) {
        // User is authenticated and status is loaded
        if (!selfReflectionCompleted && !selfReflectionSkipped) {
          // User is authenticated but hasn't completed or skipped self-reflection
          console.log('Redirecting to self-reflection for user:', user.id);
          setHasNavigated(true);
          router.replace('/(onboarding)/self-reflection');
        } else {
          // User is fully onboarded or has skipped - go to main app
          console.log('User fully onboarded or skipped, going to main app');
          setHasNavigated(true);
          router.replace('/(tabs)');
        }
      } else {
        // User is not authenticated
        if (onboardingCompleted) {
          // User has seen onboarding before - go directly to auth
          // This prevents returning users from being sent back to onboarding on failed login
          console.log('Returning user (onboarding completed), going to auth');
          setHasNavigated(true);
          router.replace('/(auth)');
        } else {
          // First time user - show onboarding
          console.log('First time user, showing onboarding');
          setHasNavigated(true);
          router.replace('/(onboarding)');
        }
      }
    } else {
      console.log('Still loading or already navigated:', { loading, userStatusLoading, hasNavigated });
    }
  }, [user, loading, userStatusLoading, onboardingCompleted, selfReflectionCompleted, selfReflectionSkipped, hasNavigated]);

  // Show loading screen while determining initial route
  if (loading || userStatusLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#FEFEFE' }}>
        <ActivityIndicator size="large" color="#A08CFB" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#6B7280' }}>
          {loading ? 'Loading...' : 'Loading user data...'}
        </Text>
      </View>
    );
  }

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
      <Stack.Screen name="(auth)" options={{ headerShown: false }} />
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="+not-found" />
    </Stack>
  );
}

function QueryProvider({ children }: { children: React.ReactNode }) {
  // Setup mobile optimizations for TanStack Query
  useMobileOptimizations();

  // Setup query persistence on mount
  useEffect(() => {
    setupQueryPersistence();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

export default Sentry.wrap(function SupabaseRootLayout() {
  useFrameworkReady();

  return (
    <QueryProvider>
      <AuthProvider>
        <VoiceModeProvider>
          <JournalProvider>
            <RootNavigator />
            <StatusBar style="auto" />
          </JournalProvider>
        </VoiceModeProvider>
      </AuthProvider>
    </QueryProvider>
  );
});
