/**
 * Question Configuration Service
 * Handles dynamic loading and management of self-reflection questions
 */

import { supabase } from './supabase';
import type {
  QuestionSet,
  Question,
  RiskAssessmentRule,
  ValidationResult
} from '@/types/selfReflection';
import { QuestionConfigError } from '@/types/selfReflection';

export class QuestionConfigService {
  private static questionSetCache: Map<string, QuestionSet> = new Map();
  private static cacheExpiry: Map<string, number> = new Map();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get the active question set for a specific language
   */
  static async getActiveQuestionSet(language: string = 'id'): Promise<QuestionSet> {
    const cacheKey = `active_${language}`;
    
    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      const cached = this.questionSetCache.get(cacheKey);
      if (cached) return cached;
    }

    try {
      console.log(`Loading question set for language: ${language}`);

      // Fetch active question set
      const { data: questionSetData, error: setError } = await supabase
        .from('question_sets')
        .select('*')
        .eq('is_active', true)
        .eq('language', language)
        .single();

      console.log('Question set query result:', { data: questionSetData, error: setError });

      if (setError) {
        // Handle specific RLS/authentication errors
        if (setError.code === 'PGRST116') {
          throw new QuestionConfigError(
            `No active question set found for language: ${language}. Please ensure you are authenticated.`,
            { language, error: setError, code: 'AUTHENTICATION_REQUIRED' }
          );
        }
        throw new QuestionConfigError(
          `Failed to fetch active question set: ${setError.message}`,
          { language, error: setError }
        );
      }

      if (!questionSetData) {
        throw new QuestionConfigError(
          `No active question set found for language: ${language}`,
          { language }
        );
      }

      // Fetch questions for this set
      const { data: questionsData, error: questionsError } = await supabase
        .from('questions')
        .select('*')
        .eq('question_set_id', questionSetData.id)
        .order('order_index', { ascending: true });

      if (questionsError) {
        throw new QuestionConfigError(
          `Failed to fetch questions: ${questionsError.message}`,
          { questionSetId: questionSetData.id, error: questionsError }
        );
      }

      // Fetch risk assessment rules
      const { data: rulesData, error: rulesError } = await supabase
        .from('risk_assessment_rules')
        .select('*')
        .eq('question_set_id', questionSetData.id)
        .eq('is_active', true);

      if (rulesError) {
        throw new QuestionConfigError(
          `Failed to fetch risk assessment rules: ${rulesError.message}`,
          { questionSetId: questionSetData.id, error: rulesError }
        );
      }

      // Transform data to TypeScript interfaces
      const questionSet: QuestionSet = {
        id: questionSetData.id,
        name: questionSetData.name,
        version: questionSetData.version,
        description: questionSetData.description,
        isActive: questionSetData.is_active,
        language: questionSetData.language,
        questions: (questionsData || []).map(this.transformQuestion),
        riskAssessmentRules: (rulesData || []).map(this.transformRiskRule),
        createdAt: new Date(questionSetData.created_at),
        updatedAt: new Date(questionSetData.updated_at)
      };

      // Validate question set
      const validation = this.validateQuestionSet(questionSet);
      if (!validation.isValid) {
        console.warn('Question set validation warnings:', validation.warnings);
        if (validation.errors.length > 0) {
          throw new QuestionConfigError(
            `Invalid question set configuration: ${validation.errors.join(', ')}`,
            { questionSet, validation }
          );
        }
      }

      // Cache the result
      this.questionSetCache.set(cacheKey, questionSet);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION);

      return questionSet;

    } catch (error) {
      if (error instanceof QuestionConfigError) {
        throw error;
      }
      throw new QuestionConfigError(
        `Unexpected error loading question set: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { language, originalError: error }
      );
    }
  }

  /**
   * Get a specific question set by ID
   */
  static async getQuestionSetById(id: string): Promise<QuestionSet | null> {
    const cacheKey = `set_${id}`;
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.questionSetCache.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const { data: questionSetData, error } = await supabase
        .from('question_sets')
        .select(`
          *,
          questions(*),
          risk_assessment_rules(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw new QuestionConfigError(
          `Failed to fetch question set: ${error.message}`,
          { id, error }
        );
      }

      const questionSet: QuestionSet = {
        id: questionSetData.id,
        name: questionSetData.name,
        version: questionSetData.version,
        description: questionSetData.description,
        isActive: questionSetData.is_active,
        language: questionSetData.language,
        questions: (questionSetData.questions || [])
          .sort((a: any, b: any) => a.order_index - b.order_index)
          .map(this.transformQuestion),
        riskAssessmentRules: (questionSetData.risk_assessment_rules || [])
          .filter((rule: any) => rule.is_active)
          .map(this.transformRiskRule),
        createdAt: new Date(questionSetData.created_at),
        updatedAt: new Date(questionSetData.updated_at)
      };

      // Cache the result
      this.questionSetCache.set(cacheKey, questionSet);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION);

      return questionSet;

    } catch (error) {
      if (error instanceof QuestionConfigError) {
        throw error;
      }
      throw new QuestionConfigError(
        `Unexpected error loading question set by ID: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id, originalError: error }
      );
    }
  }

  /**
   * Clear cache for a specific key or all cache
   */
  static clearCache(key?: string): void {
    if (key) {
      this.questionSetCache.delete(key);
      this.cacheExpiry.delete(key);
    } else {
      this.questionSetCache.clear();
      this.cacheExpiry.clear();
    }
  }

  /**
   * Check if cache is valid for a given key
   */
  private static isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    return expiry ? Date.now() < expiry : false;
  }

  /**
   * Transform database question to TypeScript interface
   */
  private static transformQuestion(questionData: any): Question {
    return {
      id: questionData.id,
      questionSetId: questionData.question_set_id,
      questionKey: questionData.question_key,
      questionText: questionData.question_text,
      questionType: questionData.question_type,
      options: questionData.options,
      scoringConfig: questionData.scoring_config,
      orderIndex: questionData.order_index,
      isRequired: questionData.is_required,
      conditionalLogic: questionData.conditional_logic,
      metadata: questionData.metadata || {},
      createdAt: new Date(questionData.created_at),
      updatedAt: new Date(questionData.updated_at)
    };
  }

  /**
   * Transform database risk rule to TypeScript interface
   */
  private static transformRiskRule(ruleData: any): RiskAssessmentRule {
    return {
      id: ruleData.id,
      questionSetId: ruleData.question_set_id,
      ruleName: ruleData.rule_name,
      scoreRanges: ruleData.score_ranges,
      actions: ruleData.actions,
      isActive: ruleData.is_active,
      createdAt: new Date(ruleData.created_at),
      updatedAt: new Date(ruleData.updated_at)
    };
  }

  /**
   * Validate question set configuration
   */
  private static validateQuestionSet(questionSet: QuestionSet): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if questions exist
    if (!questionSet.questions || questionSet.questions.length === 0) {
      errors.push('Question set must have at least one question');
    }

    // Check if risk assessment rules exist
    if (!questionSet.riskAssessmentRules || questionSet.riskAssessmentRules.length === 0) {
      warnings.push('Question set has no risk assessment rules');
    }

    // Validate question order
    const orderIndices = questionSet.questions.map(q => q.orderIndex).sort((a, b) => a - b);
    for (let i = 0; i < orderIndices.length; i++) {
      if (orderIndices[i] !== i + 1) {
        warnings.push(`Question order indices are not sequential: expected ${i + 1}, got ${orderIndices[i]}`);
        break;
      }
    }

    // Validate question keys are unique
    const questionKeys = questionSet.questions.map(q => q.questionKey);
    const uniqueKeys = new Set(questionKeys);
    if (questionKeys.length !== uniqueKeys.size) {
      errors.push('Question keys must be unique within a question set');
    }

    // Validate required questions have proper configuration
    questionSet.questions.forEach((question, index) => {
      if (question.isRequired && (!question.options || question.options.length === 0)) {
        if (question.questionType === 'multiple_choice' || question.questionType === 'multi_select') {
          errors.push(`Required question ${question.questionKey} must have options`);
        }
      }

      // Validate scoring configuration
      if (question.scoringConfig.method !== 'none' && question.scoringConfig.maxScore <= 0) {
        warnings.push(`Question ${question.questionKey} has scoring enabled but maxScore is ${question.scoringConfig.maxScore}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Preload question sets for better performance
   */
  static async preloadQuestionSets(languages: string[] = ['id']): Promise<void> {
    const loadPromises = languages.map(lang => 
      this.getActiveQuestionSet(lang).catch(error => {
        console.warn(`Failed to preload question set for language ${lang}:`, error);
      })
    );

    await Promise.all(loadPromises);
  }
}
