/**
 * TanStack Query Client Configuration
 * Optimized for React Native + Expo + Supabase
 */

import { QueryClient } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Create QueryClient with mobile-optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes before considering it stale
      staleTime: 5 * 60 * 1000, // 5 minutes
      
      // Keep data in cache for 24 hours
      gcTime: 24 * 60 * 60 * 1000, // 24 hours (formerly cacheTime)
      
      // Retry failed requests 2 times (mobile-friendly)
      retry: 2,
      
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Refetch on window focus (important for mobile apps)
      refetchOnWindowFocus: true,
      
      // Refetch on reconnect
      refetchOnReconnect: true,
      
      // Don't refetch on mount if data is fresh
      refetchOnMount: true,
    },
    mutations: {
      // Retry mutations once
      retry: 1,
      
      // Retry delay for mutations
      retryDelay: 1000,
    },
  },
});

// Setup persistence with AsyncStorage (optional but recommended)
export const setupQueryPersistence = () => {
  try {
    // For now, we'll skip persistence to avoid compatibility issues
    // This can be added later with proper persister setup
    console.log('[QueryClient] Persistence setup skipped for now');
  } catch (error) {
    console.warn('[QueryClient] Failed to setup persistence:', error);
  }
};

// Query key factories for consistent key management
export const queryKeys = {
  // Journal related queries
  journal: {
    all: ['journal'] as const,
    history: (userId: string) => ['journal', 'history', userId] as const,
    today: (userId: string) => ['journal', 'today', userId] as const,
    questions: () => ['journal', 'questions'] as const,
    byDate: (userId: string, date: string) => ['journal', 'byDate', userId, date] as const,
    // Custom field queries
    customFields: (userId: string) => ['journal', 'customFields', userId] as const,
    userFields: (userId: string) => ['journal', 'userFields', userId] as const,
  },


  ai: {
    all: ['ai'] as const,
    rateLimit: (userId: string) => ['ai', 'rateLimit', userId] as const,
    stats: (userId: string) => ['ai', 'stats', userId] as const,
    history: (userId: string) => ['ai', 'history', userId] as const,
  },

  // Mood related queries
  mood: {
    all: ['mood'] as const,
    entries: () => ['mood', 'entries'] as const,
    entry: (userId: string, date: string) => ['mood', 'entry', userId, date] as const,
    today: (userId: string) => ['mood', 'today', userId] as const,
    weekly: (userId: string, weekStart: string) => ['mood', 'weekly', userId, weekStart] as const,
    currentWeek: (userId: string) => ['mood', 'currentWeek', userId] as const,
    history: (userId: string, startDate: string, endDate: string) => ['mood', 'history', userId, startDate, endDate] as const,
    trends: (userId: string, period: string, count: number) => ['mood', 'trends', userId, period, count] as const,
    analytics: (userId: string, period: string) => ['mood', 'analytics', userId, period] as const,

  },

  // User related queries
  user: {
    all: ['user'] as const,
    profile: (userId: string) => ['user', 'profile', userId] as const,
    preferences: (userId: string) => ['user', 'preferences', userId] as const,
    completionStatus: (userId: string) => ['user', 'completionStatus', userId] as const,
    status: (userId: string) => ['user', 'status', userId] as const,
  },
} as const;

// Helper function to invalidate all journal queries
export const invalidateJournalQueries = () => {
  queryClient.invalidateQueries({ queryKey: queryKeys.journal.all });
};

// Helper function to invalidate specific user's journal data
export const invalidateUserJournalQueries = (userId: string) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.journal.history(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.journal.today(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.journal.customFields(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.journal.userFields(userId) });
};

// Helper function to invalidate AI-related queries
export const invalidateAIQueries = (userId: string) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.ai.rateLimit(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.ai.stats(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.ai.history(userId) });
};

// Helper function to invalidate all mood queries
export const invalidateMoodQueries = () => {
  queryClient.invalidateQueries({ queryKey: queryKeys.mood.all });
};

// Helper function to invalidate specific user's mood data
export const invalidateUserMoodQueries = (userId: string) => {
  queryClient.invalidateQueries({ queryKey: ['mood', 'entry', userId] });
  queryClient.invalidateQueries({ queryKey: ['mood', 'today', userId] });
  queryClient.invalidateQueries({ queryKey: ['mood', 'weekly', userId] });
  queryClient.invalidateQueries({ queryKey: ['mood', 'currentWeek', userId] });
  queryClient.invalidateQueries({ queryKey: ['mood', 'history', userId] });
  queryClient.invalidateQueries({ queryKey: ['mood', 'trends', userId] });
  queryClient.invalidateQueries({ queryKey: ['mood', 'analytics', userId] });
};
