/**
 * Background Sync Service
 * Research-based implementation for healthcare data integrity
 * Ensures critical mental health data is never lost
 */

import { supabase } from './supabase';

interface SyncQueueItem {
  id: string;
  type: 'journal' | 'self_reflection' | 'user_profile';
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

class BackgroundSyncService {
  private syncQueue: SyncQueueItem[] = [];
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;
  private readonly STORAGE_KEY = 'temani_sync_queue';
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 5000; // 5 seconds

  constructor() {
    this.initializeService();
  }

  private initializeService() {
    // Only initialize on web platform
    if (typeof window === 'undefined') return;

    // Load existing queue from storage
    this.loadSyncQueue();

    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Set initial online status
    this.isOnline = navigator.onLine;

    // Register service worker message handler
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage.bind(this));
    }

    // Attempt sync on initialization if online
    if (this.isOnline) {
      this.processSyncQueue();
    }
  }

  private handleOnline() {
    console.log('[BackgroundSync] Device came online');
    this.isOnline = true;
    this.processSyncQueue();
  }

  private handleOffline() {
    console.log('[BackgroundSync] Device went offline');
    this.isOnline = false;
  }

  private handleServiceWorkerMessage(event: MessageEvent) {
    if (event.data?.type === 'BACKGROUND_SYNC') {
      console.log('[BackgroundSync] Service worker triggered sync');
      this.processSyncQueue();
    }
  }

  private loadSyncQueue() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.syncQueue = JSON.parse(stored);
        console.log(`[BackgroundSync] Loaded ${this.syncQueue.length} items from storage`);
      }
    } catch (error) {
      console.error('[BackgroundSync] Error loading sync queue:', error);
      this.syncQueue = [];
    }
  }

  private saveSyncQueue() {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('[BackgroundSync] Error saving sync queue:', error);
    }
  }

  /**
   * Add item to sync queue
   */
  public addToQueue(type: SyncQueueItem['type'], data: any): string {
    const item: SyncQueueItem = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: this.MAX_RETRIES,
    };

    this.syncQueue.push(item);
    this.saveSyncQueue();

    console.log(`[BackgroundSync] Added ${type} to queue:`, item.id);

    // Try immediate sync if online
    if (this.isOnline && !this.syncInProgress) {
      this.processSyncQueue();
    }

    return item.id;
  }

  /**
   * Process the sync queue
   */
  private async processSyncQueue() {
    if (!this.isOnline || this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`[BackgroundSync] Processing ${this.syncQueue.length} items`);

    const itemsToRemove: string[] = [];

    for (const item of this.syncQueue) {
      try {
        const success = await this.syncItem(item);
        
        if (success) {
          itemsToRemove.push(item.id);
          console.log(`[BackgroundSync] Successfully synced:`, item.id);
        } else {
          // Increment retry count
          item.retryCount++;
          
          if (item.retryCount >= item.maxRetries) {
            console.error(`[BackgroundSync] Max retries reached for:`, item.id);
            itemsToRemove.push(item.id);
            
            // Optionally notify user of failed sync
            this.notifyFailedSync(item);
          } else {
            console.warn(`[BackgroundSync] Retry ${item.retryCount}/${item.maxRetries} for:`, item.id);
          }
        }
      } catch (error) {
        console.error(`[BackgroundSync] Error syncing item ${item.id}:`, error);
        item.retryCount++;
        
        if (item.retryCount >= item.maxRetries) {
          itemsToRemove.push(item.id);
          this.notifyFailedSync(item);
        }
      }
    }

    // Remove successfully synced and failed items
    this.syncQueue = this.syncQueue.filter(item => !itemsToRemove.includes(item.id));
    this.saveSyncQueue();

    this.syncInProgress = false;
    console.log(`[BackgroundSync] Sync complete. ${this.syncQueue.length} items remaining`);
  }

  /**
   * Sync individual item based on type
   */
  private async syncItem(item: SyncQueueItem): Promise<boolean> {
    try {
      switch (item.type) {
        case 'journal':
          return await this.syncJournalEntry(item.data);
        
        case 'self_reflection':
          return await this.syncSelfReflection(item.data);
        
        case 'user_profile':
          return await this.syncUserProfile(item.data);
        
        default:
          console.error(`[BackgroundSync] Unknown sync type: ${item.type}`);
          return false;
      }
    } catch (error) {
      console.error(`[BackgroundSync] Error in syncItem for ${item.type}:`, error);
      return false;
    }
  }

  /**
   * Sync journal entry
   */
  private async syncJournalEntry(data: any): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('journal_entries')
        .upsert(data);

      return !error;
    } catch (error) {
      console.error('[BackgroundSync] Journal sync error:', error);
      return false;
    }
  }

  /**
   * Sync self-reflection session
   */
  private async syncSelfReflection(data: any): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('self_reflection_sessions')
        .upsert(data);

      return !error;
    } catch (error) {
      console.error('[BackgroundSync] Self-reflection sync error:', error);
      return false;
    }
  }

  /**
   * Sync user profile
   */
  private async syncUserProfile(data: any): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .upsert(data);

      return !error;
    } catch (error) {
      console.error('[BackgroundSync] User profile sync error:', error);
      return false;
    }
  }

  /**
   * Notify user of failed sync
   */
  private notifyFailedSync(item: SyncQueueItem) {
    // In a real app, you might show a toast notification or update UI
    console.warn(`[BackgroundSync] Failed to sync ${item.type} after ${item.maxRetries} attempts`);
    
    // Could dispatch a custom event for UI components to listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('sync-failed', { 
        detail: { type: item.type, id: item.id } 
      }));
    }
  }

  /**
   * Get current queue status
   */
  public getQueueStatus() {
    return {
      itemCount: this.syncQueue.length,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      items: this.syncQueue.map(item => ({
        id: item.id,
        type: item.type,
        timestamp: item.timestamp,
        retryCount: item.retryCount,
      })),
    };
  }

  /**
   * Force sync attempt
   */
  public forcSync() {
    if (this.isOnline) {
      this.processSyncQueue();
    }
  }

  /**
   * Clear sync queue (use with caution)
   */
  public clearQueue() {
    this.syncQueue = [];
    this.saveSyncQueue();
    console.log('[BackgroundSync] Queue cleared');
  }
}

// Export singleton instance
export const backgroundSync = new BackgroundSyncService();

// Export types for use in other files
export type { SyncQueueItem };
