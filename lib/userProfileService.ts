/**
 * User Profile Service
 * Manages user completion status and preferences in Supabase
 * Replaces local storage with database storage
 */

import { supabase } from './supabase';
import { SelfReflectionService } from './selfReflectionService';
import type { SelfReflectionSession } from '@/types/selfReflection';
import { startTransaction, addBreadcrumb, reportError } from '@/lib/sentryConfig';

export interface UserProfile {
  id: string;
  userId: string;
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
  latestSessionId?: string;
  latestRiskLevel?: string;
  latestCompletedAt?: Date;
  preferences: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CompletionStatus {
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
}

export interface CompletionUpdates {
  onboardingCompleted?: boolean;
  selfReflectionCompleted?: boolean;
  preferences?: Record<string, any>;
}

export class UserProfileError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'UserProfileError';
  }
}

export class UserProfileService {
  private static cache = new Map<string, UserProfile>();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private static cacheExpiry = new Map<string, number>();

  /**
   * Get or create user profile
   */
  static async getOrCreateProfile(userId: string): Promise<UserProfile> {
    try {
      // Check cache first
      if (this.isCacheValid(userId)) {
        const cached = this.cache.get(userId);
        if (cached) return cached;
      }

      console.log(`Getting or creating profile for user: ${userId}`);

      // Use database function for atomic get-or-create
      const { data, error } = await supabase
        .rpc('get_or_create_user_profile', { target_user_id: userId });

      if (error) {
        throw new UserProfileError(
          `Failed to get or create profile: ${error.message}`,
          'GET_OR_CREATE_ERROR',
          { userId, error }
        );
      }

      const profile = this.transformProfileData(data);
      
      // Cache the result
      this.cache.set(userId, profile);
      this.cacheExpiry.set(userId, Date.now() + this.CACHE_DURATION);

      return profile;

    } catch (error) {
      if (error instanceof UserProfileError) {
        throw error;
      }
      throw new UserProfileError(
        `Unexpected error getting profile: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_ERROR',
        { userId, originalError: error }
      );
    }
  }

  /**
   * Get completion status for a user
   */
  static async getCompletionStatus(userId: string): Promise<CompletionStatus> {
    try {
      console.log(`Getting completion status for user: ${userId}`);

      // For new users, there might not be a profile yet
      // Use maybeSingle() instead of single() to handle 0 rows gracefully
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('onboarding_completed, self_reflection_completed')
        .eq('user_id', userId)
        .maybeSingle();

      if (profileData && !profileError) {
        console.log('Profile data found:', profileData);
        return {
          onboardingCompleted: profileData.onboarding_completed,
          selfReflectionCompleted: profileData.self_reflection_completed
        };
      }

      // If no profile exists (new user), return defaults
      if (!profileData && !profileError) {
        console.log('No profile found for new user, returning defaults');
        return {
          onboardingCompleted: false,
          selfReflectionCompleted: false
        };
      }

      // Log any actual errors
      if (profileError) {
        console.warn('Profile lookup error:', {
          code: profileError.code,
          message: profileError.message,
          details: profileError.details
        });
      }

      // Fallback: Check sessions directly for existing users
      try {
        const hasSession = await SelfReflectionService.hasCompletedSelfReflection(userId);
        console.log('Session check fallback result:', hasSession);
        return {
          onboardingCompleted: false, // Will be migrated separately
          selfReflectionCompleted: hasSession
        };
      } catch (sessionError) {
        console.warn('Session check also failed:', sessionError);
        // Return safe defaults for new users
        return {
          onboardingCompleted: false,
          selfReflectionCompleted: false
        };
      }

    } catch (error) {
      console.error('Error getting completion status:', error);

      // Return safe defaults for new users
      return {
        onboardingCompleted: false,
        selfReflectionCompleted: false
      };
    }
  }

  /**
   * Update completion status
   */
  static async updateCompletionStatus(
    userId: string,
    updates: CompletionUpdates
  ): Promise<void> {
    // Start performance transaction for profile update
    const transaction = startTransaction('profile.update_completion', 'db');
    transaction.setTag('operation', 'update_completion_status');

    // Add breadcrumb for profile update (privacy-safe)
    addBreadcrumb(
      'Profile completion status update started',
      'profile',
      {
        // Don't include userId for privacy
        hasOnboardingUpdate: updates.onboardingCompleted !== undefined,
        hasSelfReflectionUpdate: updates.selfReflectionCompleted !== undefined,
        updateCount: Object.keys(updates).length,
      }
    );

    try {
      console.log(`Updating completion status for user ${userId}:`, updates);

      // Get latest session if updating self-reflection completion
      let latestSession: SelfReflectionSession | null = null;
      if (updates.selfReflectionCompleted) {
        latestSession = await SelfReflectionService.getLatestSession(userId);
      }

      // Prepare update data with correct snake_case column names
      const updateData: any = {
        user_id: userId,
        updated_at: new Date().toISOString()
      };

      // Map camelCase to snake_case for database
      if (updates.onboardingCompleted !== undefined) {
        updateData.onboarding_completed = updates.onboardingCompleted;
      }
      if (updates.selfReflectionCompleted !== undefined) {
        updateData.self_reflection_completed = updates.selfReflectionCompleted;
      }
      if (updates.preferences !== undefined) {
        updateData.preferences = updates.preferences;
      }

      // Add latest session data if available
      if (latestSession) {
        updateData.latest_session_id = latestSession.id;
        updateData.latest_risk_level = latestSession.riskLevel;
        updateData.latest_completed_at = latestSession.completedAt.toISOString();
      }

      // Use explicit onConflict handling to properly resolve unique constraint violations
      const { error } = await supabase
        .from('user_profiles')
        .upsert(updateData, {
          onConflict: 'user_id',
          ignoreDuplicates: false // Update existing record instead of failing
        });

      if (error) {
        transaction.setStatus('internal_error');

        // Report error to Sentry with privacy-safe context
        reportError(error, {
          component: 'UserProfileService',
          action: 'update_completion_status',
          metadata: {
            updateCount: Object.keys(updates).length,
            hasOnboardingUpdate: updates.onboardingCompleted !== undefined,
            hasSelfReflectionUpdate: updates.selfReflectionCompleted !== undefined,
          }
        });

        throw new UserProfileError(
          `Failed to update completion status: ${error.message}`,
          'UPDATE_ERROR',
          { userId, updates, error }
        );
      }

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('fields_updated', Object.keys(updates).length, 'none');

      addBreadcrumb(
        'Profile completion status updated successfully',
        'profile',
        {
          updateCount: Object.keys(updates).length,
          hasOnboardingUpdate: updates.onboardingCompleted !== undefined,
          hasSelfReflectionUpdate: updates.selfReflectionCompleted !== undefined,
        }
      );

      // Clear cache to force refresh
      this.clearCache(userId);

    } catch (error) {
      if (!transaction.status) {
        transaction.setStatus('internal_error');
      }

      if (error instanceof UserProfileError) {
        throw error;
      }
      throw new UserProfileError(
        `Unexpected error updating completion status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_UPDATE_ERROR',
        { userId, updates, originalError: error }
      );
    } finally {
      transaction.finish();
    }
  }

  /**
   * Update user preferences
   */
  static async updatePreferences(
    userId: string,
    preferences: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          preferences,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id',
          ignoreDuplicates: false // Update existing record instead of failing
        });

      if (error) {
        throw new UserProfileError(
          `Failed to update preferences: ${error.message}`,
          'UPDATE_PREFERENCES_ERROR',
          { userId, preferences, error }
        );
      }

      // Clear cache
      this.clearCache(userId);

    } catch (error) {
      if (error instanceof UserProfileError) {
        throw error;
      }
      throw new UserProfileError(
        `Unexpected error updating preferences: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_PREFERENCES_ERROR',
        { userId, preferences, originalError: error }
      );
    }
  }

  /**
   * Update completion status for skip operations (graceful error handling)
   */
  static async updateCompletionStatusSafe(
    userId: string,
    updates: CompletionUpdates
  ): Promise<boolean> {
    try {
      await this.updateCompletionStatus(userId, updates);
      console.log(`✅ Completion status updated successfully for user ${userId}`);
      return true;
    } catch (error) {
      console.warn(`⚠️ Failed to update completion status for user ${userId}:`, error);

      // For skip operations, don't block the user - let them continue
      if (error instanceof UserProfileError && error.message.includes('schema cache')) {
        console.warn('Schema cache issue detected - user can still proceed');
      }

      return false; // Indicate failure but don't throw
    }
  }

  /**
   * Migrate completion status from local storage
   */
  static async migrateFromLocalStorage(
    userId: string,
    localOnboardingCompleted: boolean,
    localSelfReflectionCompleted: boolean
  ): Promise<void> {
    try {
      console.log(`Migrating local storage data for user ${userId}`);

      // Only migrate if there's data to migrate
      if (localOnboardingCompleted || localSelfReflectionCompleted) {
        await this.updateCompletionStatus(userId, {
          onboardingCompleted: localOnboardingCompleted,
          selfReflectionCompleted: localSelfReflectionCompleted
        });

        console.log(`Successfully migrated data for user ${userId}`);
      }

    } catch (error) {
      console.error(`Failed to migrate data for user ${userId}:`, error);
      // Don't throw - migration failure shouldn't break the app
    }
  }

  /**
   * Clear cache for a user or all users
   */
  static clearCache(userId?: string): void {
    if (userId) {
      this.cache.delete(userId);
      this.cacheExpiry.delete(userId);
    } else {
      this.cache.clear();
      this.cacheExpiry.clear();
    }
  }

  /**
   * Check if cache is valid for a user
   */
  private static isCacheValid(userId: string): boolean {
    const expiry = this.cacheExpiry.get(userId);
    return expiry ? Date.now() < expiry : false;
  }

  /**
   * Transform database profile data to TypeScript interface
   */
  private static transformProfileData(data: any): UserProfile {
    return {
      id: data.id,
      userId: data.user_id,
      onboardingCompleted: data.onboarding_completed || false,
      selfReflectionCompleted: data.self_reflection_completed || false,
      latestSessionId: data.latest_session_id,
      latestRiskLevel: data.latest_risk_level,
      latestCompletedAt: data.latest_completed_at ? new Date(data.latest_completed_at) : undefined,
      preferences: data.preferences || {},
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }
}
