/**
 * Centralized Sentry Configuration
 * Privacy-first monitoring for mental health app
 */

import * as Sentry from '@sentry/react-native';
import { Platform } from 'react-native';
import { APP_CONFIG } from '@/lib/env';

// Environment detection
const isDevelopment = __DEV__;

// Privacy-sensitive patterns to scrub from error messages
const SENSITIVE_PATTERNS = [
  /password/i,
  /token/i,
  /secret/i,
  /key/i,
  /journal/i,
  /mood/i,
  /feeling/i,
  /emotion/i,
  /therapy/i,
  /mental/i,
  /depression/i,
  /anxiety/i,
  /stress/i,
  /transcription/i,
  /voice.*content/i,
  /user.*message/i,
  /chat.*content/i,
];

// Sensitive data scrubbing function
const scrubSensitiveData = (data: any): any => {
  if (typeof data === 'string') {
    // Check if string contains sensitive patterns
    const hasSensitiveContent = SENSITIVE_PATTERNS.some(pattern => pattern.test(data));
    return hasSensitiveContent ? '[REDACTED]' : data;
  }
  
  if (Array.isArray(data)) {
    return data.map(scrubSensitiveData);
  }
  
  if (data && typeof data === 'object') {
    const scrubbed: any = {};
    for (const [key, value] of Object.entries(data)) {
      // Scrub sensitive keys
      if (SENSITIVE_PATTERNS.some(pattern => pattern.test(key))) {
        scrubbed[key] = '[REDACTED]';
      } else {
        scrubbed[key] = scrubSensitiveData(value);
      }
    }
    return scrubbed;
  }
  
  return data;
};

// Custom beforeSend hook for privacy protection
const beforeSend = (event: any, hint?: any) => {
  // In development, allow more data for debugging
  if (isDevelopment) {
    console.log('[Sentry] Sending event:', event);
    return event;
  }

  // Production privacy filtering
  if (event.exception) {
    event.exception.values?.forEach((exception: any) => {
      if (exception.value) {
        exception.value = scrubSensitiveData(exception.value);
      }
      if (exception.stacktrace?.frames) {
        exception.stacktrace.frames.forEach((frame: any) => {
          if (frame.vars) {
            frame.vars = scrubSensitiveData(frame.vars);
          }
        });
      }
    });
  }

  // Scrub request data
  if (event.request) {
    event.request.data = scrubSensitiveData(event.request.data);
    event.request.query_string = scrubSensitiveData(event.request.query_string);
    
    // Remove sensitive headers
    if (event.request.headers) {
      const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
      sensitiveHeaders.forEach(header => {
        if (event.request?.headers?.[header]) {
          event.request.headers[header] = '[REDACTED]';
        }
      });
    }
  }

  // Scrub extra data
  if (event.extra) {
    event.extra = scrubSensitiveData(event.extra);
  }

  // Scrub breadcrumbs
  if (event.breadcrumbs) {
    event.breadcrumbs.forEach((breadcrumb: any) => {
      if (breadcrumb.data) {
        breadcrumb.data = scrubSensitiveData(breadcrumb.data);
      }
      if (breadcrumb.message) {
        breadcrumb.message = scrubSensitiveData(breadcrumb.message);
      }
    });
  }

  return event;
};

// Sentry configuration
export const sentryConfig: Sentry.ReactNativeOptions = {
  dsn: 'https://<EMAIL>/4509690572505088',
  
  // Environment and release configuration
  environment: APP_CONFIG.environment,
  release: `temani@${APP_CONFIG.version}`,
  
  // Privacy and data protection
  sendDefaultPii: false, // Disable for mental health app
  beforeSend,
  
  // Performance monitoring
  tracesSampleRate: isDevelopment ? 1.0 : 0.1, // 100% in dev, 10% in prod
  enableAutoSessionTracking: true,
  sessionTrackingIntervalMillis: 30000, // 30 seconds
  
  // Session Replay configuration
  replaysSessionSampleRate: isDevelopment ? 1.0 : 0.05, // 100% in dev, 5% in prod
  replaysOnErrorSampleRate: 1.0, // Always capture on errors
  
  // Integrations
  integrations: [
    Sentry.mobileReplayIntegration({
      // Privacy settings for session replay
      maskAllText: true, // Mask all text for privacy
      maskAllImages: true, // Mask all images
      maskAllVectors: true, // Mask all vector graphics
    }),
    Sentry.feedbackIntegration({
      // Feedback widget configuration
      colorScheme: 'light',
      showBranding: false,
    }),
    // React Native specific integrations
    Sentry.reactNativeTracingIntegration(),
  ],
  
  // Debug settings
  debug: isDevelopment,
  
  // Error filtering
  ignoreErrors: [
    // Network errors that are expected
    'Network request failed',
    'Network Error',
    'fetch',
    // React Native specific errors to ignore
    'Non-Error promise rejection captured',
    'ResizeObserver loop limit exceeded',
    // Voice/audio related errors that are recoverable
    'getUserMedia',
    'NotAllowedError',
    'NotFoundError',
  ],
  
  // Breadcrumb filtering
  beforeBreadcrumb: (breadcrumb) => {
    // Filter out sensitive breadcrumbs
    if (breadcrumb.category === 'console' && breadcrumb.data?.logger === 'console') {
      const message = breadcrumb.message || '';
      const hasSensitiveContent = SENSITIVE_PATTERNS.some(pattern => pattern.test(message));
      if (hasSensitiveContent) {
        return null; // Don't record sensitive console logs
      }
    }
    
    // Filter navigation breadcrumbs for sensitive routes
    if (breadcrumb.category === 'navigation') {
      const sensitiveRoutes = ['/journal/', '/mood/', '/chat/', '/voice/'];
      const to = breadcrumb.data?.to || '';
      if (sensitiveRoutes.some(route => to.includes(route))) {
        breadcrumb.data = { ...breadcrumb.data, to: '[SENSITIVE_ROUTE]' };
      }
    }
    
    return breadcrumb;
  },
};

// Initialize Sentry with configuration
export const initializeSentry = () => {
  Sentry.init(sentryConfig);
  
  // Set initial context
  Sentry.setContext('app', {
    platform: Platform.OS,
    version: APP_CONFIG.version,
    environment: APP_CONFIG.environment,
  });
  
  console.log(`[Sentry] Initialized for ${sentryConfig.environment} environment`);
};

// Utility functions for custom error reporting
export const reportError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope(scope => {
    if (context) {
      // Scrub context data before sending
      const scrubbedContext = scrubSensitiveData(context);
      scope.setContext('custom', scrubbedContext);
    }
    Sentry.captureException(error);
  });
};

export const reportMessage = (message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) => {
  Sentry.withScope(scope => {
    scope.setLevel(level);
    if (context) {
      const scrubbedContext = scrubSensitiveData(context);
      scope.setContext('custom', scrubbedContext);
    }
    Sentry.captureMessage(message);
  });
};

// User context management (privacy-safe)
export const setUserContext = (userId: string, additionalContext?: Record<string, any>) => {
  Sentry.setUser({
    id: userId, // Use anonymized/hashed user ID
    // Never include email, name, or other PII
  });
  
  if (additionalContext) {
    const scrubbedContext = scrubSensitiveData(additionalContext);
    Sentry.setContext('user_context', scrubbedContext);
  }
};

export const clearUserContext = () => {
  Sentry.setUser(null);
};

// Performance monitoring utilities
export const startTransaction = (name: string, op: string) => {
  try {
    // Use startSpanManual to get a span object that can be finished manually
    return Sentry.startSpanManual({ name, op }, (span) => {
      return span;
    });
  } catch (error) {
    console.warn('[Sentry] Failed to start transaction:', error);
    // Return a mock object with the expected methods to prevent crashes
    return {
      setStatus: (status: any) => {},
      finish: () => {},
      updateName: (name: string) => {},
      setData: (key: string, value: any) => {},
    };
  }
};

export const addBreadcrumb = (message: string, category: string, data?: Record<string, any>) => {
  Sentry.addBreadcrumb({
    message,
    category,
    data: data ? scrubSensitiveData(data) : undefined,
    level: 'info',
  });
};
