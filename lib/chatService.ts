import { supabase } from '@/lib/supabase';
import type { Thread, Message } from '@/types/chat';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { startTransaction, addBreadcrumb, reportError } from '@/lib/sentryConfig';

export class ChatService {
  private static instance: ChatService;
  private rateLimitMap = new Map<string, { count: number; resetTime: number }>();
  private readonly RATE_LIMIT = 50; // 50 requests per hour
  private readonly RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  private checkRateLimit(userId: string): boolean {
    const now = Date.now();
    const userLimit = this.rateLimitMap.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      this.rateLimitMap.set(userId, {
        count: 1,
        resetTime: now + this.RATE_LIMIT_WINDOW,
      });
      return true;
    }

    if (userLimit.count >= this.RATE_LIMIT) {
      return false;
    }

    userLimit.count++;
    return true;
  }

  async createThread(userId: string, title?: string): Promise<Thread> {
    const { data, error } = await supabase
      .from('threads')
      .insert({
        user_id: userId,
        title: title || 'Percakapan Baru',
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserThreads(userId: string): Promise<Thread[]> {
    const { data, error } = await supabase
      .from('threads')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async deleteThread(threadId: string): Promise<void> {
    const { error } = await supabase
      .from('threads')
      .delete()
      .eq('id', threadId);

    if (error) throw error;
  }

  async updateThreadTitle(threadId: string, newTitle: string): Promise<Thread> {
    const { data, error } = await supabase
      .from('threads')
      .update({ 
        title: newTitle,
        updated_at: new Date().toISOString()
      })
      .eq('id', threadId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getMessages(threadId: string, limit = 20, offset = 0): Promise<Message[]> {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('thread_id', threadId)
      .order('timestamp', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return (data || []).reverse();
  }

  async sendMessage(
    threadId: string,
    content: string,
    senderType: 'user' | 'ai' = 'user',
    transcription?: string
  ): Promise<Message> {
    // Start performance transaction for message sending
    const transaction = startTransaction('chat.send_message', 'db');
    transaction.setTag('operation', 'send_message');
    transaction.setTag('sender_type', senderType);
    transaction.setTag('has_transcription', !!transcription);

    // Add breadcrumb for message sending (privacy-safe)
    addBreadcrumb(
      'Chat message send started',
      'chat',
      {
        threadId,
        senderType,
        // Never include actual message content for privacy
        messageLength: content.length,
        hasTranscription: !!transcription,
        transcriptionLength: transcription?.length || 0,
      }
    );

    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          thread_id: threadId,
          sender_type: senderType,
          content,
          status: 'sent',
          transcription,
        })
        .select()
        .single();

      if (error) {
        transaction.setStatus('internal_error');

        // Report error to Sentry with privacy-safe context
        reportError(error, {
          component: 'ChatService',
          action: 'send_message',
          metadata: {
            threadId,
            senderType,
            messageLength: content.length,
            hasTranscription: !!transcription,
          }
        });

        throw error;
      }

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('message_length', content.length, 'character');
      if (transcription) {
        transaction.setMeasurement('transcription_length', transcription.length, 'character');
      }

      addBreadcrumb(
        'Chat message sent successfully',
        'chat',
        {
          messageId: data.id,
          threadId,
          senderType,
          messageLength: content.length,
        }
      );

      return data;
    } catch (error) {
      if (!transaction.status) {
        transaction.setStatus('internal_error');
      }
      throw error;
    } finally {
      transaction.finish();
    }
  }

  async sendVoiceMessage(
    threadId: string,
    transcription: string,
    senderType: 'user' | 'ai' = 'user',
    content?: string
  ): Promise<Message> {
    // For voice messages, use transcription as content if no content provided
    const messageContent = content || transcription;

    const { data, error } = await supabase
      .from('messages')
      .insert({
        thread_id: threadId,
        sender_type: senderType,
        content: messageContent,
        transcription,
        status: 'sent',
        metadata: { isVoiceMessage: true },
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateMessageStatus(
    messageId: string,
    status: 'sent' | 'delivered' | 'read'
  ): Promise<void> {
    const { error } = await supabase
      .from('messages')
      .update({ status })
      .eq('message_id', messageId);

    if (error) throw error;
  }

  async getAIResponse(message: string, userId: string, conversationHistory: Message[] = []): Promise<string> {
    // Start performance transaction for AI response generation
    const transaction = startTransaction('chat.ai_response', 'ai');
    transaction.setTag('operation', 'generate_ai_response');
    transaction.setTag('history_length', conversationHistory.length.toString());

    // Add breadcrumb for AI response generation (privacy-safe)
    addBreadcrumb(
      'AI response generation started',
      'chat',
      {
        // Never include actual message content for privacy
        messageLength: message.length,
        historyLength: conversationHistory.length,
        userId: 'anonymized', // Don't include actual user ID
      }
    );

    try {
      // Check rate limit
      if (!this.checkRateLimit(userId)) {
        transaction.setStatus('resource_exhausted');
        addBreadcrumb(
          'AI response rate limit exceeded',
          'chat',
          {
            userId: 'anonymized',
          }
        );
        throw new Error('Rate limit exceeded. Please try again later.');
      }

      // Prepare conversation history for Groq API
      // Transform from internal format to Groq's expected message format
      const history = conversationHistory.slice(-10).map(msg => ({
        role: msg.sender_type === 'user' ? 'user' : 'model',
        parts: [{ text: msg.content }],
      }));
      const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/groq-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          message,
          conversationHistory: history,
        }),
      });

      // Enhanced error handling based on HTTP status
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Handle specific HTTP status codes
        switch (response.status) {
          case 400:
            throw new Error('InvalidRequest: ' + (errorData.error || 'Invalid request format'));
          case 401:
            throw new Error('Authentication: API authentication failed');
          case 403:
            throw new Error('Permission: Access denied');
          case 408:
            throw new Error('Timeout: Request timed out');
          case 422:
            throw new Error('ContentPolicy: Content violates policy');
          case 429:
            throw new Error('RateLimit: Rate limit exceeded');
          case 503:
            throw new Error('ServiceUnavailable: Service temporarily unavailable');
          default:
            throw new Error(errorData.error || `HTTP ${response.status}`);
        }
      }

      const data = await response.json();

      if (!data.success) {
        transaction.setStatus('internal_error');
        throw new Error(data.error || 'AI response failed');
      }

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('response_length', data.response.length, 'character');
      transaction.setMeasurement('history_items_used', history.length, 'none');

      addBreadcrumb(
        'AI response generated successfully',
        'chat',
        {
          responseLength: data.response.length,
          historyItemsUsed: history.length,
        }
      );

      return data.response;
    } catch (error) {
      console.error('AI response error:', error);

      // Report error to Sentry with privacy-safe context
      if (error instanceof Error) {
        reportError(error, {
          component: 'ChatService',
          action: 'generate_ai_response',
          metadata: {
            messageLength: message.length,
            historyLength: conversationHistory.length,
            errorType: error.message.split(':')[0], // Get error type without sensitive details
          }
        });
      }

      transaction.setStatus('internal_error');

      // Enhanced fallback responses based on specific error types
      if (error instanceof Error) {
        const errorMessage = error.message;

        // Handle specific error types with appropriate user messages
        if (errorMessage.includes('Timeout:')) {
          return 'Maaf, respons membutuhkan waktu terlalu lama. Aku tetap di sini untuk mendengarkan kamu. Silakan coba lagi.';
        }

        if (errorMessage.includes('RateLimit:')) {
          return 'Kamu telah mencapai batas percakapan untuk saat ini. Silakan coba lagi nanti. Sementara itu, cobalah teknik pernapasan dalam untuk menenangkan diri.';
        }

        if (errorMessage.includes('ContentPolicy:')) {
          return 'Maaf, pesan kamu tidak bisa diproses. Bisa coba dengan topik yang berbeda? Aku di sini untuk membantu dengan cara yang aman.';
        }

        if (errorMessage.includes('ServiceUnavailable:')) {
          return 'Aku sedang mengalami gangguan teknis sementara. Coba lagi dalam beberapa saat ya. Aku tetap di sini untuk mendengarkan kamu.';
        }

        if (errorMessage.includes('Authentication:') || errorMessage.includes('Permission:')) {
          return 'Maaf, aku sedang mengalami masalah teknis. Tim kami akan segera memperbaikinya. Coba lagi nanti ya.';
        }

        if (errorMessage.includes('InvalidRequest:')) {
          return 'Maaf, ada masalah dengan format pesan. Bisa coba tulis ulang dengan cara yang berbeda?';
        }
      }

      // Generic fallback message
      return 'Maaf, aku sedang mengalami kesulitan teknis. Namun, aku di sini untuk mendengarkan kamu. Bisakah kamu mencoba lagi dalam beberapa saat?';
    } finally {
      transaction.finish();
    }
  }

  subscribeToMessages(
    threadId: string, 
    onNewMessage: (message: Message) => void,
    onMessageUpdate: (message: Message) => void
  ): RealtimeChannel {
    const channel = supabase
      .channel(`messages:${threadId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `thread_id=eq.${threadId}`,
        },
        (payload) => {
          onNewMessage(payload.new as Message);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `thread_id=eq.${threadId}`,
        },
        (payload) => {
          onMessageUpdate(payload.new as Message);
        }
      )
      .subscribe();

    return channel;
  }
}

export const chatService = ChatService.getInstance();