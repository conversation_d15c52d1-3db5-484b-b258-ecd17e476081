/**
 * Voice Call Performance Monitoring
 * Tracks voice call quality, connection issues, and user experience metrics
 */

import { startTransaction, addBreadcrumb, reportError, reportMessage } from '@/lib/sentryConfig';
import * as Sentry from '@sentry/react-native';

export interface VoiceCallMetrics {
  callId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  connectionTime?: number;
  quality?: 'excellent' | 'good' | 'fair' | 'poor';
  disconnectReason?: 'user' | 'error' | 'timeout' | 'network';
  errorCount: number;
  reconnectCount: number;
  audioIssues: string[];
  creditsUsed?: number;
}

export interface VoiceConnectionEvent {
  type: 'connecting' | 'connected' | 'disconnected' | 'error' | 'quality_change';
  timestamp: number;
  data?: any;
}

class VoiceMonitoringService {
  private activeCall: VoiceCallMetrics | null = null;
  private callTransaction: any | null = null;
  private connectionEvents: VoiceConnectionEvent[] = [];

  /**
   * Start monitoring a voice call
   */
  startCall(callId: string): void {
    const startTime = Date.now();
    
    this.activeCall = {
      callId,
      startTime,
      errorCount: 0,
      reconnectCount: 0,
      audioIssues: [],
    };

    this.connectionEvents = [];

    // Start Sentry transaction for call performance
    this.callTransaction = startTransaction('voice.call', 'voice');
    this.callTransaction.setTag('call_id', callId);
    this.callTransaction.setTag('call_type', 'ai_conversation');

    addBreadcrumb('Voice call started', 'voice', {
      callId,
      startTime: new Date(startTime).toISOString(),
    });

    console.log(`[VoiceMonitoring] Started monitoring call: ${callId}`);
  }

  /**
   * Record connection attempt
   */
  recordConnectionAttempt(): void {
    if (!this.activeCall) return;

    this.addConnectionEvent('connecting');
    addBreadcrumb('Voice connection attempt', 'voice', {
      callId: this.activeCall.callId,
    });
  }

  /**
   * Record successful connection
   */
  recordConnectionSuccess(connectionTime: number): void {
    if (!this.activeCall || !this.callTransaction) return;

    this.activeCall.connectionTime = connectionTime;
    this.addConnectionEvent('connected');

    // Set connection time as a metric
    this.callTransaction.setMeasurement('connection_time', connectionTime, 'millisecond');

    addBreadcrumb('Voice connection established', 'voice', {
      callId: this.activeCall.callId,
      connectionTime,
    });

    console.log(`[VoiceMonitoring] Connection established in ${connectionTime}ms`);
  }

  /**
   * Record connection error
   */
  recordConnectionError(error: Error): void {
    if (!this.activeCall) return;

    this.activeCall.errorCount++;
    this.addConnectionEvent('error', { error: error.message });

    addBreadcrumb('Voice connection error', 'voice', {
      callId: this.activeCall.callId,
      error: error.message,
      errorCount: this.activeCall.errorCount,
    });

    // Report error to Sentry with voice context
    reportError(error, {
      component: 'VoiceMonitoring',
      action: 'voice_connection',
      metadata: {
        callId: this.activeCall.callId,
        errorCount: this.activeCall.errorCount,
        connectionEvents: this.connectionEvents.slice(-5), // Last 5 events
      },
    });

    console.error(`[VoiceMonitoring] Connection error:`, error);
  }

  /**
   * Record reconnection attempt
   */
  recordReconnection(): void {
    if (!this.activeCall) return;

    this.activeCall.reconnectCount++;
    this.addConnectionEvent('connecting');

    addBreadcrumb('Voice reconnection attempt', 'voice', {
      callId: this.activeCall.callId,
      reconnectCount: this.activeCall.reconnectCount,
    });

    console.log(`[VoiceMonitoring] Reconnection attempt #${this.activeCall.reconnectCount}`);
  }

  /**
   * Record audio quality issue
   */
  recordAudioIssue(issue: string): void {
    if (!this.activeCall) return;

    this.activeCall.audioIssues.push(issue);
    
    addBreadcrumb('Voice audio issue', 'voice', {
      callId: this.activeCall.callId,
      issue,
      totalIssues: this.activeCall.audioIssues.length,
    });

    // Report significant audio issues
    if (this.activeCall.audioIssues.length >= 3) {
      reportMessage(
        'Multiple audio issues detected in voice call',
        'warning',
        {
          component: 'VoiceMonitoring',
          action: 'audio_quality',
          metadata: {
            callId: this.activeCall.callId,
            issues: this.activeCall.audioIssues,
          },
        }
      );
    }

    console.warn(`[VoiceMonitoring] Audio issue: ${issue}`);
  }

  /**
   * Update call quality assessment
   */
  updateCallQuality(quality: VoiceCallMetrics['quality']): void {
    if (!this.activeCall || !this.callTransaction) return;

    this.activeCall.quality = quality;
    this.callTransaction.setTag('call_quality', quality);
    this.addConnectionEvent('quality_change', { quality });

    addBreadcrumb('Voice call quality updated', 'voice', {
      callId: this.activeCall.callId,
      quality,
    });
  }

  /**
   * Record voice credits usage
   */
  recordCreditsUsed(credits: number): void {
    if (!this.activeCall || !this.callTransaction) return;

    this.activeCall.creditsUsed = credits;
    this.callTransaction.setMeasurement('credits_used', credits, 'none');

    addBreadcrumb('Voice credits used', 'voice', {
      callId: this.activeCall.callId,
      creditsUsed: credits,
    });
  }

  /**
   * End call monitoring
   */
  endCall(disconnectReason: VoiceCallMetrics['disconnectReason'] = 'user'): void {
    if (!this.activeCall || !this.callTransaction) return;

    const endTime = Date.now();
    const duration = endTime - this.activeCall.startTime;

    this.activeCall.endTime = endTime;
    this.activeCall.duration = duration;
    this.activeCall.disconnectReason = disconnectReason;

    this.addConnectionEvent('disconnected', { reason: disconnectReason });

    // Set final transaction data
    this.callTransaction.setMeasurement('call_duration', duration, 'millisecond');
    this.callTransaction.setTag('disconnect_reason', disconnectReason);
    this.callTransaction.setTag('error_count', this.activeCall.errorCount.toString());
    this.callTransaction.setTag('reconnect_count', this.activeCall.reconnectCount.toString());

    // Determine transaction status based on call quality
    if (disconnectReason === 'error') {
      this.callTransaction.setStatus('internal_error');
    } else if (this.activeCall.errorCount > 2) {
      this.callTransaction.setStatus('unknown_error');
    } else {
      this.callTransaction.setStatus('ok');
    }

    addBreadcrumb('Voice call ended', 'voice', {
      callId: this.activeCall.callId,
      duration,
      disconnectReason,
      quality: this.activeCall.quality,
      errorCount: this.activeCall.errorCount,
      reconnectCount: this.activeCall.reconnectCount,
      creditsUsed: this.activeCall.creditsUsed,
    });

    // Log call summary
    console.log(`[VoiceMonitoring] Call ended:`, {
      callId: this.activeCall.callId,
      duration: `${(duration / 1000).toFixed(1)}s`,
      quality: this.activeCall.quality,
      errors: this.activeCall.errorCount,
      reconnects: this.activeCall.reconnectCount,
      reason: disconnectReason,
    });

    // Finish transaction
    this.callTransaction.finish();

    // Reset state
    this.activeCall = null;
    this.callTransaction = null;
    this.connectionEvents = [];
  }

  /**
   * Get current call metrics
   */
  getCurrentCallMetrics(): VoiceCallMetrics | null {
    return this.activeCall;
  }

  /**
   * Add connection event to timeline
   */
  private addConnectionEvent(type: VoiceConnectionEvent['type'], data?: any): void {
    this.connectionEvents.push({
      type,
      timestamp: Date.now(),
      data,
    });

    // Keep only last 20 events to prevent memory issues
    if (this.connectionEvents.length > 20) {
      this.connectionEvents = this.connectionEvents.slice(-20);
    }
  }
}

// Export singleton instance
export const voiceMonitoring = new VoiceMonitoringService();
