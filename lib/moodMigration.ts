/**
 * Mood Data Migration and Progressive Enhancement
 * Handles graceful transition from mock data to Supabase storage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import { moodTransformUtils, moodValidationUtils } from './moodUtils';
import type { DailyMoodData, WeeklyMoodData, MoodEntry } from '@/types/mood';

const STORAGE_KEYS = {
  MOOD_DATA: '@temani_mood_data',
  MIGRATION_STATUS: '@temani_mood_migration_status',
  LAST_SYNC: '@temani_mood_last_sync',
} as const;

interface LocalMoodData {
  [date: string]: DailyMoodData;
}

interface MigrationStatus {
  isCompleted: boolean;
  lastMigrationDate: string;
  migratedEntries: number;
  failedEntries: number;
}

/**
 * Mood Migration Service
 * Handles data migration from local storage to Supabase
 */
export class MoodMigrationService {
  private static instance: MoodMigrationService;

  static getInstance(): MoodMigrationService {
    if (!MoodMigrationService.instance) {
      MoodMigrationService.instance = new MoodMigrationService();
    }
    return MoodMigrationService.instance;
  }

  /**
   * Check if migration is needed
   */
  async needsMigration(userId: string): Promise<boolean> {
    try {
      const migrationStatus = await this.getMigrationStatus();
      if (migrationStatus.isCompleted) {
        return false;
      }

      const localData = await this.getLocalMoodData();
      return Object.keys(localData).length > 0;
    } catch (error) {
      console.error('[MoodMigration] Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<MigrationStatus> {
    try {
      const statusJson = await AsyncStorage.getItem(STORAGE_KEYS.MIGRATION_STATUS);
      if (statusJson) {
        return JSON.parse(statusJson);
      }
    } catch (error) {
      console.error('[MoodMigration] Error getting migration status:', error);
    }

    return {
      isCompleted: false,
      lastMigrationDate: '',
      migratedEntries: 0,
      failedEntries: 0,
    };
  }

  /**
   * Update migration status
   */
  async updateMigrationStatus(status: Partial<MigrationStatus>): Promise<void> {
    try {
      const currentStatus = await this.getMigrationStatus();
      const updatedStatus = { ...currentStatus, ...status };
      await AsyncStorage.setItem(
        STORAGE_KEYS.MIGRATION_STATUS,
        JSON.stringify(updatedStatus)
      );
    } catch (error) {
      console.error('[MoodMigration] Error updating migration status:', error);
    }
  }

  /**
   * Get local mood data from AsyncStorage
   */
  async getLocalMoodData(): Promise<LocalMoodData> {
    try {
      const dataJson = await AsyncStorage.getItem(STORAGE_KEYS.MOOD_DATA);
      if (dataJson) {
        const data = JSON.parse(dataJson);
        // Validate and clean the data
        const cleanedData: LocalMoodData = {};
        
        Object.entries(data).forEach(([date, moodData]) => {
          if (moodValidationUtils.validateDailyMoodData(moodData)) {
            cleanedData[date] = moodTransformUtils.cleanMoodData(moodData as DailyMoodData);
          }
        });

        return cleanedData;
      }
    } catch (error) {
      console.error('[MoodMigration] Error getting local mood data:', error);
    }

    return {};
  }

  /**
   * Save mood data to local storage (fallback)
   */
  async saveLocalMoodData(date: string, data: DailyMoodData): Promise<void> {
    try {
      const existingData = await this.getLocalMoodData();
      const updatedData = {
        ...existingData,
        [date]: moodTransformUtils.cleanMoodData(data),
      };

      await AsyncStorage.setItem(
        STORAGE_KEYS.MOOD_DATA,
        JSON.stringify(updatedData)
      );

      // Update last sync timestamp
      await AsyncStorage.setItem(
        STORAGE_KEYS.LAST_SYNC,
        new Date().toISOString()
      );
    } catch (error) {
      console.error('[MoodMigration] Error saving local mood data:', error);
      throw error;
    }
  }

  /**
   * Save mood entry directly to Supabase (to avoid circular dependency)
   */
  private async saveMoodEntryToSupabase(userId: string, date: string, data: DailyMoodData): Promise<void> {
    const entryData = {
      user_id: userId,
      entry_date: date,
      day_of_week: this.getDayOfWeek(date),
      mood_level: data.mood,
      physical_health: data.physicalHealth,
      sleep_hours: data.sleepHours,
      sleep_quality: data.sleepQuality,
      stress_level: data.stressLevel,
      daily_feeling: data.dailyFeeling,
    };

    const { error } = await supabase
      .from('mood_entries')
      .upsert(entryData, {
        onConflict: 'user_id,entry_date',
        ignoreDuplicates: false,
      });

    if (error) {
      throw error;
    }
  }

  /**
   * Get day of week from date string
   */
  private getDayOfWeek(dateString: string): string {
    const date = new Date(dateString);
    const dayIndex = date.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Map to Indonesian day abbreviations
    const dayMap = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];
    return dayMap[dayIndex];
  }

  /**
   * Migrate local data to Supabase
   */
  async migrateToSupabase(userId: string): Promise<MigrationStatus> {
    console.log('[MoodMigration] Starting migration for user:', userId);

    const migrationStatus: MigrationStatus = {
      isCompleted: false,
      lastMigrationDate: new Date().toISOString(),
      migratedEntries: 0,
      failedEntries: 0,
    };

    try {
      const localData = await this.getLocalMoodData();
      const entries = Object.entries(localData);

      if (entries.length === 0) {
        console.log('[MoodMigration] No local data to migrate');
        migrationStatus.isCompleted = true;
        await this.updateMigrationStatus(migrationStatus);
        return migrationStatus;
      }

      console.log(`[MoodMigration] Migrating ${entries.length} entries`);

      // Migrate each entry
      for (const [date, moodData] of entries) {
        try {
          await this.saveMoodEntryToSupabase(userId, date, moodData);
          migrationStatus.migratedEntries++;
          console.log(`[MoodMigration] Migrated entry for ${date}`);
        } catch (error) {
          console.error(`[MoodMigration] Failed to migrate entry for ${date}:`, error);
          migrationStatus.failedEntries++;
        }
      }

      // Mark migration as completed if all entries were migrated successfully
      migrationStatus.isCompleted = migrationStatus.failedEntries === 0;

      await this.updateMigrationStatus(migrationStatus);

      // Clear local data if migration was successful
      if (migrationStatus.isCompleted) {
        await this.clearLocalData();
        console.log('[MoodMigration] Migration completed successfully');
      } else {
        console.log(`[MoodMigration] Migration completed with ${migrationStatus.failedEntries} failures`);
      }

      return migrationStatus;
    } catch (error) {
      console.error('[MoodMigration] Migration failed:', error);
      migrationStatus.failedEntries = Object.keys(await this.getLocalMoodData()).length;
      await this.updateMigrationStatus(migrationStatus);
      throw error;
    }
  }

  /**
   * Clear local mood data
   */
  async clearLocalData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.MOOD_DATA,
        STORAGE_KEYS.LAST_SYNC,
      ]);
      console.log('[MoodMigration] Local data cleared');
    } catch (error) {
      console.error('[MoodMigration] Error clearing local data:', error);
    }
  }

  /**
   * Sync data with Supabase (for offline support)
   */
  async syncWithSupabase(userId: string): Promise<void> {
    try {
      const localData = await this.getLocalMoodData();
      const entries = Object.entries(localData);

      if (entries.length === 0) {
        return;
      }

      console.log(`[MoodMigration] Syncing ${entries.length} local entries`);

      for (const [date, moodData] of entries) {
        try {
          await this.saveMoodEntryToSupabase(userId, date, moodData);
          console.log(`[MoodMigration] Synced entry for ${date}`);
        } catch (error) {
          console.error(`[MoodMigration] Failed to sync entry for ${date}:`, error);
        }
      }

      // Clear local data after successful sync
      await this.clearLocalData();
    } catch (error) {
      console.error('[MoodMigration] Sync failed:', error);
    }
  }

  /**
   * Get fallback data (for offline mode)
   */
  async getFallbackData(date: string): Promise<DailyMoodData | null> {
    try {
      const localData = await this.getLocalMoodData();
      return localData[date] || null;
    } catch (error) {
      console.error('[MoodMigration] Error getting fallback data:', error);
      return null;
    }
  }

  /**
   * Check if app is in offline mode
   */
  async isOfflineMode(): Promise<boolean> {
    try {
      // Simple network check - in a real app you might use NetInfo
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        timeout: 5000,
      });
      return !response.ok;
    } catch {
      return true;
    }
  }

  /**
   * Handle offline mood entry
   */
  async handleOfflineEntry(date: string, data: DailyMoodData): Promise<void> {
    console.log('[MoodMigration] Saving mood entry offline for date:', date);
    await this.saveLocalMoodData(date, data);
  }

  /**
   * Get last sync timestamp
   */
  async getLastSyncTime(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
    } catch (error) {
      console.error('[MoodMigration] Error getting last sync time:', error);
      return null;
    }
  }

  /**
   * Check if data needs sync
   */
  async needsSync(): Promise<boolean> {
    try {
      const localData = await this.getLocalMoodData();
      return Object.keys(localData).length > 0;
    } catch (error) {
      console.error('[MoodMigration] Error checking sync status:', error);
      return false;
    }
  }
}

// Export singleton instance
export const moodMigration = MoodMigrationService.getInstance();
