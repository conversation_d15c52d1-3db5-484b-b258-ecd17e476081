/**
 * Mood Utilities for Data Migration and Enhancement
 * Provides helper functions for mood data processing, validation, and migration
 */

import type {
  DailyMoodData,
  WeeklyMoodData,
  MoodEntry,
  DayOfWeek,
  MoodLevel,
  StressLevel,
  SleepQualityLevel,
  DailyFeelingLevel,
  PhysicalHealthStatus,
  SleepHours,
  DEFAULT_WEEKLY_MOOD_DATA,
  DAYS_OF_WEEK,
  MOOD_CONSTANTS,
} from '@/types/mood';

/**
 * Date utilities for mood tracking
 */
export const moodDateUtils = {
  /**
   * Format date to Indonesian format
   */
  formatDateIndonesian: (date: string): string => {
    const dateObj = new Date(date);
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return dateObj.toLocaleDateString('id-ID', options);
  },

  /**
   * Get week start date (Monday) from any date
   */
  getWeekStartDate: (date: string): string => {
    const dateObj = new Date(date);
    const dayOfWeek = dateObj.getDay();
    const diff = dateObj.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
    const monday = new Date(dateObj.setDate(diff));
    return monday.toISOString().split('T')[0];
  },

  /**
   * Get all dates in a week starting from Monday
   */
  getWeekDates: (weekStart: string): string[] => {
    const dates: string[] = [];
    const startDate = new Date(weekStart);
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      dates.push(date.toISOString().split('T')[0]);
    }
    
    return dates;
  },

  /**
   * Check if a date is today
   */
  isToday: (date: string): boolean => {
    const today = new Date().toISOString().split('T')[0];
    return date === today;
  },

  /**
   * Get day of week from date string
   */
  getDayOfWeek: (dateString: string): DayOfWeek => {
    const date = new Date(dateString);
    const dayIndex = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Map to Indonesian day abbreviations
    const dayMap: DayOfWeek[] = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];
    return dayMap[dayIndex];
  },

  /**
   * Get date range for a specific period
   */
  getDateRange: (period: 'week' | 'month', count: number): { startDate: string; endDate: string } => {
    const endDate = new Date();
    const startDate = new Date();
    
    if (period === 'week') {
      startDate.setDate(endDate.getDate() - (count * 7));
    } else {
      startDate.setMonth(endDate.getMonth() - count);
    }

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    };
  },
};

/**
 * Data validation utilities
 */
export const moodValidationUtils = {
  /**
   * Validate mood level
   */
  isValidMoodLevel: (value: any): value is MoodLevel => {
    return typeof value === 'number' && 
           value >= MOOD_CONSTANTS.MIN_MOOD_LEVEL && 
           value <= MOOD_CONSTANTS.MAX_MOOD_LEVEL;
  },

  /**
   * Validate stress level
   */
  isValidStressLevel: (value: any): value is StressLevel => {
    return typeof value === 'number' && 
           value >= MOOD_CONSTANTS.MIN_STRESS_LEVEL && 
           value <= MOOD_CONSTANTS.MAX_STRESS_LEVEL;
  },

  /**
   * Validate sleep hours
   */
  isValidSleepHours: (value: any): value is SleepHours => {
    return typeof value === 'number' && 
           value >= MOOD_CONSTANTS.MIN_SLEEP_HOURS && 
           value <= MOOD_CONSTANTS.MAX_SLEEP_HOURS;
  },

  /**
   * Validate sleep quality
   */
  isValidSleepQuality: (value: any): value is SleepQualityLevel => {
    return typeof value === 'number' && 
           value >= MOOD_CONSTANTS.MIN_SLEEP_QUALITY && 
           value <= MOOD_CONSTANTS.MAX_SLEEP_QUALITY;
  },

  /**
   * Validate daily feeling
   */
  isValidDailyFeeling: (value: any): value is DailyFeelingLevel => {
    return typeof value === 'number' && 
           value >= MOOD_CONSTANTS.MIN_DAILY_FEELING && 
           value <= MOOD_CONSTANTS.MAX_DAILY_FEELING;
  },

  /**
   * Validate physical health status
   */
  isValidPhysicalHealth: (value: any): value is PhysicalHealthStatus => {
    return typeof value === 'boolean';
  },

  /**
   * Validate complete daily mood data
   */
  validateDailyMoodData: (data: any): data is DailyMoodData => {
    if (!data || typeof data !== 'object') return false;

    const {
      mood,
      physicalHealth,
      sleepHours,
      sleepQuality,
      stressLevel,
      dailyFeeling,
    } = data;

    // All fields can be null, but if they exist, they must be valid
    return (
      (mood === null || moodValidationUtils.isValidMoodLevel(mood)) &&
      (physicalHealth === null || moodValidationUtils.isValidPhysicalHealth(physicalHealth)) &&
      (sleepHours === null || moodValidationUtils.isValidSleepHours(sleepHours)) &&
      (sleepQuality === null || moodValidationUtils.isValidSleepQuality(sleepQuality)) &&
      (stressLevel === null || moodValidationUtils.isValidStressLevel(stressLevel)) &&
      (dailyFeeling === null || moodValidationUtils.isValidDailyFeeling(dailyFeeling))
    );
  },
};

/**
 * Data transformation utilities
 */
export const moodTransformUtils = {
  /**
   * Clean and normalize mood data
   */
  cleanMoodData: (data: Partial<DailyMoodData>): DailyMoodData => {
    return {
      mood: moodValidationUtils.isValidMoodLevel(data.mood) ? data.mood : null,
      physicalHealth: moodValidationUtils.isValidPhysicalHealth(data.physicalHealth) ? data.physicalHealth : null,
      sleepHours: moodValidationUtils.isValidSleepHours(data.sleepHours) ? data.sleepHours : null,
      sleepQuality: moodValidationUtils.isValidSleepQuality(data.sleepQuality) ? data.sleepQuality : null,
      stressLevel: moodValidationUtils.isValidStressLevel(data.stressLevel) ? data.stressLevel : null,
      dailyFeeling: moodValidationUtils.isValidDailyFeeling(data.dailyFeeling) ? data.dailyFeeling : null,
    };
  },

  /**
   * Merge mood data with defaults
   */
  mergeWithDefaults: (data: Partial<DailyMoodData>): DailyMoodData => {
    const defaultData: DailyMoodData = {
      mood: null,
      physicalHealth: null,
      sleepHours: null,
      sleepQuality: null,
      stressLevel: null,
      dailyFeeling: null,
    };

    return {
      ...defaultData,
      ...moodTransformUtils.cleanMoodData(data),
    };
  },

  /**
   * Convert weekly data to daily entries array
   */
  weeklyToDaily: (weeklyData: WeeklyMoodData, weekStart: string): Array<{ date: string; day: DayOfWeek; data: DailyMoodData }> => {
    const weekDates = moodDateUtils.getWeekDates(weekStart);
    
    return DAYS_OF_WEEK.map((day, index) => ({
      date: weekDates[index],
      day,
      data: weeklyData[day] || DEFAULT_WEEKLY_MOOD_DATA[day],
    }));
  },

  /**
   * Convert daily entries array to weekly data
   */
  dailyToWeekly: (entries: MoodEntry[]): WeeklyMoodData => {
    const weeklyData: WeeklyMoodData = { ...DEFAULT_WEEKLY_MOOD_DATA };

    entries.forEach(entry => {
      const day = entry.day_of_week as DayOfWeek;
      weeklyData[day] = {
        mood: entry.mood_level,
        physicalHealth: entry.physical_health,
        sleepHours: entry.sleep_hours,
        sleepQuality: entry.sleep_quality,
        stressLevel: entry.stress_level,
        dailyFeeling: entry.daily_feeling,
      };
    });

    return weeklyData;
  },
};

/**
 * Analytics utilities
 */
export const moodAnalyticsUtils = {
  /**
   * Calculate average mood from entries
   */
  calculateAverageMood: (entries: MoodEntry[]): number => {
    const validEntries = entries.filter(e => e.mood_level !== null);
    if (validEntries.length === 0) return 0;
    
    const sum = validEntries.reduce((acc, e) => acc + (e.mood_level || 0), 0);
    return sum / validEntries.length;
  },

  /**
   * Calculate average sleep hours
   */
  calculateAverageSleep: (entries: MoodEntry[]): number => {
    const validEntries = entries.filter(e => e.sleep_hours !== null);
    if (validEntries.length === 0) return 0;
    
    const sum = validEntries.reduce((acc, e) => acc + (e.sleep_hours || 0), 0);
    return sum / validEntries.length;
  },

  /**
   * Calculate average stress level
   */
  calculateAverageStress: (entries: MoodEntry[]): number => {
    const validEntries = entries.filter(e => e.stress_level !== null);
    if (validEntries.length === 0) return 0;
    
    const sum = validEntries.reduce((acc, e) => acc + (e.stress_level || 0), 0);
    return sum / validEntries.length;
  },

  /**
   * Count healthy days
   */
  countHealthyDays: (entries: MoodEntry[]): number => {
    return entries.filter(e => e.physical_health === true).length;
  },

  /**
   * Detect mood trend
   */
  detectMoodTrend: (entries: MoodEntry[]): 'improving' | 'declining' | 'stable' => {
    if (entries.length < 2) return 'stable';

    const validEntries = entries.filter(e => e.mood_level !== null);
    if (validEntries.length < 2) return 'stable';

    const firstHalf = validEntries.slice(0, Math.floor(validEntries.length / 2));
    const secondHalf = validEntries.slice(Math.floor(validEntries.length / 2));

    const firstAvg = moodAnalyticsUtils.calculateAverageMood(firstHalf);
    const secondAvg = moodAnalyticsUtils.calculateAverageMood(secondHalf);

    const difference = secondAvg - firstAvg;

    if (difference > 0.3) return 'improving';
    if (difference < -0.3) return 'declining';
    return 'stable';
  },

  /**
   * Generate mood insights
   */
  generateInsights: (entries: MoodEntry[]): string[] => {
    const insights: string[] = [];

    if (entries.length === 0) {
      insights.push('Mulai isi mood tracker untuk mendapatkan insights!');
      return insights;
    }

    const avgMood = moodAnalyticsUtils.calculateAverageMood(entries);
    const avgSleep = moodAnalyticsUtils.calculateAverageSleep(entries);
    const avgStress = moodAnalyticsUtils.calculateAverageStress(entries);
    const healthyDays = moodAnalyticsUtils.countHealthyDays(entries);
    const moodTrend = moodAnalyticsUtils.detectMoodTrend(entries);

    // Mood insights
    if (avgMood >= 4) {
      insights.push('🌟 Mood Anda sangat baik! Pertahankan!');
    } else if (avgMood <= 2) {
      insights.push('💙 Mood Anda perlu perhatian. Coba aktivitas yang menyenangkan.');
    }

    // Sleep insights
    if (avgSleep >= 7 && avgSleep <= 9) {
      insights.push('😴 Pola tidur Anda sangat baik!');
    } else if (avgSleep < 6) {
      insights.push('⏰ Coba tidur lebih awal untuk kesehatan yang lebih baik.');
    }

    // Stress insights
    if (avgStress <= 2) {
      insights.push('🧘 Level stress Anda terkendali dengan baik!');
    } else if (avgStress >= 4) {
      insights.push('🌱 Coba teknik relaksasi untuk mengurangi stress.');
    }

    // Health insights
    const healthyRatio = healthyDays / entries.length;
    if (healthyRatio >= 0.8) {
      insights.push('💪 Kesehatan fisik Anda sangat baik!');
    }

    // Trend insights
    if (moodTrend === 'improving') {
      insights.push('📈 Mood Anda menunjukkan tren positif!');
    } else if (moodTrend === 'declining') {
      insights.push('📉 Perhatikan pola mood Anda dan jaga kesehatan mental.');
    }

    return insights;
  },
};
