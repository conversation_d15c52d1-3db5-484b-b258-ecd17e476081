/**
 * Timezone-aware date utilities for mood tracking
 * Handles date calculations in user's local timezone instead of UTC
 */

import type { DayOfWeek } from '@/types/mood';

// Default timezone for Indonesian users
export const DEFAULT_TIMEZONE = 'Asia/Jakarta';

// Indonesian day abbreviations mapping
const DAY_MAP: DayOfWeek[] = ['Min', '<PERSON>', 'Se<PERSON>', 'Ra<PERSON>', 'Ka<PERSON>', '<PERSON><PERSON>', 'Sab'];

/**
 * Get user's timezone from preferences or device default
 */
export function getUserTimezone(userTimezone?: string): string {
  if (userTimezone) {
    return userTimezone;
  }
  
  // Try to get device timezone
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone || DEFAULT_TIMEZONE;
  } catch (error) {
    console.warn('[TimezoneUtils] Failed to get device timezone, using default:', error);
    return DEFAULT_TIMEZONE;
  }
}

/**
 * Get current date in user's timezone (YYYY-MM-DD format)
 */
export function getTodayInTimezone(timezone?: string): string {
  const userTimezone = getUserTimezone(timezone);
  
  try {
    const now = new Date();
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: userTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    
    return formatter.format(now); // Returns YYYY-MM-DD format
  } catch (error) {
    console.error('[TimezoneUtils] Error getting today in timezone:', error);
    // Fallback to UTC
    return new Date().toISOString().split('T')[0];
  }
}

/**
 * Get day of week for a date in user's timezone
 */
export function getDayOfWeekInTimezone(dateString: string, timezone?: string): DayOfWeek {
  const userTimezone = getUserTimezone(timezone);

  try {
    const date = new Date(dateString + 'T12:00:00'); // Add noon time to avoid timezone edge cases

    // Use getDay() method with timezone-aware date
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: userTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });

    const formattedDate = formatter.format(date);
    const timezoneAwareDate = new Date(formattedDate + 'T12:00:00');
    const dayIndex = timezoneAwareDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    return DAY_MAP[dayIndex];
  } catch (error) {
    console.error('[TimezoneUtils] Error getting day of week in timezone:', error);
    // Fallback to UTC calculation
    const date = new Date(dateString);
    const dayIndex = date.getDay();
    return DAY_MAP[dayIndex];
  }
}

/**
 * Get current day of week in user's timezone
 */
export function getCurrentDayOfWeek(timezone?: string): DayOfWeek {
  const userTimezone = getUserTimezone(timezone);
  const today = getTodayInTimezone(userTimezone);
  const dayOfWeek = getDayOfWeekInTimezone(today, userTimezone);

  console.log('[TimezoneUtils] getCurrentDayOfWeek:', {
    timezone: userTimezone,
    today,
    dayOfWeek,
    utcToday: new Date().toISOString().split('T')[0],
    utcDay: DAY_MAP[new Date().getDay()]
  });

  return dayOfWeek;
}

/**
 * Get Monday of current week in user's timezone
 */
export function getCurrentWeekStartInTimezone(timezone?: string): string {
  const userTimezone = getUserTimezone(timezone);

  try {
    // Get current date in user's timezone
    const todayInTimezone = getTodayInTimezone(userTimezone);
    const dateInTimezone = new Date(todayInTimezone + 'T12:00:00');

    // Get day of week using standard getDay() method
    const dayOfWeek = dateInTimezone.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Adjust for Monday start

    const monday = new Date(dateInTimezone);
    monday.setDate(monday.getDate() + daysToMonday);

    return monday.toISOString().split('T')[0];
  } catch (error) {
    console.error('[TimezoneUtils] Error getting week start in timezone:', error);

    // Fallback to UTC calculation
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
    const monday = new Date(today);
    monday.setDate(diff);
    return monday.toISOString().split('T')[0];
  }
}

/**
 * Get all dates in a week starting from Monday in user's timezone
 */
export function getWeekDatesInTimezone(weekStart: string, timezone?: string): string[] {
  const dates: string[] = [];
  const startDate = new Date(weekStart + 'T12:00:00'); // Add noon to avoid timezone issues
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    dates.push(date.toISOString().split('T')[0]);
  }
  
  return dates;
}

/**
 * Check if a date is today in user's timezone
 */
export function isTodayInTimezone(date: string, timezone?: string): boolean {
  const today = getTodayInTimezone(timezone);
  return date === today;
}

/**
 * Format date to Indonesian format in user's timezone
 */
export function formatDateIndonesian(date: string, timezone?: string): string {
  const userTimezone = getUserTimezone(timezone);
  
  try {
    const dateObj = new Date(date + 'T12:00:00');
    const options: Intl.DateTimeFormatOptions = {
      timeZone: userTimezone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return dateObj.toLocaleDateString('id-ID', options);
  } catch (error) {
    console.error('[TimezoneUtils] Error formatting date in Indonesian:', error);
    // Fallback to basic formatting
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('id-ID');
  }
}

/**
 * Convert a date from one timezone to another
 */
export function convertDateBetweenTimezones(
  date: string, 
  fromTimezone: string, 
  toTimezone: string
): string {
  try {
    // Parse the date assuming it's in the source timezone
    const dateObj = new Date(date + 'T12:00:00');
    
    // Format in target timezone
    const formatter = new Intl.DateTimeFormat('en-CA', {
      timeZone: toTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    
    return formatter.format(dateObj);
  } catch (error) {
    console.error('[TimezoneUtils] Error converting date between timezones:', error);
    return date; // Return original date as fallback
  }
}

/**
 * Validate timezone string
 */
export function isValidTimezone(timezone: string): boolean {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Get timezone offset in hours
 */
export function getTimezoneOffset(timezone?: string): number {
  const userTimezone = getUserTimezone(timezone);
  
  try {
    const now = new Date();
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
    const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: userTimezone }));
    const offset = (targetTime.getTime() - utc.getTime()) / (1000 * 60 * 60);
    return offset;
  } catch (error) {
    console.error('[TimezoneUtils] Error getting timezone offset:', error);
    return 0;
  }
}

/**
 * Debug function to log timezone information
 */
export function debugTimezoneInfo(timezone?: string): void {
  const userTimezone = getUserTimezone(timezone);
  const today = getTodayInTimezone(userTimezone);
  const currentDay = getCurrentDayOfWeek(userTimezone);
  const weekStart = getCurrentWeekStartInTimezone(userTimezone);
  const offset = getTimezoneOffset(userTimezone);

  // Additional debug info
  const now = new Date();
  const utcToday = now.toISOString().split('T')[0];
  const utcDay = DAY_MAP[now.getDay()];

  console.log('[TimezoneUtils] Debug Info:', {
    timezone: userTimezone,
    today,
    currentDay,
    weekStart,
    offset,
    utcToday,
    utcDay,
    utcTime: now.toISOString(),
    localTime: now.toString(),
    // Check what day July 16, 2025 should be
    july16Day: DAY_MAP[new Date('2025-07-16T12:00:00').getDay()],
    july17Day: DAY_MAP[new Date('2025-07-17T12:00:00').getDay()],
  });
}
