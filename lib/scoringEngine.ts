/**
 * Scoring Engine for Self-Reflection Check-In
 * Handles flexible scoring calculations and risk assessment
 */

import type {
  Question,
  QuestionSet,
  RiskAssessmentRule,
  ScoringResult,
  RiskAssessment,
  RiskLevel
} from '@/types/selfReflection';
import { ScoringError } from '@/types/selfReflection';

export class ScoringEngine {
  /**
   * Calculate scores for a set of responses
   */
  static calculateScore(
    responses: Record<string, any>,
    questions: Question[]
  ): ScoringResult {
    try {
      const questionScores: Record<string, number> = {};
      const criticalFlags: string[] = [];
      let totalScore = 0;
      let maxPossibleScore = 0;

      for (const question of questions) {
        const response = responses[question.questionKey];
        const score = this.calculateQuestionScore(question, response);
        
        questionScores[question.questionKey] = score;
        totalScore += score;
        maxPossibleScore += question.scoringConfig.maxScore;

        // Check for critical flags
        if (question.scoringConfig.critical && score > 0) {
          criticalFlags.push(question.questionKey);
        }

        // Check for safety-critical responses
        if (question.metadata?.isSafetyQuestion && this.isCriticalResponse(question, response)) {
          criticalFlags.push(`${question.questionKey}_critical`);
        }
      }

      // Determine risk level based on total score
      const riskLevel = this.determineRiskLevel(totalScore, criticalFlags);

      return {
        questionScores,
        totalScore,
        maxPossibleScore,
        riskLevel,
        criticalFlags
      };

    } catch (error) {
      throw new ScoringError(
        `Failed to calculate scores: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { responses, questions, originalError: error }
      );
    }
  }

  /**
   * Calculate score for an individual question
   */
  private static calculateQuestionScore(question: Question, response: any): number {
    if (response === null || response === undefined) {
      return 0;
    }

    const { scoringConfig } = question;

    switch (scoringConfig.method) {
      case 'none':
        return 0;

      case 'direct':
        return this.calculateDirectScore(question, response);

      case 'weighted':
        return this.calculateWeightedScore(question, response);

      case 'conditional':
        return this.calculateConditionalScore(question, response);

      default:
        console.warn(`Unknown scoring method: ${scoringConfig.method}`);
        return 0;
    }
  }

  /**
   * Calculate direct score (most common method)
   */
  private static calculateDirectScore(question: Question, response: any): number {
    if (question.questionType === 'multiple_choice') {
      const selectedOption = question.options?.find(option => option.id === response);
      return selectedOption?.score || 0;
    }

    if (question.questionType === 'scale') {
      // For scale questions, response is the numeric value
      const numericResponse = Number(response);
      if (isNaN(numericResponse)) return 0;
      
      // Clamp to max score
      return Math.min(numericResponse, question.scoringConfig.maxScore);
    }

    if (question.questionType === 'boolean') {
      // For boolean questions, true = maxScore, false = 0
      return response === true ? question.scoringConfig.maxScore : 0;
    }

    if (question.questionType === 'multi_select') {
      // For multi-select, sum scores of selected options
      if (!Array.isArray(response)) return 0;
      
      let totalScore = 0;
      for (const selectedId of response) {
        const option = question.options?.find(opt => opt.id === selectedId);
        totalScore += option?.score || 0;
      }
      
      return Math.min(totalScore, question.scoringConfig.maxScore);
    }

    return 0;
  }

  /**
   * Calculate weighted score
   */
  private static calculateWeightedScore(question: Question, response: any): number {
    const baseScore = this.calculateDirectScore(question, response);
    const weights = question.scoringConfig.weights || {};
    const weight = weights[response] || 1;
    
    return Math.min(baseScore * weight, question.scoringConfig.maxScore);
  }

  /**
   * Calculate conditional score
   */
  private static calculateConditionalScore(question: Question, response: any): number {
    let baseScore = this.calculateDirectScore(question, response);
    
    const conditions = question.scoringConfig.conditions || [];
    
    for (const condition of conditions) {
      // Note: Conditional scoring would need access to other responses
      // This is a simplified implementation
      if (condition.then.score !== undefined) {
        baseScore = condition.then.score;
      }
      if (condition.then.multiplier !== undefined) {
        baseScore *= condition.then.multiplier;
      }
      if (condition.then.addScore !== undefined) {
        baseScore += condition.then.addScore;
      }
    }
    
    return Math.min(baseScore, question.scoringConfig.maxScore);
  }

  /**
   * Check if a response is safety-critical
   */
  private static isCriticalResponse(question: Question, response: any): boolean {
    if (question.questionType === 'multiple_choice') {
      const selectedOption = question.options?.find(option => option.id === response);
      return selectedOption?.critical === true;
    }
    
    return false;
  }

  /**
   * Determine risk level based on total score and critical flags
   */
  private static determineRiskLevel(totalScore: number, criticalFlags: string[]): RiskLevel {
    // Emergency level if any critical flags are present
    if (criticalFlags.some(flag => flag.includes('safety_check') || flag.includes('critical'))) {
      return 'emergency';
    }

    // Standard risk level determination based on score ranges
    // These ranges match the seeded risk assessment rules
    if (totalScore >= 10) return 'emergency';
    if (totalScore >= 8) return 'red';
    if (totalScore >= 5) return 'yellow';
    return 'green';
  }

  /**
   * Assess risk level with detailed information
   */
  static assessRisk(
    scoringResult: ScoringResult,
    riskAssessmentRule: RiskAssessmentRule,
    responses: Record<string, any>
  ): RiskAssessment {
    try {
      const { totalScore, maxPossibleScore, riskLevel, criticalFlags } = scoringResult;
      const scoreRanges = riskAssessmentRule.scoreRanges;
      const actions = riskAssessmentRule.actions;

      const currentRange = scoreRanges[riskLevel];
      const currentActions = actions[riskLevel];

      const percentage = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;

      // Check if immediate action is required
      const requiresImmediateAction = 
        riskLevel === 'emergency' || 
        criticalFlags.length > 0 ||
        criticalFlags.some(flag => flag.includes('safety_check'));

      return {
        level: riskLevel,
        score: totalScore,
        maxScore: maxPossibleScore,
        percentage: Math.round(percentage),
        message: currentRange.label,
        aiTone: currentActions.aiTone,
        recommendedActions: currentActions.actions,
        requiresImmediateAction
      };

    } catch (error) {
      throw new ScoringError(
        `Failed to assess risk: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { scoringResult, riskAssessmentRule, responses, originalError: error }
      );
    }
  }

  /**
   * Get AI tone preference from responses
   */
  static getAITonePreference(responses: Record<string, any>): string {
    // Extract support preference from responses
    const supportPreference = responses.support_preference;
    
    const toneMapping: Record<string, string> = {
      'light_chat': 'casual_friendly',
      'validation': 'empathetic_supportive',
      'insight': 'thoughtful_reflective',
      'coping_skills': 'educational_encouraging',
      'unsure': 'gentle_exploratory'
    };

    return toneMapping[supportPreference] || 'warm_supportive';
  }

  /**
   * Get support preference from responses
   */
  static getSupportPreference(responses: Record<string, any>): string {
    return responses.support_preference || 'unsure';
  }

  /**
   * Validate scoring configuration
   */
  static validateScoringConfig(questions: Question[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const question of questions) {
      const { scoringConfig, questionType, options } = question;

      // Check if scoring method is valid
      if (!['direct', 'weighted', 'conditional', 'none'].includes(scoringConfig.method)) {
        errors.push(`Invalid scoring method for question ${question.questionKey}: ${scoringConfig.method}`);
      }

      // Check if maxScore is valid
      if (scoringConfig.maxScore < 0) {
        errors.push(`Invalid maxScore for question ${question.questionKey}: ${scoringConfig.maxScore}`);
      }

      // Check if options have valid scores for multiple choice questions
      if (questionType === 'multiple_choice' && scoringConfig.method !== 'none') {
        if (!options || options.length === 0) {
          errors.push(`Multiple choice question ${question.questionKey} must have options`);
        } else {
          const hasInvalidScores = options.some(option => 
            option.score !== undefined && 
            (typeof option.score !== 'number' || option.score < 0 || option.score > scoringConfig.maxScore)
          );
          
          if (hasInvalidScores) {
            errors.push(`Invalid option scores for question ${question.questionKey}`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate theoretical maximum score for a question set
   */
  static calculateMaxPossibleScore(questions: Question[]): number {
    return questions.reduce((total, question) => {
      return total + question.scoringConfig.maxScore;
    }, 0);
  }

  /**
   * Get score breakdown for display purposes
   */
  static getScoreBreakdown(
    scoringResult: ScoringResult,
    questions: Question[]
  ): Array<{ questionKey: string; questionText: string; score: number; maxScore: number }> {
    return questions.map(question => ({
      questionKey: question.questionKey,
      questionText: question.questionText.id,
      score: scoringResult.questionScores[question.questionKey] || 0,
      maxScore: question.scoringConfig.maxScore
    }));
  }
}
