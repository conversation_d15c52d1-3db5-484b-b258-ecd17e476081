/**
 * Self-Reflection Service
 * Handles saving and retrieving self-reflection sessions
 */

import { supabase } from './supabase';
import { QuestionConfigService } from './questionConfigService';
import { ScoringEngine } from './scoringEngine';
import type {
  SelfReflectionSession,
  QuestionSet,
  ScoringResult,
  RiskAssessment
} from '@/types/selfReflection';
import { SelfReflectionError } from '@/types/selfReflection';

export class SelfReflectionService {
  /**
   * Save a completed self-reflection session
   */
  static async saveSession(
    userId: string,
    responses: Record<string, any>,
    language: string = 'id'
  ): Promise<SelfReflectionSession> {
    try {
      // Get the active question set
      const questionSet = await QuestionConfigService.getActiveQuestionSet(language);
      
      // Calculate scores
      const scoringResult = ScoringEngine.calculateScore(responses, questionSet.questions);
      
      // Get risk assessment
      const riskAssessmentRule = questionSet.riskAssessmentRules[0]; // Use first active rule
      if (!riskAssessmentRule) {
        throw new SelfReflectionError(
          'No active risk assessment rule found',
          'MISSING_RISK_RULE',
          { questionSetId: questionSet.id }
        );
      }
      
      const riskAssessment = ScoringEngine.assessRisk(scoringResult, riskAssessmentRule, responses);
      
      // Extract preferences
      const aiTonePreference = ScoringEngine.getAITonePreference(responses);
      const supportPreference = ScoringEngine.getSupportPreference(responses);
      
      // Prepare session data
      const sessionData = {
        user_id: userId,
        question_set_id: questionSet.id,
        responses: responses,
        calculated_scores: scoringResult.questionScores,
        total_score: scoringResult.totalScore,
        risk_level: scoringResult.riskLevel,
        ai_tone_preference: aiTonePreference,
        support_preference: supportPreference,
        metadata: {
          questionSetVersion: questionSet.version,
          maxPossibleScore: scoringResult.maxPossibleScore,
          criticalFlags: scoringResult.criticalFlags,
          riskAssessment: {
            percentage: riskAssessment.percentage,
            message: riskAssessment.message,
            requiresImmediateAction: riskAssessment.requiresImmediateAction
          }
        },
        completed_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Save to database
      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        throw new SelfReflectionError(
          `Failed to save session: ${error.message}`,
          'SAVE_SESSION_ERROR',
          { sessionData, error }
        );
      }

      // Transform to TypeScript interface
      const session: SelfReflectionSession = {
        id: data.id,
        userId: data.user_id,
        questionSetId: data.question_set_id,
        responses: data.responses,
        calculatedScores: data.calculated_scores,
        totalScore: data.total_score,
        riskLevel: data.risk_level,
        aiTonePreference: data.ai_tone_preference,
        supportPreference: data.support_preference,
        metadata: data.metadata,
        completedAt: new Date(data.completed_at),
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };

      return session;

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error saving session: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_SAVE_ERROR',
        { userId, responses, language, originalError: error }
      );
    }
  }

  /**
   * Get the most recent session for a user
   */
  static async getLatestSession(userId: string): Promise<SelfReflectionSession | null> {
    try {
      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('completed_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw new SelfReflectionError(
          `Failed to fetch latest session: ${error.message}`,
          'FETCH_SESSION_ERROR',
          { userId, error }
        );
      }

      return this.transformSessionData(data);

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error fetching latest session: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_FETCH_ERROR',
        { userId, originalError: error }
      );
    }
  }

  /**
   * Get all sessions for a user with pagination
   */
  static async getUserSessions(
    userId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ sessions: SelfReflectionSession[]; total: number }> {
    try {
      // Get total count
      const { count, error: countError } = await supabase
        .from('self_reflection_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) {
        throw new SelfReflectionError(
          `Failed to count user sessions: ${countError.message}`,
          'COUNT_SESSIONS_ERROR',
          { userId, error: countError }
        );
      }

      // Get sessions
      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('completed_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new SelfReflectionError(
          `Failed to fetch user sessions: ${error.message}`,
          'FETCH_SESSIONS_ERROR',
          { userId, limit, offset, error }
        );
      }

      const sessions = (data || []).map(this.transformSessionData);

      return {
        sessions,
        total: count || 0
      };

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error fetching user sessions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_FETCH_SESSIONS_ERROR',
        { userId, limit, offset, originalError: error }
      );
    }
  }

  /**
   * Get session by ID
   */
  static async getSessionById(sessionId: string, userId: string): Promise<SelfReflectionSession | null> {
    try {
      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw new SelfReflectionError(
          `Failed to fetch session: ${error.message}`,
          'FETCH_SESSION_ERROR',
          { sessionId, userId, error }
        );
      }

      return this.transformSessionData(data);

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error fetching session by ID: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_FETCH_BY_ID_ERROR',
        { sessionId, userId, originalError: error }
      );
    }
  }

  /**
   * Check if user has completed self-reflection recently
   */
  static async hasRecentSession(userId: string, hoursThreshold: number = 24): Promise<boolean> {
    try {
      const thresholdDate = new Date();
      thresholdDate.setHours(thresholdDate.getHours() - hoursThreshold);

      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .select('id')
        .eq('user_id', userId)
        .gte('completed_at', thresholdDate.toISOString())
        .limit(1);

      if (error) {
        throw new SelfReflectionError(
          `Failed to check recent sessions: ${error.message}`,
          'CHECK_RECENT_ERROR',
          { userId, hoursThreshold, error }
        );
      }

      return (data || []).length > 0;

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error checking recent sessions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_CHECK_RECENT_ERROR',
        { userId, hoursThreshold, originalError: error }
      );
    }
  }

  /**
   * Check if user has any completed self-reflection sessions
   */
  static async hasCompletedSelfReflection(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (error) {
        throw new SelfReflectionError(
          `Failed to check self-reflection completion: ${error.message}`,
          'CHECK_COMPLETION_ERROR',
          { userId, error }
        );
      }

      return (data || []).length > 0;

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error checking self-reflection completion: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_CHECK_COMPLETION_ERROR',
        { userId, originalError: error }
      );
    }
  }

  /**
   * Get risk level statistics for a user
   */
  static async getUserRiskStats(userId: string, days: number = 30): Promise<Record<string, number>> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .select('risk_level')
        .eq('user_id', userId)
        .gte('completed_at', startDate.toISOString());

      if (error) {
        throw new SelfReflectionError(
          `Failed to fetch risk statistics: ${error.message}`,
          'FETCH_RISK_STATS_ERROR',
          { userId, days, error }
        );
      }

      // Count risk levels
      const stats: Record<string, number> = {
        green: 0,
        yellow: 0,
        red: 0,
        emergency: 0
      };

      (data || []).forEach((session: any) => {
        if (session.risk_level && stats.hasOwnProperty(session.risk_level)) {
          stats[session.risk_level]++;
        }
      });

      return stats;

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error fetching risk statistics: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_RISK_STATS_ERROR',
        { userId, days, originalError: error }
      );
    }
  }

  /**
   * Transform database session data to TypeScript interface
   */
  private static transformSessionData(data: any): SelfReflectionSession {
    return {
      id: data.id,
      userId: data.user_id,
      questionSetId: data.question_set_id,
      responses: data.responses || {},
      calculatedScores: data.calculated_scores || {},
      totalScore: data.total_score || 0,
      riskLevel: data.risk_level,
      aiTonePreference: data.ai_tone_preference,
      supportPreference: data.support_preference,
      metadata: data.metadata || {},
      completedAt: new Date(data.completed_at),
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  /**
   * Delete old sessions (for privacy/cleanup)
   */
  static async cleanupOldSessions(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const { data, error } = await supabase
        .from('self_reflection_sessions')
        .delete()
        .lt('created_at', cutoffDate.toISOString())
        .select('id');

      if (error) {
        throw new SelfReflectionError(
          `Failed to cleanup old sessions: ${error.message}`,
          'CLEANUP_ERROR',
          { daysToKeep, error }
        );
      }

      return (data || []).length;

    } catch (error) {
      if (error instanceof SelfReflectionError) {
        throw error;
      }
      throw new SelfReflectionError(
        `Unexpected error during cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UNEXPECTED_CLEANUP_ERROR',
        { daysToKeep, originalError: error }
      );
    }
  }
}
