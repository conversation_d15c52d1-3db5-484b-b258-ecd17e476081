/**
 * Convex Self-Reflection Service
 * Replaces the Supabase-based SelfReflectionService with Convex implementation
 */

import { api } from '@/convex/_generated/api';
import { useConvexAuth } from 'convex/react';
import { useMutation, useQuery } from 'convex/react';
import type { 
  SelfReflectionSession, 
  QuestionSet, 
  RiskAssessment,
  Question,
  RiskLevel 
} from '@/types/selfReflection';

// Scoring Engine - Keep the existing logic
export class ScoringEngine {
  static calculateScore(
    responses: Record<string, any>,
    questions: Question[]
  ): {
    questionScores: Record<string, number>;
    totalScore: number;
    maxPossibleScore: number;
    riskLevel: RiskLevel;
    criticalFlags: string[];
  } {
    const questionScores: Record<string, number> = {};
    let totalScore = 0;
    let maxPossibleScore = 0;
    const criticalFlags: string[] = [];

    for (const question of questions) {
      const response = responses[question.key];
      let score = 0;
      let maxScore = 0;

      if (question.scoringMethod === 'direct') {
        if (question.type === 'multiple_choice') {
          const selectedOption = question.options?.find((opt: any) => opt.value === response);
          score = selectedOption?.score || 0;
          maxScore = Math.max(...(question.options?.map((opt: any) => opt.score) || [0]));
        } else if (question.type === 'scale') {
          score = parseInt(response) || 0;
          maxScore = question.options?.max || 5;
        }

        // Apply weight
        const weight = question.scoringConfig?.weight || 1;
        score *= weight;
        maxScore *= weight;

        // Check for critical flags
        if (score >= maxScore * 0.8) {
          criticalFlags.push(question.key);
        }
      }

      questionScores[question.key] = score;
      totalScore += score;
      maxPossibleScore += maxScore;
    }

    // Determine risk level based on percentage
    const percentage = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;
    let riskLevel: RiskLevel = 'green';
    
    if (percentage >= 70) {
      riskLevel = 'red';
    } else if (percentage >= 40) {
      riskLevel = 'yellow';
    }

    return {
      questionScores,
      totalScore,
      maxPossibleScore,
      riskLevel,
      criticalFlags,
    };
  }
}

// Service class that matches the existing SelfReflectionService interface
export class ConvexSelfReflectionService {
  /**
   * Get the active question set
   */
  static async getActiveQuestionSet(language: string = 'id'): Promise<QuestionSet | null> {
    // This will be called from a component using useQuery
    return null; // Placeholder - actual implementation uses hooks
  }

  /**
   * Save a self-reflection session
   */
  static async saveSession(
    userId: string,
    responses: Record<string, any>,
    language: string = 'id',
    aiTonePreference?: string,
    supportPreference?: string
  ): Promise<SelfReflectionSession> {
    // This will be called from a component using useMutation
    throw new Error('Use ConvexSelfReflectionHooks instead');
  }

  /**
   * Get user's latest session
   */
  static async getUserLatestSession(userId: string): Promise<SelfReflectionSession | null> {
    // This will be called from a component using useQuery
    return null; // Placeholder - actual implementation uses hooks
  }
}

// React hooks for Convex operations
export const useConvexSelfReflection = () => {
  const { isAuthenticated } = useConvexAuth();
  
  // Queries
  const getActiveQuestionSet = useQuery(
    api.selfReflection.getActiveQuestionSet,
    isAuthenticated ? { language: 'id' } : 'skip'
  );
  
  const getUserLatestSession = useQuery(
    api.selfReflection.getUserLatestSession,
    isAuthenticated ? {} : 'skip'
  );

  // Mutations
  const saveSessionMutation = useMutation(api.selfReflection.saveSession);
  const updateProfileMutation = useMutation(api.selfReflection.updateUserProfileCompletion);

  /**
   * Save a complete self-reflection session
   */
  const saveSession = async (
    responses: Record<string, any>,
    language: string = 'id',
    aiTonePreference?: string,
    supportPreference?: string
  ): Promise<SelfReflectionSession> => {
    if (!getActiveQuestionSet) {
      throw new Error('No active question set found');
    }

    // Calculate scores using the existing scoring engine
    const scoringResult = ScoringEngine.calculateScore(responses, getActiveQuestionSet.questions);

    // Simple risk assessment (can be enhanced later)
    const percentage = scoringResult.maxPossibleScore > 0 ?
      (scoringResult.totalScore / scoringResult.maxPossibleScore) * 100 : 0;

    let riskLevel = 'green';
    let riskMessage = 'You\'re doing well!';

    if (percentage >= 70) {
      riskLevel = 'red';
      riskMessage = 'Consider seeking professional support.';
    } else if (percentage >= 40) {
      riskLevel = 'yellow';
      riskMessage = 'Some areas may need attention.';
    }

    const riskAssessment = {
      level: riskLevel,
      score: scoringResult.totalScore,
      maxScore: scoringResult.maxPossibleScore,
      percentage,
      message: riskMessage,
      aiTone: riskLevel === 'red' ? 'supportive' : 'encouraging',
      recommendedActions: [],
      requiresImmediateAction: riskLevel === 'red',
    };

    // Save session
    const sessionId = await saveSessionMutation({
      questionSetId: getActiveQuestionSet._id,
      responses,
      calculatedScores: scoringResult.questionScores,
      totalScore: scoringResult.totalScore,
      riskLevel: riskAssessment.level,
      aiTonePreference,
      supportPreference,
      metadata: {
        questionSetVersion: getActiveQuestionSet.version,
        maxPossibleScore: scoringResult.maxPossibleScore,
        criticalFlags: scoringResult.criticalFlags,
        riskAssessment,
      },
    });

    // Return session object in expected format
    const session: SelfReflectionSession = {
      id: sessionId,
      userId: 'current-user', // Will be set by Convex
      questionSetId: getActiveQuestionSet._id,
      responses,
      calculatedScores: scoringResult.questionScores,
      totalScore: scoringResult.totalScore,
      riskLevel: riskAssessment.level as RiskLevel,
      aiTonePreference,
      supportPreference,
      metadata: {
        questionSetVersion: getActiveQuestionSet.version,
        maxPossibleScore: scoringResult.maxPossibleScore,
        criticalFlags: scoringResult.criticalFlags,
        riskAssessment,
      },
      completedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return session;
  };

  /**
   * Update user profile completion status
   */
  const updateCompletionStatus = async (options: {
    selfReflectionCompleted?: boolean;
    selfReflectionSkipped?: boolean;
  }) => {
    return await updateProfileMutation(options);
  };

  return {
    // Data
    questionSet: getActiveQuestionSet,
    latestSession: getUserLatestSession,
    
    // Actions
    saveSession,
    updateCompletionStatus,
    
    // Loading states
    isLoading: getActiveQuestionSet === undefined,
  };
};

// Export for backward compatibility
export const SelfReflectionService = ConvexSelfReflectionService;
