/**
 * Voice Credits Service
 * Manages credits-based rate limiting for voice calls
 */

import { supabase } from './supabase';
import { VOICE_CALL_CONFIG, VoiceConfigHelpers } from '@/constants/voiceConfig';
import { startTransaction, addBreadcrumb, reportError } from '@/lib/sentryConfig';
import type {
  VoiceCredits,
  VoiceCallSession,
  CreditCheckResult,
  SessionStartResult,
  SessionEndResult,
  VoiceCreditsError,
  VoiceSessionStatus,
} from '@/types/voiceCredits';
import {
  VOICE_CREDITS_CONFIG,
  calculateCreditsFromDuration,
  calculateMaxDurationFromCredits,
} from '@/types/voiceCredits';

export class VoiceCreditsService {
  private static instance: VoiceCreditsService;

  static getInstance(): VoiceCreditsService {
    if (!this.instance) {
      this.instance = new VoiceCreditsService();
    }
    return this.instance;
  }

  /**
   * Force a fresh database connection for critical operations
   */
  private async ensureFreshConnection(): Promise<void> {
    try {
      // Execute a simple query to ensure connection is active
      await supabase.from('user_profiles').select('user_id').limit(1);
    } catch (error) {
      console.warn('[VoiceCreditsService] Connection refresh failed:', error);
    }
  }

  /**
   * Check if user has sufficient credits for a voice call
   */
  async checkCredits(userId: string, estimatedDurationSeconds?: number): Promise<CreditCheckResult> {
    try {
      const credits = await this.getUserCredits(userId);
      const estimatedDuration = estimatedDurationSeconds || VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT;
      const requiredCredits = calculateCreditsFromDuration(estimatedDuration);

      const hasCredits = credits.creditsRemaining >= requiredCredits;
      const maxCallTime = calculateMaxDurationFromCredits(credits.creditsRemaining);

      return {
        hasCredits,
        creditsRemaining: credits.creditsRemaining,
        estimatedCallTime: maxCallTime,
        message: hasCredits 
          ? `You have ${credits.creditsRemaining} credits remaining`
          : `Insufficient credits. You need ${requiredCredits} credits but only have ${credits.creditsRemaining}.`
      };
    } catch (error) {
      console.error('[VoiceCreditsService] Error checking credits:', error);
      throw this.createError('DATABASE_ERROR', 'Failed to check credits', error);
    }
  }

  /**
   * Get user's current credit information with direct query fallback
   */
  async getUserCredits(userId: string): Promise<VoiceCredits> {
    try {
      // Ensure fresh connection for critical credit operations
      await this.ensureFreshConnection();

      // Try direct SQL query first (bypasses function caching)
      const directResult = await this.getUserCreditsDirectQuery(userId);

      // Try RPC function as fallback and compare results
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_user_voice_credits', { target_user_id: userId });

      let rpcResult: VoiceCredits | null = null;

      if (!rpcError && rpcData && rpcData.length > 0) {
        const result = rpcData[0];
        rpcResult = {
          creditsRemaining: result.credits_remaining || VOICE_CREDITS_CONFIG.INITIAL_CREDITS,
          creditsUsed: result.credits_used || 0,
          totalSessions: Number(result.total_sessions) || 0,
          lastSession: result.last_session ? new Date(result.last_session) : undefined,
          lastCreditReset: result.last_credit_reset ? new Date(result.last_credit_reset) : new Date(),
        };
      }

      // Compare results and log discrepancies
      if (directResult && rpcResult) {
        const creditsMatch = directResult.creditsRemaining === rpcResult.creditsRemaining;
        const usedMatch = directResult.creditsUsed === rpcResult.creditsUsed;
        const sessionsMatch = directResult.totalSessions === rpcResult.totalSessions;

        if (!creditsMatch || !usedMatch || !sessionsMatch) {
          console.warn('[VoiceCreditsService] Credit data discrepancy detected:', {
            userId,
            timestamp: new Date().toISOString(),
            directQuery: {
              creditsRemaining: directResult.creditsRemaining,
              creditsUsed: directResult.creditsUsed,
              totalSessions: directResult.totalSessions
            },
            rpcFunction: {
              creditsRemaining: rpcResult.creditsRemaining,
              creditsUsed: rpcResult.creditsUsed,
              totalSessions: rpcResult.totalSessions
            },
            discrepancies: {
              credits: !creditsMatch,
              used: !usedMatch,
              sessions: !sessionsMatch
            },
            note: 'Using direct query result as authoritative'
          });
        } else {
          console.log('[VoiceCreditsService] Credit data validation passed:', {
            userId,
            creditsRemaining: directResult.creditsRemaining,
            creditsUsed: directResult.creditsUsed,
            totalSessions: directResult.totalSessions
          });
        }
      }

      // Prefer direct query result, fallback to RPC if direct query failed
      return directResult || rpcResult || {
        creditsRemaining: VOICE_CREDITS_CONFIG.INITIAL_CREDITS,
        creditsUsed: 0,
        totalSessions: 0,
        lastCreditReset: new Date(),
      };
    } catch (error) {
      console.error('[VoiceCreditsService] Error getting user credits:', error);
      throw this.createError('DATABASE_ERROR', 'Failed to get user credits', error);
    }
  }

  /**
   * Get user credits using direct SQL query (bypasses function caching)
   */
  private async getUserCreditsDirectQuery(userId: string): Promise<VoiceCredits | null> {
    try {
      // Get user profile data
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('voice_credits, voice_credits_used, last_credit_reset')
        .eq('user_id', userId)
        .single();

      if (profileError) {
        if (profileError.code === 'PGRST116') {
          // User doesn't exist, return defaults
          return {
            creditsRemaining: VOICE_CREDITS_CONFIG.INITIAL_CREDITS,
            creditsUsed: 0,
            totalSessions: 0,
            lastCreditReset: new Date(),
          };
        }
        throw profileError;
      }

      // Get session statistics
      const { data: sessionData, error: sessionError } = await supabase
        .from('voice_call_sessions')
        .select('id, session_start')
        .eq('user_id', userId)
        .eq('session_status', 'completed')
        .order('session_start', { ascending: false });

      if (sessionError) {
        console.warn('[VoiceCreditsService] Error getting session data:', sessionError);
      }

      const sessions = sessionData || [];

      return {
        creditsRemaining: profileData.voice_credits ?? VOICE_CREDITS_CONFIG.INITIAL_CREDITS,
        creditsUsed: profileData.voice_credits_used ?? 0,
        totalSessions: sessions.length,
        lastSession: sessions.length > 0 ? new Date(sessions[0].session_start) : undefined,
        lastCreditReset: profileData.last_credit_reset ? new Date(profileData.last_credit_reset) : new Date(),
      };
    } catch (error) {
      console.error('[VoiceCreditsService] Error in direct query:', error);
      return null;
    }
  }

  /**
   * Start a new voice session
   */
  async startVoiceSession(userId: string, agentId?: string): Promise<SessionStartResult> {
    // Start performance transaction for voice session start
    const transaction = startTransaction('voice.start_session', 'voice');
    transaction.setTag('operation', 'start_voice_session');
    transaction.setTag('has_agent_id', !!agentId);

    // Add breadcrumb for voice session start (privacy-safe)
    addBreadcrumb(
      'Voice session start requested',
      'voice',
      {
        // Don't include userId for privacy
        hasAgentId: !!agentId,
      }
    );

    try {
      // Check if user has credits
      const creditCheck = await this.checkCredits(userId);
      if (!creditCheck.hasCredits) {
        transaction.setStatus('resource_exhausted');
        addBreadcrumb(
          'Voice session start failed - insufficient credits',
          'voice',
          {
            availableCredits: creditCheck.availableCredits,
            requiredCredits: creditCheck.requiredCredits,
          }
        );
        throw this.createError('INSUFFICIENT_CREDITS', creditCheck.message || 'No credits available');
      }

      // Create new session record
      const sessionData = {
        user_id: userId,
        agent_id: agentId,
        session_status: 'active' as VoiceSessionStatus,
        session_metadata: {
          startedAt: new Date().toISOString(),
          userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        },
      };

      const { data, error } = await supabase
        .from('voice_call_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      const maxDuration = calculateMaxDurationFromCredits(creditCheck.creditsRemaining);

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('available_credits', creditCheck.availableCredits, 'none');
      transaction.setMeasurement('max_call_duration', Math.min(maxDuration, VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS), 'second');

      addBreadcrumb(
        'Voice session started successfully',
        'voice',
        {
          sessionId: data.id,
          availableCredits: creditCheck.availableCredits,
          maxCallDuration: Math.min(maxDuration, VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS),
        }
      );

      return {
        sessionId: data.id,
        creditsReserved: 0, // We don't reserve credits upfront
        maxCallDuration: Math.min(maxDuration, VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS),
      };
    } catch (error) {
      console.error('[VoiceCreditsService] Error starting voice session:', error);

      if (!transaction.status) {
        transaction.setStatus('internal_error');
      }

      // Report error to Sentry with privacy-safe context
      if (error instanceof Error) {
        reportError(error, {
          component: 'VoiceCreditsService',
          action: 'start_voice_session',
          metadata: {
            hasAgentId: !!agentId,
            errorCode: (error as any).code,
          }
        });
      }

      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw VoiceCreditsError
      }
      throw this.createError('DATABASE_ERROR', 'Failed to start voice session', error);
    } finally {
      transaction.finish();
    }
  }

  /**
   * End a voice session and deduct credits
   */
  async endVoiceSession(sessionId: string, durationSeconds: number): Promise<SessionEndResult> {
    // Start performance transaction for voice session end
    const transaction = startTransaction('voice.end_session', 'voice');
    transaction.setTag('operation', 'end_voice_session');
    transaction.setTag('session_id', sessionId);

    // Add breadcrumb for voice session end (privacy-safe)
    addBreadcrumb(
      'Voice session end requested',
      'voice',
      {
        sessionId,
        durationSeconds,
      }
    );

    try {
      if (durationSeconds < 0) {
        transaction.setStatus('invalid_argument');
        addBreadcrumb(
          'Voice session end failed - invalid duration',
          'voice',
          {
            sessionId,
            durationSeconds,
          }
        );
        throw this.createError('INVALID_DURATION', 'Duration cannot be negative');
      }

      // Ensure fresh connection for critical session end operation
      await this.ensureFreshConnection();

      const creditsUsed = calculateCreditsFromDuration(durationSeconds);

      // Update session record
      const { data: sessionData, error: sessionError } = await supabase
        .from('voice_call_sessions')
        .update({
          session_end: new Date().toISOString(),
          duration_seconds: durationSeconds,
          credits_used: creditsUsed,
          session_status: 'completed' as VoiceSessionStatus,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sessionId)
        .select()
        .single();

      if (sessionError) {
        throw sessionError;
      }

      if (!sessionData) {
        throw this.createError('SESSION_NOT_FOUND', 'Voice session not found');
      }

      // Get current credits before update for validation
      const creditsBefore = await this.getUserCredits(sessionData.user_id);

      // Get updated user credits (the trigger will have updated them)
      const updatedCredits = await this.getUserCredits(sessionData.user_id);

      // Validate credit deduction
      const expectedCreditsAfter = Math.max(0, creditsBefore.creditsRemaining - creditsUsed);
      const actualCreditsAfter = updatedCredits.creditsRemaining;

      if (expectedCreditsAfter !== actualCreditsAfter) {
        console.warn('[VoiceCreditsService] Credit deduction validation failed:', {
          sessionId,
          userId: sessionData.user_id,
          durationSeconds,
          creditsUsed,
          creditsBefore: creditsBefore.creditsRemaining,
          expectedAfter: expectedCreditsAfter,
          actualAfter: actualCreditsAfter,
          difference: actualCreditsAfter - expectedCreditsAfter,
          timestamp: new Date().toISOString()
        });
      } else {
        console.log('[VoiceCreditsService] Credit deduction validated successfully:', {
          sessionId,
          creditsUsed,
          creditsRemaining: actualCreditsAfter
        });
      }

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('session_duration', durationSeconds, 'second');
      transaction.setMeasurement('credits_used', creditsUsed, 'none');
      transaction.setMeasurement('credits_remaining', updatedCredits.creditsRemaining, 'none');

      addBreadcrumb(
        'Voice session ended successfully',
        'voice',
        {
          sessionId,
          durationSeconds,
          creditsUsed,
          creditsRemaining: updatedCredits.creditsRemaining,
        }
      );

      return {
        sessionId,
        durationSeconds,
        creditsUsed,
        creditsRemaining: updatedCredits.creditsRemaining,
      };
    } catch (error) {
      console.error('[VoiceCreditsService] Error ending voice session:', error);

      if (!transaction.status) {
        transaction.setStatus('internal_error');
      }

      // Report error to Sentry with privacy-safe context
      if (error instanceof Error) {
        reportError(error, {
          component: 'VoiceCreditsService',
          action: 'end_voice_session',
          metadata: {
            sessionId,
            durationSeconds,
            creditsUsed: calculateCreditsFromDuration(durationSeconds),
            errorCode: (error as any).code,
          }
        });
      }

      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw VoiceCreditsError
      }
      throw this.createError('DATABASE_ERROR', 'Failed to end voice session', error);
    } finally {
      transaction.finish();
    }
  }

  /**
   * Initialize credits for a new user
   */
  async initializeUserCredits(userId: string): Promise<void> {
    try {
      // This will be handled by the database trigger, but we can also do it explicitly
      const { error } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          voice_credits: VOICE_CREDITS_CONFIG.INITIAL_CREDITS,
          voice_credits_used: 0,
          last_credit_reset: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id',
          ignoreDuplicates: false,
        });

      if (error) {
        throw error;
      }

      console.log(`[VoiceCreditsService] Initialized ${VOICE_CREDITS_CONFIG.INITIAL_CREDITS} credits for user:`, userId);
    } catch (error) {
      console.error('[VoiceCreditsService] Error initializing user credits:', error);
      throw this.createError('DATABASE_ERROR', 'Failed to initialize user credits', error);
    }
  }

  /**
   * Handle interrupted or failed sessions
   */
  async handleSessionInterruption(sessionId: string, reason: 'interrupted' | 'failed', durationSeconds?: number): Promise<void> {
    try {
      const updateData: any = {
        session_status: reason,
        updated_at: new Date().toISOString(),
      };

      if (durationSeconds !== undefined && durationSeconds > 0) {
        updateData.session_end = new Date().toISOString();
        updateData.duration_seconds = durationSeconds;
        updateData.credits_used = calculateCreditsFromDuration(durationSeconds);
      }

      const { error } = await supabase
        .from('voice_call_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (error) {
        throw error;
      }

      console.log(`[VoiceCreditsService] Handled session ${reason}:`, sessionId);
    } catch (error) {
      console.error(`[VoiceCreditsService] Error handling session ${reason}:`, error);
      // Don't throw here as this is cleanup - just log the error
    }
  }

  /**
   * Get user's voice usage statistics
   */
  async getUserUsageStats(userId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('user_voice_usage')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data || {
        user_id: userId,
        credits_remaining: VOICE_CREDITS_CONFIG.INITIAL_CREDITS,
        credits_used: 0,
        total_sessions: 0,
        total_call_time_seconds: 0,
        last_call_time: null,
        last_credit_reset: new Date(),
      };
    } catch (error) {
      console.error('[VoiceCreditsService] Error getting usage stats:', error);
      throw this.createError('DATABASE_ERROR', 'Failed to get usage statistics', error);
    }
  }

  /**
   * Check if user needs weekly credit recharge
   */
  async checkWeeklyRecharge(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('check_user_needs_weekly_recharge', { target_user_id: userId });

      if (error) {
        console.error('[VoiceCreditsService] Error checking weekly recharge:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('[VoiceCreditsService] Error checking weekly recharge:', error);
      return false;
    }
  }

  /**
   * Perform weekly credit recharge for user (client-side backup)
   */
  async performWeeklyRecharge(userId: string): Promise<boolean> {
    try {
      addBreadcrumb({
        message: 'Performing weekly credit recharge',
        category: 'voice_credits',
        data: { userId },
      });

      const { data, error } = await supabase
        .rpc('recharge_user_weekly_credits', {
          target_user_id: userId,
          recharge_type_param: 'client_backup',
          triggered_by_param: 'client_app',
        });

      if (error) {
        console.error('[VoiceCreditsService] Error performing weekly recharge:', error);
        reportError(error, {
          context: 'weekly_recharge',
          userId,
        });
        return false;
      }

      console.log('[VoiceCreditsService] Weekly recharge successful:', data);

      addBreadcrumb({
        message: 'Weekly credit recharge completed',
        category: 'voice_credits',
        data: { userId, result: data },
      });

      return data?.success || false;
    } catch (error) {
      console.error('[VoiceCreditsService] Error performing weekly recharge:', error);
      reportError(error, {
        context: 'weekly_recharge',
        userId,
      });
      return false;
    }
  }

  /**
   * Check and perform weekly recharge if needed
   */
  async checkAndPerformWeeklyRecharge(userId: string): Promise<boolean> {
    try {
      const needsRecharge = await this.checkWeeklyRecharge(userId);

      if (needsRecharge) {
        console.log('[VoiceCreditsService] User needs weekly recharge, performing recharge...');
        return await this.performWeeklyRecharge(userId);
      }

      return false; // No recharge needed
    } catch (error) {
      console.error('[VoiceCreditsService] Error in checkAndPerformWeeklyRecharge:', error);
      return false;
    }
  }

  /**
   * Get user's recharge history
   */
  async getRechargeHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('voice_credit_recharges')
        .select('*')
        .eq('user_id', userId)
        .order('recharge_date', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('[VoiceCreditsService] Error getting recharge history:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('[VoiceCreditsService] Error getting recharge history:', error);
      return [];
    }
  }

  /**
   * Create a VoiceCreditsError
   */
  private createError(code: VoiceCreditsError['code'], message: string, details?: any): VoiceCreditsError {
    const error = new Error(message) as VoiceCreditsError;
    error.code = code;
    error.details = details;
    return error;
  }
}

export const voiceCreditsService = VoiceCreditsService.getInstance();
