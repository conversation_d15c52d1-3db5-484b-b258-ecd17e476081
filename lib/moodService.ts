import { supabase } from './supabase';
import { moodMigration } from './moodMigration';
import { getTodayInTimezone, getCurrentWeekStartInTimezone, getUserTimezone } from './timezoneUtils';
import { startTransaction, addBreadcrumb, reportError } from '@/lib/sentryConfig';
import type {
  DailyMoodData,
  WeeklyMoodData,
  MoodEntry,
  MoodEntryInput,
  MoodEntryUpdate,
  MoodAnalytics,
  MoodTrend,
  MoodInsights,
  MoodError,
  MoodValidationResult,
  DayOfWeek,
  MoodLevel,
  StressLevel,
  SleepQualityLevel,
  DailyFeelingLevel,
  PhysicalHealthStatus,
  SleepHours,
} from '@/types/mood';
import { MOOD_CONSTANTS, DAYS_OF_WEEK } from '@/types/mood';

class MoodService {
  private static instance: MoodService;

  static getInstance(): MoodService {
    if (!MoodService.instance) {
      MoodService.instance = new MoodService();
    }
    return MoodService.instance;
  }

  /**
   * Validate mood entry input
   */
  private validateMoodData(data: Partial<DailyMoodData>): MoodValidationResult {
    const errors: string[] = [];

    // Validate mood level
    if (data.mood !== null && data.mood !== undefined) {
      if (data.mood < MOOD_CONSTANTS.MIN_MOOD_LEVEL || data.mood > MOOD_CONSTANTS.MAX_MOOD_LEVEL) {
        errors.push(`Mood level harus antara ${MOOD_CONSTANTS.MIN_MOOD_LEVEL} dan ${MOOD_CONSTANTS.MAX_MOOD_LEVEL}`);
      }
    }

    // Validate sleep hours
    if (data.sleepHours !== null && data.sleepHours !== undefined) {
      if (data.sleepHours < MOOD_CONSTANTS.MIN_SLEEP_HOURS || data.sleepHours > MOOD_CONSTANTS.MAX_SLEEP_HOURS) {
        errors.push(`Jam tidur harus antara ${MOOD_CONSTANTS.MIN_SLEEP_HOURS} dan ${MOOD_CONSTANTS.MAX_SLEEP_HOURS}`);
      }
    }

    // Validate sleep quality
    if (data.sleepQuality !== null && data.sleepQuality !== undefined) {
      if (data.sleepQuality < MOOD_CONSTANTS.MIN_SLEEP_QUALITY || data.sleepQuality > MOOD_CONSTANTS.MAX_SLEEP_QUALITY) {
        errors.push(`Kualitas tidur harus antara ${MOOD_CONSTANTS.MIN_SLEEP_QUALITY} dan ${MOOD_CONSTANTS.MAX_SLEEP_QUALITY}`);
      }
    }

    // Validate stress level
    if (data.stressLevel !== null && data.stressLevel !== undefined) {
      if (data.stressLevel < MOOD_CONSTANTS.MIN_STRESS_LEVEL || data.stressLevel > MOOD_CONSTANTS.MAX_STRESS_LEVEL) {
        errors.push(`Stress level harus antara ${MOOD_CONSTANTS.MIN_STRESS_LEVEL} dan ${MOOD_CONSTANTS.MAX_STRESS_LEVEL}`);
      }
    }

    // Validate daily feeling
    if (data.dailyFeeling !== null && data.dailyFeeling !== undefined) {
      if (data.dailyFeeling < MOOD_CONSTANTS.MIN_DAILY_FEELING || data.dailyFeeling > MOOD_CONSTANTS.MAX_DAILY_FEELING) {
        errors.push(`Daily feeling harus antara ${MOOD_CONSTANTS.MIN_DAILY_FEELING} dan ${MOOD_CONSTANTS.MAX_DAILY_FEELING}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Handle and format errors with Sentry integration
   */
  private handleError(error: any, context?: { action?: string; metadata?: any }): MoodError {
    console.error('Mood service error:', error);

    // Add breadcrumb for mood service error
    addBreadcrumb(
      `Mood service error: ${context?.action || 'unknown_action'}`,
      'error',
      {
        errorCode: error.code,
        errorMessage: error.message,
        action: context?.action,
        // Don't include sensitive mood data
      }
    );

    // Report error to Sentry with privacy-safe context
    if (error instanceof Error) {
      reportError(error, {
        component: 'MoodService',
        action: context?.action || 'unknown_action',
        metadata: {
          errorCode: error.code,
          // Never include actual mood data
          hasMetadata: !!context?.metadata,
        }
      });
    }

    if (error.code === 'PGRST301') {
      return {
        code: 'DUPLICATE_ENTRY',
        message: 'Anda sudah mengisi mood untuk tanggal ini',
        details: error,
      };
    }

    if (error.code === '23505') {
      return {
        code: 'DUPLICATE_ENTRY',
        message: 'Anda sudah mengisi mood untuk tanggal ini',
        details: error,
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'Terjadi kesalahan yang tidak diketahui',
      details: error,
    };
  }

  /**
   * Get day of week from date string
   */
  private getDayOfWeek(dateString: string): DayOfWeek {
    const date = new Date(dateString);
    const dayIndex = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Map to Indonesian day abbreviations
    const dayMap: DayOfWeek[] = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];
    return dayMap[dayIndex];
  }

  /**
   * Get week start date (Monday) from any date
   */
  private getWeekStartDate(dateString: string): string {
    const date = new Date(dateString);
    const dayOfWeek = date.getDay();
    const diff = date.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Adjust for Monday start
    const monday = new Date(date.setDate(diff));
    return monday.toISOString().split('T')[0];
  }

  /**
   * Transform database entry to DailyMoodData
   */
  private transformFromDatabase(entry: MoodEntry | null): DailyMoodData {
    if (!entry) {
      return {
        mood: null,
        physicalHealth: null,
        sleepHours: null,
        sleepQuality: null,
        stressLevel: null,
        dailyFeeling: null,
      };
    }

    return {
      mood: entry.mood_level,
      physicalHealth: entry.physical_health,
      sleepHours: entry.sleep_hours,
      sleepQuality: entry.sleep_quality,
      stressLevel: entry.stress_level,
      dailyFeeling: entry.daily_feeling,
    };
  }

  /**
   * Transform DailyMoodData to database format
   */
  private transformToDatabase(data: DailyMoodData, entryDate: string): MoodEntryInput {
    return {
      entry_date: entryDate,
      day_of_week: this.getDayOfWeek(entryDate),
      mood_level: data.mood,
      physical_health: data.physicalHealth,
      sleep_hours: data.sleepHours,
      sleep_quality: data.sleepQuality,
      stress_level: data.stressLevel,
      daily_feeling: data.dailyFeeling,
    };
  }

  /**
   * Get mood entry for a specific date
   */
  async getMoodEntry(userId: string, date: string): Promise<DailyMoodData> {
    const { data, error } = await supabase
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .eq('entry_date', date)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw this.handleError(error);
    }

    return this.transformFromDatabase(data);
  }

  /**
   * Get today's mood entry for a user (timezone-aware)
   */
  async getTodayMoodEntry(userId: string, userTimezone?: string): Promise<DailyMoodData> {
    const today = getTodayInTimezone(userTimezone);
    console.log('[MoodService] Getting today\'s mood entry for date:', today, 'timezone:', getUserTimezone(userTimezone));
    return this.getMoodEntry(userId, today);
  }

  /**
   * Save or update mood entry for a specific date
   */
  async saveMoodEntry(userId: string, date: string, data: DailyMoodData): Promise<MoodEntry> {
    console.log('[MoodService] Starting saveMoodEntry');
    console.log('[MoodService] User ID:', userId);
    console.log('[MoodService] Date:', date);
    console.log('[MoodService] Input data:', JSON.stringify(data, null, 2));

    // Start performance transaction for mood entry saving
    const transaction = startTransaction('mood.save_entry', 'db');
    transaction.setTag('operation', 'save_mood_entry');
    transaction.setTag('entry_date', date);

    // Add breadcrumb for mood entry saving
    addBreadcrumb(
      'Mood entry save started',
      'mood',
      {
        entryDate: date,
        // Privacy-safe mood data summary
        hasMood: data.mood !== null && data.mood !== undefined,
        hasSleep: data.sleepHours !== null && data.sleepHours !== undefined,
        hasStress: data.stressLevel !== null && data.stressLevel !== undefined,
        hasFeeling: data.dailyFeeling !== null && data.dailyFeeling !== undefined,
      }
    );

    try {
      // Validate input
      const validation = this.validateMoodData(data);
      console.log('[MoodService] Validation result:', validation);

      if (!validation.isValid) {
        console.error('[MoodService] Validation failed:', validation.errors);
        transaction.setStatus('invalid_argument');
        addBreadcrumb(
          'Mood entry validation failed',
          'validation',
          {
            errorCount: validation.errors.length,
          }
        );
        throw {
          code: 'VALIDATION_ERROR',
          message: validation.errors.join(', '),
        };
      }

      const transformedData = this.transformToDatabase(data, date);
      console.log('[MoodService] Transformed data:', JSON.stringify(transformedData, null, 2));

      const entryData = {
        user_id: userId,
        ...transformedData,
      };

      console.log('[MoodService] Final entry data for database:', JSON.stringify(entryData, null, 2));

      // Use upsert to handle both create and update
      const { data: result, error } = await supabase
        .from('mood_entries')
        .upsert(entryData, {
          onConflict: 'user_id,entry_date',
          ignoreDuplicates: false,
        })
        .select()
        .single();

      if (error) {
        console.error('[MoodService] Database error:', error);
        transaction.setStatus('internal_error');
        throw this.handleError(error, {
          action: 'save_mood_entry',
          metadata: { entryDate: date }
        });
      }

      console.log('[MoodService] Successfully saved mood entry:', result);

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('mood_fields_saved', Object.keys(transformedData).length, 'none');

      addBreadcrumb(
        'Mood entry saved successfully',
        'mood',
        {
          entryId: result.id,
          entryDate: result.entry_date,
          fieldsCount: Object.keys(transformedData).length,
        }
      );

      return result;
    } catch (error) {
      // Check if we're offline and handle gracefully
      const isOffline = await moodMigration.isOfflineMode();
      if (isOffline) {
        console.log('[MoodService] Offline mode detected, saving locally');
        transaction.setStatus('ok');
        transaction.setTag('offline_mode', 'true');

        addBreadcrumb(
          'Mood entry saved offline',
          'mood',
          {
            entryDate: date,
            offlineMode: true,
          }
        );

        await moodMigration.handleOfflineEntry(date, data);

        // Return a mock entry for offline mode
        return {
          id: `offline_${date}_${Date.now()}`,
          user_id: userId,
          entry_date: date,
          day_of_week: this.getDayOfWeek(date),
          mood_level: data.mood,
          physical_health: data.physicalHealth,
          sleep_hours: data.sleepHours,
          sleep_quality: data.sleepQuality,
          stress_level: data.stressLevel,
          daily_feeling: data.dailyFeeling,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      }

      if (!transaction.status) {
        transaction.setStatus('internal_error');
      }
      throw error;
    } finally {
      transaction.finish();
    }
  }

  /**
   * Update specific fields of a mood entry
   */
  async updateMoodEntry(userId: string, date: string, updates: Partial<DailyMoodData>): Promise<MoodEntry> {
    // Validate input
    const validation = this.validateMoodData(updates);
    if (!validation.isValid) {
      throw {
        code: 'VALIDATION_ERROR',
        message: validation.errors.join(', '),
      };
    }

    // Transform updates to database format
    const updateData: Partial<MoodEntryUpdate> = {};
    if (updates.mood !== undefined) updateData.mood_level = updates.mood;
    if (updates.physicalHealth !== undefined) updateData.physical_health = updates.physicalHealth;
    if (updates.sleepHours !== undefined) updateData.sleep_hours = updates.sleepHours;
    if (updates.sleepQuality !== undefined) updateData.sleep_quality = updates.sleepQuality;
    if (updates.stressLevel !== undefined) updateData.stress_level = updates.stressLevel;
    if (updates.dailyFeeling !== undefined) updateData.daily_feeling = updates.dailyFeeling;

    const { data, error } = await supabase
      .from('mood_entries')
      .update(updateData)
      .eq('user_id', userId)
      .eq('entry_date', date)
      .select()
      .single();

    if (error) {
      throw this.handleError(error);
    }

    return data;
  }

  /**
   * Delete mood entry for a specific date
   */
  async deleteMoodEntry(userId: string, date: string): Promise<void> {
    const { error } = await supabase
      .from('mood_entries')
      .delete()
      .eq('user_id', userId)
      .eq('entry_date', date);

    if (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get weekly mood data starting from Monday
   */
  async getWeeklyMood(userId: string, weekStart: string): Promise<WeeklyMoodData> {
    const startDate = this.getWeekStartDate(weekStart);
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 6);
    const endDateString = endDate.toISOString().split('T')[0];

    const { data, error } = await supabase
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .gte('entry_date', startDate)
      .lte('entry_date', endDateString)
      .order('entry_date', { ascending: true });

    if (error) {
      throw this.handleError(error);
    }

    // Initialize empty weekly data
    const weeklyData: WeeklyMoodData = {
      Sen: this.transformFromDatabase(null),
      Sel: this.transformFromDatabase(null),
      Rab: this.transformFromDatabase(null),
      Kam: this.transformFromDatabase(null),
      Jum: this.transformFromDatabase(null),
      Sab: this.transformFromDatabase(null),
      Min: this.transformFromDatabase(null),
    };

    // Fill in the data we have
    (data || []).forEach(entry => {
      const dayOfWeek = entry.day_of_week as DayOfWeek;
      weeklyData[dayOfWeek] = this.transformFromDatabase(entry);
    });

    return weeklyData;
  }

  /**
   * Get current week's mood data (timezone-aware)
   */
  async getCurrentWeekMood(userId: string, userTimezone?: string): Promise<WeeklyMoodData> {
    const weekStart = getCurrentWeekStartInTimezone(userTimezone);
    console.log('[MoodService] Getting current week mood for week starting:', weekStart, 'timezone:', getUserTimezone(userTimezone));
    return this.getWeeklyMood(userId, weekStart);
  }

  /**
   * Save entire weekly mood data
   */
  async saveWeeklyMood(userId: string, weekStart: string, weeklyData: WeeklyMoodData): Promise<void> {
    console.log('[MoodService] Starting saveWeeklyMood');
    console.log('[MoodService] User ID:', userId);
    console.log('[MoodService] Week start:', weekStart);
    console.log('[MoodService] Weekly data:', JSON.stringify(weeklyData, null, 2));

    // Use weekStart directly - it's already calculated as Monday
    // Don't call getWeekStartDate() again as it causes double calculation
    const startDate = weekStart;
    console.log('[MoodService] Using week start date directly:', startDate);

    const promises: Promise<MoodEntry>[] = [];

    DAYS_OF_WEEK.forEach((day, index) => {
      const dayData = weeklyData[day];
      const date = new Date(startDate + 'T12:00:00'); // Add noon to avoid timezone edge cases
      date.setDate(date.getDate() + index);
      const dateString = date.toISOString().split('T')[0];

      console.log(`[MoodService] Processing ${day} (${dateString}):`, JSON.stringify(dayData, null, 2));

      // Only save if there's actual data
      const hasData = Object.values(dayData).some(value => value !== null);
      if (hasData) {
        console.log(`[MoodService] ${day} has data, adding to save queue`);
        promises.push(this.saveMoodEntry(userId, dateString, dayData));
      } else {
        console.log(`[MoodService] ${day} has no data, skipping`);
      }
    });

    console.log(`[MoodService] Saving ${promises.length} mood entries`);

    try {
      await Promise.all(promises);
      console.log('[MoodService] All mood entries saved successfully');
    } catch (error) {
      console.error('[MoodService] Failed to save mood entries:', error);
      throw error;
    }
  }

  /**
   * Get mood entries for a date range
   */
  async getMoodHistory(
    userId: string,
    startDate: string,
    endDate: string,
    limit: number = MOOD_CONSTANTS.HISTORY_PAGE_SIZE,
    offset: number = 0
  ): Promise<MoodEntry[]> {
    const { data, error } = await supabase
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .gte('entry_date', startDate)
      .lte('entry_date', endDate)
      .order('entry_date', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw this.handleError(error);
    }

    return data || [];
  }

  /**
   * Get mood trends for analysis
   */
  async getMoodTrends(
    userId: string,
    period: 'week' | 'month',
    count: number = 4
  ): Promise<MoodTrend[]> {
    const endDate = new Date();
    const startDate = new Date();

    if (period === 'week') {
      startDate.setDate(endDate.getDate() - (count * 7));
    } else {
      startDate.setMonth(endDate.getMonth() - count);
    }

    const { data, error } = await supabase
      .from('mood_entries')
      .select('*')
      .eq('user_id', userId)
      .gte('entry_date', startDate.toISOString().split('T')[0])
      .lte('entry_date', endDate.toISOString().split('T')[0])
      .order('entry_date', { ascending: true });

    if (error) {
      throw this.handleError(error);
    }

    // Group data by period and calculate averages
    const trends: MoodTrend[] = [];
    const entries = data || [];

    // This is a simplified implementation - in a real app you'd want more sophisticated grouping
    for (let i = 0; i < count; i++) {
      const periodStart = new Date(startDate);
      if (period === 'week') {
        periodStart.setDate(startDate.getDate() + (i * 7));
      } else {
        periodStart.setMonth(startDate.getMonth() + i);
      }

      const periodEnd = new Date(periodStart);
      if (period === 'week') {
        periodEnd.setDate(periodStart.getDate() + 6);
      } else {
        periodEnd.setMonth(periodStart.getMonth() + 1);
        periodEnd.setDate(0); // Last day of month
      }

      const periodEntries = entries.filter(entry => {
        const entryDate = new Date(entry.entry_date);
        return entryDate >= periodStart && entryDate <= periodEnd;
      });

      if (periodEntries.length > 0) {
        const avgMood = periodEntries.reduce((sum, e) => sum + (e.mood_level || 0), 0) / periodEntries.length;
        const avgSleepHours = periodEntries.reduce((sum, e) => sum + (e.sleep_hours || 0), 0) / periodEntries.length;
        const avgSleepQuality = periodEntries.reduce((sum, e) => sum + (e.sleep_quality || 0), 0) / periodEntries.length;
        const avgStressLevel = periodEntries.reduce((sum, e) => sum + (e.stress_level || 0), 0) / periodEntries.length;
        const avgDailyFeeling = periodEntries.reduce((sum, e) => sum + (e.daily_feeling || 0), 0) / periodEntries.length;
        const healthyDays = periodEntries.filter(e => e.physical_health === true).length;

        trends.push({
          period: periodStart.toISOString().split('T')[0],
          avgMood,
          avgSleepHours,
          avgSleepQuality,
          avgStressLevel,
          avgDailyFeeling,
          healthyDays,
          totalEntries: periodEntries.length,
        });
      }
    }

    return trends;
  }

  /**
   * Initialize mood service for a user (handle migration and sync)
   */
  async initializeForUser(userId: string): Promise<void> {
    try {
      console.log('[MoodService] Initializing for user:', userId);

      // Check if migration is needed
      const needsMigration = await moodMigration.needsMigration(userId);
      if (needsMigration) {
        console.log('[MoodService] Migration needed, starting migration...');
        const migrationStatus = await moodMigration.migrateToSupabase(userId);
        console.log('[MoodService] Migration completed:', migrationStatus);
      }

      // Check if sync is needed (for offline data)
      const needsSync = await moodMigration.needsSync();
      if (needsSync) {
        console.log('[MoodService] Sync needed, starting sync...');
        await moodMigration.syncWithSupabase(userId);
        console.log('[MoodService] Sync completed');
      }
    } catch (error) {
      console.error('[MoodService] Initialization failed:', error);
      // Don't throw error to prevent app from breaking
    }
  }

  /**
   * Get mood entry with offline fallback
   */
  async getMoodEntryWithFallback(userId: string, date: string): Promise<DailyMoodData> {
    try {
      return await this.getMoodEntry(userId, date);
    } catch (error) {
      console.log('[MoodService] Failed to get online data, checking offline fallback');
      const fallbackData = await moodMigration.getFallbackData(date);
      return fallbackData || this.transformFromDatabase(null);
    }
  }

  /**
   * Check sync status
   */
  async getSyncStatus(): Promise<{
    needsSync: boolean;
    lastSyncTime: string | null;
    pendingEntries: number;
  }> {
    const needsSync = await moodMigration.needsSync();
    const lastSyncTime = await moodMigration.getLastSyncTime();
    const localData = await moodMigration.getLocalMoodData();

    return {
      needsSync,
      lastSyncTime,
      pendingEntries: Object.keys(localData).length,
    };
  }
}

export const moodService = MoodService.getInstance();
