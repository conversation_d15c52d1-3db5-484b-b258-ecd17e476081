import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';
import { supabaseStorage } from './storage';
import { SUPABASE_CONFIG, validateEnvironment } from './env';

// Validate environment on import
const envValidation = validateEnvironment();
if (!envValidation.isValid) {
  console.error('[Supabase] Environment validation failed:', envValidation.errors);
  // Don't throw here, let the client creation handle it gracefully
}

// Enhanced logging for debugging storage issues
const environmentInfo = supabaseStorage.getEnvironmentInfo();
console.log('[Supabase] Storage configuration:', {
  type: supabaseStorage.getStorageType(),
  environment: environmentInfo
});

// Platform-specific configuration for OAuth handling
const isWeb = environmentInfo.isWeb;
const detectSessionInUrl = isWeb; // Enable for web, disable for mobile

console.log('[Supabase] OAuth configuration:', {
  platform: isWeb ? 'web' : 'mobile',
  detectSessionInUrl,
  reason: isWeb ? 'Web needs URL fragment processing for OAuth' : 'Mobile uses custom deep link handling'
});

// Create Supabase client with enhanced error handling
let supabase: any;

try {
  supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey, {
    auth: {
      storage: supabaseStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl, // Platform-specific: true for web, false for mobile
    },
  });

  console.log('[Supabase] Client created successfully with URL:', SUPABASE_CONFIG.url.substring(0, 30) + '...');

} catch (error) {
  console.error('[Supabase] Failed to create client:', error);

  // Create a fallback client without storage for SSR environments
  supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false, // Always false for fallback
    },
  });

  console.warn('[Supabase] Created fallback client without storage');
}

export { supabase };

// Enhanced helper function to check if Supabase is properly configured
export const validateSupabaseConnection = async (): Promise<{
  isValid: boolean;
  error?: string;
  storageInfo?: any;
}> => {
  try {
    // Get detailed storage information
    const storageAvailable = await supabaseStorage.isAvailable();
    const environmentInfo = supabaseStorage.getEnvironmentInfo();

    console.log('[Supabase] Storage validation:', {
      available: storageAvailable,
      environment: environmentInfo
    });

    // Attempt to get session with enhanced error handling
    let sessionResult;
    try {
      sessionResult = await supabase.auth.getSession();
    } catch (sessionError) {
      console.warn('[Supabase] Session retrieval failed:', sessionError);

      // In SSR environments, this might be expected
      if (environmentInfo.isSSR) {
        console.log('[Supabase] SSR environment detected, session failure expected');
        return {
          isValid: true,
          storageInfo: environmentInfo,
        };
      }

      throw sessionError;
    }

    const { data, error } = sessionResult;

    if (error) {
      if (error.message.includes('Invalid API key')) {
        return {
          isValid: false,
          error: 'Invalid Supabase credentials. Please check your API keys.',
          storageInfo: environmentInfo,
        };
      }

      // Log other errors but don't necessarily fail validation
      console.warn('[Supabase] Session error (may be expected):', error.message);
    }

    return {
      isValid: true,
      storageInfo: environmentInfo,
    };
  } catch (error) {
    console.error('[Supabase] Validation error:', error);
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      storageInfo: supabaseStorage.getEnvironmentInfo(),
    };
  }
};