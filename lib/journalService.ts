import { supabase } from './supabase';
import type {
  JournalQuestion,
  JournalEntry,
  JournalEntryInput,
  JournalEntryUpdate,
  JournalEntryWithQuestion,
  JournalEntryGroup,
  JournalError,
  JournalValidationResult,
} from '@/types/journal';
import { JOURNAL_CONSTANTS } from '@/types/journal';
import { startTransaction, addBreadcrumb, reportError } from '@/lib/sentryConfig';

class JournalService {
  private static instance: JournalService;

  static getInstance(): JournalService {
    if (!JournalService.instance) {
      JournalService.instance = new JournalService();
    }
    return JournalService.instance;
  }

  /**
   * Validate journal entry input
   */
  private validateEntry(answer: string): JournalValidationResult {
    const errors: string[] = [];

    if (!answer || answer.trim().length < JOURNAL_CONSTANTS.MIN_ANSWER_LENGTH) {
      errors.push('Jawaban tidak boleh kosong');
    }

    if (answer.length > JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH) {
      errors.push(`Jawaban tidak boleh lebih dari ${JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} karakter`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Handle and format errors with Sentry integration
   */
  private handleError(error: any, context?: { action?: string; userId?: string; metadata?: any }): JournalError {
    console.error('Journal service error:', error);

    // Add breadcrumb for journal error context
    addBreadcrumb(
      `Journal service error: ${context?.action || 'unknown_action'}`,
      'error',
      {
        errorCode: error.code,
        errorMessage: error.message,
        action: context?.action,
        // Don't include userId or sensitive metadata for privacy
      }
    );

    // Report error to Sentry with privacy-safe context
    if (error instanceof Error) {
      reportError(error, {
        component: 'JournalService',
        action: context?.action || 'unknown_action',
        metadata: {
          errorCode: error.code,
          // Never include actual journal content or user data
          hasMetadata: !!context?.metadata,
        }
      });
    }

    if (error.code === 'PGRST301') {
      return {
        code: 'DUPLICATE_ENTRY',
        message: 'Anda sudah mengisi journal untuk pertanyaan ini hari ini',
        details: error,
      };
    }

    if (error.code === '23505') {
      return {
        code: 'DUPLICATE_ENTRY',
        message: 'Anda sudah mengisi journal untuk pertanyaan ini hari ini',
        details: error,
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'Terjadi kesalahan yang tidak diketahui',
      details: error,
    };
  }

  /**
   * Get all active journal questions (system only)
   */
  async getJournalQuestions(): Promise<JournalQuestion[]> {
    const { data, error } = await supabase
      .from('journal_questions')
      .select('*')
      .eq('is_active', true)
      .eq('field_type', 'system')
      .order('question_order', { ascending: true });

    if (error) {
      throw this.handleError(error);
    }

    return data || [];
  }

  /**
   * Get all fields for a user (system + custom)
   */
  async getUserJournalFields(userId: string): Promise<JournalQuestion[]> {
    try {
      const { data, error } = await supabase.rpc('get_user_journal_fields', {
        target_user_id: userId
      });

      if (error) {
        throw this.handleError(error);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching user journal fields:', error);
      throw error;
    }
  }

  /**
   * Get today's journal entries for a user
   */
  async getTodayEntries(userId: string): Promise<JournalEntryWithQuestion[]> {
    const today = new Date().toISOString().split('T')[0];

    const { data, error } = await supabase
      .from('journal_entries')
      .select(`
        *,
        question:journal_questions(*)
      `)
      .eq('user_id', userId)
      .eq('entry_date', today)
      .order('created_at', { ascending: true });

    if (error) {
      throw this.handleError(error);
    }

    return (data || []).map(entry => ({
      ...entry,
      question: entry.question as JournalQuestion,
    }));
  }

  /**
   * Get journal entries grouped by date for history view
   */
  async getJournalHistory(
    userId: string,
    limit: number = JOURNAL_CONSTANTS.HISTORY_PAGE_SIZE,
    offset: number = 0
  ): Promise<JournalEntryGroup[]> {
    console.log('[JournalService] getJournalHistory called:', {
      userId,
      limit,
      offset,
      timestamp: new Date().toISOString()
    });

    // Start performance transaction for journal history loading
    const transaction = startTransaction('journal.get_history', 'db');
    transaction.setTag('operation', 'get_journal_history');
    transaction.setTag('limit', limit.toString());
    transaction.setTag('offset', offset.toString());

    // Add breadcrumb for journal history request
    addBreadcrumb(
      'Journal history requested',
      'journal',
      {
        limit,
        offset,
        // Don't include userId for privacy
      }
    );

    try {
      console.log('[JournalService] Executing Supabase query...');

      const { data, error } = await supabase
        .from('journal_entries')
        .select(`
          *,
          question:journal_questions(*)
        `)
        .eq('user_id', userId)
        .order('entry_date', { ascending: false })
        .order('created_at', { ascending: true })
        .range(offset, offset + limit - 1);

      console.log('[JournalService] Supabase query completed:', {
        dataLength: data?.length || 0,
        error: error?.message || null,
        hasData: !!data,
        dataType: typeof data,
        isArray: Array.isArray(data),
        firstEntry: data?.[0] ? {
          id: data[0].id,
          entry_date: data[0].entry_date,
          hasQuestion: !!data[0].question
        } : null
      });

      if (error) {
        console.error('[JournalService] Supabase error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        transaction.setStatus('internal_error');
        throw this.handleError(error, {
          action: 'get_journal_history',
          metadata: { limit, offset }
        });
      }

      // Group entries by date
      const groupedEntries: { [key: string]: JournalEntryWithQuestion[] } = {};

      (data || []).forEach(entry => {
        const entryWithQuestion: JournalEntryWithQuestion = {
          ...entry,
          question: entry.question as JournalQuestion,
        };

        if (!groupedEntries[entry.entry_date]) {
          groupedEntries[entry.entry_date] = [];
        }
        groupedEntries[entry.entry_date].push(entryWithQuestion);
      });

      // Convert to array format and format dates
      const result = Object.entries(groupedEntries).map(([date, entries]) => ({
        date: this.formatDateIndonesian(date),
        entries,
      }));

      console.log('[JournalService] Processed result:', {
        groupCount: result.length,
        totalEntries: result.reduce((sum, group) => sum + group.entries.length, 0)
      });

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('entries_loaded', result.reduce((sum, group) => sum + group.entries.length, 0), 'none');
      transaction.setMeasurement('groups_created', result.length, 'none');

      addBreadcrumb(
        'Journal history loaded successfully',
        'journal',
        {
          groupCount: result.length,
          totalEntries: result.reduce((sum, group) => sum + group.entries.length, 0),
        }
      );

      return result;
    } catch (error) {
      console.error('[JournalService] getJournalHistory error:', error);
      transaction.setStatus('internal_error');
      throw error;
    } finally {
      transaction.finish();
    }
  }

  /**
   * Create a new journal entry
   */
  async createEntry(userId: string, input: JournalEntryInput): Promise<JournalEntry> {
    // Start performance transaction for journal entry creation
    const transaction = startTransaction('journal.create_entry', 'db');
    transaction.setTag('operation', 'create_journal_entry');
    transaction.setTag('question_id', input.question_id);

    // Add breadcrumb for journal entry creation
    addBreadcrumb(
      'Journal entry creation started',
      'journal',
      {
        questionId: input.question_id,
        entryDate: input.entry_date,
        // Never include actual answer content for privacy
        hasAnswer: !!input.answer,
        answerLength: input.answer?.length || 0,
      }
    );

    try {
      // Validate input
      const validation = this.validateEntry(input.answer);
      if (!validation.isValid) {
        transaction.setStatus('invalid_argument');
        addBreadcrumb(
          'Journal entry validation failed',
          'validation',
          {
            errorCount: validation.errors.length,
            // Don't include actual validation errors as they might contain sensitive info
          }
        );
        throw {
          code: 'VALIDATION_ERROR',
          message: validation.errors.join(', '),
        };
      }

      const entryData = {
        user_id: userId,
        question_id: input.question_id,
        answer: input.answer.trim(),
        entry_date: input.entry_date || new Date().toISOString().split('T')[0],
      };

      const { data, error } = await supabase
        .from('journal_entries')
        .insert(entryData)
        .select()
        .single();

      if (error) {
        transaction.setStatus('internal_error');
        throw this.handleError(error, {
          action: 'create_journal_entry',
          metadata: { questionId: input.question_id }
        });
      }

      // Set success metrics
      transaction.setStatus('ok');
      transaction.setMeasurement('answer_length', input.answer.length, 'character');

      addBreadcrumb(
        'Journal entry created successfully',
        'journal',
        {
          entryId: data.id,
          questionId: input.question_id,
          entryDate: data.entry_date,
        }
      );

      return data;
    } catch (error) {
      if (!transaction.status) {
        transaction.setStatus('internal_error');
      }
      throw error;
    } finally {
      transaction.finish();
    }
  }

  /**
   * Update an existing journal entry
   */
  async updateEntry(entryId: string, answer: string): Promise<JournalEntry> {
    // Validate input
    const validation = this.validateEntry(answer);
    if (!validation.isValid) {
      throw {
        code: 'VALIDATION_ERROR',
        message: validation.errors.join(', '),
      };
    }

    const { data, error } = await supabase
      .from('journal_entries')
      .update({ answer: answer.trim() })
      .eq('id', entryId)
      .select()
      .single();

    if (error) {
      throw this.handleError(error);
    }

    return data;
  }

  /**
   * Delete a journal entry
   */
  async deleteEntry(entryId: string): Promise<void> {
    const { error } = await supabase
      .from('journal_entries')
      .delete()
      .eq('id', entryId);

    if (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get entries for a specific date range
   */
  async getEntriesByDateRange(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<JournalEntryWithQuestion[]> {
    const { data, error } = await supabase
      .from('journal_entries')
      .select(`
        *,
        question:journal_questions(*)
      `)
      .eq('user_id', userId)
      .gte('entry_date', startDate)
      .lte('entry_date', endDate)
      .order('entry_date', { ascending: false })
      .order('created_at', { ascending: true });

    if (error) {
      throw this.handleError(error);
    }

    return (data || []).map(entry => ({
      ...entry,
      question: entry.question as JournalQuestion,
    }));
  }

  /**
   * Get entry dates for a specific month (for calendar view)
   */
  async getEntryDatesForMonth(
    userId: string,
    year: number,
    month: number
  ): Promise<string[]> {
    // Get first and last day of the month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const startDate = firstDay.toISOString().split('T')[0];
    const endDate = lastDay.toISOString().split('T')[0];

    const { data, error } = await supabase
      .from('journal_entries')
      .select('entry_date')
      .eq('user_id', userId)
      .gte('entry_date', startDate)
      .lte('entry_date', endDate);

    if (error) {
      throw this.handleError(error);
    }

    // Return unique dates
    return [...new Set((data || []).map(entry => entry.entry_date))];
  }

  /**
   * Format date to Indonesian format
   */
  private formatDateIndonesian(dateString: string): string {
    const date = new Date(dateString);
    const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
    const months = [
      'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
      'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    const dayName = days[date.getDay()];
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${dayName}, ${day} ${month} ${year}`;
  }
}

export const journalService = JournalService.getInstance();
