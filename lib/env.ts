/**
 * Environment Configuration
 * Centralized environment variable management with fallbacks
 */

// Environment variable getter with secure logging
const getEnvVar = (key: string, fallback?: string): string => {
  const value = process.env[key] || fallback;
  // Only log in development, never log actual values
  if (process.env.NODE_ENV === 'development') {
    console.log(`[ENV] ${key}:`, value ? 'SET' : 'MISSING');
  }
  return value || '';
};

// Boolean environment variable getter with secure logging
const getBooleanEnvVar = (key: string, fallback: boolean = false): boolean => {
  const value = process.env[key];
  if (value === undefined) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[ENV] ${key}:`, 'MISSING (using fallback:', fallback, ')');
    }
    return fallback;
  }
  const boolValue = value.toLowerCase() === 'true';
  if (process.env.NODE_ENV === 'development') {
    console.log(`[ENV] ${key}:`, boolValue);
  }
  return boolValue;
};

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: getEnvVar(
    'EXPO_PUBLIC_SUPABASE_URL',
    'https://smzennhnotmrjdfvwmaa.supabase.co'
  ),
  anonKey: getEnvVar(
    'EXPO_PUBLIC_SUPABASE_ANON_KEY',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNtemVubmhub3RtcmpkZnZ3bWFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1NDY5NTgsImV4cCI6MjA2ODEyMjk1OH0.dyAHy_f6hd82HHv41Y_xYCWO0eBpWIOaZ1dtuvbHKWM'
  ),
};

// Convex Configuration
export const CONVEX_CONFIG = {
  url: getEnvVar('EXPO_PUBLIC_CONVEX_URL', ''),
};

// API Keys (optional)
export const API_KEYS = {
  groq: getEnvVar('EXPO_PUBLIC_GROQ_API_KEY'),
  elevenlabs: getEnvVar('EXPO_PUBLIC_ELEVENLABS_AGENT_ID'),
};

// App Configuration
export const APP_CONFIG = {
  version: getEnvVar('EXPO_PUBLIC_APP_VERSION', '1.0.0'),
  environment: getEnvVar('EXPO_PUBLIC_ENVIRONMENT', __DEV__ ? 'development' : 'production'),
};

// Authentication Feature Flags
export const AUTH_CONFIG = {
  // Google Sign-in functionality (default: disabled for security)
  googleSigninEnabled: getBooleanEnvVar('EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED', true),

  // User registration/sign-up functionality (default: disabled for security)
  signupEnabled: getBooleanEnvVar('EXPO_PUBLIC_SIGNUP_ENABLED', true),

  // Convex Auth migration flag (default: disabled for gradual rollout)
  useConvexAuth: getBooleanEnvVar('EXPO_PUBLIC_USE_CONVEX_AUTH', false),
};

// Environment validation
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!SUPABASE_CONFIG.url) {
    errors.push('EXPO_PUBLIC_SUPABASE_URL is required');
  }

  if (!SUPABASE_CONFIG.anonKey) {
    errors.push('EXPO_PUBLIC_SUPABASE_ANON_KEY is required');
  }

  // Only log validation in development
  if (process.env.NODE_ENV === 'development') {
    console.log('[ENV] Validation result:', {
      isValid: errors.length === 0,
      errorCount: errors.length,
      config: {
        supabaseUrl: SUPABASE_CONFIG.url ? 'SET' : 'MISSING',
        supabaseKey: SUPABASE_CONFIG.anonKey ? 'SET' : 'MISSING',
        groqKey: API_KEYS.groq ? 'SET' : 'MISSING',
        elevenlabsKey: API_KEYS.elevenlabs ? 'SET' : 'MISSING',
        googleSigninEnabled: AUTH_CONFIG.googleSigninEnabled,
        signupEnabled: AUTH_CONFIG.signupEnabled,
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Initialize environment validation on import
validateEnvironment();
