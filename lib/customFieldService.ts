import { supabase } from './supabase';
import type {
  JournalQuestion,
  JournalCustomField,
  CustomFieldInput,
  CustomFieldUpdate,
  FieldType,
  CustomFieldConfig,
} from '@/types/journal';
import { JOURNAL_CONSTANTS } from '@/types/journal';

export class CustomFieldService {
  private static instance: CustomFieldService;

  static getInstance(): CustomFieldService {
    if (!CustomFieldService.instance) {
      CustomFieldService.instance = new CustomFieldService();
    }
    return CustomFieldService.instance;
  }

  /**
   * Get all fields for a user (system + custom)
   */
  async getUserFields(userId: string): Promise<JournalQuestion[]> {
    try {
      const { data, error } = await supabase.rpc('get_user_journal_fields', {
        target_user_id: userId
      });

      if (error) {
        throw new Error(`Failed to fetch user fields: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching user fields:', error);
      throw error;
    }
  }

  /**
   * Get only custom fields for a user
   */
  async getCustomFields(userId: string): Promise<JournalCustomField[]> {
    try {
      const { data, error } = await supabase
        .from('journal_questions')
        .select('*')
        .eq('user_id', userId)
        .in('field_type', ['custom_question', 'custom_title'])
        .eq('is_active', true)
        .eq('is_archived', false)
        .order('question_order', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch custom fields: ${error.message}`);
      }

      return (data || []) as JournalCustomField[];
    } catch (error) {
      console.error('Error fetching custom fields:', error);
      throw error;
    }
  }

  /**
   * Create a new custom field
   */
  async createCustomField(userId: string, input: CustomFieldInput): Promise<JournalCustomField> {
    try {
      // Validate input
      this.validateCustomFieldInput(input);

      // Check user's custom field limit
      await this.checkCustomFieldLimit(userId);

      const fieldData = {
        user_id: userId,
        question_text: input.question_text.trim(),
        field_type: input.field_type,
        custom_config: input.custom_config || {},
        question_order: input.question_order,
        is_active: true,
        is_archived: false,
      };

      const { data, error } = await supabase
        .from('journal_questions')
        .insert(fieldData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create custom field: ${error.message}`);
      }

      return data as JournalCustomField;
    } catch (error) {
      console.error('Error creating custom field:', error);
      throw error;
    }
  }

  /**
   * Update an existing custom field
   */
  async updateCustomField(
    userId: string,
    fieldId: string,
    update: CustomFieldUpdate
  ): Promise<JournalCustomField> {
    try {
      // Validate update data
      if (update.question_text) {
        this.validateQuestionText(update.question_text);
      }

      const updateData: any = {
        updated_at: new Date().toISOString(),
      };

      if (update.question_text !== undefined) {
        updateData.question_text = update.question_text.trim();
      }
      if (update.custom_config !== undefined) {
        updateData.custom_config = update.custom_config;
      }
      if (update.question_order !== undefined) {
        updateData.question_order = update.question_order;
      }
      if (update.is_active !== undefined) {
        updateData.is_active = update.is_active;
      }

      const { data, error } = await supabase
        .from('journal_questions')
        .update(updateData)
        .eq('id', fieldId)
        .eq('user_id', userId)
        .in('field_type', ['custom_question', 'custom_title'])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update custom field: ${error.message}`);
      }

      if (!data) {
        throw new Error('Custom field not found or access denied');
      }

      return data as JournalCustomField;
    } catch (error) {
      console.error('Error updating custom field:', error);
      throw error;
    }
  }

  /**
   * Archive (soft delete) a custom field
   */
  async archiveCustomField(userId: string, fieldId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('journal_questions')
        .update({
          is_archived: true,
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('id', fieldId)
        .eq('user_id', userId)
        .in('field_type', ['custom_question', 'custom_title']);

      if (error) {
        throw new Error(`Failed to archive custom field: ${error.message}`);
      }
    } catch (error) {
      console.error('Error archiving custom field:', error);
      throw error;
    }
  }

  /**
   * Reorder custom fields
   */
  async reorderCustomFields(
    userId: string,
    fieldOrders: Array<{ id: string; order: number }>
  ): Promise<void> {
    try {
      const updates = fieldOrders.map(({ id, order }) => ({
        id,
        question_order: order,
        updated_at: new Date().toISOString(),
      }));

      for (const update of updates) {
        const { error } = await supabase
          .from('journal_questions')
          .update({
            question_order: update.question_order,
            updated_at: update.updated_at,
          })
          .eq('id', update.id)
          .eq('user_id', userId)
          .in('field_type', ['custom_question', 'custom_title']);

        if (error) {
          throw new Error(`Failed to reorder field ${update.id}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('Error reordering custom fields:', error);
      throw error;
    }
  }

  /**
   * Validate custom field input
   */
  private validateCustomFieldInput(input: CustomFieldInput): void {
    if (!input.question_text || input.question_text.trim().length === 0) {
      throw new Error('Question text is required');
    }

    this.validateQuestionText(input.question_text);

    if (!['custom_question', 'custom_title'].includes(input.field_type)) {
      throw new Error('Invalid field type');
    }

    if (input.custom_config) {
      this.validateCustomConfig(input.custom_config, input.field_type);
    }
  }

  /**
   * Validate question text
   */
  private validateQuestionText(text: string): void {
    if (text.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH) {
      throw new Error(
        `Question text too long. Maximum ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH} characters allowed`
      );
    }
  }

  /**
   * Validate custom configuration
   */
  private validateCustomConfig(config: CustomFieldConfig, fieldType: FieldType): void {
    if (config.placeholder && config.placeholder.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH) {
      throw new Error(
        `Placeholder too long. Maximum ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH} characters allowed`
      );
    }

    if (config.maxLength && config.maxLength > JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH) {
      throw new Error(`Max length cannot exceed ${JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} characters`);
    }

    if (fieldType === 'custom_title' && config.maxLength && config.maxLength > 100) {
      throw new Error('Title fields cannot have max length greater than 100 characters');
    }
  }

  /**
   * Check if user has reached custom field limit
   */
  private async checkCustomFieldLimit(userId: string): Promise<void> {
    const { count, error } = await supabase
      .from('journal_questions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .in('field_type', ['custom_question', 'custom_title'])
      .eq('is_active', true)
      .eq('is_archived', false);

    if (error) {
      throw new Error(`Failed to check field limit: ${error.message}`);
    }

    if ((count || 0) >= JOURNAL_CONSTANTS.MAX_CUSTOM_FIELDS_PER_USER) {
      throw new Error(
        `Maximum ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELDS_PER_USER} custom fields allowed per user`
      );
    }
  }
}

// Export singleton instance
export const customFieldService = CustomFieldService.getInstance();
