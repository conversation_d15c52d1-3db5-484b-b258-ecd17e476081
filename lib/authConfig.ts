/**
 * Authentication Feature Flags
 *
 * Configuration to control authentication features via environment variables.
 * Set EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED=true and EXPO_PUBLIC_SIGNUP_ENABLED=true to enable features.
 */

import { AUTH_CONFIG } from './env';

/**
 * Helper function to check if Google sign-in is enabled
 */
export const isGoogleSigninEnabled = (): boolean => {
  return AUTH_CONFIG.googleSigninEnabled;
};

/**
 * Helper function to check if sign-up is enabled
 */
export const isSignupEnabled = (): boolean => {
  return AUTH_CONFIG.signupEnabled;
};

// Export the config for direct access if needed
export const AUTH_FEATURES = AUTH_CONFIG;
