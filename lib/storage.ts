/**
 * Enhanced Custom Storage Adapter for Supabase
 *
 * This adapter provides robust cross-platform storage compatibility:
 * - Uses localStorage for web environments
 * - Uses AsyncStorage for React Native environments
 * - Handles SSR/build-time scenarios gracefully
 * - Enhanced error handling and fallback mechanisms
 *
 * Fixes the "ReferenceError: window is not defined" error that occurs
 * when AsyncStorage tries to access window.localStorage during Expo web builds.
 */

interface StorageAdapter {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
}

export class SupabaseStorage implements StorageAdapter {
  private _asyncStorageCache: any = null;
  private _asyncStorageLoadAttempted = false;
  private _isSSR = false;

  constructor() {
    // Detect SSR environment early
    this._isSSR = typeof window === 'undefined' && typeof global !== 'undefined';
    if (this._isSSR) {
      console.log('[SupabaseStorage] SSR environment detected, using fallback storage');
    }
  }

  private isWeb(): boolean {
    // Enhanced web detection with SSR safety
    try {
      return !this._isSSR &&
             typeof window !== 'undefined' &&
             typeof window.localStorage !== 'undefined' &&
             window.localStorage !== null;
    } catch (error) {
      console.warn('[SupabaseStorage] Web detection failed:', error);
      return false;
    }
  }

  private isReactNative(): boolean {
    try {
      // Check for React Native environment
      return typeof navigator !== 'undefined' &&
             navigator.product === 'ReactNative';
    } catch (error) {
      return false;
    }
  }

  private isNode(): boolean {
    // Detect Node.js environment (SSR/build time)
    return typeof process !== 'undefined' &&
           process.versions &&
           process.versions.node;
  }

  private async getAsyncStorage() {
    // Return cached instance if available
    if (this._asyncStorageCache) {
      return this._asyncStorageCache;
    }

    // Don't attempt to load AsyncStorage in web or SSR environments
    if (this.isWeb() || this._isSSR || this.isNode()) {
      return null;
    }

    // Only attempt to load once to avoid repeated failures
    if (this._asyncStorageLoadAttempted) {
      return null;
    }

    this._asyncStorageLoadAttempted = true;

    try {
      // Dynamic import with enhanced error handling
      const AsyncStorageModule = await import('@react-native-async-storage/async-storage');
      this._asyncStorageCache = AsyncStorageModule.default || AsyncStorageModule;

      // Verify the imported module has the expected methods
      if (this._asyncStorageCache &&
          typeof this._asyncStorageCache.getItem === 'function' &&
          typeof this._asyncStorageCache.setItem === 'function') {
        console.log('[SupabaseStorage] AsyncStorage loaded successfully');
        return this._asyncStorageCache;
      } else {
        console.warn('[SupabaseStorage] AsyncStorage module invalid');
        this._asyncStorageCache = null;
        return null;
      }
    } catch (error) {
      console.warn('[SupabaseStorage] AsyncStorage not available:', error);
      this._asyncStorageCache = null;
      return null;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      // SSR/Node environment - return null gracefully
      if (this._isSSR || this.isNode()) {
        console.debug('[SupabaseStorage] SSR environment, returning null for getItem:', key);
        return null;
      }

      // Web environment - use localStorage with enhanced error handling
      if (this.isWeb()) {
        try {
          const value = localStorage.getItem(key);
          console.debug('[SupabaseStorage] localStorage getItem:', key, value ? 'found' : 'not found');
          return value;
        } catch (localStorageError) {
          console.warn('[SupabaseStorage] localStorage getItem failed:', localStorageError);
          return null;
        }
      }

      // React Native environment - use AsyncStorage
      const AsyncStorage = await this.getAsyncStorage();
      if (AsyncStorage) {
        try {
          const value = await AsyncStorage.getItem(key);
          console.debug('[SupabaseStorage] AsyncStorage getItem:', key, value ? 'found' : 'not found');
          return value;
        } catch (asyncStorageError) {
          console.warn('[SupabaseStorage] AsyncStorage getItem failed:', asyncStorageError);
          return null;
        }
      }

      // Fallback for unsupported environments
      console.warn('[SupabaseStorage] No storage available for getItem, key:', key);
      return null;
    } catch (error) {
      console.error('[SupabaseStorage] Unexpected error in getItem:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      // SSR/Node environment - silently ignore
      if (this._isSSR || this.isNode()) {
        console.debug('[SupabaseStorage] SSR environment, ignoring setItem:', key);
        return;
      }

      // Web environment - use localStorage with enhanced error handling
      if (this.isWeb()) {
        try {
          localStorage.setItem(key, value);
          console.debug('[SupabaseStorage] localStorage setItem successful:', key);
          return;
        } catch (localStorageError) {
          console.warn('[SupabaseStorage] localStorage setItem failed:', localStorageError);
          // Don't throw, just log the error
          return;
        }
      }

      // React Native environment - use AsyncStorage
      const AsyncStorage = await this.getAsyncStorage();
      if (AsyncStorage) {
        try {
          await AsyncStorage.setItem(key, value);
          console.debug('[SupabaseStorage] AsyncStorage setItem successful:', key);
          return;
        } catch (asyncStorageError) {
          console.warn('[SupabaseStorage] AsyncStorage setItem failed:', asyncStorageError);
          return;
        }
      }

      // Fallback for unsupported environments
      console.warn('[SupabaseStorage] No storage available for setItem, key:', key);
    } catch (error) {
      console.error('[SupabaseStorage] Unexpected error in setItem:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      // SSR/Node environment - silently ignore
      if (this._isSSR || this.isNode()) {
        console.debug('[SupabaseStorage] SSR environment, ignoring removeItem:', key);
        return;
      }

      // Web environment - use localStorage with enhanced error handling
      if (this.isWeb()) {
        try {
          localStorage.removeItem(key);
          console.debug('[SupabaseStorage] localStorage removeItem successful:', key);
          return;
        } catch (localStorageError) {
          console.warn('[SupabaseStorage] localStorage removeItem failed:', localStorageError);
          return;
        }
      }

      // React Native environment - use AsyncStorage
      const AsyncStorage = await this.getAsyncStorage();
      if (AsyncStorage) {
        try {
          await AsyncStorage.removeItem(key);
          console.debug('[SupabaseStorage] AsyncStorage removeItem successful:', key);
          return;
        } catch (asyncStorageError) {
          console.warn('[SupabaseStorage] AsyncStorage removeItem failed:', asyncStorageError);
          return;
        }
      }

      // Fallback for unsupported environments
      console.warn('[SupabaseStorage] No storage available for removeItem, key:', key);
    } catch (error) {
      console.error('[SupabaseStorage] Unexpected error in removeItem:', error);
    }
  }

  /**
   * Enhanced utility method to check if storage is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      // SSR/Node environment - storage not available
      if (this._isSSR || this.isNode()) {
        return false;
      }

      // Web environment
      if (this.isWeb()) {
        try {
          // Test localStorage availability
          const testKey = '__supabase_storage_test__';
          localStorage.setItem(testKey, 'test');
          localStorage.removeItem(testKey);
          return true;
        } catch (error) {
          console.warn('[SupabaseStorage] localStorage test failed:', error);
          return false;
        }
      }

      // React Native environment
      const AsyncStorage = await this.getAsyncStorage();
      if (AsyncStorage) {
        try {
          // Test AsyncStorage availability
          const testKey = '__supabase_storage_test__';
          await AsyncStorage.setItem(testKey, 'test');
          await AsyncStorage.removeItem(testKey);
          return true;
        } catch (error) {
          console.warn('[SupabaseStorage] AsyncStorage test failed:', error);
          return false;
        }
      }

      return false;
    } catch (error) {
      console.error('[SupabaseStorage] Error checking storage availability:', error);
      return false;
    }
  }

  /**
   * Enhanced storage type detection for debugging
   */
  getStorageType(): string {
    if (this._isSSR || this.isNode()) {
      return 'none (SSR)';
    }
    if (this.isWeb()) {
      return 'localStorage';
    }
    if (this.isReactNative()) {
      return 'AsyncStorage';
    }
    return 'none';
  }

  /**
   * Get detailed environment information for debugging
   */
  getEnvironmentInfo(): {
    isSSR: boolean;
    isWeb: boolean;
    isReactNative: boolean;
    isNode: boolean;
    storageType: string;
    hasWindow: boolean;
    hasNavigator: boolean;
    hasProcess: boolean;
  } {
    return {
      isSSR: this._isSSR,
      isWeb: this.isWeb(),
      isReactNative: this.isReactNative(),
      isNode: this.isNode(),
      storageType: this.getStorageType(),
      hasWindow: typeof window !== 'undefined',
      hasNavigator: typeof navigator !== 'undefined',
      hasProcess: typeof process !== 'undefined',
    };
  }
}

// Export a singleton instance
export const supabaseStorage = new SupabaseStorage();

// Export the class for custom instances if needed
export default SupabaseStorage;
