/**
 * Performance Monitoring Service
 * Comprehensive performance tracking for critical user journeys
 */

import { startTransaction, addBreadcrumb, reportMessage } from '@/lib/sentryConfig';
import * as Sentry from '@sentry/react-native';

export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: number;
  networkRequests?: number;
  errors?: number;
  customMetrics?: Record<string, number>;
}

export interface UserJourneyStep {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
}

export interface UserJourney {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  steps: UserJourneyStep[];
  success: boolean;
  metadata?: Record<string, any>;
}

class PerformanceMonitoringService {
  private activeTransactions: Map<string, any> = new Map();
  private activeJourneys: Map<string, UserJourney> = new Map();
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();

  /**
   * Start monitoring a user journey
   */
  startUserJourney(journeyId: string, journeyName: string, metadata?: Record<string, any>): void {
    const startTime = Date.now();
    
    const journey: UserJourney = {
      id: journeyId,
      name: journeyName,
      startTime,
      steps: [],
      success: false,
      metadata
    };

    this.activeJourneys.set(journeyId, journey);

    // Start Sentry transaction for the journey
    const transaction = startTransaction(`journey.${journeyName}`, 'user_journey');
    transaction.setTag('journey_id', journeyId);
    transaction.setTag('journey_name', journeyName);
    
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        transaction.setTag(key, String(value));
      });
    }

    this.activeTransactions.set(journeyId, transaction);

    addBreadcrumb(
      `User journey started: ${journeyName}`,
      'performance',
      {
        journeyId,
        journeyName,
        metadata
      }
    );

    console.log(`[PerformanceMonitoring] Started journey: ${journeyName} (${journeyId})`);
  }

  /**
   * Add a step to an active user journey
   */
  addJourneyStep(journeyId: string, stepName: string, success: boolean = true, metadata?: Record<string, any>): void {
    const journey = this.activeJourneys.get(journeyId);
    const transaction = this.activeTransactions.get(journeyId);
    
    if (!journey || !transaction) {
      console.warn(`[PerformanceMonitoring] Journey not found: ${journeyId}`);
      return;
    }

    const now = Date.now();
    const step: UserJourneyStep = {
      name: stepName,
      startTime: now,
      endTime: now,
      duration: 0,
      success,
      metadata
    };

    journey.steps.push(step);

    // Add span to transaction
    const span = transaction.startChild({
      op: 'journey_step',
      description: stepName
    });
    
    span.setTag('step_name', stepName);
    span.setTag('success', success);
    span.setStatus(success ? 'ok' : 'internal_error');
    span.finish();

    addBreadcrumb(
      `Journey step: ${stepName}`,
      'performance',
      {
        journeyId,
        stepName,
        success,
        stepCount: journey.steps.length,
        metadata
      }
    );

    console.log(`[PerformanceMonitoring] Journey step: ${stepName} (${success ? 'success' : 'failed'})`);
  }

  /**
   * Complete a user journey
   */
  completeUserJourney(journeyId: string, success: boolean = true, metadata?: Record<string, any>): void {
    const journey = this.activeJourneys.get(journeyId);
    const transaction = this.activeTransactions.get(journeyId);
    
    if (!journey || !transaction) {
      console.warn(`[PerformanceMonitoring] Journey not found: ${journeyId}`);
      return;
    }

    const endTime = Date.now();
    const duration = endTime - journey.startTime;

    journey.endTime = endTime;
    journey.duration = duration;
    journey.success = success;
    
    if (metadata) {
      journey.metadata = { ...journey.metadata, ...metadata };
    }

    // Set transaction metrics
    transaction.setMeasurement('journey_duration', duration, 'millisecond');
    transaction.setMeasurement('journey_steps', journey.steps.length, 'none');
    transaction.setMeasurement('successful_steps', journey.steps.filter(s => s.success).length, 'none');
    transaction.setStatus(success ? 'ok' : 'internal_error');

    addBreadcrumb(
      `User journey completed: ${journey.name}`,
      'performance',
      {
        journeyId,
        journeyName: journey.name,
        duration,
        success,
        stepCount: journey.steps.length,
        successfulSteps: journey.steps.filter(s => s.success).length
      }
    );

    // Log journey summary
    console.log(`[PerformanceMonitoring] Journey completed: ${journey.name}`, {
      duration: `${duration}ms`,
      success,
      steps: journey.steps.length,
      successfulSteps: journey.steps.filter(s => s.success).length
    });

    // Report slow journeys
    if (duration > 10000) { // 10 seconds
      reportMessage(
        `Slow user journey detected: ${journey.name}`,
        'warning',
        {
          component: 'PerformanceMonitoring',
          action: 'slow_journey_detection',
          metadata: {
            journeyId,
            journeyName: journey.name,
            duration,
            stepCount: journey.steps.length
          }
        }
      );
    }

    // Finish transaction
    transaction.finish();

    // Clean up
    this.activeJourneys.delete(journeyId);
    this.activeTransactions.delete(journeyId);
  }

  /**
   * Monitor database query performance
   */
  monitorDatabaseQuery<T>(
    queryName: string,
    queryFunction: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const transaction = startTransaction(`db.${queryName}`, 'db');
    transaction.setTag('query_name', queryName);
    
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        transaction.setTag(key, String(value));
      });
    }

    const startTime = Date.now();

    addBreadcrumb(
      `Database query started: ${queryName}`,
      'db',
      { queryName, metadata }
    );

    return queryFunction()
      .then(result => {
        const duration = Date.now() - startTime;
        
        transaction.setMeasurement('query_duration', duration, 'millisecond');
        transaction.setStatus('ok');

        addBreadcrumb(
          `Database query completed: ${queryName}`,
          'db',
          { queryName, duration, success: true }
        );

        // Report slow queries
        if (duration > 2000) { // 2 seconds
          reportMessage(
            `Slow database query detected: ${queryName}`,
            'warning',
            {
              component: 'PerformanceMonitoring',
              action: 'slow_query_detection',
              metadata: { queryName, duration, ...metadata }
            }
          );
        }

        return result;
      })
      .catch(error => {
        const duration = Date.now() - startTime;
        
        transaction.setMeasurement('query_duration', duration, 'millisecond');
        transaction.setStatus('internal_error');

        addBreadcrumb(
          `Database query failed: ${queryName}`,
          'db',
          { queryName, duration, success: false, error: error.message }
        );

        throw error;
      })
      .finally(() => {
        transaction.finish();
      });
  }

  /**
   * Monitor API call performance
   */
  monitorApiCall<T>(
    apiName: string,
    apiFunction: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const transaction = startTransaction(`api.${apiName}`, 'http');
    transaction.setTag('api_name', apiName);
    
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        transaction.setTag(key, String(value));
      });
    }

    const startTime = Date.now();

    addBreadcrumb(
      `API call started: ${apiName}`,
      'http',
      { apiName, metadata }
    );

    return apiFunction()
      .then(result => {
        const duration = Date.now() - startTime;
        
        transaction.setMeasurement('api_duration', duration, 'millisecond');
        transaction.setStatus('ok');

        addBreadcrumb(
          `API call completed: ${apiName}`,
          'http',
          { apiName, duration, success: true }
        );

        // Report slow API calls
        if (duration > 5000) { // 5 seconds
          reportMessage(
            `Slow API call detected: ${apiName}`,
            'warning',
            {
              component: 'PerformanceMonitoring',
              action: 'slow_api_detection',
              metadata: { apiName, duration, ...metadata }
            }
          );
        }

        return result;
      })
      .catch(error => {
        const duration = Date.now() - startTime;
        
        transaction.setMeasurement('api_duration', duration, 'millisecond');
        transaction.setStatus('internal_error');

        addBreadcrumb(
          `API call failed: ${apiName}`,
          'http',
          { apiName, duration, success: false, error: error.message }
        );

        throw error;
      })
      .finally(() => {
        transaction.finish();
      });
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): Record<string, any> {
    return {
      activeJourneys: this.activeJourneys.size,
      activeTransactions: this.activeTransactions.size,
      journeyNames: Array.from(this.activeJourneys.values()).map(j => j.name),
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Get memory usage (if available)
   */
  private getMemoryUsage(): number | undefined {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      return (performance as any).memory?.usedJSHeapSize;
    }
    return undefined;
  }
}

// Export singleton instance
export const performanceMonitoring = new PerformanceMonitoringService();
