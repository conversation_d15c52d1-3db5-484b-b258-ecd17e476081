import { supabase } from './supabase';
import type {
  AIQuestionGenerationRequest,
  AIQuestionGenerationResponse,
  AIGenerationLog,
  AIRateLimit,
  AIGenerationType,
} from '@/types/journal';
import { JOURNAL_CONSTANTS } from '@/types/journal';

export class AIQuestionService {
  private static instance: AIQuestionService;

  static getInstance(): AIQuestionService {
    if (!AIQuestionService.instance) {
      AIQuestionService.instance = new AIQuestionService();
    }
    return AIQuestionService.instance;
  }

  /**
   * Generate AI questions using Supabase Edge Function
   */
  async generateQuestions(
    userId: string,
    request: AIQuestionGenerationRequest
  ): Promise<AIQuestionGenerationResponse> {
    try {
      // Check rate limit first
      const rateLimitCheck = await this.checkRateLimit(userId);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          questions: [],
          error: `Daily limit exceeded. You can generate ${rateLimitCheck.daily_limit} questions per day. Try again tomorrow.`,
        };
      }

      // Validate request
      this.validateGenerationRequest(request);

      const startTime = Date.now();

      // Call Supabase Edge Function
      const response = await fetch(
        `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/journal-ai-generator`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY}`,
          },
          body: JSON.stringify({
            baseQuestion: request.baseQuestion,
            baseQuestionId: request.baseQuestionId,
            generationType: request.questionType,
            count: Math.min(request.count || 3, JOURNAL_CONSTANTS.AI_MAX_QUESTIONS_PER_REQUEST),
            context: request.context,
            userPreferences: request.userPreferences,
          }),
        }
      );

      if (!response.ok) {
        // Try to get detailed error information from response
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
          if (errorData.errorCode === 'API_KEY_MISSING') {
            errorMessage = 'AI question generation is currently unavailable. Please contact support.';
          }
        } catch (parseError) {
          // If we can't parse the error response, use the generic message
          console.warn('Could not parse error response:', parseError);
        }
        throw new Error(errorMessage);
      }

      const result: AIQuestionGenerationResponse = await response.json();
      const processingTime = Date.now() - startTime;

      // Log the generation attempt
      const logData = {
        user_id: userId,
        base_question_id: request.baseQuestionId || undefined,
        generation_type: request.questionType,
        prompt_used: `${request.baseQuestion} (${request.questionType})`,
        generated_questions: result.questions || [],
        generation_context: {
          context: request.context,
          userPreferences: request.userPreferences,
          requestCount: request.count,
        },
        model_used: result.model,
        processing_time_ms: processingTime,
        success: result.success,
        error_message: result.error || null,
      };

      await this.logGeneration(logData);

      // Increment rate limit counter if successful
      if (result.success) {
        await this.incrementRateLimit(userId);
      }

      return {
        ...result,
        processingTime,
      };
    } catch (error: any) {
      console.error('Error generating AI questions:', error);

      // Log failed attempt
      await this.logGeneration({
        user_id: userId,
        base_question_id: request.baseQuestionId || undefined,
        generation_type: request.questionType,
        prompt_used: `${request.baseQuestion} (${request.questionType})`,
        generated_questions: [],
        generation_context: { error: error.message },
        success: false,
        error_message: error.message,
        processing_time_ms: 0,
      });

      return {
        success: false,
        questions: [],
        error: error.message || 'Failed to generate questions',
      };
    }
  }

  /**
   * Check user's daily rate limit
   */
  async checkRateLimit(userId: string): Promise<AIRateLimit> {
    try {
      const { data, error } = await supabase.rpc('check_ai_generation_rate_limit', {
        target_user_id: userId,
        daily_limit: JOURNAL_CONSTANTS.AI_DAILY_GENERATION_LIMIT,
      });

      if (error) {
        console.error('Error checking rate limit:', error);
        // Default to allowing if check fails
        return {
          allowed: true,
          current_count: 0,
          daily_limit: JOURNAL_CONSTANTS.AI_DAILY_GENERATION_LIMIT,
        };
      }

      return data as AIRateLimit;
    } catch (error) {
      console.error('Error checking rate limit:', error);
      return {
        allowed: true,
        current_count: 0,
        daily_limit: JOURNAL_CONSTANTS.AI_DAILY_GENERATION_LIMIT,
      };
    }
  }

  /**
   * Get user's AI generation statistics
   */
  async getUserStats(userId: string): Promise<Record<string, any>> {
    try {
      const { data, error } = await supabase.rpc('get_user_ai_generation_stats', {
        target_user_id: userId,
      });

      if (error) {
        console.error('Error fetching user stats:', error);
        return {};
      }

      return data || {};
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return {};
    }
  }

  /**
   * Get user's generation history
   */
  async getGenerationHistory(
    userId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<AIGenerationLog[]> {
    try {
      const { data, error } = await supabase
        .from('ai_question_generations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Failed to fetch generation history: ${error.message}`);
      }

      return (data || []) as AIGenerationLog[];
    } catch (error) {
      console.error('Error fetching generation history:', error);
      throw error;
    }
  }

  /**
   * Update user feedback for a generation
   */
  async updateGenerationFeedback(
    userId: string,
    generationId: string,
    feedback: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('ai_question_generations')
        .update({
          user_feedback: feedback,
          updated_at: new Date().toISOString(),
        })
        .eq('id', generationId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to update feedback: ${error.message}`);
      }
    } catch (error) {
      console.error('Error updating generation feedback:', error);
      throw error;
    }
  }

  /**
   * Mark questions as selected by user
   */
  async markQuestionsSelected(
    userId: string,
    generationId: string,
    selectedQuestions: string[]
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('ai_question_generations')
        .update({
          selected_questions: selectedQuestions,
          updated_at: new Date().toISOString(),
        })
        .eq('id', generationId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to mark selected questions: ${error.message}`);
      }
    } catch (error) {
      console.error('Error marking selected questions:', error);
      throw error;
    }
  }

  /**
   * Validate generation request
   */
  private validateGenerationRequest(request: AIQuestionGenerationRequest): void {
    if (!request.baseQuestion || request.baseQuestion.trim().length === 0) {
      throw new Error('Base question is required');
    }

    if (request.baseQuestion.length > 500) {
      throw new Error('Base question too long (max 500 characters)');
    }

    if (!['follow_up', 'alternative', 'related'].includes(request.questionType)) {
      throw new Error('Invalid question type');
    }

    if (request.count && (request.count < 1 || request.count > JOURNAL_CONSTANTS.AI_MAX_QUESTIONS_PER_REQUEST)) {
      throw new Error(`Count must be between 1 and ${JOURNAL_CONSTANTS.AI_MAX_QUESTIONS_PER_REQUEST}`);
    }

    if (request.context && request.context.length > 1000) {
      throw new Error('Context too long (max 1000 characters)');
    }
  }

  /**
   * Log generation attempt to database
   */
  private async logGeneration(logData: Partial<AIGenerationLog>): Promise<void> {
    try {
      const { error } = await supabase
        .from('ai_question_generations')
        .insert(logData);

      if (error) {
        console.error('Error logging generation:', error);
        // Don't throw error here to avoid breaking the main flow
      }
    } catch (error) {
      console.error('Error logging generation:', error);
    }
  }

  /**
   * Increment user's rate limit counter
   */
  private async incrementRateLimit(userId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('increment_ai_generation_count', {
        target_user_id: userId,
      });

      if (error) {
        console.error('Error incrementing rate limit:', error);
      }
    } catch (error) {
      console.error('Error incrementing rate limit:', error);
    }
  }
}

// Export singleton instance
export const aiQuestionService = AIQuestionService.getInstance();
