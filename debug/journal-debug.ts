/**
 * Debug script to test journal functionality
 * Run this to diagnose journal loading issues
 */

import { supabase } from '@/lib/supabase';
import { journalService } from '@/lib/journalService';

export const debugJournalConnection = async () => {
  console.log('=== Journal Debug Script ===');
  
  try {
    // 1. Check Supabase connection
    console.log('1. Testing Supabase connection...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    if (!session) {
      console.log('❌ No active session - user not logged in');
      return;
    }
    
    console.log('✅ Session found:', {
      userId: session.user.id,
      email: session.user.email,
      expiresAt: session.expires_at
    });
    
    // 2. Test direct database query
    console.log('2. Testing direct database query...');
    const { data: entries, error: entriesError } = await supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', session.user.id)
      .limit(5);
    
    if (entriesError) {
      console.error('❌ Database query error:', entriesError);
      return;
    }
    
    console.log('✅ Direct query successful:', {
      entriesCount: entries?.length || 0,
      entries: entries?.slice(0, 2) // Show first 2 entries
    });
    
    // 3. Test journal service
    console.log('3. Testing journal service...');
    try {
      const historyData = await journalService.getJournalHistory(session.user.id, 10, 0);
      console.log('✅ Journal service successful:', {
        groupsCount: historyData.length,
        totalEntries: historyData.reduce((sum, group) => sum + group.entries.length, 0)
      });
    } catch (serviceError) {
      console.error('❌ Journal service error:', serviceError);
    }
    
    // 4. Test journal questions
    console.log('4. Testing journal questions...');
    try {
      const questions = await journalService.getJournalQuestions();
      console.log('✅ Journal questions successful:', {
        questionsCount: questions.length
      });
    } catch (questionsError) {
      console.error('❌ Journal questions error:', questionsError);
    }
    
    console.log('=== Debug Complete ===');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
};

export const debugUserAuth = async () => {
  console.log('=== User Auth Debug ===');
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Auth error:', error);
      return;
    }
    
    if (!user) {
      console.log('❌ No authenticated user');
      return;
    }
    
    console.log('✅ User authenticated:', {
      id: user.id,
      email: user.email,
      createdAt: user.created_at,
      lastSignIn: user.last_sign_in_at
    });
    
  } catch (error) {
    console.error('❌ Unexpected auth error:', error);
  }
};

// Helper function to run all debug checks
export const runFullDebug = async () => {
  await debugUserAuth();
  await debugJournalConnection();
};
