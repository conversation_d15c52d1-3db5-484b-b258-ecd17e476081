/**
 * Weekly Voice Credit Recharge Edge Function
 * 
 * Automatically recharges voice credits for all users on a weekly basis.
 * This function should be called by a cron job or scheduled task.
 * 
 * Features:
 * - Recharges all eligible users to 12 credits
 * - Tracks recharge history
 * - Provides detailed logging and error handling
 * - Supports manual execution with authentication
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};

interface RechargeResult {
  success: boolean;
  totalRecharged: number;
  totalErrors: number;
  executionTime: string;
  results?: any[];
  error?: string;
}

interface RechargeRequest {
  manual?: boolean;
  userId?: string; // For manual single-user recharge
  authToken?: string; // For manual execution authentication
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  const startTime = Date.now();
  
  try {
    // Initialize Supabase client with service role key for admin operations
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase configuration');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse request body for manual execution
    let requestData: RechargeRequest = {};
    if (req.method === 'POST') {
      try {
        requestData = await req.json();
      } catch {
        // Ignore JSON parse errors for automated calls
      }
    }

    console.log('Starting weekly credit recharge process...', {
      manual: requestData.manual || false,
      userId: requestData.userId || 'all',
      timestamp: new Date().toISOString(),
    });

    let result: RechargeResult;

    if (requestData.userId) {
      // Single user recharge (manual)
      result = await rechargeSingleUser(supabase, requestData.userId, startTime);
    } else {
      // Bulk recharge all users
      result = await rechargeAllUsers(supabase, startTime);
    }

    console.log('Weekly credit recharge completed:', result);

    return new Response(
      JSON.stringify(result),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );

  } catch (error) {
    console.error('Weekly credit recharge failed:', error);
    
    const errorResult: RechargeResult = {
      success: false,
      totalRecharged: 0,
      totalErrors: 1,
      executionTime: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };

    return new Response(
      JSON.stringify(errorResult),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});

/**
 * Recharge credits for all eligible users
 */
async function rechargeAllUsers(supabase: any, startTime: number): Promise<RechargeResult> {
  try {
    // Call the database function to recharge all users
    const { data, error } = await supabase.rpc('recharge_all_weekly_credits');

    if (error) {
      throw error;
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      totalRecharged: data.total_recharged || 0,
      totalErrors: data.total_errors || 0,
      executionTime: new Date().toISOString(),
      results: data.results || [],
    };

  } catch (error) {
    console.error('Error in rechargeAllUsers:', error);
    throw error;
  }
}

/**
 * Recharge credits for a single user
 */
async function rechargeSingleUser(supabase: any, userId: string, startTime: number): Promise<RechargeResult> {
  try {
    // Check if user needs recharge
    const { data: needsRecharge, error: checkError } = await supabase
      .rpc('check_user_needs_weekly_recharge', { target_user_id: userId });

    if (checkError) {
      throw checkError;
    }

    if (!needsRecharge) {
      return {
        success: true,
        totalRecharged: 0,
        totalErrors: 0,
        executionTime: new Date().toISOString(),
        results: [{
          success: true,
          user_id: userId,
          message: 'User does not need recharge yet',
        }],
      };
    }

    // Perform recharge
    const { data: rechargeResult, error: rechargeError } = await supabase
      .rpc('recharge_user_weekly_credits', {
        target_user_id: userId,
        recharge_type_param: 'manual',
        triggered_by_param: 'edge_function_manual',
      });

    if (rechargeError) {
      throw rechargeError;
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      totalRecharged: 1,
      totalErrors: 0,
      executionTime: new Date().toISOString(),
      results: [rechargeResult],
    };

  } catch (error) {
    console.error('Error in rechargeSingleUser:', error);
    
    return {
      success: false,
      totalRecharged: 0,
      totalErrors: 1,
      executionTime: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      results: [{
        success: false,
        user_id: userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      }],
    };
  }
}

/**
 * Validate authentication for manual requests
 */
async function validateAuth(supabase: any, authToken: string): Promise<boolean> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser(authToken);
    
    if (error || !user) {
      return false;
    }

    // Additional validation can be added here (e.g., check if user is admin)
    return true;
  } catch {
    return false;
  }
}
