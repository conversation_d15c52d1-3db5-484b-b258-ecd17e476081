/**
 * Journal AI Question Generator Edge Function
 * 
 * Generates contextual journal questions using Groq LLM
 * Specialized for Indonesian language and mental health context
 */

import { Groq } from "npm:groq-sdk";

// Secure CORS configuration
const getAllowedOrigins = () => {
  const origins = [
    'http://localhost:8081',
    'http://localhost:3000',
    'https://app.temani.co',
    'https://temani.co'
  ];

  const customDomain = Deno.env.get("ALLOWED_ORIGIN");
  if (customDomain) {
    origins.push(customDomain);
  }

  return origins;
};

const getCorsHeaders = (origin?: string) => {
  const allowedOrigins = getAllowedOrigins();
  const isAllowed = origin && allowedOrigins.includes(origin);

  return {
    "Access-Control-Allow-Origin": isAllowed ? origin : allowedOrigins[0],
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type, X-CSRF-Token",
    "Access-Control-Allow-Credentials": "true",
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
  };
};

interface JournalAIRequest {
  baseQuestion: string;
  baseQuestionId?: string;
  generationType: 'follow_up' | 'alternative' | 'related' | 'mixed';
  count?: number;
  context?: string;
  userPreferences?: {
    tone?: 'casual' | 'formal' | 'empathetic';
    length?: 'short' | 'medium' | 'long';
  };
}

interface JournalAIResponse {
  success: boolean;
  questions: string[];
  model?: string;
  processingTime?: number;
  error?: string;
  generationId?: string;
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
};

// Model configuration with fallbacks (same as groq-chat function)
const MODELS_TO_TRY = [
  "moonshotai/kimi-k2-instruct",
  "qwen/qwen3-32b",
];

/**
 * Build specialized prompt for journal question generation
 */
function buildJournalPrompt(request: JournalAIRequest): { systemPrompt: string; userPrompt: string } {
  const baseSystemPrompt = `Kamu adalah asisten AI yang ahli dalam membuat pertanyaan reflektif untuk jurnal pribadi dalam bahasa Indonesia.

KONTEKS:
- Kamu membantu remaja Indonesia dalam perjalanan kesehatan mental mereka
- Pertanyaan harus mendorong refleksi diri yang sehat dan konstruktif
- Gunakan bahasa yang hangat, personal, dan tidak menghakimi
- Hindari pertanyaan yang terlalu invasif atau memicu trauma
- Fokus pada pertumbuhan pribadi, kesadaran diri, dan kesejahteraan emosional

PRINSIP UTAMA:
1. Gunakan bahasa Indonesia yang natural dan ramah
2. Pertanyaan harus open-ended dan mendorong eksplorasi
3. Hindari pertanyaan yang bisa memicu respons negatif atau berbahaya
4. Sesuaikan dengan konteks kesehatan mental remaja
5. Pertanyaan harus praktis dan dapat dijawab dalam konteks harian

TONE: Empati, mendukung, tidak menghakimi, seperti teman yang peduli`;

  let specificInstructions = '';
  let userPromptTemplate = '';

  switch (request.generationType) {
    case 'follow_up':
      specificInstructions = `
TUGAS KHUSUS: Buat pertanyaan lanjutan yang menggali lebih dalam dari pertanyaan dasar.

KARAKTERISTIK PERTANYAAN LANJUTAN:
- Membantu pengguna mengeksplorasi perasaan atau pengalaman lebih detail
- Mendorong refleksi tentang penyebab, dampak, atau makna dari jawaban sebelumnya
- Membantu mengidentifikasi pola atau pembelajaran
- Menghubungkan pengalaman dengan pertumbuhan pribadi`;

      userPromptTemplate = `Berdasarkan pertanyaan jurnal: "{baseQuestion}"

Buatlah {count} pertanyaan lanjutan yang menggali lebih dalam topik yang sama.
Konteks: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering atau bullet points.`;
      break;

    case 'alternative':
      specificInstructions = `
TUGAS KHUSUS: Buat variasi pertanyaan dengan tujuan sama tapi kata-kata berbeda.

KARAKTERISTIK PERTANYAAN ALTERNATIF:
- Mempertahankan maksud dan tujuan pertanyaan asli
- Menggunakan sudut pandang atau framing yang berbeda
- Tetap mudah dipahami dan dijawab
- Memberikan variasi untuk menghindari kebosanan`;

      userPromptTemplate = `Berdasarkan pertanyaan jurnal: "{baseQuestion}"

Buatlah {count} variasi pertanyaan dengan tujuan refleksi yang sama tapi menggunakan kata-kata berbeda.
Konteks: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering atau bullet points.`;
      break;

    case 'related':
      specificInstructions = `
TUGAS KHUSUS: Buat pertanyaan baru yang berkaitan dengan tema dari pertanyaan asli.

KARAKTERISTIK PERTANYAAN TERKAIT:
- Mengeksplorasi aspek berbeda dari tema yang sama
- Memperluas cakupan refleksi pada area terkait
- Membantu pengguna melihat koneksi antar pengalaman
- Mendorong pemahaman holistik tentang diri`;

      userPromptTemplate = `Berdasarkan pertanyaan jurnal: "{baseQuestion}"

Buatlah {count} pertanyaan terkait yang mengeksplorasi tema atau topik yang berkaitan.
Konteks: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering atau bullet points.`;
      break;

    case 'mixed':
      specificInstructions = `
TUGAS KHUSUS: Buat campuran pertanyaan yang beragam untuk eksplorasi mendalam.

KARAKTERISTIK PERTANYAAN CAMPURAN:
- 1 pertanyaan lanjutan yang menggali lebih dalam
- 1 pertanyaan alternatif dengan framing berbeda
- 1 pertanyaan terkait yang mengeksplorasi aspek lain
- Memberikan variasi lengkap untuk refleksi holistik`;

      userPromptTemplate = `Berdasarkan pertanyaan jurnal: "{baseQuestion}"

Buatlah 3 pertanyaan yang beragam:
1. Satu pertanyaan lanjutan yang menggali lebih dalam
2. Satu pertanyaan alternatif dengan kata-kata berbeda
3. Satu pertanyaan terkait yang mengeksplorasi aspek lain

Konteks: {context}

Format: Berikan hanya pertanyaan-pertanyaan tersebut, satu per baris, tanpa numbering atau bullet points.`;
      break;
  }

  // Customize based on user preferences
  let toneInstruction = '';
  if (request.userPreferences?.tone === 'casual') {
    toneInstruction = '\n\nGUNAKAN TONE: Santai dan bersahabat, seperti ngobrol dengan teman dekat.';
  } else if (request.userPreferences?.tone === 'formal') {
    toneInstruction = '\n\nGUNAKAN TONE: Lebih formal tapi tetap hangat dan mendukung.';
  } else if (request.userPreferences?.tone === 'empathetic') {
    toneInstruction = '\n\nGUNAKAN TONE: Sangat empati dan penuh perhatian, fokus pada dukungan emosional.';
  }

  const systemPrompt = baseSystemPrompt + specificInstructions + toneInstruction;
  
  const userPrompt = userPromptTemplate
    .replace('{baseQuestion}', request.baseQuestion)
    .replace('{count}', (request.count || 3).toString())
    .replace('{context}', request.context || 'Tidak ada konteks tambahan');

  return { systemPrompt, userPrompt };
}

/**
 * Validate and clean generated questions
 */
function validateAndCleanQuestions(rawResponse: string): string[] {
  const lines = rawResponse
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .filter(line => !line.match(/^\d+\.?\s/)) // Remove numbered items
    .filter(line => !line.match(/^[-*•]\s/)) // Remove bullet points
    .map(line => line.replace(/^["']|["']$/g, '')) // Remove quotes
    .filter(line => line.length >= 10 && line.length <= 200) // Length check
    .filter(line => line.includes('?')) // Must be questions
    .slice(0, 5); // Max 5 questions

  // Additional safety filtering
  const dangerousPatterns = [
    /bunuh diri|suicide/i,
    /menyakiti diri|self.?harm/i,
    /mati|death/i,
    /putus asa|hopeless/i,
  ];

  return lines.filter(line => 
    !dangerousPatterns.some(pattern => pattern.test(line))
  );
}

/**
 * Attempt Groq request with retry logic
 */
async function attemptGroqRequest(
  groq: Groq,
  systemPrompt: string,
  userPrompt: string,
  modelName: string
): Promise<string> {
  for (let attempt = 0; attempt < RETRY_CONFIG.maxRetries; attempt++) {
    try {
      console.log(`Attempting ${modelName}, try ${attempt + 1}/${RETRY_CONFIG.maxRetries}`);

      // Use same model-specific configuration as groq-chat function
      let chatCompletion;
      if (modelName === "qwen/qwen3-32b") {
        chatCompletion = await groq.chat.completions.create({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          model: modelName,
          temperature: 0.7,
          max_completion_tokens: 1000,
          top_p: 1,
          stream: false,
          reasoning_format: "hidden",
        });
      } else {
        chatCompletion = await groq.chat.completions.create({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          model: modelName,
          temperature: 0.7,
          max_completion_tokens: 1000,
          top_p: 1,
          stream: false,
        });
      }

      const responseText = chatCompletion.choices[0]?.message?.content;

      if (!responseText) {
        throw new Error("Empty response from Groq");
      }

      console.log(`Successfully used ${modelName} on attempt ${attempt + 1}`);
      return responseText;

    } catch (error: any) {
      console.error(`Attempt ${attempt + 1} failed for ${modelName}:`, error.message);
      
      if (attempt === RETRY_CONFIG.maxRetries - 1) {
        throw error;
      }
      
      // Exponential backoff
      const delay = Math.min(
        RETRY_CONFIG.baseDelay * Math.pow(2, attempt),
        RETRY_CONFIG.maxDelay
      );
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error(`All retry attempts failed for ${modelName}`);
}

Deno.serve(async (req) => {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Metode tidak diizinkan.',
        error_code: 'method_not_allowed'
      }),
      {
        status: 405,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }

  try {
    const startTime = Date.now();
    
    // Parse request
    const request: JournalAIRequest = await req.json();
    
    // Validate request
    if (!request.baseQuestion || request.baseQuestion.trim().length === 0) {
      throw new Error('Base question is required');
    }

    if (!['follow_up', 'alternative', 'related', 'mixed'].includes(request.generationType)) {
      throw new Error('Invalid generation type');
    }

    // Get API key (using same variable name as groq-chat function)
    const apiKey = Deno.env.get('EXPO_PUBLIC_GROQ_API_KEY');
    if (!apiKey) {
      console.error('EXPO_PUBLIC_GROQ_API_KEY environment variable not configured');
      return new Response(
        JSON.stringify({
          success: false,
          questions: [],
          error: 'Maaf, layanan sedang mengalami gangguan teknis. Tim kami akan segera memperbaikinya.',
          errorCode: 'service_unavailable'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        }
      );
    }

    // Initialize Groq client
    const groq = new Groq({ apiKey });
    
    // Build prompts
    const { systemPrompt, userPrompt } = buildJournalPrompt(request);
    
    console.log('Generated prompts:', { systemPrompt: systemPrompt.substring(0, 200), userPrompt });

    // Try different models with fallback
    let lastError: any = null;
    let responseText: string | null = null;
    let modelUsed: string | null = null;

    for (const modelName of MODELS_TO_TRY) {
      try {
        responseText = await attemptGroqRequest(groq, systemPrompt, userPrompt, modelName);
        modelUsed = modelName;
        break;
      } catch (error) {
        console.error(`Model ${modelName} failed:`, error);
        lastError = error;
        continue;
      }
    }

    if (!responseText || !modelUsed) {
      throw lastError || new Error('All models failed');
    }

    // Validate and clean questions
    const questions = validateAndCleanQuestions(responseText);
    
    if (questions.length === 0) {
      throw new Error('No valid questions generated');
    }

    const processingTime = Date.now() - startTime;

    const response: JournalAIResponse = {
      success: true,
      questions,
      model: modelUsed,
      processingTime,
    };

    return new Response(
      JSON.stringify(response),
      {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );

  } catch (error: any) {
    console.error('Journal AI generation error:', error);
    
    const errorResponse: JournalAIResponse = {
      success: false,
      questions: [],
      error: error.message || 'Failed to generate questions',
    };
    
    return new Response(
      JSON.stringify(errorResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});
