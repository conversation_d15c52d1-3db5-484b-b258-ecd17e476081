const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

Deno.serve(async (req: Request) => {
  try {
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    if (req.method !== "GET") {
      return new Response("Method not allowed", {
        status: 405,
        headers: corsHeaders,
      });
    }

    const apiKey = Deno.env.get("ELEVENLABS_API_KEY");
    const agentId = Deno.env.get("EXPO_PUBLIC_ELEVENLABS_AGENT_ID");

    if (!apiKey) {
      return new Response("ElevenLabs API key not configured", {
        status: 500,
        headers: corsHeaders,
      });
    }

    if (!agentId) {
      return new Response("ElevenLabs Agent ID not configured", {
        status: 500,
        headers: corsHeaders,
      });
    }

    // Generate signed URL for private agent
    const response = await fetch(
      `https://api.elevenlabs.io/v1/convai/conversation/get-signed-url?agent_id=${agentId}`,
      {
        headers: {
          'xi-api-key': apiKey,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('ElevenLabs API error:', errorText);
      return new Response("Failed to get signed URL from ElevenLabs", {
        status: response.status,
        headers: corsHeaders,
      });
    }

    const data = await response.json();
    
    return new Response(
      JSON.stringify({ 
        signedUrl: data.signed_url,
        success: true 
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );

  } catch (error) {
    console.error('Signed URL generation error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: "Failed to generate signed URL",
        success: false
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});