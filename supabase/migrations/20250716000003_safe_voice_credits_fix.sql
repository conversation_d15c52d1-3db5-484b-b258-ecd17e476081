/*
  # Safe Voice Credits Function Fix
  
  This is an alternative migration that safely handles the function update
  with proper error handling and rollback capabilities.
  
  ## Changes:
  - Safely drop and recreate get_user_voice_credits function
  - Handle any existing dependencies
  - Ensure proper permissions are maintained
*/

-- Store the current function definition for potential rollback
DO $$
BEGIN
  -- Check if function exists and log it
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_user_voice_credits') THEN
    RAISE NOTICE 'Function get_user_voice_credits exists, will be replaced';
  ELSE
    RAISE NOTICE 'Function get_user_voice_credits does not exist, will be created';
  END IF;
END $$;

-- Safely drop the function with CASCADE to handle any dependencies
DROP FUNCTION IF EXISTS get_user_voice_credits(uuid) CASCADE;

-- Create the new function with the correct return type
CREATE FUNCTION get_user_voice_credits(target_user_id uuid)
RETURNS TABLE(
  credits_remaining integer,
  credits_used integer,
  total_sessions bigint,
  last_session timestamptz,
  last_credit_reset timestamptz
) AS $$
BEGIN
  -- Handle case where user doesn't have a profile yet
  IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE user_id = target_user_id) THEN
    RETURN QUERY
    SELECT 
      10 as credits_remaining,
      0 as credits_used,
      0::bigint as total_sessions,
      NULL::timestamptz as last_session,
      now() as last_credit_reset;
    RETURN;
  END IF;

  -- Return actual user data
  RETURN QUERY
  SELECT 
    COALESCE(p.voice_credits, 10) as credits_remaining,
    COALESCE(p.voice_credits_used, 0) as credits_used,
    COALESCE(COUNT(s.id), 0) as total_sessions,
    MAX(s.session_start) as last_session,
    COALESCE(p.last_credit_reset, now()) as last_credit_reset
  FROM user_profiles p
  LEFT JOIN voice_call_sessions s ON p.user_id = s.user_id AND s.session_status = 'completed'
  WHERE p.user_id = target_user_id
  GROUP BY p.voice_credits, p.voice_credits_used, p.last_credit_reset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant proper permissions
GRANT EXECUTE ON FUNCTION get_user_voice_credits(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_voice_credits(uuid) TO anon;

-- Recreate the user_voice_usage view if it was dropped due to CASCADE
CREATE OR REPLACE VIEW user_voice_usage AS
SELECT
  u.id as user_id,
  u.email,
  COALESCE(p.voice_credits, 10) as credits_remaining,
  COALESCE(p.voice_credits_used, 0) as credits_used,
  COUNT(s.id) as total_sessions,
  SUM(CASE WHEN s.session_status = 'completed' THEN s.duration_seconds ELSE 0 END) as total_call_time_seconds,
  MAX(s.session_start) as last_call_time,
  COALESCE(p.last_credit_reset, now()) as last_credit_reset
FROM auth.users u
LEFT JOIN user_profiles p ON u.id = p.user_id
LEFT JOIN voice_call_sessions s ON u.id = s.user_id
WHERE u.id = auth.uid() -- Built-in RLS for views
GROUP BY u.id, u.email, p.voice_credits, p.voice_credits_used, p.last_credit_reset;

-- Grant access to the view
GRANT SELECT ON user_voice_usage TO authenticated;

-- Test the function to ensure it works
DO $$
DECLARE
  test_result RECORD;
BEGIN
  -- Test with a dummy UUID
  SELECT * INTO test_result FROM get_user_voice_credits('00000000-0000-0000-0000-000000000000');
  
  IF test_result.credits_remaining = 10 THEN
    RAISE NOTICE 'Function test PASSED: Returns default 10 credits for non-existent user';
  ELSE
    RAISE EXCEPTION 'Function test FAILED: Expected 10 credits, got %', test_result.credits_remaining;
  END IF;
  
  IF test_result.last_credit_reset IS NOT NULL THEN
    RAISE NOTICE 'Function test PASSED: Returns last_credit_reset field';
  ELSE
    RAISE EXCEPTION 'Function test FAILED: last_credit_reset field is NULL';
  END IF;
END $$;
