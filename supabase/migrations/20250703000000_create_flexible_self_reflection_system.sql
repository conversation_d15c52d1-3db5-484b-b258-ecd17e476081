/*
  # Flexible Self-Reflection Check-In System
  
  This migration creates a flexible, extensible system for self-reflection questions
  that allows adding new questions without code deployment.

  ## Tables Created:
  1. question_sets - Version control for question configurations
  2. questions - Dynamic question definitions with flexible types
  3. self_reflection_sessions - User responses with flexible storage
  4. risk_assessment_rules - Configurable risk assessment logic

  ## Features:
  - Multi-language support
  - Multiple question types (multiple_choice, scale, boolean, text)
  - Flexible scoring systems
  - Conditional question logic
  - A/B testing support
*/

-- Create custom types
CREATE TYPE question_type AS ENUM (
  'multiple_choice', 
  'scale', 
  'boolean', 
  'text_input',
  'multi_select'
);

CREATE TYPE risk_level AS ENUM ('green', 'yellow', 'red', 'emergency');

-- Question Sets (for versioning and A/B testing)
CREATE TABLE IF NOT EXISTS question_sets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  version text NOT NULL,
  description text,
  is_active boolean DEFAULT false,
  language text DEFAULT 'id', -- Indonesian by default
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure only one active set per language
  CONSTRAINT unique_active_per_language UNIQUE (language, is_active) 
    DEFERRABLE INITIALLY DEFERRED
);

-- Questions Configuration
CREATE TABLE IF NOT EXISTS questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_set_id uuid NOT NULL REFERENCES question_sets(id) ON DELETE CASCADE,
  question_key text NOT NULL, -- e.g., 'general_feeling', 'energy_level'
  question_text jsonb NOT NULL, -- Multi-language support: {"id": "Indonesian text", "en": "English text"}
  question_type question_type NOT NULL,
  options jsonb, -- Question options and configuration
  scoring_config jsonb NOT NULL DEFAULT '{"method": "direct", "maxScore": 4}', -- Scoring rules
  order_index integer NOT NULL,
  is_required boolean DEFAULT true,
  conditional_logic jsonb, -- Show/hide based on other answers
  metadata jsonb DEFAULT '{}', -- Additional question metadata
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure unique question keys within a set
  CONSTRAINT unique_question_key_per_set UNIQUE (question_set_id, question_key),
  -- Ensure unique order within a set
  CONSTRAINT unique_order_per_set UNIQUE (question_set_id, order_index)
);

-- User Response Sessions
CREATE TABLE IF NOT EXISTS self_reflection_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  question_set_id uuid NOT NULL REFERENCES question_sets(id),
  responses jsonb NOT NULL DEFAULT '{}', -- Flexible response storage: {"question_key": "response_value"}
  calculated_scores jsonb NOT NULL DEFAULT '{}', -- Individual question scores: {"question_key": score}
  total_score integer DEFAULT 0,
  risk_level risk_level,
  ai_tone_preference text, -- Preferred AI interaction style
  support_preference text, -- Type of support user wants
  metadata jsonb DEFAULT '{}', -- Additional session data
  completed_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Risk Assessment Rules
CREATE TABLE IF NOT EXISTS risk_assessment_rules (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_set_id uuid NOT NULL REFERENCES question_sets(id) ON DELETE CASCADE,
  rule_name text NOT NULL,
  score_ranges jsonb NOT NULL, -- {"green": [0, 4], "yellow": [5, 7], "red": [8, 9], "emergency": [10, 999]}
  actions jsonb DEFAULT '{}', -- Actions to take for each risk level
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure unique active rule per question set
  CONSTRAINT unique_active_rule_per_set UNIQUE (question_set_id, is_active)
    DEFERRABLE INITIALLY DEFERRED
);

-- Enable Row Level Security
ALTER TABLE question_sets ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE self_reflection_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_assessment_rules ENABLE ROW LEVEL SECURITY;

-- RLS Policies for question_sets (public read for active sets)
CREATE POLICY "Anyone can read active question sets"
  ON question_sets
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- RLS Policies for questions (public read for questions in active sets)
CREATE POLICY "Anyone can read questions from active sets"
  ON questions
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM question_sets 
      WHERE question_sets.id = questions.question_set_id 
      AND question_sets.is_active = true
    )
  );

-- RLS Policies for self_reflection_sessions (users can only access their own)
CREATE POLICY "Users can read own reflection sessions"
  ON self_reflection_sessions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own reflection sessions"
  ON self_reflection_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own reflection sessions"
  ON self_reflection_sessions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policies for risk_assessment_rules (public read for active rules)
CREATE POLICY "Anyone can read active risk assessment rules"
  ON risk_assessment_rules
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_question_sets_active ON question_sets(is_active, language);
CREATE INDEX IF NOT EXISTS idx_questions_set_order ON questions(question_set_id, order_index);
CREATE INDEX IF NOT EXISTS idx_questions_key ON questions(question_key);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON self_reflection_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON self_reflection_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_sessions_risk_level ON self_reflection_sessions(risk_level);
CREATE INDEX IF NOT EXISTS idx_risk_rules_active ON risk_assessment_rules(question_set_id, is_active);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_question_sets_updated_at
  BEFORE UPDATE ON question_sets
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_questions_updated_at
  BEFORE UPDATE ON questions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at
  BEFORE UPDATE ON self_reflection_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_risk_rules_updated_at
  BEFORE UPDATE ON risk_assessment_rules
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to safely activate a question set (deactivates others)
CREATE OR REPLACE FUNCTION activate_question_set(set_id uuid, lang text DEFAULT 'id')
RETURNS void AS $$
BEGIN
  -- Deactivate all other sets for this language
  UPDATE question_sets 
  SET is_active = false 
  WHERE language = lang AND id != set_id;
  
  -- Activate the target set
  UPDATE question_sets 
  SET is_active = true 
  WHERE id = set_id;
END;
$$ LANGUAGE plpgsql;

-- Function to safely activate a risk assessment rule
CREATE OR REPLACE FUNCTION activate_risk_rule(rule_id uuid, set_id uuid)
RETURNS void AS $$
BEGIN
  -- Deactivate all other rules for this question set
  UPDATE risk_assessment_rules 
  SET is_active = false 
  WHERE question_set_id = set_id AND id != rule_id;
  
  -- Activate the target rule
  UPDATE risk_assessment_rules 
  SET is_active = true 
  WHERE id = rule_id;
END;
$$ LANGUAGE plpgsql;
