/*
  # Add Missing Database Functions
  
  This migration adds any missing database functions that are referenced
  by the voice credits system but may not exist in all environments.
  
  ## Functions Added:
  - update_updated_at_column(): Generic trigger function for updating timestamps
  - get_table_columns(): Helper function for testing and verification
*/

-- Create or ensure update_updated_at_column function exists
-- This is a common utility function used by many triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Helper function to get table columns (useful for testing and verification)
CREATE OR REPLACE FUNCTION get_table_columns(table_name text)
RETURNS TABLE(column_name text, data_type text, is_nullable text, column_default text) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.column_name::text,
    c.data_type::text,
    c.is_nullable::text,
    c.column_default::text
  FROM information_schema.columns c
  WHERE c.table_name = get_table_columns.table_name
    AND c.table_schema = 'public'
  ORDER BY c.ordinal_position;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION update_updated_at_column() TO authenticated;
GRANT EXECUTE ON FUNCTION get_table_columns(text) TO authenticated;

-- Verify that the voice credits system tables exist and add any missing triggers
-- This is safe to run multiple times

-- Ensure the updated_at trigger exists on voice_call_sessions
DO $$
BEGIN
  -- Check if trigger exists, if not create it
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_voice_call_sessions_updated_at'
  ) THEN
    CREATE TRIGGER update_voice_call_sessions_updated_at
      BEFORE UPDATE ON voice_call_sessions
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- Ensure the updated_at trigger exists on user_profiles (if it doesn't already exist)
DO $$
BEGIN
  -- Check if trigger exists, if not create it
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_user_profiles_updated_at'
  ) THEN
    CREATE TRIGGER update_user_profiles_updated_at
      BEFORE UPDATE ON user_profiles
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;
