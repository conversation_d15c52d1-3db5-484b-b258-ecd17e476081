/*
  # Add transcription column to messages table

  1. Changes
    - Add `transcription` column to `messages` table to store voice transcriptions
    - Column is nullable since not all messages will have transcriptions
    - Uses TEXT type to accommodate variable-length transcriptions

  2. Notes
    - This migration is safe to run on existing data
    - Existing messages will have NULL transcription values
    - New voice messages will populate this field
*/

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'messages' AND column_name = 'transcription'
  ) THEN
    ALTER TABLE messages ADD COLUMN transcription text;
  END IF;
END $$;