/*
  # Update Voice Credits System to 12 Credits with Weekly Recharge
  
  This migration updates the voice credits system from 10 to 12 credits
  and implements a weekly recharge mechanism.
  
  ## Changes:
  - Update existing users from 10 to 12 credits
  - Add voice credit recharge tracking table
  - Create functions for weekly credit recharge
  - Add client-side recharge check function
  
  ## Weekly Recharge System:
  - Users get 12 credits recharged every week
  - Recharge happens on the same day of week as their last reset
  - Tracks recharge history for analytics
*/

-- Create table to track voice credit recharges
CREATE TABLE IF NOT EXISTS voice_credit_recharges (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  recharge_date timestamptz NOT NULL DEFAULT now(),
  credits_added integer NOT NULL DEFAULT 12,
  credits_before integer NOT NULL DEFAULT 0,
  credits_after integer NOT NULL DEFAULT 12,
  recharge_type text NOT NULL CHECK (recharge_type IN ('weekly_auto', 'manual', 'system', 'client_backup')) DEFAULT 'weekly_auto',
  triggered_by text DEFAULT 'system',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Add RLS policies for voice_credit_recharges
ALTER TABLE voice_credit_recharges ENABLE ROW LEVEL SECURITY;

-- Users can only see their own recharge history
CREATE POLICY "Users can view own recharge history" ON voice_credit_recharges
  FOR SELECT USING (auth.uid() = user_id);

-- Only authenticated users can insert recharge records (for client-side backup)
CREATE POLICY "Authenticated users can insert recharge records" ON voice_credit_recharges
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_voice_credit_recharges_user_id ON voice_credit_recharges(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_credit_recharges_date ON voice_credit_recharges(recharge_date);
CREATE INDEX IF NOT EXISTS idx_voice_credit_recharges_type ON voice_credit_recharges(recharge_type);

-- Add trigger for updated_at
CREATE TRIGGER update_voice_credit_recharges_updated_at
  BEFORE UPDATE ON voice_credit_recharges
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to check if user needs weekly recharge
CREATE OR REPLACE FUNCTION check_user_needs_weekly_recharge(target_user_id uuid)
RETURNS boolean AS $$
DECLARE
  last_reset timestamptz;
  days_since_reset integer;
BEGIN
  -- Get the last credit reset time
  SELECT last_credit_reset INTO last_reset
  FROM user_profiles
  WHERE user_id = target_user_id;
  
  -- If no profile exists, user needs initialization
  IF last_reset IS NULL THEN
    RETURN true;
  END IF;
  
  -- Calculate days since last reset
  days_since_reset := EXTRACT(days FROM (now() - last_reset));
  
  -- Return true if 7 or more days have passed
  RETURN days_since_reset >= 7;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to perform weekly credit recharge for a user
CREATE OR REPLACE FUNCTION recharge_user_weekly_credits(
  target_user_id uuid,
  recharge_type_param text DEFAULT 'weekly_auto',
  triggered_by_param text DEFAULT 'system'
)
RETURNS jsonb AS $$
DECLARE
  current_credits integer;
  credits_used integer;
  recharge_record_id uuid;
  result jsonb;
BEGIN
  -- Get current credit status
  SELECT voice_credits, voice_credits_used INTO current_credits, credits_used
  FROM user_profiles
  WHERE user_id = target_user_id;
  
  -- If user doesn't exist, create profile with 12 credits
  IF current_credits IS NULL THEN
    INSERT INTO user_profiles (user_id, voice_credits, voice_credits_used, last_credit_reset)
    VALUES (target_user_id, 12, 0, now())
    ON CONFLICT (user_id) DO UPDATE SET
      voice_credits = 12,
      voice_credits_used = 0,
      last_credit_reset = now(),
      updated_at = now();
    
    current_credits := 0;
    credits_used := 0;
  ELSE
    -- Update user credits to 12 and reset last_credit_reset
    UPDATE user_profiles
    SET 
      voice_credits = 12,
      voice_credits_used = 0,
      last_credit_reset = now(),
      updated_at = now()
    WHERE user_id = target_user_id;
  END IF;
  
  -- Record the recharge
  INSERT INTO voice_credit_recharges (
    user_id,
    credits_added,
    credits_before,
    credits_after,
    recharge_type,
    triggered_by
  ) VALUES (
    target_user_id,
    12,
    current_credits,
    12,
    recharge_type_param,
    triggered_by_param
  ) RETURNING id INTO recharge_record_id;
  
  -- Return result
  result := jsonb_build_object(
    'success', true,
    'user_id', target_user_id,
    'credits_before', current_credits,
    'credits_after', 12,
    'credits_added', 12,
    'recharge_id', recharge_record_id,
    'recharge_type', recharge_type_param
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to recharge all users who need weekly recharge
CREATE OR REPLACE FUNCTION recharge_all_weekly_credits()
RETURNS jsonb AS $$
DECLARE
  user_record RECORD;
  recharge_count integer := 0;
  error_count integer := 0;
  results jsonb := '[]'::jsonb;
  user_result jsonb;
BEGIN
  -- Loop through all users who need recharge
  FOR user_record IN
    SELECT DISTINCT u.id as user_id
    FROM auth.users u
    LEFT JOIN user_profiles p ON u.id = p.user_id
    WHERE 
      p.last_credit_reset IS NULL 
      OR EXTRACT(days FROM (now() - p.last_credit_reset)) >= 7
  LOOP
    BEGIN
      -- Recharge this user
      SELECT recharge_user_weekly_credits(user_record.user_id, 'weekly_auto', 'system_batch')
      INTO user_result;
      
      results := results || jsonb_build_array(user_result);
      recharge_count := recharge_count + 1;
      
    EXCEPTION WHEN OTHERS THEN
      error_count := error_count + 1;
      results := results || jsonb_build_array(jsonb_build_object(
        'success', false,
        'user_id', user_record.user_id,
        'error', SQLERRM
      ));
    END;
  END LOOP;
  
  RETURN jsonb_build_object(
    'total_recharged', recharge_count,
    'total_errors', error_count,
    'results', results,
    'execution_time', now()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION check_user_needs_weekly_recharge(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION recharge_user_weekly_credits(uuid, text, text) TO authenticated;
GRANT EXECUTE ON FUNCTION recharge_all_weekly_credits() TO service_role;

-- Update existing users from 10 to 12 credits
UPDATE user_profiles 
SET 
  voice_credits = CASE 
    WHEN voice_credits = 10 THEN 12 
    ELSE voice_credits 
  END,
  last_credit_reset = now(),
  updated_at = now()
WHERE voice_credits IS NOT NULL;

-- Update the default value for new users
ALTER TABLE user_profiles 
ALTER COLUMN voice_credits SET DEFAULT 12;

-- Update the initialization function to use 12 credits
CREATE OR REPLACE FUNCTION initialize_user_voice_credits()
RETURNS TRIGGER AS $$
BEGIN
  -- Set default voice credits for new user profiles
  IF NEW.voice_credits IS NULL THEN
    NEW.voice_credits := 12;
  END IF;
  
  IF NEW.voice_credits_used IS NULL THEN
    NEW.voice_credits_used := 0;
  END IF;
  
  IF NEW.last_credit_reset IS NULL THEN
    NEW.last_credit_reset := now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update the user_voice_usage view to reflect new defaults
CREATE OR REPLACE VIEW user_voice_usage AS
SELECT
  u.id as user_id,
  u.email,
  COALESCE(p.voice_credits, 12) as credits_remaining,
  COALESCE(p.voice_credits_used, 0) as credits_used,
  COUNT(s.id) as total_sessions,
  SUM(CASE WHEN s.session_status = 'completed' THEN s.duration_seconds ELSE 0 END) as total_call_time_seconds,
  MAX(s.session_start) as last_call_time,
  COALESCE(p.last_credit_reset, now()) as last_credit_reset
FROM auth.users u
LEFT JOIN user_profiles p ON u.id = p.user_id
LEFT JOIN voice_call_sessions s ON u.id = s.user_id
WHERE u.id = auth.uid() -- Built-in RLS for views
GROUP BY u.id, u.email, p.voice_credits, p.voice_credits_used, p.last_credit_reset;

-- Update the get_user_voice_credits function to use 12 as default
CREATE OR REPLACE FUNCTION get_user_voice_credits(target_user_id uuid)
RETURNS TABLE(
  credits_remaining integer,
  credits_used integer,
  total_sessions bigint,
  last_session timestamptz,
  last_credit_reset timestamptz
) AS $$
BEGIN
  -- Handle case where user doesn't have a profile yet
  IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE user_id = target_user_id) THEN
    RETURN QUERY
    SELECT 
      12 as credits_remaining,
      0 as credits_used,
      0::bigint as total_sessions,
      NULL::timestamptz as last_session,
      now() as last_credit_reset;
    RETURN;
  END IF;
  
  RETURN QUERY
  SELECT 
    COALESCE(p.voice_credits, 12) as credits_remaining,
    COALESCE(p.voice_credits_used, 0) as credits_used,
    COALESCE(COUNT(s.id), 0) as total_sessions,
    MAX(s.session_start) as last_session,
    COALESCE(p.last_credit_reset, now()) as last_credit_reset
  FROM user_profiles p
  LEFT JOIN voice_call_sessions s ON p.user_id = s.user_id AND s.session_status = 'completed'
  WHERE p.user_id = target_user_id
  GROUP BY p.voice_credits, p.voice_credits_used, p.last_credit_reset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
