/*
  # Seed Initial Self-Reflection Questions
  
  This migration seeds the initial 5 questions for the Temani Self-Reflection Check-In
  based on the user requirements. These questions are configured dynamically and can
  be modified without code changes.
*/

-- Insert the initial question set
INSERT INTO question_sets (name, version, description, is_active, language)
VALUES (
  'Temani Self-Reflection Check-In',
  'v1.0',
  'Initial self-reflection questions for emotional state assessment',
  true,
  'id'
);

-- Get the question set ID for reference
DO $$
DECLARE
  question_set_uuid uuid;
BEGIN
  SELECT id INTO question_set_uuid 
  FROM question_sets 
  WHERE name = 'Temani Self-Reflection Check-In' AND version = 'v1.0';

  -- Question 1: General Feeling Today
  INSERT INTO questions (
    question_set_id,
    question_key,
    question_text,
    question_type,
    options,
    scoring_config,
    order_index,
    is_required
  ) VALUES (
    question_set_uuid,
    'general_feeling',
    '{"id": "<PERSON><PERSON> boleh tahu, gimana perasaan kamu hari ini?"}',
    'multiple_choice',
    '[
      {
        "id": "very_good",
        "text": {"id": "Aku merasa cukup baik"},
        "emoji": "😊",
        "score": 1,
        "color": "#4EAF64"
      },
      {
        "id": "neutral",
        "text": {"id": "B aja, gak terlalu gimana-gimana"},
        "emoji": "😐",
        "score": 2,
        "color": "#FFCE5C"
      },
      {
        "id": "sad",
        "text": {"id": "Lagi agak sedih atau capek"},
        "emoji": "😞",
        "score": 3,
        "color": "#ED7E1C"
      },
      {
        "id": "very_sad",
        "text": {"id": "Lagi berat banget rasanya"},
        "emoji": "😣",
        "score": 4,
        "color": "#926247"
      },
      {
        "id": "skip",
        "text": {"id": "Aku lebih nyaman skip pertanyaan ini"},
        "emoji": "🕊️",
        "score": 0,
        "color": "#A694F5"
      }
    ]',
    '{"method": "direct", "maxScore": 4}',
    1,
    true
  );

  -- Question 2: Energy/Spirit Level
  INSERT INTO questions (
    question_set_id,
    question_key,
    question_text,
    question_type,
    options,
    scoring_config,
    order_index,
    is_required
  ) VALUES (
    question_set_uuid,
    'energy_level',
    '{"id": "Gimana dengan energi atau semangat kamu hari ini?"}',
    'multiple_choice',
    '[
      {
        "id": "energetic",
        "text": {"id": "Semangat dan cukup bertenaga"},
        "emoji": "⚡",
        "score": 1,
        "color": "#4EAF64"
      },
      {
        "id": "tired",
        "text": {"id": "Cepat lelah atau susah fokus"},
        "emoji": "💤",
        "score": 2,
        "color": "#FFCE5C"
      },
      {
        "id": "drained",
        "text": {"id": "Ngerasa drained banget akhir-akhir ini"},
        "emoji": "🪫",
        "score": 3,
        "color": "#ED7E1C"
      },
      {
        "id": "skip",
        "text": {"id": "Skip aja dulu"},
        "emoji": "🚫",
        "score": 0,
        "color": "#A694F5"
      }
    ]',
    '{"method": "direct", "maxScore": 3}',
    2,
    true
  );

  -- Question 3: Things Bothering You
  INSERT INTO questions (
    question_set_id,
    question_key,
    question_text,
    question_type,
    options,
    scoring_config,
    order_index,
    is_required
  ) VALUES (
    question_set_uuid,
    'bothering_things',
    '{"id": "Ada hal yang akhir-akhir ini sering kepikiran atau bikin kamu gak tenang?"}',
    'multiple_choice',
    '[
      {
        "id": "heavy_things",
        "text": {"id": "Ya, ada beberapa hal yang cukup berat"},
        "emoji": "😰",
        "score": 3,
        "color": "#ED7E1C"
      },
      {
        "id": "not_comfortable",
        "text": {"id": "Ada, tapi aku belum nyaman cerita sekarang"},
        "emoji": "😔",
        "score": 2,
        "color": "#FFCE5C"
      },
      {
        "id": "nothing_major",
        "text": {"id": "Gak ada hal yang terlalu mengganggu"},
        "emoji": "😌",
        "score": 1,
        "color": "#4EAF64"
      },
      {
        "id": "skip",
        "text": {"id": "Skip"},
        "emoji": "🤐",
        "score": 0,
        "color": "#A694F5"
      }
    ]',
    '{"method": "direct", "maxScore": 3}',
    3,
    true
  );

  -- Question 4: Support Preference (No scoring - preference only)
  INSERT INTO questions (
    question_set_id,
    question_key,
    question_text,
    question_type,
    options,
    scoring_config,
    order_index,
    is_required
  ) VALUES (
    question_set_uuid,
    'support_preference',
    '{"id": "Temani bisa nemenin kamu dengan berbagai cara. Hari ini kamu lebih butuh yang mana?"}',
    'multiple_choice',
    '[
      {
        "id": "light_chat",
        "text": {"id": "Cuma butuh teman ngobrol ringan"},
        "emoji": "💬",
        "score": 0,
        "preference": "light_conversation"
      },
      {
        "id": "validation",
        "text": {"id": "Mau cerita dan divalidasi aja dulu"},
        "emoji": "🤗",
        "score": 0,
        "preference": "emotional_validation"
      },
      {
        "id": "insight",
        "text": {"id": "Lagi pengen dapat insight atau refleksi"},
        "emoji": "💡",
        "score": 0,
        "preference": "reflection_insights"
      },
      {
        "id": "coping_skills",
        "text": {"id": "Pengen belajar coping atau skill baru"},
        "emoji": "🛠️",
        "score": 0,
        "preference": "skill_building"
      },
      {
        "id": "unsure",
        "text": {"id": "Aku belum tahu / skip dulu aja"},
        "emoji": "🤷",
        "score": 0,
        "preference": "unsure"
      }
    ]',
    '{"method": "none", "maxScore": 0}',
    4,
    true
  );

  -- Question 5: Safety Check (Critical scoring)
  INSERT INTO questions (
    question_set_id,
    question_key,
    question_text,
    question_type,
    options,
    scoring_config,
    order_index,
    is_required,
    metadata
  ) VALUES (
    question_set_uuid,
    'safety_check',
    '{"id": "Kadang kita ngerasa sangat berat sampai gak tahu harus gimana. Apakah akhir-akhir ini kamu sempat merasa ingin menyakiti diri sendiri?"}',
    'multiple_choice',
    '[
      {
        "id": "yes_thoughts",
        "text": {"id": "Iya, pernah terpikir seperti itu"},
        "emoji": "⚠️",
        "score": 10,
        "color": "#EF4444",
        "critical": true
      },
      {
        "id": "no",
        "text": {"id": "Tidak"},
        "emoji": "✅",
        "score": 0,
        "color": "#4EAF64"
      },
      {
        "id": "prefer_not_answer",
        "text": {"id": "Lebih nyaman gak jawab"},
        "emoji": "🤐",
        "score": 2,
        "color": "#FFCE5C"
      }
    ]',
    '{"method": "direct", "maxScore": 10, "critical": true}',
    5,
    true,
    '{"isSafetyQuestion": true, "requiresImmediateAction": true}'
  );

  -- Insert risk assessment rules
  INSERT INTO risk_assessment_rules (
    question_set_id,
    rule_name,
    score_ranges,
    actions,
    is_active
  ) VALUES (
    question_set_uuid,
    'Standard Risk Assessment',
    '{
      "green": {"min": 0, "max": 4, "label": "Kondisi emosional relatif stabil"},
      "yellow": {"min": 5, "max": 7, "label": "Ada tanda kelelahan emosional atau distress ringan"},
      "red": {"min": 8, "max": 9, "label": "Distress cukup tinggi"},
      "emergency": {"min": 10, "max": 999, "label": "Ada potensi risiko - perlu perhatian khusus"}
    }',
    '{
      "green": {
        "aiTone": "warm_casual",
        "message": "Temani bisa mulai dengan gaya ringan, hangat",
        "actions": ["normal_conversation"]
      },
      "yellow": {
        "aiTone": "supportive_careful",
        "message": "Mulai dengan validasi dan check-in lebih hati-hati",
        "actions": ["emotional_validation", "gentle_check_in"]
      },
      "red": {
        "aiTone": "empathetic_gentle",
        "message": "AI mulai dengan empati yang tinggi, pendekatan sangat pelan",
        "actions": ["high_empathy", "avoid_challenging", "frequent_check_ins"]
      },
      "emergency": {
        "aiTone": "crisis_support",
        "message": "Aktifkan protokol keselamatan",
        "actions": ["safety_protocol", "professional_resources", "crisis_support"]
      }
    }',
    true
  );

END $$;
