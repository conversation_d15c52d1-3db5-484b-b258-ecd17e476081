/*
  # Voice Credits System Migration
  
  This migration adds a credits-based rate limiting system for voice calls.
  
  ## Features:
  - Voice credits tracking in user_profiles table
  - Voice call sessions tracking for usage monitoring
  - Automatic credit initialization for new users
  - RLS policies for security
  - Triggers for automatic credit management
  
  ## Credit System:
  - New users get 10 credits
  - 1 credit = 10 seconds of call time
  - Maximum call duration: 2 minutes (120 seconds)
  - Credits are deducted after each call based on duration
*/

-- Add voice credits columns to user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS voice_credits integer DEFAULT 10,
ADD COLUMN IF NOT EXISTS voice_credits_used integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_credit_reset timestamptz DEFAULT now();

-- Create voice_call_sessions table for tracking usage
CREATE TABLE IF NOT EXISTS voice_call_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_start timestamptz NOT NULL DEFAULT now(),
  session_end timestamptz,
  duration_seconds integer DEFAULT 0,
  credits_used integer DEFAULT 0,
  agent_id text,
  session_status text DEFAULT 'active' CHECK (session_status IN ('active', 'completed', 'interrupted', 'failed')),
  session_metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create or ensure update_updated_at_column function exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security
ALTER TABLE voice_call_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for voice_call_sessions
CREATE POLICY "Users can read own voice sessions"
  ON voice_call_sessions FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own voice sessions"
  ON voice_call_sessions FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own voice sessions"
  ON voice_call_sessions FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_voice_call_sessions_user_id ON voice_call_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_call_sessions_status ON voice_call_sessions(session_status);
CREATE INDEX IF NOT EXISTS idx_voice_call_sessions_start_time ON voice_call_sessions(session_start);
CREATE INDEX IF NOT EXISTS idx_user_profiles_voice_credits ON user_profiles(voice_credits);

-- Trigger for updated_at on voice_call_sessions
CREATE TRIGGER update_voice_call_sessions_updated_at
  BEFORE UPDATE ON voice_call_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to initialize voice credits for new users
CREATE OR REPLACE FUNCTION initialize_user_voice_credits()
RETURNS TRIGGER AS $$
BEGIN
  -- Set default voice credits for new user profiles
  IF NEW.voice_credits IS NULL THEN
    NEW.voice_credits := 10;
  END IF;
  
  IF NEW.voice_credits_used IS NULL THEN
    NEW.voice_credits_used := 0;
  END IF;
  
  IF NEW.last_credit_reset IS NULL THEN
    NEW.last_credit_reset := now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to initialize credits on user profile creation
CREATE TRIGGER trigger_initialize_voice_credits
  BEFORE INSERT ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION initialize_user_voice_credits();

-- Function to update user credits when a voice session ends
CREATE OR REPLACE FUNCTION update_user_credits_on_session_end()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update credits when session status changes to completed
  IF NEW.session_status = 'completed' AND OLD.session_status != 'completed' THEN
    -- Update user's voice credits
    UPDATE user_profiles 
    SET 
      voice_credits = GREATEST(0, voice_credits - NEW.credits_used),
      voice_credits_used = voice_credits_used + NEW.credits_used,
      updated_at = now()
    WHERE user_id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update user credits when session ends
CREATE TRIGGER trigger_update_credits_on_session_end
  AFTER UPDATE ON voice_call_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_user_credits_on_session_end();

-- Function to get user voice credits (for easy querying)
CREATE OR REPLACE FUNCTION get_user_voice_credits(target_user_id uuid)
RETURNS TABLE(
  credits_remaining integer,
  credits_used integer,
  total_sessions bigint,
  last_session timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(p.voice_credits, 10) as credits_remaining,
    COALESCE(p.voice_credits_used, 0) as credits_used,
    COALESCE(COUNT(s.id), 0) as total_sessions,
    MAX(s.session_start) as last_session
  FROM user_profiles p
  LEFT JOIN voice_call_sessions s ON p.user_id = s.user_id AND s.session_status = 'completed'
  WHERE p.user_id = target_user_id
  GROUP BY p.voice_credits, p.voice_credits_used;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_user_voice_credits(uuid) TO authenticated;

-- Initialize voice credits for existing users who don't have them
UPDATE user_profiles 
SET 
  voice_credits = 10,
  voice_credits_used = 0,
  last_credit_reset = now()
WHERE voice_credits IS NULL;

-- Create view for voice usage analytics
CREATE OR REPLACE VIEW user_voice_usage AS
SELECT
  u.id as user_id,
  u.email,
  COALESCE(p.voice_credits, 10) as credits_remaining,
  COALESCE(p.voice_credits_used, 0) as credits_used,
  COUNT(s.id) as total_sessions,
  SUM(CASE WHEN s.session_status = 'completed' THEN s.duration_seconds ELSE 0 END) as total_call_time_seconds,
  MAX(s.session_start) as last_call_time,
  p.last_credit_reset
FROM auth.users u
LEFT JOIN user_profiles p ON u.id = p.user_id
LEFT JOIN voice_call_sessions s ON u.id = s.user_id
WHERE u.id = auth.uid() -- Built-in RLS for views
GROUP BY u.id, u.email, p.voice_credits, p.voice_credits_used, p.last_credit_reset;

-- Grant access to the view
GRANT SELECT ON user_voice_usage TO authenticated;

-- Helper function to get table columns (for testing)
CREATE OR REPLACE FUNCTION get_table_columns(table_name text)
RETURNS TABLE(column_name text, data_type text) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.column_name::text,
    c.data_type::text
  FROM information_schema.columns c
  WHERE c.table_name = get_table_columns.table_name
    AND c.table_schema = 'public';
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on helper function
GRANT EXECUTE ON FUNCTION get_table_columns(text) TO authenticated;
