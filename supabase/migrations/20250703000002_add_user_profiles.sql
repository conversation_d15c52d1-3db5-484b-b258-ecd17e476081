/*
  # User Profiles Table for Completion Status and Quick Lookup
  
  This migration creates a user_profiles table to store completion status
  and provide quick lookup for user preferences, replacing local storage.
  
  ## Features:
  - Quick completion status lookup (onboarding, self-reflection)
  - Latest session reference for performance
  - User preferences storage
  - Prevents duplication with unique constraints
  - RLS policies for security
*/

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Quick completion status lookup
  onboarding_completed boolean DEFAULT false,
  self_reflection_completed boolean DEFAULT false,
  
  -- Latest session reference for quick access
  latest_session_id uuid REFERENCES self_reflection_sessions(id) ON DELETE SET NULL,
  latest_risk_level risk_level,
  latest_completed_at timestamptz,
  
  -- User preferences and settings
  preferences jsonb DEFAULT '{}',
  
  -- Timestamps
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Prevent duplication - one profile per user
  CONSTRAINT unique_user_profile UNIQUE (user_id)
);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- R<PERSON> Policies
CREATE POLICY "Users can read own profile"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own profile"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_completion ON user_profiles(onboarding_completed, self_reflection_completed);
CREATE INDEX IF NOT EXISTS idx_user_profiles_latest_session ON user_profiles(latest_session_id);

-- Trigger for updated_at
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to sync user profile with latest session
CREATE OR REPLACE FUNCTION sync_user_profile_with_session()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user profile when a new self-reflection session is created
  INSERT INTO user_profiles (
    user_id,
    self_reflection_completed,
    latest_session_id,
    latest_risk_level,
    latest_completed_at
  ) VALUES (
    NEW.user_id,
    true,
    NEW.id,
    NEW.risk_level,
    NEW.completed_at
  )
  ON CONFLICT (user_id) 
  DO UPDATE SET
    self_reflection_completed = true,
    latest_session_id = NEW.id,
    latest_risk_level = NEW.risk_level,
    latest_completed_at = NEW.completed_at,
    updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update user profile when session is created
CREATE TRIGGER sync_profile_on_session_insert
  AFTER INSERT ON self_reflection_sessions
  FOR EACH ROW
  EXECUTE FUNCTION sync_user_profile_with_session();

-- Function to get or create user profile
CREATE OR REPLACE FUNCTION get_or_create_user_profile(target_user_id uuid)
RETURNS user_profiles AS $$
DECLARE
  profile user_profiles;
BEGIN
  -- Try to get existing profile
  SELECT * INTO profile
  FROM user_profiles
  WHERE user_id = target_user_id;
  
  -- If no profile exists, create one
  IF NOT FOUND THEN
    INSERT INTO user_profiles (user_id)
    VALUES (target_user_id)
    RETURNING * INTO profile;
  END IF;
  
  RETURN profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_or_create_user_profile(uuid) TO authenticated;

-- Function to check if user has completed self-reflection
CREATE OR REPLACE FUNCTION has_completed_self_reflection(target_user_id uuid)
RETURNS boolean AS $$
DECLARE
  profile_exists boolean;
  session_exists boolean;
BEGIN
  -- First check user_profiles table (fast)
  SELECT self_reflection_completed INTO profile_exists
  FROM user_profiles
  WHERE user_id = target_user_id;
  
  -- If profile exists and shows completed, return true
  IF FOUND AND profile_exists THEN
    RETURN true;
  END IF;
  
  -- Fallback: check if user has any self-reflection sessions (slower but accurate)
  SELECT EXISTS(
    SELECT 1 FROM self_reflection_sessions 
    WHERE user_id = target_user_id
  ) INTO session_exists;
  
  -- If session exists but profile doesn't reflect it, sync the profile
  IF session_exists THEN
    INSERT INTO user_profiles (user_id, self_reflection_completed)
    VALUES (target_user_id, true)
    ON CONFLICT (user_id) 
    DO UPDATE SET 
      self_reflection_completed = true,
      updated_at = now();
  END IF;
  
  RETURN session_exists;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION has_completed_self_reflection(uuid) TO authenticated;

-- Create view for user completion status (for easy querying)
CREATE OR REPLACE VIEW user_completion_status AS
SELECT
  u.id as user_id,
  u.email,
  COALESCE(p.onboarding_completed, false) as onboarding_completed,
  COALESCE(p.self_reflection_completed, false) as self_reflection_completed,
  p.latest_risk_level,
  p.latest_completed_at,
  p.created_at as profile_created_at,
  p.updated_at as profile_updated_at
FROM auth.users u
LEFT JOIN user_profiles p ON u.id = p.user_id
WHERE u.id = auth.uid(); -- Built-in RLS for views

-- Grant access to the view
GRANT SELECT ON user_completion_status TO authenticated;
