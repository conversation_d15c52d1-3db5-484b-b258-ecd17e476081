/*
  # Create mood tracking tables for daily mood monitoring feature

  1. New Tables
    - `mood_entries`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `entry_date` (date, date of the mood entry)
      - `day_of_week` (text, Indonesian day abbreviation)
      - `mood_level` (integer, 1-5 scale)
      - `physical_health` (boolean, true=healthy, false=sick)
      - `sleep_hours` (numeric, 0-24 hours)
      - `sleep_quality` (integer, 0-10 scale)
      - `stress_level` (integer, 1-5 scale)
      - `daily_feeling` (integer, 1-4 scale)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

    - `mood_analytics` (optional, for pre-calculated analytics)
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `period_type` (text, 'week' or 'month')
      - `period_start` (date)
      - `period_end` (date)
      - Aggregated metrics for performance

  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to access their own mood data
    - Add indexes for performance

  3. Functions and Triggers
    - Add trigger to update updated_at timestamp on mood_entries
    - Constraint to ensure one entry per user per date
*/

-- Create mood_entries table
CREATE TABLE IF NOT EXISTS mood_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Date and context
  entry_date date NOT NULL,
  day_of_week text NOT NULL CHECK (day_of_week IN ('Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min')),
  
  -- Mood metrics (matching TypeScript types exactly)
  mood_level integer CHECK (mood_level >= 1 AND mood_level <= 5),
  physical_health boolean,
  sleep_hours numeric(3,1) CHECK (sleep_hours >= 0 AND sleep_hours <= 24),
  sleep_quality integer CHECK (sleep_quality >= 0 AND sleep_quality <= 10),
  stress_level integer CHECK (stress_level >= 1 AND stress_level <= 5),
  daily_feeling integer CHECK (daily_feeling >= 1 AND daily_feeling <= 4),
  
  -- Metadata
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create mood_analytics table for pre-calculated analytics
CREATE TABLE IF NOT EXISTS mood_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Period definition
  period_type text NOT NULL CHECK (period_type IN ('week', 'month')),
  period_start date NOT NULL,
  period_end date NOT NULL,
  
  -- Aggregated metrics
  avg_mood numeric(3,2),
  avg_sleep_hours numeric(3,2),
  avg_sleep_quality numeric(3,2),
  avg_stress_level numeric(3,2),
  avg_daily_feeling numeric(3,2),
  healthy_days integer DEFAULT 0,
  sick_days integer DEFAULT 0,
  total_entries integer DEFAULT 0,
  
  -- Metadata
  calculated_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE mood_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for mood_entries
CREATE POLICY "Users can read own mood entries"
  ON mood_entries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own mood entries"
  ON mood_entries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own mood entries"
  ON mood_entries
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own mood entries"
  ON mood_entries
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create policies for mood_analytics
CREATE POLICY "Users can read own mood analytics"
  ON mood_analytics
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own mood analytics"
  ON mood_analytics
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own mood analytics"
  ON mood_analytics
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_id ON mood_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_mood_entries_entry_date ON mood_entries(entry_date DESC);
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_date ON mood_entries(user_id, entry_date DESC);
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_created ON mood_entries(user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_mood_analytics_user_id ON mood_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_mood_analytics_period ON mood_analytics(user_id, period_type, period_start);

-- Create trigger to update mood_entries timestamp when entry is updated
CREATE TRIGGER update_mood_entries_updated_at
  BEFORE UPDATE ON mood_entries
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create trigger to update mood_analytics timestamp when analytics are updated
CREATE TRIGGER update_mood_analytics_updated_at
  BEFORE UPDATE ON mood_analytics
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add unique constraint to prevent duplicate entries for same user and date
ALTER TABLE mood_entries 
ADD CONSTRAINT unique_user_date 
UNIQUE (user_id, entry_date);

-- Add unique constraint for analytics periods
ALTER TABLE mood_analytics
ADD CONSTRAINT unique_user_period
UNIQUE (user_id, period_type, period_start);
