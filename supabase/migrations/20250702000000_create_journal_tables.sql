/*
  # Create journal tables for personal journaling feature

  1. New Tables
    - `journal_questions`
      - `id` (uuid, primary key)
      - `question_text` (text, the journal question)
      - `question_order` (integer, display order)
      - `is_active` (boolean, whether question is active)
      - `created_at` (timestamp)
    
    - `journal_entries`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `question_id` (uuid, foreign key to journal_questions)
      - `answer` (text, user's journal entry)
      - `entry_date` (date, date of the journal entry)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to access their own journal entries
    - Add policies for reading journal questions (public read for authenticated users)
    - Add indexes for performance

  3. Functions and Triggers
    - Add trigger to update updated_at timestamp on journal_entries
*/

-- Create journal_questions table
CREATE TABLE IF NOT EXISTS journal_questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question_text text NOT NULL,
  question_order integer NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Create journal_entries table
CREATE TABLE IF NOT EXISTS journal_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  question_id uuid NOT NULL REFERENCES journal_questions(id) ON DELETE CASCADE,
  answer text NOT NULL,
  entry_date date NOT NULL DEFAULT CURRENT_DATE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE journal_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE journal_entries ENABLE ROW LEVEL SECURITY;

-- Create policies for journal_questions (authenticated users can read all active questions)
CREATE POLICY "Authenticated users can read active journal questions"
  ON journal_questions
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Create policies for journal_entries
CREATE POLICY "Users can read own journal entries"
  ON journal_entries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own journal entries"
  ON journal_entries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own journal entries"
  ON journal_entries
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own journal entries"
  ON journal_entries
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_journal_questions_order ON journal_questions(question_order);
CREATE INDEX IF NOT EXISTS idx_journal_questions_active ON journal_questions(is_active);
CREATE INDEX IF NOT EXISTS idx_journal_entries_user_id ON journal_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_journal_entries_entry_date ON journal_entries(entry_date DESC);
CREATE INDEX IF NOT EXISTS idx_journal_entries_user_date ON journal_entries(user_id, entry_date DESC);
CREATE INDEX IF NOT EXISTS idx_journal_entries_question_id ON journal_entries(question_id);

-- Create function to update journal_entries updated_at timestamp
CREATE OR REPLACE FUNCTION update_journal_entry_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update journal_entries timestamp when entry is updated
CREATE TRIGGER update_journal_entry_timestamp_trigger
  BEFORE UPDATE ON journal_entries
  FOR EACH ROW
  EXECUTE FUNCTION update_journal_entry_timestamp();

-- Add unique constraint to prevent duplicate entries for same user, question, and date
ALTER TABLE journal_entries 
ADD CONSTRAINT unique_user_question_date 
UNIQUE (user_id, question_id, entry_date);
