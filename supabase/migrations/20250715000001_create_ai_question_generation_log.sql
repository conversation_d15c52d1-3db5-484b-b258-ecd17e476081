/*
  # AI Question Generation Tracking
  
  This migration creates tables and functions to track AI-generated questions
  for analytics, rate limiting, and user feedback collection.
*/

-- Create table to track AI question generation requests
CREATE TABLE IF NOT EXISTS ai_question_generations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  base_question_id uuid REFERENCES journal_questions(id) ON DELETE SET NULL,
  generation_type text NOT NULL CHECK (generation_type IN ('follow_up', 'alternative', 'related')),
  prompt_used text NOT NULL,
  generated_questions jsonb NOT NULL DEFAULT '[]',
  selected_questions jsonb DEFAULT '[]',
  generation_context jsonb DEFAULT '{}',
  model_used text,
  processing_time_ms integer,
  success boolean DEFAULT true,
  error_message text,
  user_feedback jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes for performance and analytics
CREATE INDEX IF NOT EXISTS idx_ai_generations_user_id ON ai_question_generations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_generations_created_at ON ai_question_generations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_generations_type ON ai_question_generations(generation_type);
CREATE INDEX IF NOT EXISTS idx_ai_generations_success ON ai_question_generations(success);
CREATE INDEX IF NOT EXISTS idx_ai_generations_base_question ON ai_question_generations(base_question_id) WHERE base_question_id IS NOT NULL;

-- Enable RLS
ALTER TABLE ai_question_generations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can read own AI generations"
  ON ai_question_generations
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own AI generations"
  ON ai_question_generations
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own AI generations"
  ON ai_question_generations
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create table for rate limiting AI requests
CREATE TABLE IF NOT EXISTS ai_generation_rate_limits (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  date_bucket date NOT NULL DEFAULT CURRENT_DATE,
  generation_count integer DEFAULT 0,
  last_generation_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure one record per user per day
  CONSTRAINT unique_user_date_bucket UNIQUE (user_id, date_bucket)
);

-- Create indexes for rate limiting
CREATE INDEX IF NOT EXISTS idx_rate_limits_user_date ON ai_generation_rate_limits(user_id, date_bucket);
CREATE INDEX IF NOT EXISTS idx_rate_limits_date_bucket ON ai_generation_rate_limits(date_bucket);

-- Enable RLS
ALTER TABLE ai_generation_rate_limits ENABLE ROW LEVEL SECURITY;

-- RLS Policies for rate limits
CREATE POLICY "Users can read own rate limits"
  ON ai_generation_rate_limits
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own rate limits"
  ON ai_generation_rate_limits
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Function to check and update rate limits
CREATE OR REPLACE FUNCTION check_ai_generation_rate_limit(
  target_user_id uuid,
  daily_limit integer DEFAULT 20
)
RETURNS jsonb AS $$
DECLARE
  current_count integer;
  rate_limit_record ai_generation_rate_limits%ROWTYPE;
  result jsonb;
BEGIN
  -- Get or create today's rate limit record
  INSERT INTO ai_generation_rate_limits (user_id, date_bucket, generation_count)
  VALUES (target_user_id, CURRENT_DATE, 0)
  ON CONFLICT (user_id, date_bucket) 
  DO NOTHING;
  
  -- Get current count
  SELECT * INTO rate_limit_record
  FROM ai_generation_rate_limits
  WHERE user_id = target_user_id AND date_bucket = CURRENT_DATE;
  
  current_count := COALESCE(rate_limit_record.generation_count, 0);
  
  -- Check if limit exceeded
  IF current_count >= daily_limit THEN
    result := jsonb_build_object(
      'allowed', false,
      'current_count', current_count,
      'daily_limit', daily_limit,
      'reset_time', (CURRENT_DATE + INTERVAL '1 day')::timestamptz
    );
  ELSE
    result := jsonb_build_object(
      'allowed', true,
      'current_count', current_count,
      'daily_limit', daily_limit,
      'remaining', daily_limit - current_count
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment rate limit counter
CREATE OR REPLACE FUNCTION increment_ai_generation_count(target_user_id uuid)
RETURNS void AS $$
BEGIN
  INSERT INTO ai_generation_rate_limits (user_id, date_bucket, generation_count, last_generation_at)
  VALUES (target_user_id, CURRENT_DATE, 1, now())
  ON CONFLICT (user_id, date_bucket) 
  DO UPDATE SET 
    generation_count = ai_generation_rate_limits.generation_count + 1,
    last_generation_at = now(),
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get AI generation analytics for user
CREATE OR REPLACE FUNCTION get_user_ai_generation_stats(target_user_id uuid)
RETURNS jsonb AS $$
DECLARE
  stats jsonb;
BEGIN
  SELECT jsonb_build_object(
    'total_generations', COUNT(*),
    'successful_generations', COUNT(*) FILTER (WHERE success = true),
    'failed_generations', COUNT(*) FILTER (WHERE success = false),
    'avg_processing_time_ms', AVG(processing_time_ms) FILTER (WHERE processing_time_ms IS NOT NULL),
    'generation_types', jsonb_object_agg(
      generation_type, 
      COUNT(*)
    ),
    'last_generation', MAX(created_at),
    'today_count', (
      SELECT COALESCE(generation_count, 0)
      FROM ai_generation_rate_limits
      WHERE user_id = target_user_id AND date_bucket = CURRENT_DATE
    )
  ) INTO stats
  FROM ai_question_generations
  WHERE user_id = target_user_id;
  
  RETURN COALESCE(stats, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION check_ai_generation_rate_limit(uuid, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_ai_generation_count(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_ai_generation_stats(uuid) TO authenticated;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_ai_generations_updated_at
  BEFORE UPDATE ON ai_question_generations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_limits_updated_at
  BEFORE UPDATE ON ai_generation_rate_limits
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up old AI generation logs (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_ai_generations(days_to_keep integer DEFAULT 90)
RETURNS integer AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM ai_question_generations
  WHERE created_at < (CURRENT_DATE - INTERVAL '1 day' * days_to_keep);
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Also clean up old rate limit records
  DELETE FROM ai_generation_rate_limits
  WHERE date_bucket < (CURRENT_DATE - INTERVAL '1 day' * days_to_keep);
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission for cleanup function (typically for admin use)
GRANT EXECUTE ON FUNCTION cleanup_old_ai_generations(integer) TO authenticated;
