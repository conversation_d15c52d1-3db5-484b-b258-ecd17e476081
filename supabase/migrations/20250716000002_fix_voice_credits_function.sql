/*
  # Fix Voice Credits Database Function
  
  This migration fixes the get_user_voice_credits function to include
  the missing last_credit_reset field that the service layer expects.
  
  ## Changes:
  - Update get_user_voice_credits function to return last_credit_reset
  - Ensure the function matches the VoiceCredits interface expectations
*/

-- Drop the existing function first to change return type
DROP FUNCTION IF EXISTS get_user_voice_credits(uuid);

-- Create the updated get_user_voice_credits function to include last_credit_reset
CREATE FUNCTION get_user_voice_credits(target_user_id uuid)
RETURNS TABLE(
  credits_remaining integer,
  credits_used integer,
  total_sessions bigint,
  last_session timestamptz,
  last_credit_reset timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(p.voice_credits, 10) as credits_remaining,
    COALESCE(p.voice_credits_used, 0) as credits_used,
    COALESCE(COUNT(s.id), 0) as total_sessions,
    MAX(s.session_start) as last_session,
    p.last_credit_reset as last_credit_reset
  FROM user_profiles p
  LEFT JOIN voice_call_sessions s ON p.user_id = s.user_id AND s.session_status = 'completed'
  WHERE p.user_id = target_user_id
  GROUP BY p.voice_credits, p.voice_credits_used, p.last_credit_reset;
END;
$$ LANGUAGE plpgsql;

-- Ensure the function has proper permissions
GRANT EXECUTE ON FUNCTION get_user_voice_credits(uuid) TO authenticated;

-- Update the user_voice_usage view to include last_credit_reset
CREATE OR REPLACE VIEW user_voice_usage AS
SELECT
  u.id as user_id,
  u.email,
  COALESCE(p.voice_credits, 10) as credits_remaining,
  COALESCE(p.voice_credits_used, 0) as credits_used,
  COUNT(s.id) as total_sessions,
  SUM(CASE WHEN s.session_status = 'completed' THEN s.duration_seconds ELSE 0 END) as total_call_time_seconds,
  MAX(s.session_start) as last_call_time,
  p.last_credit_reset
FROM auth.users u
LEFT JOIN user_profiles p ON u.id = p.user_id
LEFT JOIN voice_call_sessions s ON u.id = s.user_id
WHERE u.id = auth.uid() -- Built-in RLS for views
GROUP BY u.id, u.email, p.voice_credits, p.voice_credits_used, p.last_credit_reset;

-- Grant access to the updated view
GRANT SELECT ON user_voice_usage TO authenticated;
