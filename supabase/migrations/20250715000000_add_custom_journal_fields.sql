/*
  # Add Custom Journal Fields Support
  
  This migration extends the existing journal system to support user-defined custom fields
  while maintaining full backward compatibility with existing journal entries.
  
  ## Changes:
  1. Add field_type enum to distinguish system vs custom fields
  2. Extend journal_questions table for custom field support
  3. Add proper indexing for performance
  4. Maintain RLS policies for security
  5. Add validation functions
*/

-- Create field type enum
CREATE TYPE field_type AS ENUM ('system', 'custom_question', 'custom_title');

-- Add new columns to journal_questions table
ALTER TABLE journal_questions 
ADD COLUMN field_type field_type DEFAULT 'system',
ADD COLUMN user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
ADD COLUMN custom_config jsonb DEFAULT '{}',
ADD COLUMN parent_field_id uuid REFERENCES journal_questions(id) ON DELETE SET NULL,
ADD COLUMN ai_generated boolean DEFAULT false,
ADD COLUMN is_archived boolean DEFAULT false;

-- Update existing questions to be system type
UPDATE journal_questions SET field_type = 'system' WHERE field_type IS NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_journal_questions_user_type ON journal_questions(user_id, field_type) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_journal_questions_parent ON journal_questions(parent_field_id) WHERE parent_field_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_journal_questions_ai_generated ON journal_questions(ai_generated) WHERE ai_generated = true;
CREATE INDEX IF NOT EXISTS idx_journal_questions_archived ON journal_questions(is_archived, user_id) WHERE is_archived = false;

-- Update RLS policies for custom fields
DROP POLICY IF EXISTS "Authenticated users can read active journal questions" ON journal_questions;

-- New policy for system questions (everyone can read active system questions)
CREATE POLICY "Users can read active system questions"
  ON journal_questions
  FOR SELECT
  TO authenticated
  USING (field_type = 'system' AND is_active = true AND is_archived = false);

-- New policy for custom questions (users can only read their own custom fields)
CREATE POLICY "Users can read own custom fields"
  ON journal_questions
  FOR SELECT
  TO authenticated
  USING (
    field_type IN ('custom_question', 'custom_title') 
    AND user_id = auth.uid() 
    AND is_active = true 
    AND is_archived = false
  );

-- Policy for creating custom fields
CREATE POLICY "Users can create own custom fields"
  ON journal_questions
  FOR INSERT
  TO authenticated
  WITH CHECK (
    field_type IN ('custom_question', 'custom_title') 
    AND user_id = auth.uid()
  );

-- Policy for updating custom fields
CREATE POLICY "Users can update own custom fields"
  ON journal_questions
  FOR UPDATE
  TO authenticated
  USING (
    field_type IN ('custom_question', 'custom_title') 
    AND user_id = auth.uid()
  )
  WITH CHECK (
    field_type IN ('custom_question', 'custom_title') 
    AND user_id = auth.uid()
  );

-- Policy for deleting custom fields (soft delete by archiving)
CREATE POLICY "Users can archive own custom fields"
  ON journal_questions
  FOR UPDATE
  TO authenticated
  USING (
    field_type IN ('custom_question', 'custom_title') 
    AND user_id = auth.uid()
  )
  WITH CHECK (
    field_type IN ('custom_question', 'custom_title') 
    AND user_id = auth.uid()
  );

-- Function to validate custom field configuration
CREATE OR REPLACE FUNCTION validate_custom_field_config(config jsonb, field_type field_type)
RETURNS boolean AS $$
BEGIN
  -- Basic validation for custom_question fields
  IF field_type = 'custom_question' THEN
    -- Check for required fields and reasonable limits
    IF config ? 'maxLength' AND (config->>'maxLength')::int > 5000 THEN
      RETURN false;
    END IF;
    IF config ? 'placeholder' AND length(config->>'placeholder') > 200 THEN
      RETURN false;
    END IF;
  END IF;
  
  -- Basic validation for custom_title fields
  IF field_type = 'custom_title' THEN
    -- Title fields should be simpler
    IF config ? 'maxLength' AND (config->>'maxLength')::int > 100 THEN
      RETURN false;
    END IF;
  END IF;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Add constraint to validate custom_config
ALTER TABLE journal_questions 
ADD CONSTRAINT valid_custom_config 
CHECK (validate_custom_field_config(custom_config, field_type));

-- Function to get user's custom fields with proper ordering
CREATE OR REPLACE FUNCTION get_user_journal_fields(target_user_id uuid)
RETURNS TABLE (
  id uuid,
  question_text text,
  question_order integer,
  field_type field_type,
  custom_config jsonb,
  parent_field_id uuid,
  ai_generated boolean,
  created_at timestamptz
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    jq.id,
    jq.question_text,
    jq.question_order,
    jq.field_type,
    jq.custom_config,
    jq.parent_field_id,
    jq.ai_generated,
    jq.created_at
  FROM journal_questions jq
  WHERE 
    jq.is_active = true 
    AND jq.is_archived = false
    AND (
      (jq.field_type = 'system' AND jq.user_id IS NULL)
      OR 
      (jq.field_type IN ('custom_question', 'custom_title') AND jq.user_id = target_user_id)
    )
  ORDER BY jq.question_order ASC, jq.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_journal_fields(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_custom_field_config(jsonb, field_type) TO authenticated;

-- Add trigger to automatically set question_order for new custom fields
CREATE OR REPLACE FUNCTION set_custom_field_order()
RETURNS TRIGGER AS $$
DECLARE
  max_order integer;
BEGIN
  -- Only set order for custom fields if not explicitly provided
  IF NEW.field_type IN ('custom_question', 'custom_title') AND NEW.question_order IS NULL THEN
    -- Get the maximum order for this user's custom fields
    SELECT COALESCE(MAX(question_order), 0) INTO max_order
    FROM journal_questions
    WHERE user_id = NEW.user_id AND field_type IN ('custom_question', 'custom_title');
    
    NEW.question_order := max_order + 1;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_custom_field_order_trigger
  BEFORE INSERT ON journal_questions
  FOR EACH ROW
  EXECUTE FUNCTION set_custom_field_order();
