/**
 * Centralized theme system for page-specific colors
 * Used to make the navbar and other components dynamic based on current page
 */

export interface PageTheme {
  primary: string;      // Main color for active elements
  background: string;   // Light tint for navbar background
  secondary: string;    // Secondary accent color
  light: string;        // Very light tint for subtle backgrounds
}

export interface ThemeConfig {
  [routeName: string]: PageTheme;
}

/**
 * Page-specific theme definitions
 * Colors extracted from existing page designs
 */
export const PAGE_THEMES: ThemeConfig = {
  // Chat page - Gold/Yellow theme
  chat: {
    primary: '#B5A136',      // Darker golden (from activeTabText in ChatStyles)
    background: '#FDF8E8',   // Very light golden tint
    secondary: '#E5D787',    // Golden yellow (from tabContainer in ChatStyles)
    light: '#FFF1D2',       // Light golden (from userMessage in ChatStyles)
  },

  // Home page - Blue theme  
  index: {
    primary: '#2B7EFF',      // Blue (from manifest theme_color and various styles)
    background: '#F0F8FF',   // Very light blue tint
    secondary: '#8EC8FF',    // Light blue (from HomeStyles)
    light: '#E8F4FD',       // Very light blue (from settings gradient)
  },

  // Mood page - Green theme
  mood: {
    primary: '#4EAF64',      // Green (from MOOD_COLORS.primary)
    background: '#F0FDF4',   // Very light green tint
    secondary: '#C6FFDD',    // Light green (from MOOD_COLORS.secondary)
    light: '#F8FDF8',       // Very light green
  },

  // Journal page - Pink theme
  journal: {
    primary: '#DD76B9',      // Pink (from journal icon color)
    background: '#FDF2F8',   // Very light pink tint
    secondary: '#FFE7F6',    // Light pink (from journal gradient)
    light: '#FFEBF9',       // Very light pink (from journal card background)
  },

  // Appointment page - Orange/Calendar theme
  appointment: {
    primary: '#B5A136',      // Golden color (consistent with calendar icon)
    background: '#FDF8E8',   // Very light golden tint
    secondary: '#F6EBB1',    // Light golden (from appointment gradient)
    light: '#FFFBF0',       // Very light golden
  },

  // Mic page - Blue theme (default, no specific theme found)
  mic: {
    primary: '#2B7EFF',      // Default blue
    background: '#F0F8FF',   // Very light blue tint
    secondary: '#8EC8FF',    // Light blue
    light: '#E8F4FD',       // Very light blue
  },
};

/**
 * Default theme for unknown routes
 */
export const DEFAULT_THEME: PageTheme = {
  primary: '#2B7EFF',      // Default blue
  background: '#F0F8FF',   // Very light blue tint
  secondary: '#8EC8FF',    // Light blue
  light: '#E8F4FD',       // Very light blue
};

/**
 * Neutral colors that work well with all themes
 */
export const NEUTRAL_COLORS = {
  inactive: '#64748B',     // Gray for inactive icons
  text: '#1A202C',        // Dark text
  border: '#E2E8F0',      // Light border
  white: '#FFFFFF',       // Pure white
};

/**
 * Get theme for a specific route
 */
export function getThemeForRoute(routeName: string): PageTheme {
  return PAGE_THEMES[routeName] || DEFAULT_THEME;
}

/**
 * Check if a route has a custom theme
 */
export function hasCustomTheme(routeName: string): boolean {
  return routeName in PAGE_THEMES;
}
