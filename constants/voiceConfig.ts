/**
 * Centralized Voice Call Configuration
 * 
 * This file contains all voice call related constants to ensure consistency
 * across all voice components and prevent hardcoded values.
 */

export const VOICE_CALL_CONFIG = {
  // Call Duration Limits
  MAX_CALL_DURATION_SECONDS: 120, // 2 minutes maximum call duration
  
  // Credits System
  CREDITS_PER_SECOND: 0.1, // 0.1 credits per second (12 credits for 2 minutes)
  TOTAL_CREDITS_FOR_MAX_CALL: 12, // 12 credits for full 2-minute call
  
  // Timeout Configuration
  TIMEOUT_BUFFER_MS: 1000, // 1 second buffer for timeout calculations
  BACKUP_TIMEOUT_BUFFER_MS: 5000, // 5 second buffer for backup timeout
  
  // Monitoring Intervals
  MONITORING_INTERVAL_MS: 30000, // 30 seconds - session monitoring interval
  CLEANUP_INTERVAL_MS: 60000, // 1 minute - cleanup orphaned sessions
  
  // Error Handling
  MAX_RETRY_ATTEMPTS: 3, // Maximum retry attempts for failed operations
  RETRY_DELAY_MS: 2000, // 2 seconds delay between retries
  
  // User Experience
  NOTIFICATION_TIMEOUT_WARNING_SECONDS: 30, // Warn user 30 seconds before timeout
  NOTIFICATION_DISPLAY_DURATION_MS: 5000, // 5 seconds for notification display
  
  // Development/Debug
  ENABLE_VERBOSE_LOGGING: __DEV__, // Enable detailed logging in development
  LOG_SESSION_EVENTS: true, // Log session lifecycle events
} as const;

/**
 * Voice Error Types
 * Standardized error types for consistent error handling
 */
export enum VoiceErrorType {
  MICROPHONE_PERMISSION = 'microphone_permission',
  NETWORK_ERROR = 'network_error', 
  CREDITS_INSUFFICIENT = 'credits_insufficient',
  TIMEOUT_EXCEEDED = 'timeout_exceeded',
  AGENT_UNAVAILABLE = 'agent_unavailable',
  SESSION_FAILED = 'session_failed',
  UNKNOWN_ERROR = 'unknown_error',
}

/**
 * Voice Error Messages
 * User-friendly error messages for each error type
 */
export const VOICE_ERROR_MESSAGES = {
  [VoiceErrorType.MICROPHONE_PERMISSION]: {
    title: 'Izin Mikrofon Diperlukan',
    message: 'Tidak dapat mengakses mikrofon. Pastikan izin mikrofon telah diberikan di pengaturan aplikasi.',
    action: 'Buka Pengaturan'
  },
  [VoiceErrorType.NETWORK_ERROR]: {
    title: 'Masalah Koneksi',
    message: 'Koneksi internet tidak stabil. Periksa koneksi Anda dan coba lagi.',
    action: 'Coba Lagi'
  },
  [VoiceErrorType.CREDITS_INSUFFICIENT]: {
    title: 'Kredit Tidak Mencukupi',
    message: 'Kredit suara Anda tidak mencukupi untuk memulai panggilan. Hubungi psikolog Anda untuk menambah kredit.',
    action: 'Hubungi Psikolog'
  },
  [VoiceErrorType.TIMEOUT_EXCEEDED]: {
    title: 'Panggilan Berakhir',
    message: 'Panggilan telah berakhir karena mencapai batas waktu maksimal (2 menit).',
    action: 'OK'
  },
  [VoiceErrorType.AGENT_UNAVAILABLE]: {
    title: 'Layanan Tidak Tersedia',
    message: 'Layanan panggilan suara sedang tidak tersedia. Silakan coba lagi nanti.',
    action: 'Coba Lagi'
  },
  [VoiceErrorType.SESSION_FAILED]: {
    title: 'Gagal Memulai Panggilan',
    message: 'Tidak dapat memulai sesi panggilan suara. Silakan coba lagi.',
    action: 'Coba Lagi'
  },
  [VoiceErrorType.UNKNOWN_ERROR]: {
    title: 'Terjadi Kesalahan',
    message: 'Terjadi kesalahan yang tidak diketahui. Silakan coba lagi atau hubungi dukungan.',
    action: 'Coba Lagi'
  }
} as const;

/**
 * Voice Session Status
 * Standardized session status values
 */
export enum VoiceSessionStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
}

/**
 * Helper Functions
 */
export const VoiceConfigHelpers = {
  /**
   * Calculate maximum call duration based on available credits
   */
  calculateMaxDurationFromCredits: (availableCredits: number): number => {
    const maxSecondsFromCredits = Math.floor(availableCredits / VOICE_CALL_CONFIG.CREDITS_PER_SECOND);
    return Math.min(maxSecondsFromCredits, VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS);
  },

  /**
   * Calculate credits needed for a given duration
   */
  calculateCreditsForDuration: (durationSeconds: number): number => {
    return Math.ceil(durationSeconds * VOICE_CALL_CONFIG.CREDITS_PER_SECOND);
  },

  /**
   * Get timeout value in milliseconds with buffer
   */
  getTimeoutMs: (durationSeconds: number): number => {
    return (durationSeconds * 1000) + VOICE_CALL_CONFIG.TIMEOUT_BUFFER_MS;
  },

  /**
   * Get backup timeout value in milliseconds with additional buffer
   */
  getBackupTimeoutMs: (durationSeconds: number): number => {
    return (durationSeconds * 1000) + VOICE_CALL_CONFIG.BACKUP_TIMEOUT_BUFFER_MS;
  },

  /**
   * Check if verbose logging is enabled
   */
  shouldLog: (): boolean => {
    return VOICE_CALL_CONFIG.ENABLE_VERBOSE_LOGGING;
  }
};
