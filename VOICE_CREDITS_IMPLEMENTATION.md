# Voice Credits System Implementation

## Overview
This document outlines the implementation of a credits-based rate limiting system for voice calls in the Temani app. The system provides 12 free credits to new users, with each credit equivalent to 10 seconds of call time. Credits are automatically recharged to 12 every week.

## System Architecture

### Database Schema
- **user_profiles table**: Extended with voice credits columns
  - `voice_credits`: Current available credits (default: 12)
  - `voice_credits_used`: Total credits used by user
  - `last_credit_reset`: Timestamp of last credit reset

- **voice_credit_recharges table**: New table for tracking weekly recharges
  - `user_id`: Reference to user
  - `recharge_date`: When the recharge occurred
  - `credits_added`: Number of credits added (always 12)
  - `credits_before`: Credits before recharge
  - `credits_after`: Credits after recharge (always 12)
  - `recharge_type`: Type of recharge (weekly_auto, manual, client_backup)
  - `triggered_by`: What triggered the recharge

- **voice_call_sessions table**: New table for tracking voice sessions
  - Session tracking with start/end times
  - Duration and credits used per session
  - Session status (active, completed, interrupted, failed)

### Core Components

#### 1. VoiceCreditsService (`lib/voiceCreditsService.ts`)
- **checkCredits()**: Validates if user has sufficient credits
- **startVoiceSession()**: Creates new session record
- **endVoiceSession()**: Calculates duration and deducts credits
- **initializeUserCredits()**: Sets up credits for new users
- **getUserCredits()**: Retrieves current credit balance
- **checkWeeklyRecharge()**: Checks if user needs weekly recharge
- **performWeeklyRecharge()**: Performs weekly credit recharge
- **checkAndPerformWeeklyRecharge()**: Combined check and recharge
- **getRechargeHistory()**: Gets user's recharge history

#### 2. useVoiceCredits Hook (`hooks/useVoiceCredits.ts`)
- React hook for managing voice credits state
- Session tracking and management
- Real-time credit balance updates
- Error handling and loading states
- Automatic weekly recharge checking on app startup

#### 3. Weekly Recharge Edge Function (`supabase/functions/weekly-credit-recharge/`)
- Automated weekly credit recharge via Supabase Edge Function
- Can be triggered by cron job or manual execution
- Recharges all eligible users to 12 credits
- Comprehensive logging and error handling

#### 3. UI Components
- **VoiceCreditsDisplay**: Shows current credit balance
- **InsufficientCreditsModal**: Displayed when credits are exhausted
- **VoiceCreditsTest**: Development component for testing

### Integration Points

#### Voice Components Updated
1. **VoiceContainer.tsx**
   - Added credits checking before call initiation
   - Integrated session start/end with credits tracking
   - Added credits display in UI

2. **VoiceChatButton.tsx**
   - Pre-call credits validation
   - Session management integration
   - Insufficient credits modal

3. **AuthContext.tsx**
   - Automatic credit initialization for new users
   - Both email signup and Google OAuth integration

## Credit Calculation Logic

### Rules
- **1 Credit = 10 seconds** of call time
- **Minimum deduction**: 1 credit (even for calls < 10 seconds)
- **Rounding**: Always round up (e.g., 15 seconds = 2 credits)
- **New users**: Receive 12 credits automatically
- **Weekly recharge**: All users get recharged to 12 credits every week

### Examples
- 5-second call = 1 credit
- 15-second call = 2 credits
- 25-second call = 3 credits
- 60-second call = 6 credits
- 120-second call = 12 credits (maximum call duration)

## Weekly Recharge System

### How It Works
1. **Automatic Recharge**: Edge Function runs weekly to recharge all users
2. **Client-side Backup**: App checks for weekly recharge on startup
3. **Recharge Amount**: Always recharges to 12 credits (not additive)
4. **Timing**: 7 days after last credit reset
5. **Tracking**: All recharges are logged in `voice_credit_recharges` table

### Recharge Types
- **weekly_auto**: Automated weekly recharge via Edge Function
- **client_backup**: Client-side backup recharge mechanism
- **manual**: Manual recharge by admin
- **system**: System-initiated recharge

## User Experience Flow

### Successful Call Flow
1. User clicks voice call button
2. System checks available credits
3. If sufficient credits: call proceeds
4. Session tracking begins
5. Call ends, duration calculated
6. Credits deducted, balance updated

### Insufficient Credits Flow
1. User clicks voice call button
2. System detects insufficient credits
3. Modal displayed explaining situation
4. Alternative options presented (text chat, journaling)
5. User can refresh balance or continue with alternatives

## Error Handling

### Network Issues
- Incomplete sessions handled gracefully
- Session cleanup on app crashes
- Automatic session recovery

### Edge Cases
- Concurrent session prevention
- Credit exhaustion mid-call handling
- Database connectivity issues

## Testing

### Test Component (`VoiceCreditsTest.tsx`)
- Credit checking functionality
- Session start/end simulation
- Error scenario testing
- UI component verification

### Test Page (`app/(tabs)/credits-test.tsx`)
- Development page for manual testing
- Real-time credit balance monitoring
- Session status tracking

## Database Functions

### Triggers
- **initialize_user_voice_credits()**: Auto-sets credits on profile creation
- **update_user_credits_on_session_end()**: Deducts credits when session completes

### Views
- **user_voice_usage**: Comprehensive usage analytics
- **user_completion_status**: Extended with credit information

### Functions
- **get_user_voice_credits()**: Efficient credit balance retrieval

## Security Considerations

### Row Level Security (RLS)
- All voice-related tables protected with RLS policies
- Users can only access their own sessions and credits
- Proper authentication checks on all operations

### Data Validation
- Credit amounts validated at database level
- Session duration constraints enforced
- Proper error handling for invalid operations

## Future Enhancements

### Planned Features
- Credit recharge system
- Usage analytics dashboard
- Credit gifting between users
- Subscription-based unlimited credits

### Monitoring
- Usage pattern analysis
- Credit consumption tracking
- Performance metrics collection

## Configuration

### Environment Variables
- `EXPO_PUBLIC_ELEVENLABS_AGENT_ID`: Required for voice sessions
- Database connection via Supabase configuration

### Constants (`types/voiceCredits.ts`)
```typescript
VOICE_CREDITS_CONFIG = {
  INITIAL_CREDITS: 10,
  SECONDS_PER_CREDIT: 10,
  MINIMUM_CREDIT_DEDUCTION: 1,
  MAX_SESSION_DURATION: 120, // 2 minutes
}
```

## Deployment Checklist

### Database
- [x] Run migration to add credits columns
- [x] Create voice_call_sessions table
- [x] Set up RLS policies
- [x] Create database functions and triggers

### Application
- [x] Deploy voice credits service
- [x] Update voice components
- [x] Add UI components
- [x] Update user creation flow

### Testing
- [ ] Test with new user registration
- [ ] Verify credit deduction accuracy
- [ ] Test insufficient credits flow
- [ ] Validate session cleanup

## Monitoring and Maintenance

### Key Metrics
- Average credits used per user
- Session completion rates
- Error rates in credit operations
- User retention after credit exhaustion

### Maintenance Tasks
- Monitor database performance
- Review credit usage patterns
- Update credit allocation as needed
- Optimize session tracking efficiency

## Support and Troubleshooting

### Common Issues
1. **Credits not initializing**: Check user profile creation
2. **Incorrect deductions**: Verify session duration calculation
3. **Session not ending**: Check network connectivity and cleanup logic
4. **UI not updating**: Verify hook dependencies and state management

### Debug Tools
- VoiceCreditsTest component for manual testing
- Console logging throughout the system
- Database query monitoring via Supabase dashboard
