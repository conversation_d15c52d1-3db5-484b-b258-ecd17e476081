import { useState, useCallback, useRef } from 'react';
import { chatService } from '@/lib/chatService';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import type { Message, Thread } from '@/types/chat';

export interface VoiceTranscriptMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isFinal: boolean;
  transcription?: string;
}

export interface UseVoiceTranscriptReturn {
  messages: VoiceTranscriptMessage[];
  isVisible: boolean;
  currentThread: Thread | null;
  loading: boolean;
  error: string | null;
  showTranscript: () => void;
  hideTranscript: () => void;
  toggleTranscript: () => void;
  handleVoiceMessage: (message: any) => void;
  clearTranscript: () => void;
  saveToChat: () => Promise<void>;
  startNewVoiceSession: () => Promise<Thread>;
}

export function useVoiceTranscript(): UseVoiceTranscriptReturn {
  const { user } = useUniversalAuth();
  const [messages, setMessages] = useState<VoiceTranscriptMessage[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [currentThread, setCurrentThread] = useState<Thread | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Keep track of tentative messages to update them when final versions arrive
  const tentativeMessagesRef = useRef<Map<string, VoiceTranscriptMessage>>(new Map());

  const showTranscript = useCallback(() => {
    setIsVisible(true);
  }, []);

  const hideTranscript = useCallback(() => {
    setIsVisible(false);
  }, []);

  const toggleTranscript = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  const clearTranscript = useCallback(() => {
    setMessages([]);
    tentativeMessagesRef.current.clear();
    setError(null);
  }, []);

  // Create a new voice conversation thread for each call
  const createNewThread = useCallback(async (): Promise<Thread> => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      setLoading(true);
      console.log('=== [VoiceTranscript] CREATING NEW THREAD ===');
      console.log('[VoiceTranscript] User ID:', user.id);

      // Create a thread with timestamp for each voice call
      const now = new Date();
      const timeString = now.toLocaleString('id-ID', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      const threadTitle = `Panggilan Suara - ${timeString}`;
      console.log('[VoiceTranscript] Creating thread with title:', threadTitle);

      const thread = await chatService.createThread(user.id, threadTitle);
      console.log('[VoiceTranscript] Thread created successfully:', {
        id: thread.id,
        title: thread.title,
        userId: thread.user_id
      });

      setCurrentThread(thread);
      console.log('=== [VoiceTranscript] THREAD CREATION COMPLETE ===');
      return thread;
    } catch (err: any) {
      console.error('=== [VoiceTranscript] THREAD CREATION ERROR ===');
      console.error('[VoiceTranscript] Failed to create thread:', err);
      console.error('[VoiceTranscript] Error details:', {
        message: err.message,
        code: err.code,
        details: err.details
      });
      console.error('=== [VoiceTranscript] THREAD CREATION ERROR END ===');
      setError(err.message || 'Failed to create conversation thread');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Start a new voice conversation session
  const startNewVoiceSession = useCallback(async () => {
    try {
      // Clear any existing transcript
      clearTranscript();

      // Create a new thread for this voice session
      const thread = await createNewThread();
      console.log('[VoiceTranscript] Started new voice session with thread:', thread.id);

      return thread;
    } catch (err: any) {
      console.error('[VoiceTranscript] Failed to start new voice session:', err);
      setError(err.message || 'Failed to start new voice session');
      throw err;
    }
  }, [clearTranscript, createNewThread]);

  const handleVoiceMessage = useCallback(async (message: any) => {
    try {
      setError(null);

      // Handle null or undefined messages
      if (!message) {
        console.log('🎤 [TRANSCRIPTION] Null message received, skipping');
        return;
      }

      // Log transcription-focused information
      console.log('🎤 [TRANSCRIPTION] Processing message:', {
        type: message.type,
        hasUserTranscript: !!(message.user_transcription_event?.user_transcript),
        hasAgentResponse: !!(message.agent_response_event?.agent_response),
        userTranscript: message.user_transcription_event?.user_transcript,
        agentResponse: message.agent_response_event?.agent_response,
        isFinal: message.user_transcription_event?.is_final
      });

      // Handle string messages
      if (typeof message === 'string') {
        console.log('🎤 [TRANSCRIPTION] String message received:', message);
        const transcriptMessage: VoiceTranscriptMessage = {
          id: Date.now().toString(),
          content: message,
          sender: 'user',
          timestamp: new Date(),
          isFinal: true,
          transcription: message,
        };
        setMessages(prev => [...prev, transcriptMessage]);
        return;
      }

      // Parse the message from ElevenLabs based on actual message format
      const messageId = message.id || message.messageId || Date.now().toString();
      let content = '';
      let sender: 'user' | 'ai' = 'user';
      let isFinal = true;

      // Handle different ElevenLabs message types
      if (message.type === 'conversation_initiation_metadata') {
        console.log('🎤 [TRANSCRIPTION] Conversation initiation metadata - skipping');
        return; // Don't display metadata events
      } else if (message.type === 'user_transcript' && message.user_transcription_event) {
        // User transcript event
        content = message.user_transcription_event.user_transcript || '';
        sender = 'user';
        isFinal = message.user_transcription_event.is_final !== false; // Default to true if not specified
        console.log('🎤 [TRANSCRIPTION] User transcript:', {
          content,
          isFinal,
          length: content.length
        });
      } else if (message.type === 'agent_response' && message.agent_response_event) {
        // Agent response event
        content = message.agent_response_event.agent_response || '';
        sender = 'ai';
        isFinal = true; // Agent responses are typically final
        console.log('🎤 [TRANSCRIPTION] Agent response:', {
          content,
          length: content.length
        });
      } else if (message.type === 'audio' || message.type === 'interruption' || message.type === 'ping') {
        console.log(`🎤 [TRANSCRIPTION] ${message.type} event - skipping`);
        return; // Don't display audio/interruption/ping events
      } else if (message.role && message.message) {
        // Direct message format (from conversation history or other sources)
        content = message.message;
        sender = message.role === 'agent' ? 'ai' : 'user';
        isFinal = message.isFinal !== false; // Default to true if not specified
        console.log('🎤 [TRANSCRIPTION] Direct message format:', {
          content,
          sender,
          isFinal
        });
      } else if (Object.keys(message).length === 0) {
        console.log('🎤 [TRANSCRIPTION] Empty object - skipping');
        return;
      } else {
        // Fallback: try to extract content from various possible fields
        content = message.message || message.text || message.content || message.transcript || '';

        // Determine sender based on various possible fields
        if (message.source === 'ai' ||
            message.role === 'assistant' ||
            message.sender === 'ai' ||
            message.type === 'agent_response' ||
            message.from === 'agent' ||
            message.speaker === 'agent') {
          sender = 'ai';
        }

        isFinal = message.isFinal !== false; // Default to true if not specified
        console.log('🎤 [TRANSCRIPTION] Fallback format:', {
          content,
          sender,
          isFinal,
          availableFields: Object.keys(message)
        });
      }

      console.log('[VoiceTranscript] Processing message:', {
        messageId,
        content,
        sender,
        isFinal,
        originalMessage: message
      });

      // Only process if we have content
      if (!content.trim()) {
        console.log('🎤 [TRANSCRIPTION] No content found in message, skipping');
        return;
      }

      // Log the final transcription result
      console.log('🎤 [TRANSCRIPTION] ✅ CONTENT EXTRACTED:', {
        sender,
        content: content.trim(),
        isFinal,
        willSaveToDatabase: isFinal && !!currentThread
      });

      const transcriptMessage: VoiceTranscriptMessage = {
        id: messageId,
        content: content.trim(),
        sender,
        timestamp: new Date(),
        isFinal,
        transcription: sender === 'user' ? content.trim() : undefined,
      };

      if (isFinal) {
        // Remove any tentative version of this message
        const tentativeKey = `${sender}_tentative`;
        tentativeMessagesRef.current.delete(tentativeKey);
        
        // Add the final message
        setMessages(prev => {
          // Remove any existing tentative messages from the same sender
          const filtered = prev.filter(msg => 
            !(msg.sender === sender && !msg.isFinal)
          );
          return [...filtered, transcriptMessage];
        });

        // Enhanced console logging for final transcriptions (no database saving)
        console.log('🎤 [TRANSCRIPTION] 📝 Final message processed:', {
          timestamp: new Date().toISOString(),
          sender,
          content: content.trim(),
          contentLength: content.trim().length,
          messageId: transcriptMessage.id,
          sessionId: currentThread?.id || 'no-thread'
        });

        // Log the complete transcription for debugging
        console.log('🎤 [TRANSCRIPTION] 💬 Complete message:', {
          sender: sender === 'user' ? 'USER' : 'AI',
          text: content.trim(),
          timestamp: transcriptMessage.timestamp.toLocaleString('id-ID')
        });

        // Note: Voice messages are no longer automatically saved to chat history
        // This prevents voice calls from creating chat threads
      } else {
        // Handle tentative message (real-time transcription)
        const tentativeKey = `${sender}_tentative`;
        tentativeMessagesRef.current.set(tentativeKey, transcriptMessage);
        
        setMessages(prev => {
          // Remove any existing tentative message from the same sender
          const filtered = prev.filter(msg => 
            !(msg.sender === sender && !msg.isFinal)
          );
          return [...filtered, transcriptMessage];
        });
      }
    } catch (err: any) {
      console.error('[VoiceTranscript] Error processing voice message:', err);
      setError(err.message || 'Failed to process voice message');
    }
  }, [currentThread, user?.id, createNewThread]);

  const saveToChat = useCallback(async () => {
    if (!user?.id || messages.length === 0) return;

    try {
      setLoading(true);
      setError(null);

      // Use existing thread or create a new one if none exists
      let threadToUse = currentThread;
      if (!threadToUse) {
        threadToUse = await createNewThread();
      }
      
      // Save all final messages that haven't been saved yet
      const finalMessages = messages.filter(msg => msg.isFinal);
      
      for (const msg of finalMessages) {
        if (msg.sender === 'user') {
          await chatService.sendVoiceMessage(
            threadToUse.id,
            msg.transcription || msg.content,
            'user',
            msg.content
          );
        } else {
          await chatService.sendMessage(
            threadToUse.id,
            msg.content,
            'ai'
          );
        }
      }
      
      console.log(`[VoiceTranscript] Saved ${finalMessages.length} messages to chat`);
    } catch (err: any) {
      console.error('[VoiceTranscript] Error saving to chat:', err);
      setError(err.message || 'Failed to save conversation to chat');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user?.id, messages, currentThread, createNewThread]);

  return {
    messages,
    isVisible,
    currentThread,
    loading,
    error,
    showTranscript,
    hideTranscript,
    toggleTranscript,
    handleVoiceMessage,
    clearTranscript,
    saveToChat,
    startNewVoiceSession,
  };
}
