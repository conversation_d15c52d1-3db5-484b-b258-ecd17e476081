/**
 * Voice Credits Hook
 * Manages voice credits state and operations
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { voiceCreditsService } from '@/lib/voiceCreditsService';
import { supabase } from '@/lib/supabase';
import { VOICE_CALL_CONFIG, VoiceConfigHelpers } from '@/constants/voiceConfig';
import type {
  VoiceCredits,
  CreditCheckResult,
  SessionStartResult,
  SessionEndResult,
  VoiceCreditsError,
} from '@/types/voiceCredits';

interface UseVoiceCreditsReturn {
  // State
  credits: VoiceCredits | null;
  loading: boolean;
  error: string | null;

  // Current session
  currentSessionId: string | null;
  sessionStartTime: Date | null;
  isSessionActive: boolean;

  // Actions
  checkCredits: (estimatedDuration?: number) => Promise<CreditCheckResult>;
  startSession: (agentId?: string) => Promise<SessionStartResult>;
  endSession: (durationSeconds?: number) => Promise<SessionEndResult>;
  refreshCredits: () => Promise<void>;
  forceRefreshCredits: () => Promise<void>;
  handleSessionInterruption: (reason: 'interrupted' | 'failed') => Promise<void>;
  emergencySessionRecovery: () => Promise<string | null>;
  checkWeeklyRecharge: () => Promise<boolean>;

  // Utilities
  canStartCall: boolean;
  estimatedCallTime: number;
  formatCreditsDisplay: () => string;
}

export const useVoiceCredits = (): UseVoiceCreditsReturn => {
  const { user } = useUniversalAuth();
  const [credits, setCredits] = useState<VoiceCredits | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Session state
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const sessionIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const backupTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Protected session state - use ref to prevent race conditions
  const sessionIdRef = useRef<string | null>(null);
  const sessionStartTimeRef = useRef<Date | null>(null);

  // Network state
  const [isOnline, setIsOnline] = useState(typeof navigator !== 'undefined' ? navigator.onLine : true);
  const [networkInterrupted, setNetworkInterrupted] = useState(false);

  // Derived state
  const isSessionActive = currentSessionId !== null && sessionStartTime !== null;

  /**
   * Centralized session state change logging
   */
  const logSessionStateChange = useCallback((action: string, context: any = {}) => {
    console.log('[SessionState]', {
      action,
      timestamp: new Date().toISOString(),
      currentSessionId,
      sessionIdRef: sessionIdRef.current,
      sessionStartTime: sessionStartTime?.toISOString(),
      sessionStartTimeRef: sessionStartTimeRef.current?.toISOString(),
      isOnline,
      networkInterrupted,
      isSessionActive,
      ...context
    });
  }, [currentSessionId, sessionStartTime, isOnline, networkInterrupted, isSessionActive]);
  const canStartCall = credits !== null && credits.creditsRemaining > 0;
  const estimatedCallTime = credits ? credits.creditsRemaining * 10 : 0; // 10 seconds per credit

  /**
   * Check and perform weekly recharge if needed
   */
  const checkWeeklyRecharge = useCallback(async () => {
    if (!user?.id) return false;

    try {
      const recharged = await voiceCreditsService.checkAndPerformWeeklyRecharge(user.id);
      if (recharged) {
        console.log('[useVoiceCredits] Weekly recharge performed, reloading credits...');
        // Reload credits after recharge
        await loadCreditsInternal();
      }
      return recharged;
    } catch (err) {
      console.error('[useVoiceCredits] Error checking weekly recharge:', err);
      return false;
    }
  }, [user?.id]);

  /**
   * Internal load credits function (without weekly recharge check)
   */
  const loadCreditsInternal = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      const userCredits = await voiceCreditsService.getUserCredits(user.id);
      setCredits(userCredits);
    } catch (err) {
      console.error('[useVoiceCredits] Error loading credits:', err);
      setError(err instanceof Error ? err.message : 'Failed to load credits');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  /**
   * Load user credits with weekly recharge check
   */
  const loadCredits = useCallback(async () => {
    if (!user?.id) return;

    try {
      // First check if weekly recharge is needed
      await checkWeeklyRecharge();

      // Then load current credits
      await loadCreditsInternal();
    } catch (err) {
      console.error('[useVoiceCredits] Error in loadCredits:', err);
      // Fallback to just loading credits without recharge
      await loadCreditsInternal();
    }
  }, [user?.id, checkWeeklyRecharge, loadCreditsInternal]);

  /**
   * Check if user has sufficient credits
   */
  const checkCredits = useCallback(async (estimatedDuration?: number): Promise<CreditCheckResult> => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      const result = await voiceCreditsService.checkCredits(user.id, estimatedDuration);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check credits';
      setError(errorMessage);
      throw err;
    }
  }, [user?.id]);

  /**
   * Start a voice session
   */
  const startSession = useCallback(async (agentId?: string): Promise<SessionStartResult> => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    if (isSessionActive) {
      throw new Error('A voice session is already active');
    }

    try {
      setError(null);
      setLoading(true);

      const result = await voiceCreditsService.startVoiceSession(user.id, agentId);
      
      const startTime = new Date();

      logSessionStateChange('session_start_before_state_update', {
        newSessionId: result.sessionId,
        maxDuration: result.maxCallDuration
      });

      // Update both state and protected refs
      setCurrentSessionId(result.sessionId);
      setSessionStartTime(startTime);
      sessionIdRef.current = result.sessionId;
      sessionStartTimeRef.current = startTime;

      logSessionStateChange('session_start_after_state_update', {
        sessionId: result.sessionId,
        maxDuration: result.maxCallDuration
      });

      console.log('[useVoiceCredits] Session state updated:', {
        action: 'session_started',
        sessionId: result.sessionId,
        startTime: startTime.toISOString(),
        userId: user.id,
        isOnline,
        maxDuration: result.maxCallDuration,
        protectedRefs: {
          sessionIdRef: sessionIdRef.current,
          sessionStartTimeRef: sessionStartTimeRef.current?.toISOString()
        }
      });
      
      // Start session monitoring for cleanup purposes only
      // Note: Timeout enforcement is now handled by VoiceContainer to properly terminate the actual conversation
      sessionIntervalRef.current = setInterval(() => {
        // This interval is kept for potential future monitoring needs
        // The actual timeout enforcement is handled by VoiceContainer
        if (sessionStartTime) {
          const sessionDuration = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000);
          console.log('[useVoiceCredits] Session monitoring - duration:', sessionDuration, 'seconds');
        }
      }, VOICE_CALL_CONFIG.MONITORING_INTERVAL_MS);

      // Start backup timeout as failsafe (with additional buffer)
      // This serves as a system-level backup in case VoiceContainer timeout fails
      backupTimeoutRef.current = setTimeout(async () => {
        console.warn('[useVoiceCredits] BACKUP TIMEOUT TRIGGERED - VoiceContainer timeout may have failed');
        try {
          if (currentSessionId) {
            console.log('[useVoiceCredits] Force-ending session via backup timeout:', currentSessionId);
            await endSession(VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS);
          }
        } catch (error) {
          console.error('[useVoiceCredits] Error in backup timeout:', error);
        }
      }, VoiceConfigHelpers.getBackupTimeoutMs(result.maxCallDuration));

      console.log('[useVoiceCredits] Voice session started:', result.sessionId);
      return result;
    } catch (err) {
      console.error('[useVoiceCredits] Error starting session:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to start voice session';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user?.id, isSessionActive]);

  /**
   * End the current voice session
   */
  const endSession = useCallback(async (durationSeconds?: number): Promise<SessionEndResult> => {
    logSessionStateChange('session_end_attempt', {
      providedDuration: durationSeconds,
      trigger: 'endSession_called'
    });

    // Use protected refs as fallback for race condition protection
    const sessionId = currentSessionId || sessionIdRef.current;
    const startTime = sessionStartTime || sessionStartTimeRef.current;

    console.log('[useVoiceCredits] Attempting to end session:', {
      currentSessionId,
      sessionIdRef: sessionIdRef.current,
      sessionStartTime: sessionStartTime?.toISOString(),
      sessionStartTimeRef: sessionStartTimeRef.current?.toISOString(),
      providedDuration: durationSeconds,
      isSessionActive,
      usingFallback: !currentSessionId && !!sessionIdRef.current
    });

    if (!sessionId) {
      const error = new Error('No active voice session - both state and ref are null');
      console.error('[useVoiceCredits] Cannot end session - no sessionId available:', {
        currentSessionId,
        sessionIdRef: sessionIdRef.current,
        possibleRaceCondition: true
      });
      throw error;
    }

    try {
      setError(null);
      setLoading(true);

      // Calculate duration if not provided
      let actualDuration = durationSeconds;
      if (!actualDuration && startTime) {
        actualDuration = Math.floor((Date.now() - startTime.getTime()) / 1000);
        console.log('[useVoiceCredits] Calculated session duration:', actualDuration, 'seconds');
      }

      if (!actualDuration || actualDuration < 0) {
        actualDuration = 0;
        console.log('[useVoiceCredits] Using fallback duration: 0 seconds');
      }

      console.log('[useVoiceCredits] Ending session with duration:', actualDuration, 'seconds');
      const result = await voiceCreditsService.endVoiceSession(sessionId, actualDuration);
      console.log('[useVoiceCredits] Session ended successfully:', {
        sessionId: result.sessionId,
        creditsUsed: result.creditsUsed,
        creditsRemaining: result.creditsRemaining
      });
      
      logSessionStateChange('session_end_before_cleanup', {
        sessionId,
        creditsUsed: result.creditsUsed,
        duration: actualDuration
      });

      // Clean up session state and protected refs
      const previousSessionId = sessionId;
      setCurrentSessionId(null);
      setSessionStartTime(null);
      setNetworkInterrupted(false);
      sessionIdRef.current = null;
      sessionStartTimeRef.current = null;

      logSessionStateChange('session_end_after_cleanup', {
        previousSessionId,
        creditsUsed: result.creditsUsed,
        duration: actualDuration
      });

      console.log('[useVoiceCredits] Session state cleared:', {
        action: 'session_ended_success',
        previousSessionId,
        creditsUsed: result.creditsUsed,
        creditsRemaining: result.creditsRemaining,
        duration: actualDuration,
        isOnline,
        clearedRefs: true
      });

      if (sessionIntervalRef.current) {
        clearInterval(sessionIntervalRef.current);
        sessionIntervalRef.current = null;
      }

      // Clear backup timeout
      if (backupTimeoutRef.current) {
        clearTimeout(backupTimeoutRef.current);
        backupTimeoutRef.current = null;
        console.log('[useVoiceCredits] Backup timeout cleared');
      }

      // Clean up persisted session state
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('temani_voice_session');
      }

      // Clear cached credits and force fresh reload
      setCredits(null);

      // Add small delay to ensure database operations complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Force fresh credit reload
      await loadCredits();

      return result;
    } catch (err) {
      console.error('[useVoiceCredits] Error ending session:', {
        error: err,
        sessionId: currentSessionId,
        sessionStartTime: sessionStartTime?.toISOString(),
        errorMessage: err instanceof Error ? err.message : 'Unknown error',
        errorStack: err instanceof Error ? err.stack : undefined
      });

      const errorMessage = err instanceof Error ? err.message : 'Failed to end voice session';
      setError(errorMessage);

      // Don't clean up session state on error - let it be handled by cleanup mechanisms
      throw err;
    } finally {
      setLoading(false);
    }
  }, [currentSessionId, sessionStartTime, loadCredits]);

  /**
   * Handle session interruption (network issues, app crash, etc.)
   */
  const handleSessionInterruption = useCallback(async (reason: 'interrupted' | 'failed') => {
    const sessionId = currentSessionId || sessionIdRef.current;
    const startTime = sessionStartTime || sessionStartTimeRef.current;

    if (!sessionId) return;

    console.log('[useVoiceCredits] Handling session interruption:', {
      reason,
      sessionId,
      currentSessionId,
      sessionIdRef: sessionIdRef.current,
      isOnline,
      networkInterrupted,
      usingFallback: !currentSessionId && !!sessionIdRef.current
    });

    try {
      let durationSeconds = 0;
      if (startTime) {
        durationSeconds = Math.floor((Date.now() - startTime.getTime()) / 1000);
      }

      await voiceCreditsService.handleSessionInterruption(sessionId, reason, durationSeconds);
      console.log('[useVoiceCredits] Session interruption handled successfully');

      // Clean up local state and protected refs
      setCurrentSessionId(null);
      setSessionStartTime(null);
      setNetworkInterrupted(false);
      sessionIdRef.current = null;
      sessionStartTimeRef.current = null;

      if (sessionIntervalRef.current) {
        clearInterval(sessionIntervalRef.current);
        sessionIntervalRef.current = null;
      }

      // Clean up persisted session state
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('temani_voice_session');
      }

      // Clear cached credits and force fresh reload
      setCredits(null);

      // Add small delay to ensure database operations complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Force fresh credit reload
      await loadCredits();
    } catch (err) {
      console.error('[useVoiceCredits] Error handling session interruption:', err);
    }
  }, [currentSessionId, sessionStartTime, loadCredits, isOnline, networkInterrupted]);

  /**
   * Refresh credits data
   */
  const refreshCredits = useCallback(async () => {
    await loadCredits();
  }, [loadCredits]);

  /**
   * Force refresh credits with cache clearing
   */
  const forceRefreshCredits = useCallback(async () => {
    console.log('[useVoiceCredits] Force refreshing credits with cache clear');

    // Clear all cached state
    setCredits(null);
    setError(null);

    // Add delay to ensure any pending operations complete
    await new Promise(resolve => setTimeout(resolve, 150));

    // Force fresh reload
    await loadCredits();
  }, [loadCredits]);

  /**
   * Format credits for display
   */
  const formatCreditsDisplay = useCallback((): string => {
    if (!credits) return '0 credits';
    
    const totalSeconds = credits.creditsRemaining * 10;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    
    if (minutes > 0) {
      return `${credits.creditsRemaining} credits (${minutes}m ${seconds}s)`;
    }
    return `${credits.creditsRemaining} credits (${seconds}s)`;
  }, [credits]);

  // Load credits on mount and when user changes, plus cleanup orphaned sessions
  useEffect(() => {
    if (user?.id) {
      loadCredits();
      cleanupOrphanedSessions();
    } else {
      setCredits(null);
      setError(null);
    }
  }, [user?.id, loadCredits]);

  /**
   * Cleanup orphaned sessions on app startup
   */
  const cleanupOrphanedSessions = useCallback(async () => {
    if (!user?.id) return;

    try {
      console.log('[useVoiceCredits] Checking for orphaned sessions...');

      // Get all active sessions older than 10 minutes (should have been completed)
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();

      const { data: orphanedSessions, error } = await supabase
        .from('voice_call_sessions')
        .select('id, session_start, user_id')
        .eq('user_id', user.id)
        .eq('session_status', 'active')
        .lt('session_start', tenMinutesAgo);

      if (error) {
        console.error('[useVoiceCredits] Error checking orphaned sessions:', error);
        return;
      }

      if (orphanedSessions && orphanedSessions.length > 0) {
        console.log(`[useVoiceCredits] Found ${orphanedSessions.length} orphaned sessions, cleaning up...`);

        for (const session of orphanedSessions) {
          try {
            // Calculate estimated duration (assume 2 minutes for orphaned sessions)
            const estimatedDuration = 2 * 60; // 2 minutes in seconds
            await voiceCreditsService.handleSessionInterruption(session.id, 'interrupted', estimatedDuration);
            console.log(`[useVoiceCredits] Cleaned up orphaned session: ${session.id}`);
          } catch (cleanupError) {
            console.error(`[useVoiceCredits] Failed to cleanup session ${session.id}:`, cleanupError);
          }
        }

        // Refresh credits after cleanup
        await loadCredits();
      } else {
        console.log('[useVoiceCredits] No orphaned sessions found');
      }
    } catch (error) {
      console.error('[useVoiceCredits] Error during session cleanup:', error);
    }
  }, [user?.id, loadCredits]);

  /**
   * Persist session state to localStorage for recovery
   */
  const persistSessionState = useCallback(() => {
    if (!currentSessionId || !sessionStartTime || !user?.id) return;

    try {
      const sessionData = {
        sessionId: currentSessionId,
        startTime: sessionStartTime.toISOString(),
        userId: user.id,
        timestamp: Date.now()
      };

      localStorage.setItem('temani_voice_session', JSON.stringify(sessionData));
      console.log('[useVoiceCredits] Session state persisted:', sessionData.sessionId);
    } catch (error) {
      console.error('[useVoiceCredits] Failed to persist session state:', error);
    }
  }, [currentSessionId, sessionStartTime, user?.id]);

  /**
   * Restore session state from localStorage
   */
  const restoreSessionState = useCallback(() => {
    if (!user?.id) return;

    try {
      const stored = localStorage.getItem('temani_voice_session');
      if (!stored) return;

      const sessionData = JSON.parse(stored);

      // Validate session data
      if (sessionData.userId !== user.id) {
        console.log('[useVoiceCredits] Session belongs to different user, ignoring');
        localStorage.removeItem('temani_voice_session');
        return;
      }

      // Check if session is recent (less than 10 minutes old)
      const age = Date.now() - sessionData.timestamp;
      const maxAge = 10 * 60 * 1000; // 10 minutes

      if (age > maxAge) {
        console.log('[useVoiceCredits] Stored session too old, ignoring');
        localStorage.removeItem('temani_voice_session');
        return;
      }

      // Only restore if we don't already have an active session
      if (!currentSessionId) {
        const restoredStartTime = new Date(sessionData.startTime);

        console.log('[useVoiceCredits] Restoring session state:', {
          action: 'session_restored',
          sessionId: sessionData.sessionId,
          originalStartTime: sessionData.startTime,
          ageMinutes: Math.floor(age / (60 * 1000)),
          userId: sessionData.userId
        });

        // Restore both state and protected refs
        setCurrentSessionId(sessionData.sessionId);
        setSessionStartTime(restoredStartTime);
        sessionIdRef.current = sessionData.sessionId;
        sessionStartTimeRef.current = restoredStartTime;

        // Start monitoring for the restored session (monitoring only)
        // Note: Timeout enforcement is handled by VoiceContainer
        sessionIntervalRef.current = setInterval(() => {
          if (sessionStartTime) {
            const sessionDuration = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000);
            console.log('[useVoiceCredits] Restored session monitoring - duration:', sessionDuration, 'seconds');
          }
        }, VOICE_CALL_CONFIG.MONITORING_INTERVAL_MS);

        // Start backup timeout for restored session
        const elapsedTime = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000);
        const remainingTime = Math.max(0, VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS - elapsedTime);

        if (remainingTime > 0) {
          backupTimeoutRef.current = setTimeout(async () => {
            console.warn('[useVoiceCredits] BACKUP TIMEOUT TRIGGERED for restored session');
            try {
              if (currentSessionId) {
                await endSession(VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS);
              }
            } catch (error) {
              console.error('[useVoiceCredits] Error in restored session backup timeout:', error);
            }
          }, VoiceConfigHelpers.getBackupTimeoutMs(remainingTime));
        }
      }

      // Clean up stored session data
      localStorage.removeItem('temani_voice_session');
    } catch (error) {
      console.error('[useVoiceCredits] Failed to restore session state:', error);
      localStorage.removeItem('temani_voice_session');
    }
  }, [user?.id, currentSessionId, sessionStartTime, endSession]);

  /**
   * Emergency session recovery - attempt to recover session when state is unexpectedly cleared
   */
  const emergencySessionRecovery = useCallback(async (): Promise<string | null> => {
    logSessionStateChange('emergency_recovery_attempt', {
      reason: 'session_state_unexpectedly_cleared'
    });

    try {
      // Try to restore from localStorage first
      const stored = localStorage.getItem('temani_voice_session');
      if (stored) {
        const sessionData = JSON.parse(stored);

        // Validate session data
        if (sessionData.userId === user?.id) {
          const age = Date.now() - sessionData.timestamp;
          if (age < 10 * 60 * 1000) { // Less than 10 minutes old
            logSessionStateChange('emergency_recovery_success', {
              recoveredSessionId: sessionData.sessionId,
              ageMinutes: Math.floor(age / (60 * 1000))
            });

            // Restore session state
            setCurrentSessionId(sessionData.sessionId);
            setSessionStartTime(new Date(sessionData.startTime));
            sessionIdRef.current = sessionData.sessionId;
            sessionStartTimeRef.current = new Date(sessionData.startTime);

            return sessionData.sessionId;
          }
        }
      }

      // If localStorage recovery fails, check database for recent active sessions
      const { data: recentSessions, error } = await supabase
        .from('voice_call_sessions')
        .select('id, session_start')
        .eq('user_id', user?.id)
        .eq('session_status', 'active')
        .gte('session_start', new Date(Date.now() - 10 * 60 * 1000).toISOString())
        .order('session_start', { ascending: false })
        .limit(1);

      if (!error && recentSessions && recentSessions.length > 0) {
        const session = recentSessions[0];
        logSessionStateChange('emergency_recovery_from_database', {
          recoveredSessionId: session.id
        });

        setCurrentSessionId(session.id);
        setSessionStartTime(new Date(session.session_start));
        sessionIdRef.current = session.id;
        sessionStartTimeRef.current = new Date(session.session_start);

        return session.id;
      }

      logSessionStateChange('emergency_recovery_failed', {
        reason: 'no_recoverable_session_found'
      });

      return null;
    } catch (error) {
      logSessionStateChange('emergency_recovery_error', {
        error: error instanceof Error ? error.message : 'unknown_error'
      });
      return null;
    }
  }, [user?.id, logSessionStateChange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (sessionIntervalRef.current) {
        clearInterval(sessionIntervalRef.current);
      }
      if (backupTimeoutRef.current) {
        clearTimeout(backupTimeoutRef.current);
        console.log('[useVoiceCredits] Backup timeout cleared on unmount');
      }
    };
  }, []);

  // Handle app state changes (for session cleanup) - FIXED: Use proper page unload detection
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handlePageHide = (event: PageTransitionEvent) => {
      // Only cleanup on actual page unload, not network issues
      // persisted=false means the page is actually being unloaded
      if (!event.persisted && isSessionActive) {
        console.log('[useVoiceCredits] Page unload detected, cleaning up session');
        handleSessionInterruption('interrupted');
      } else if (event.persisted) {
        console.log('[useVoiceCredits] Page cached (bfcache), persisting session state');
        persistSessionState();
      }
    };

    const handleVisibilityChange = () => {
      // Handle tab switching and app backgrounding, but not network issues
      if (document.visibilityState === 'hidden' && isSessionActive) {
        console.log('[useVoiceCredits] Page hidden, persisting session state');
        persistSessionState();
      } else if (document.visibilityState === 'visible') {
        console.log('[useVoiceCredits] Page visible, checking for session recovery');
        restoreSessionState();
      }
    };

    // Use pagehide instead of beforeunload - more reliable and less prone to false triggers
    window.addEventListener('pagehide', handlePageHide);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('pagehide', handlePageHide);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isSessionActive, handleSessionInterruption, persistSessionState, restoreSessionState]);

  // Monitor network status for session management
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleOnline = () => {
      console.log('[useVoiceCredits] Network back online');
      setIsOnline(true);

      if (networkInterrupted && currentSessionId) {
        console.log('[useVoiceCredits] Network recovered, checking session state');
        setNetworkInterrupted(false);
        // Don't auto-end session on network recovery - let it continue
        // The session might still be valid on the server side
      }
    };

    const handleOffline = () => {
      console.log('[useVoiceCredits] Network went offline');
      setIsOnline(false);

      if (currentSessionId) {
        console.log('[useVoiceCredits] Network interrupted during active session');
        setNetworkInterrupted(true);
        // Don't immediately end session - network might come back
        // Persist session state for recovery
        persistSessionState();
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [currentSessionId, networkInterrupted, persistSessionState]);

  return {
    // State
    credits,
    loading,
    error,
    
    // Session state
    currentSessionId,
    sessionStartTime,
    isSessionActive,
    
    // Actions
    checkCredits,
    startSession,
    endSession,
    refreshCredits,
    forceRefreshCredits,
    handleSessionInterruption,
    emergencySessionRecovery,
    checkWeeklyRecharge,
    
    // Utilities
    canStartCall,
    estimatedCallTime,
    formatCreditsDisplay,
  };
};
