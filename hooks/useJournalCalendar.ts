import { useState, useEffect, useCallback } from 'react';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { journalService } from '@/lib/journalService';
import type { JournalEntryWithQuestion } from '@/types/journal';

export interface UseJournalCalendarReturn {
  currentMonth: Date;
  selectedDate: string | null;
  entryDates: string[];
  entriesForDate: JournalEntryWithQuestion[];
  loading: boolean;
  error: string | null;
  navigateMonth: (direction: 'prev' | 'next') => void;
  selectDate: (date: string | null) => void;
  goToToday: () => void;
  refetch: () => Promise<void>;
}

export function useJournalCalendar(): UseJournalCalendarReturn {
  const { user } = useUniversalAuth();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [entryDates, setEntryDates] = useState<string[]>([]);
  const [entriesForDate, setEntriesForDate] = useState<JournalEntryWithQuestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cache for month data to avoid repeated API calls
  const [monthCache, setMonthCache] = useState<Map<string, string[]>>(new Map());

  const getMonthKey = (date: Date): string => {
    return `${date.getFullYear()}-${date.getMonth()}`;
  };

  const fetchEntryDatesForMonth = useCallback(async (month: Date) => {
    if (!user?.id) return;

    const monthKey = getMonthKey(month);
    
    // Check cache first
    if (monthCache.has(monthKey)) {
      setEntryDates(monthCache.get(monthKey) || []);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const year = month.getFullYear();
      const monthNum = month.getMonth();
      
      // Get first and last day of the month
      const firstDay = new Date(year, monthNum, 1);
      const lastDay = new Date(year, monthNum + 1, 0);
      
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];

      const entries = await journalService.getEntriesByDateRange(user.id, startDate, endDate);
      
      // Extract unique dates
      const dates = [...new Set(entries.map(entry => entry.entry_date))];
      
      setEntryDates(dates);
      
      // Cache the result
      setMonthCache(prev => new Map(prev).set(monthKey, dates));
      
    } catch (err: any) {
      setError(err.message || 'Failed to load calendar data');
      console.error('Error fetching calendar data:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id, monthCache]);

  const fetchEntriesForDate = useCallback(async (date: string) => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      const entries = await journalService.getEntriesByDateRange(user.id, date, date);
      setEntriesForDate(entries);
      
    } catch (err: any) {
      setError(err.message || 'Failed to load entries for date');
      console.error('Error fetching entries for date:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const navigateMonth = useCallback((direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      if (direction === 'prev') {
        newMonth.setMonth(prev.getMonth() - 1);
      } else {
        newMonth.setMonth(prev.getMonth() + 1);
      }
      return newMonth;
    });
  }, []);

  const selectDate = useCallback((date: string | null) => {
    setSelectedDate(date);
    if (date) {
      // Call fetchEntriesForDate directly to avoid circular dependency
      if (user?.id) {
        setLoading(true);
        setError(null);
        journalService.getEntriesByDateRange(user.id, date, date)
          .then(entries => {
            setEntriesForDate(entries);
          })
          .catch(err => {
            setError(err.message || 'Failed to load entries for date');
            console.error('Error fetching entries for date:', err);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    } else {
      setEntriesForDate([]);
    }
  }, [user?.id]);

  const goToToday = useCallback(() => {
    const today = new Date();
    setCurrentMonth(today);
    const todayString = today.toISOString().split('T')[0];
    selectDate(todayString);
  }, [selectDate]);

  const refetch = useCallback(async () => {
    if (!user?.id) return;

    // Clear cache and refetch current month
    setMonthCache(new Map());

    // Refetch month data directly to avoid circular dependency
    const monthKey = getMonthKey(currentMonth);
    try {
      setLoading(true);
      setError(null);

      const year = currentMonth.getFullYear();
      const monthNum = currentMonth.getMonth();

      // Get first and last day of the month
      const firstDay = new Date(year, monthNum, 1);
      const lastDay = new Date(year, monthNum + 1, 0);

      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];

      const entries = await journalService.getEntriesByDateRange(user.id, startDate, endDate);

      // Extract unique dates
      const dates = [...new Set(entries.map(entry => entry.entry_date))];

      setEntryDates(dates);

      // Cache the result
      setMonthCache(prev => new Map(prev).set(monthKey, dates));

      // Refetch selected date entries if there's a selected date
      if (selectedDate) {
        const selectedEntries = await journalService.getEntriesByDateRange(user.id, selectedDate, selectedDate);
        setEntriesForDate(selectedEntries);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load calendar data');
      console.error('Error refetching calendar data:', err);
    } finally {
      setLoading(false);
    }
  }, [currentMonth, selectedDate, user?.id]);

  // Fetch data when month changes
  useEffect(() => {
    fetchEntryDatesForMonth(currentMonth);
  }, [currentMonth, fetchEntryDatesForMonth]);

  // Initialize with today's date - only run once
  useEffect(() => {
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    setSelectedDate(todayString);

    // Fetch entries for today directly to avoid circular dependency
    if (user?.id) {
      setLoading(true);
      journalService.getEntriesByDateRange(user.id, todayString, todayString)
        .then(entries => {
          setEntriesForDate(entries);
        })
        .catch(err => {
          setError(err.message || 'Failed to load entries for today');
          console.error('Error fetching entries for today:', err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [user?.id]); // Only depend on user?.id, run once when user is available

  return {
    currentMonth,
    selectedDate,
    entryDates,
    entriesForDate,
    loading,
    error,
    navigateMonth,
    selectDate,
    goToToday,
    refetch,
  };
}
