/**
 * Modern Mood Hooks using TanStack Query
 * Replaces local state patterns with modern data fetching for mood tracking
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { moodService } from '@/lib/moodService';
import { queryKeys, invalidateMoodQueries, invalidateUserMoodQueries } from '@/lib/queryClient';
import { useUserTimezone } from './useUserProfile';
import type {
  DailyMoodData,
  WeeklyMoodData,
  MoodEntry,
  MoodTrend,
  MoodInsights,
} from '@/types/mood';

/**
 * Hook for fetching mood entry for a specific date
 */
export function useMoodEntry(date: string) {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.mood.entry(user?.id || '', date),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useMoodEntry] Fetching mood entry for date:', date);
      return moodService.getMoodEntryWithFallback(user.id, date);
    },
    enabled: !!user?.id && !!date,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook for fetching today's mood entry (timezone-aware)
 */
export function useTodayMoodEntry() {
  const { user } = useUniversalAuth();
  const { data: userTimezone } = useUserTimezone();

  return useQuery({
    queryKey: queryKeys.mood.today(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      const timezone = userTimezone || 'Asia/Jakarta'; // Fallback to default timezone
      console.log('[useTodayMoodEntry] Fetching today\'s mood entry for user:', user.id, 'timezone:', timezone);
      return moodService.getTodayMoodEntry(user.id, timezone);
    },
    enabled: !!user?.id,
    staleTime: 1 * 60 * 1000, // 1 minute (more frequent for today's data)
    retry: 2,
  });
}

/**
 * Hook for fetching weekly mood data
 */
export function useWeeklyMood(weekStart: string) {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.mood.weekly(user?.id || '', weekStart),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useWeeklyMood] Fetching weekly mood for week starting:', weekStart);
      return moodService.getWeeklyMood(user.id, weekStart);
    },
    enabled: !!user?.id && !!weekStart,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook for fetching current week's mood data (timezone-aware)
 */
export function useCurrentWeekMood() {
  const { user } = useUniversalAuth();
  const { data: userTimezone } = useUserTimezone();

  return useQuery({
    queryKey: queryKeys.mood.currentWeek(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      const timezone = userTimezone || 'Asia/Jakarta'; // Fallback to default timezone
      console.log('[useCurrentWeekMood] Fetching current week mood for user:', user.id, 'timezone:', timezone);
      return moodService.getCurrentWeekMood(user.id, timezone);
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}

/**
 * Hook for fetching mood history
 */
export function useMoodHistory(startDate: string, endDate: string, limit?: number, offset?: number) {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.mood.history(user?.id || '', startDate, endDate),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useMoodHistory] Fetching mood history for user:', user.id, { startDate, endDate });
      return moodService.getMoodHistory(user.id, startDate, endDate, limit, offset);
    },
    enabled: !!user?.id && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook for fetching mood trends
 */
export function useMoodTrends(period: 'week' | 'month', count: number = 4) {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.mood.trends(user?.id || '', period, count),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useMoodTrends] Fetching mood trends for user:', user.id, { period, count });
      return moodService.getMoodTrends(user.id, period, count);
    },
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes (trends don't change often)
    retry: 2,
  });
}

/**
 * Mutation hook for saving mood entries
 */
export function useSaveMoodEntry() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ date, data }: { date: string; data: DailyMoodData }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useSaveMoodEntry] Saving mood entry for date:', date);
      return moodService.saveMoodEntry(user.id, date, data);
    },
    onSuccess: (result, variables) => {
      console.log('[useSaveMoodEntry] Mood entry saved successfully:', result.id);
      
      // Invalidate relevant queries to refetch fresh data
      if (user?.id) {
        invalidateUserMoodQueries(user.id);
        
        // Also invalidate the specific date
        queryClient.invalidateQueries({
          queryKey: queryKeys.mood.entry(user.id, variables.date)
        });
      }
    },
    onError: (error) => {
      console.error('[useSaveMoodEntry] Failed to save mood entry:', error);
    },
  });
}

/**
 * Mutation hook for updating mood entries
 */
export function useUpdateMoodEntry() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ date, updates }: { date: string; updates: Partial<DailyMoodData> }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUpdateMoodEntry] Updating mood entry for date:', date);
      return moodService.updateMoodEntry(user.id, date, updates);
    },
    onSuccess: (result, variables) => {
      console.log('[useUpdateMoodEntry] Mood entry updated successfully:', result.id);
      
      // Invalidate relevant queries
      if (user?.id) {
        invalidateUserMoodQueries(user.id);
        
        // Also invalidate the specific date
        queryClient.invalidateQueries({
          queryKey: queryKeys.mood.entry(user.id, variables.date)
        });
      }
    },
    onError: (error) => {
      console.error('[useUpdateMoodEntry] Failed to update mood entry:', error);
    },
  });
}

/**
 * Mutation hook for deleting mood entries
 */
export function useDeleteMoodEntry() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (date: string) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useDeleteMoodEntry] Deleting mood entry for date:', date);
      return moodService.deleteMoodEntry(user.id, date);
    },
    onSuccess: (_, date) => {
      console.log('[useDeleteMoodEntry] Mood entry deleted successfully for date:', date);
      
      // Invalidate relevant queries
      if (user?.id) {
        invalidateUserMoodQueries(user.id);
        
        // Also invalidate the specific date
        queryClient.invalidateQueries({
          queryKey: queryKeys.mood.entry(user.id, date)
        });
      }
    },
    onError: (error) => {
      console.error('[useDeleteMoodEntry] Failed to delete mood entry:', error);
    },
  });
}

/**
 * Mutation hook for saving weekly mood data
 */
export function useSaveWeeklyMood() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ weekStart, weeklyData }: { weekStart: string; weeklyData: WeeklyMoodData }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useSaveWeeklyMood] Saving weekly mood data for week starting:', weekStart);
      return moodService.saveWeeklyMood(user.id, weekStart, weeklyData);
    },
    onSuccess: (_, variables) => {
      console.log('[useSaveWeeklyMood] Weekly mood data saved successfully');

      // Invalidate all mood queries since we updated multiple entries
      if (user?.id) {
        invalidateUserMoodQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useSaveWeeklyMood] Failed to save weekly mood data:', error);
    },
  });
}

/**
 * Hook for initializing mood service (migration and sync)
 */
export function useMoodInitialization() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: ['mood', 'initialization', user?.id],
    queryFn: async () => {
      if (!user?.id) {
        return null;
      }
      console.log('[useMoodInitialization] Initializing mood service for user:', user.id);
      await moodService.initializeForUser(user.id);
      return { initialized: true };
    },
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
  });
}

/**
 * Hook for checking sync status
 */
export function useMoodSyncStatus() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: ['mood', 'syncStatus', user?.id],
    queryFn: async () => {
      if (!user?.id) {
        return null;
      }
      return moodService.getSyncStatus();
    },
    enabled: !!user?.id,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Check every 2 minutes
  });
}
