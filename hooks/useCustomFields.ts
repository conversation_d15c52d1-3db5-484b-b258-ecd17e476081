/**
 * TanStack Query hooks for custom field management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { customFieldService } from '@/lib/customFieldService';
import { queryKeys, invalidateUserJournalQueries } from '@/lib/queryClient';
import type {
  JournalQuestion,
  JournalCustomField,
  CustomFieldInput,
  CustomFieldUpdate,
} from '@/types/journal';

/**
 * Hook for fetching all user fields (system + custom)
 */
export function useUserFields() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.journal.userFields(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUserFields] Fetching all user fields for user:', user.id);
      return customFieldService.getUserFields(user.id);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook for fetching only custom fields
 */
export function useCustomFields() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.journal.customFields(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useCustomFields] Fetching custom fields for user:', user.id);
      return customFieldService.getCustomFields(user.id);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Mutation hook for creating custom fields
 */
export function useCreateCustomField() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (input: CustomFieldInput) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useCreateCustomField] Creating custom field for user:', user.id);
      return customFieldService.createCustomField(user.id, input);
    },
    onSuccess: (data) => {
      console.log('[useCreateCustomField] Custom field created successfully:', data.id);
      
      if (user?.id) {
        // Invalidate all journal-related queries to refresh the UI
        invalidateUserJournalQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useCreateCustomField] Error creating custom field:', error);
    },
  });
}

/**
 * Mutation hook for updating custom fields
 */
export function useUpdateCustomField() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ fieldId, update }: { fieldId: string; update: CustomFieldUpdate }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUpdateCustomField] Updating custom field:', fieldId);
      return customFieldService.updateCustomField(user.id, fieldId, update);
    },
    onSuccess: (data) => {
      console.log('[useUpdateCustomField] Custom field updated successfully:', data.id);
      
      if (user?.id) {
        invalidateUserJournalQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useUpdateCustomField] Error updating custom field:', error);
    },
  });
}

/**
 * Mutation hook for archiving custom fields
 */
export function useArchiveCustomField() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (fieldId: string) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useArchiveCustomField] Archiving custom field:', fieldId);
      return customFieldService.archiveCustomField(user.id, fieldId);
    },
    onSuccess: (_, fieldId) => {
      console.log('[useArchiveCustomField] Custom field archived successfully:', fieldId);
      
      if (user?.id) {
        invalidateUserJournalQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useArchiveCustomField] Error archiving custom field:', error);
    },
  });
}

/**
 * Mutation hook for reordering custom fields
 */
export function useReorderCustomFields() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (fieldOrders: Array<{ id: string; order: number }>) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useReorderCustomFields] Reordering custom fields for user:', user.id);
      return customFieldService.reorderCustomFields(user.id, fieldOrders);
    },
    onSuccess: () => {
      console.log('[useReorderCustomFields] Custom fields reordered successfully');
      
      if (user?.id) {
        invalidateUserJournalQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useReorderCustomFields] Error reordering custom fields:', error);
    },
  });
}

/**
 * Hook to get separated system and custom fields
 */
export function useSeparatedFields() {
  const { data: allFields, ...queryResult } = useUserFields();

  const systemFields = allFields?.filter(field => field.field_type === 'system' || !field.field_type) || [];
  const customFields = allFields?.filter(field => field.field_type && field.field_type !== 'system') || [];

  return {
    ...queryResult,
    data: allFields,
    systemFields,
    customFields,
  };
}

/**
 * Hook to get fields by type
 */
export function useFieldsByType(fieldType?: 'system' | 'custom_question' | 'custom_title') {
  const { data: allFields, ...queryResult } = useUserFields();

  const filteredFields = fieldType 
    ? allFields?.filter(field => {
        if (fieldType === 'system') {
          return field.field_type === 'system' || !field.field_type;
        }
        return field.field_type === fieldType;
      }) || []
    : allFields || [];

  return {
    ...queryResult,
    data: filteredFields,
  };
}

/**
 * Hook to check if user can create more custom fields
 */
export function useCanCreateCustomField() {
  const { data: customFields } = useCustomFields();
  const { JOURNAL_CONSTANTS } = require('@/types/journal');

  const currentCount = customFields?.length || 0;
  const canCreate = currentCount < JOURNAL_CONSTANTS.MAX_CUSTOM_FIELDS_PER_USER;
  const remaining = JOURNAL_CONSTANTS.MAX_CUSTOM_FIELDS_PER_USER - currentCount;

  return {
    canCreate,
    currentCount,
    maxAllowed: JOURNAL_CONSTANTS.MAX_CUSTOM_FIELDS_PER_USER,
    remaining: Math.max(0, remaining),
  };
}

/**
 * Hook for optimistic updates when reordering
 */
export function useOptimisticReorder() {
  const queryClient = useQueryClient();
  const { user } = useUniversalAuth();

  const updateOptimistically = (newOrder: Array<{ id: string; order: number }>) => {
    if (!user?.id) return;

    const queryKey = queryKeys.journal.userFields(user.id);
    
    queryClient.setQueryData(queryKey, (oldData: JournalQuestion[] | undefined) => {
      if (!oldData) return oldData;

      const orderMap = new Map(newOrder.map(item => [item.id, item.order]));
      
      return [...oldData].sort((a, b) => {
        const orderA = orderMap.get(a.id) ?? a.question_order;
        const orderB = orderMap.get(b.id) ?? b.question_order;
        return orderA - orderB;
      });
    });
  };

  const revertOptimistic = () => {
    if (!user?.id) return;
    
    queryClient.invalidateQueries({
      queryKey: queryKeys.journal.userFields(user.id)
    });
  };

  return {
    updateOptimistically,
    revertOptimistic,
  };
}
