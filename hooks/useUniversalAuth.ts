import { AUTH_CONFIG } from '@/lib/env';

/**
 * Universal auth hook that works with both Supabase and Convex auth systems
 * This allows components to work regardless of which auth system is active
 */
export const useUniversalAuth = () => {
  if (AUTH_CONFIG.useConvexAuth) {
    // Use Convex Auth
    try {
      const { useConvexAuthContext } = require('@/context/ConvexAuthContext');
      const convexAuth = useConvexAuthContext();
      
      // Map Convex auth to universal interface
      return {
        user: convexAuth.user ? {
          id: convexAuth.user._id,
          email: convexAuth.user.email,
          name: convexAuth.user.name,
          image: convexAuth.user.image,
          // Map other properties as needed
        } : null,
        loading: convexAuth.loading,
        userStatusLoading: convexAuth.userStatusLoading,
        onboardingCompleted: convexAuth.onboardingCompleted,
        selfReflectionCompleted: convexAuth.selfReflectionCompleted,
        selfReflectionSkipped: convexAuth.selfReflectionSkipped,
        signInWithGoogle: convexAuth.signInWithGoogle,
        signInWithEmail: convexAuth.signInWithEmail,
        signUpWithEmail: convexAuth.signUpWithEmail,
        signOut: convexAuth.signOut,
        updateProfile: convexAuth.updateProfile,
        updatePassword: convexAuth.updatePassword,
        completeOnboarding: convexAuth.completeOnboarding,
        completeSelfReflection: convexAuth.completeSelfReflection,
        skipSelfReflection: convexAuth.skipSelfReflection,
        resetPassword: convexAuth.resetPassword,
        error: convexAuth.error,
        clearError: convexAuth.clearError,
      };
    } catch (error) {
      console.error('Failed to load Convex auth context:', error);
      throw new Error('Convex auth context not available');
    }
  } else {
    // Use Supabase Auth
    try {
      const { useAuth } = require('@/context/AuthContext');
      return useAuth();
    } catch (error) {
      console.error('Failed to load Supabase auth context:', error);
      throw new Error('Supabase auth context not available');
    }
  }
};

/**
 * Type definition for the universal auth interface
 */
export interface UniversalAuthUser {
  id: string;
  email?: string;
  name?: string;
  image?: string;
}

export interface UniversalAuthContext {
  user: UniversalAuthUser | null;
  loading: boolean;
  userStatusLoading: boolean;
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
  selfReflectionSkipped: boolean;
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithEmail: (email: string, password: string, displayName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (fullName: string, avatarUrl?: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  completeSelfReflection: () => Promise<void>;
  skipSelfReflection: () => void;
  resetPassword: (email: string) => Promise<void>;
  error: string | null;
  clearError: () => void;
}
