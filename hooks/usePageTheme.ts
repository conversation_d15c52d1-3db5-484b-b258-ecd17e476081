import { useMemo } from 'react';
import { getThemeForRoute, type PageTheme } from '../constants/themes';
import { useVoiceMode } from '../context/VoiceModeContext';

/**
 * Custom hook to get the current page theme based on the active route
 * 
 * @param routeName - The current active route name
 * @returns PageTheme object with colors for the current page
 */
export function usePageTheme(routeName: string): PageTheme {
  const theme = useMemo(() => {
    return getThemeForRoute(routeName);
  }, [routeName]);

  return theme;
}

/**
 * Hook variant that takes the full navigation state
 * Useful when you have access to the full navigation state object
 *
 * @param navigationState - The navigation state object
 * @returns PageTheme object with colors for the current page
 */
export function usePageThemeFromState(navigationState: any): PageTheme {
  const { voiceMode } = useVoiceMode();

  const routeName = useMemo(() => {
    if (!navigationState?.routes || !Array.isArray(navigationState.routes)) {
      return 'index'; // Default to home
    }

    const currentRoute = navigationState.routes[navigationState.index];
    return currentRoute?.name || 'index';
  }, [navigationState]);

  const theme = useMemo(() => {
    // If we're on the chat page and voice mode is active, use voice theme
    if (routeName === 'chat' && voiceMode) {
      return {
        primary: '#4A55A2',      // Voice mode primary color
        background: '#E8F4FD',   // Light blue background
        secondary: '#87C4E5',    // Voice mode secondary (tab container color)
        light: '#F0F8FF',       // Very light blue
      };
    }

    return getThemeForRoute(routeName);
  }, [routeName, voiceMode]);

  return theme;
}
