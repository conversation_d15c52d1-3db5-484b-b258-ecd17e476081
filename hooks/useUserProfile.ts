/**
 * User Profile Hooks using TanStack Query
 * Provides access to user profile data and preferences
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { UserProfileService } from '@/lib/userProfileService';
import { queryKeys } from '@/lib/queryClient';
import type { UserProfile, UserPreferences, CompletionStatus } from '@/types/userProfile';

/**
 * Hook for fetching user profile
 */
export function useUserProfile() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.user.profile(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUserProfile] Fetching user profile for user:', user.id);
      return UserProfileService.getOrCreateProfile(user.id);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook for fetching user preferences only
 */
export function useUserPreferences() {
  const { data: profile, ...rest } = useUserProfile();
  
  return {
    data: profile?.preferences,
    ...rest,
  };
}

/**
 * Hook for fetching user timezone specifically
 */
export function useUserTimezone() {
  const { data: preferences, ...rest } = useUserPreferences();
  
  return {
    data: preferences?.timezone,
    ...rest,
  };
}

/**
 * Hook for fetching completion status
 */
export function useCompletionStatus() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.user.completionStatus(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useCompletionStatus] Fetching completion status for user:', user.id);
      return UserProfileService.getCompletionStatus(user.id);
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}

/**
 * Hook for updating user preferences
 */
export function useUpdateUserPreferences() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (preferences: Partial<UserPreferences>) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUpdateUserPreferences] Updating preferences for user:', user.id);
      return UserProfileService.updatePreferences(user.id, preferences);
    },
    onSuccess: () => {
      console.log('[useUpdateUserPreferences] Preferences updated successfully');
      
      // Invalidate user profile queries to refetch fresh data
      if (user?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.user.profile(user.id)
        });
        queryClient.invalidateQueries({
          queryKey: queryKeys.user.completionStatus(user.id)
        });
      }
    },
    onError: (error) => {
      console.error('[useUpdateUserPreferences] Failed to update preferences:', error);
    },
  });
}

/**
 * Hook for updating completion status
 */
export function useUpdateCompletionStatus() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (status: Partial<CompletionStatus>) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUpdateCompletionStatus] Updating completion status for user:', user.id);
      return UserProfileService.updateCompletionStatus(user.id, status);
    },
    onSuccess: () => {
      console.log('[useUpdateCompletionStatus] Completion status updated successfully');
      
      // Invalidate user profile queries to refetch fresh data
      if (user?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.user.profile(user.id)
        });
        queryClient.invalidateQueries({
          queryKey: queryKeys.user.completionStatus(user.id)
        });
      }
    },
    onError: (error) => {
      console.error('[useUpdateCompletionStatus] Failed to update completion status:', error);
    },
  });
}
