/**
 * Modern Journal Hooks using TanStack Query
 * Replaces the old useEffect + useState patterns with modern data fetching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { journalService } from '@/lib/journalService';
import { queryKeys, invalidateJournalQueries, invalidateUserJournalQueries } from '@/lib/queryClient';
import type {
  JournalQuestion,
  JournalEntryWithQuestion,
  JournalEntryGroup,
  JournalEntry,
  JournalEntryInput,
} from '@/types/journal';

/**
 * Hook for fetching journal history with pagination
 * Replaces the old useJournalHistory hook
 */
export function useJournalHistory() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.journal.history(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useJournalHistory] Fetching journal history for user:', user.id);
      return journalService.getJournalHistory(user.id, 30, 0);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook for fetching today's journal entries
 * Replaces the old useTodayEntries hook
 */
export function useTodayEntries() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.journal.today(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useTodayEntries] Fetching today\'s entries for user:', user.id);
      return journalService.getTodayEntries(user.id);
    },
    enabled: !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes (more frequent for today's data)
    retry: 2,
  });
}

/**
 * Hook for fetching journal questions (system only)
 * Replaces the old useJournalQuestions hook
 */
export function useJournalQuestions() {
  return useQuery({
    queryKey: queryKeys.journal.questions(),
    queryFn: async () => {
      console.log('[useJournalQuestions] Fetching journal questions');
      return journalService.getJournalQuestions();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes (questions don't change often)
    retry: 2,
  });
}

/**
 * Hook for fetching all user fields (system + custom)
 */
export function useUserJournalFields() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.journal.userFields(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUserJournalFields] Fetching user journal fields for user:', user.id);
      return journalService.getUserJournalFields(user.id);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook for fetching journal entries by date range
 */
export function useJournalByDate(date: string) {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.journal.byDate(user?.id || '', date),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useJournalByDate] Fetching entries for date:', date);
      return journalService.getEntriesByDateRange(user.id, date, date);
    },
    enabled: !!user?.id && !!date,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Mutation hook for creating journal entries
 */
export function useCreateJournalEntry() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (input: JournalEntryInput) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useCreateJournalEntry] Creating journal entry for user:', user.id);
      return journalService.createEntry(user.id, input);
    },
    onSuccess: (data, variables) => {
      console.log('[useCreateJournalEntry] Entry created successfully:', data.id);
      
      // Invalidate relevant queries to refetch fresh data
      if (user?.id) {
        invalidateUserJournalQueries(user.id);
        
        // Also invalidate the specific date if provided
        if (variables.entry_date) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.journal.byDate(user.id, variables.entry_date)
          });
        }
      }
    },
    onError: (error) => {
      console.error('[useCreateJournalEntry] Failed to create entry:', error);
    },
  });
}

/**
 * Mutation hook for updating journal entries
 */
export function useUpdateJournalEntry() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ entryId, answer }: { entryId: string; answer: string }) => {
      console.log('[useUpdateJournalEntry] Updating journal entry:', entryId);
      return journalService.updateEntry(entryId, answer);
    },
    onSuccess: (data) => {
      console.log('[useUpdateJournalEntry] Entry updated successfully:', data.id);
      
      // Invalidate relevant queries
      if (user?.id) {
        invalidateUserJournalQueries(user.id);
        
        // Also invalidate the specific date
        if (data.entry_date) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.journal.byDate(user.id, data.entry_date)
          });
        }
      }
    },
    onError: (error) => {
      console.error('[useUpdateJournalEntry] Failed to update entry:', error);
    },
  });
}

/**
 * Mutation hook for deleting journal entries
 */
export function useDeleteJournalEntry() {
  const { user } = useUniversalAuth();

  return useMutation({
    mutationFn: async (entryId: string) => {
      console.log('[useDeleteJournalEntry] Deleting journal entry:', entryId);
      return journalService.deleteEntry(entryId);
    },
    onSuccess: () => {
      console.log('[useDeleteJournalEntry] Entry deleted successfully');
      
      // Invalidate relevant queries
      if (user?.id) {
        invalidateUserJournalQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useDeleteJournalEntry] Failed to delete entry:', error);
    },
  });
}

/**
 * Hook to manually refetch all journal data
 * Useful for pull-to-refresh functionality
 */
export function useRefreshJournalData() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return () => {
    if (user?.id) {
      console.log('[useRefreshJournalData] Refreshing all journal data for user:', user.id);
      invalidateUserJournalQueries(user.id);
      queryClient.invalidateQueries({ queryKey: queryKeys.journal.questions() });
    }
  };
}
