import { useMemo } from 'react';
import { Dimensions, useWindowDimensions } from 'react-native';

export interface ResponsiveBreakpoints {
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
  isLandscape: boolean;
  isTablet: boolean;
}

export interface ResponsiveDimensions {
  screenWidth: number;
  screenHeight: number;
  availableHeight: number;
  availableWidth: number;
  breakpoints: ResponsiveBreakpoints;
  scaleFactor: number;
  fontScale: number;
  spacingScale: number;
}

export interface MoodTrackerResponsiveConfig {
  // Central display sizing
  centralDisplaySize: number;
  centralDisplayMaxSize: number;
  centralDisplayMinSize: number;
  
  // Font sizes
  questionFontSize: number;
  centralNumberFontSize: number;
  optionFontSize: number;
  labelFontSize: number;
  
  // Spacing
  containerPadding: number;
  sectionMargin: number;
  buttonMargin: number;
  
  // Component dimensions
  selectorWidth: number | string;
  sliderHeight: number;
  leftSectionWidth: number;
  circularSelectorSize: number;
  
  // Touch targets
  minTouchTarget: number;
  buttonHeight: number;
}

/**
 * Hook to get responsive dimensions and breakpoints for mood tracker components
 * Extends existing responsive patterns from the codebase
 */
export function useResponsiveDimensions(): ResponsiveDimensions {
  const { width, height } = useWindowDimensions();
  
  const dimensions = useMemo(() => {
    // Define breakpoints based on common device sizes
    const isSmallScreen = width < 375 || height < 667;
    const isMediumScreen = width >= 375 && width <= 414 && height >= 667 && height <= 896;
    const isLargeScreen = width > 414 || height > 896;
    const isLandscape = width > height;
    const isTablet = width >= 768;
    
    // Calculate scale factors
    const baseWidth = 375; // iPhone SE width as baseline
    const baseHeight = 667; // iPhone SE height as baseline
    
    const widthScale = Math.min(Math.max(width / baseWidth, 0.8), 1.3);
    const heightScale = Math.min(Math.max(height / baseHeight, 0.8), 1.3);
    const scaleFactor = Math.min(widthScale, heightScale);
    
    // Font scaling - more conservative than general scaling
    const fontScale = Math.min(Math.max(scaleFactor, 0.85), 1.15);
    
    // Spacing scaling - even more conservative
    const spacingScale = Math.min(Math.max(scaleFactor, 0.9), 1.1);
    
    // Available space calculations (accounting for safe areas and navigation)
    const availableHeight = height - 120; // Account for header, navigation, safe areas
    const availableWidth = width - 40; // Account for horizontal padding
    
    return {
      screenWidth: width,
      screenHeight: height,
      availableHeight,
      availableWidth,
      breakpoints: {
        isSmallScreen,
        isMediumScreen,
        isLargeScreen,
        isLandscape,
        isTablet,
      },
      scaleFactor,
      fontScale,
      spacingScale,
    };
  }, [width, height]);
  
  return dimensions;
}

/**
 * Hook to get mood tracker specific responsive configuration
 */
export function useMoodTrackerResponsive(): MoodTrackerResponsiveConfig {
  const { breakpoints, scaleFactor, fontScale, spacingScale, availableWidth, availableHeight } = useResponsiveDimensions();
  
  return useMemo(() => {
    const { isSmallScreen, isLargeScreen, isTablet } = breakpoints;
    
    // Central display sizing with constraints
    const baseCentralSize = 200;
    const centralDisplaySize = Math.min(
      Math.max(baseCentralSize * scaleFactor, 120), // Min 120px
      Math.min(availableWidth * 0.6, availableHeight * 0.25, 280) // Max based on available space
    );
    
    // Font sizes with responsive scaling
    const questionFontSize = Math.round(24 * fontScale);
    const centralNumberFontSize = Math.round(
      isSmallScreen ? 100 * fontScale : 
      isLargeScreen ? 150 * fontScale : 
      130 * fontScale
    );
    const optionFontSize = Math.round(16 * fontScale);
    const labelFontSize = Math.round(14 * fontScale);
    
    // Spacing with responsive scaling
    const containerPadding = Math.round(20 * spacingScale);
    const sectionMargin = Math.round(20 * spacingScale);
    const buttonMargin = Math.round(30 * spacingScale);
    
    // Component dimensions
    const selectorWidth = isSmallScreen ? '90%' : isTablet ? '70%' : '80%';
    const sliderHeight = Math.min(
      Math.max(300 * scaleFactor, 250), // Min 250px
      availableHeight * 0.4 // Max 40% of available height
    );
    
    const leftSectionWidth = Math.min(
      Math.max(140 * scaleFactor, 120), // Min 120px
      availableWidth * 0.4 // Max 40% of available width
    );
    
    const circularSelectorSize = Math.min(
      Math.max(320 * scaleFactor, 280), // Min 280px
      availableWidth - 40 // Account for padding
    );
    
    // Touch targets
    const minTouchTarget = 44; // iOS HIG minimum
    const buttonHeight = Math.max(48 * scaleFactor, minTouchTarget);
    
    return {
      centralDisplaySize,
      centralDisplayMaxSize: 280,
      centralDisplayMinSize: 120,
      questionFontSize,
      centralNumberFontSize,
      optionFontSize,
      labelFontSize,
      containerPadding,
      sectionMargin,
      buttonMargin,
      selectorWidth,
      sliderHeight,
      leftSectionWidth,
      circularSelectorSize,
      minTouchTarget,
      buttonHeight,
    };
  }, [breakpoints, scaleFactor, fontScale, spacingScale, availableWidth, availableHeight]);
}

/**
 * Utility function to get responsive font size with min/max constraints
 */
export function getResponsiveFontSize(
  baseSize: number, 
  fontScale: number, 
  minSize?: number, 
  maxSize?: number
): number {
  const scaledSize = Math.round(baseSize * fontScale);
  
  if (minSize && scaledSize < minSize) return minSize;
  if (maxSize && scaledSize > maxSize) return maxSize;
  
  return scaledSize;
}

/**
 * Utility function to get responsive spacing with constraints
 */
export function getResponsiveSpacing(
  baseSpacing: number, 
  spacingScale: number, 
  minSpacing?: number, 
  maxSpacing?: number
): number {
  const scaledSpacing = Math.round(baseSpacing * spacingScale);
  
  if (minSpacing && scaledSpacing < minSpacing) return minSpacing;
  if (maxSpacing && scaledSpacing > maxSpacing) return maxSpacing;
  
  return scaledSpacing;
}
