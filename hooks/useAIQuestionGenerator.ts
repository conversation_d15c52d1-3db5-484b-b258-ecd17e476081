/**
 * TanStack Query hooks for AI question generation
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { aiQuestionService } from '@/lib/aiQuestionService';
import { queryKeys, invalidateAIQueries } from '@/lib/queryClient';
import type {
  AIQuestionGenerationRequest,
  AIQuestionGenerationResponse,
  AIGenerationLog,
  AIRateLimit,
} from '@/types/journal';

/**
 * Hook for checking AI generation rate limit
 */
export function useAIRateLimit() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.ai.rateLimit(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useAIRateLimit] Checking rate limit for user:', user.id);
      return aiQuestionService.checkRateLimit(user.id);
    },
    enabled: !!user?.id,
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 1,
  });
}

/**
 * Hook for fetching AI generation statistics
 */
export function useAIGenerationStats() {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: queryKeys.ai.stats(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useAIGenerationStats] Fetching AI stats for user:', user.id);
      return aiQuestionService.getUserStats(user.id);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
}

/**
 * Hook for fetching AI generation history
 */
export function useAIGenerationHistory(limit: number = 20, offset: number = 0) {
  const { user } = useUniversalAuth();

  return useQuery({
    queryKey: [...queryKeys.ai.history(user?.id || ''), limit, offset],
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useAIGenerationHistory] Fetching AI history for user:', user.id);
      return aiQuestionService.getGenerationHistory(user.id, limit, offset);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
}

/**
 * Mutation hook for generating AI questions
 */
export function useGenerateAIQuestions() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (request: AIQuestionGenerationRequest) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useGenerateAIQuestions] Generating AI questions for user:', user.id);
      return aiQuestionService.generateQuestions(user.id, request);
    },
    onSuccess: (data, variables) => {
      console.log('[useGenerateAIQuestions] AI questions generated successfully');
      
      if (user?.id) {
        // Invalidate AI-related queries to refresh rate limits and stats
        invalidateAIQueries(user.id);
      }
    },
    onError: (error) => {
      console.error('[useGenerateAIQuestions] Error generating AI questions:', error);
    },
  });
}

/**
 * Mutation hook for updating generation feedback
 */
export function useUpdateGenerationFeedback() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      generationId, 
      feedback 
    }: { 
      generationId: string; 
      feedback: Record<string, any> 
    }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useUpdateGenerationFeedback] Updating feedback for generation:', generationId);
      return aiQuestionService.updateGenerationFeedback(user.id, generationId, feedback);
    },
    onSuccess: (_, { generationId }) => {
      console.log('[useUpdateGenerationFeedback] Feedback updated successfully:', generationId);
      
      if (user?.id) {
        // Invalidate history to refresh the feedback
        queryClient.invalidateQueries({
          queryKey: queryKeys.ai.history(user.id)
        });
      }
    },
    onError: (error) => {
      console.error('[useUpdateGenerationFeedback] Error updating feedback:', error);
    },
  });
}

/**
 * Mutation hook for marking questions as selected
 */
export function useMarkQuestionsSelected() {
  const { user } = useUniversalAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      generationId, 
      selectedQuestions 
    }: { 
      generationId: string; 
      selectedQuestions: string[] 
    }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      console.log('[useMarkQuestionsSelected] Marking questions as selected:', generationId);
      return aiQuestionService.markQuestionsSelected(user.id, generationId, selectedQuestions);
    },
    onSuccess: (_, { generationId }) => {
      console.log('[useMarkQuestionsSelected] Questions marked as selected:', generationId);
      
      if (user?.id) {
        // Invalidate history and stats
        queryClient.invalidateQueries({
          queryKey: queryKeys.ai.history(user.id)
        });
        queryClient.invalidateQueries({
          queryKey: queryKeys.ai.stats(user.id)
        });
      }
    },
    onError: (error) => {
      console.error('[useMarkQuestionsSelected] Error marking questions as selected:', error);
    },
  });
}

/**
 * Hook to check if user can generate more questions today
 */
export function useCanGenerateQuestions() {
  const { data: rateLimit, isLoading } = useAIRateLimit();

  return {
    canGenerate: rateLimit?.allowed ?? true,
    remaining: rateLimit?.remaining ?? 0,
    dailyLimit: rateLimit?.daily_limit ?? 20,
    currentCount: rateLimit?.current_count ?? 0,
    resetTime: rateLimit?.reset_time,
    isLoading,
  };
}

/**
 * Hook for AI generation with automatic rate limit checking
 */
export function useAIGenerationWithRateLimit() {
  const generateMutation = useGenerateAIQuestions();
  const { canGenerate, remaining } = useCanGenerateQuestions();

  const generateWithCheck = async (request: AIQuestionGenerationRequest) => {
    if (!canGenerate) {
      throw new Error('You\'ve reached your daily limit for writing ideas. Please try again tomorrow!');
    }

    return generateMutation.mutateAsync(request);
  };

  return {
    ...generateMutation,
    generateWithCheck,
    canGenerate,
    remaining,
  };
}

/**
 * Hook for getting recent successful generations
 */
export function useRecentSuccessfulGenerations(limit: number = 5) {
  const { data: history } = useAIGenerationHistory(limit * 2); // Get more to filter

  const recentSuccessful = history?.filter(gen => gen.success).slice(0, limit) || [];

  return {
    data: recentSuccessful,
    count: recentSuccessful.length,
  };
}

/**
 * Hook for AI generation analytics
 */
export function useAIGenerationAnalytics() {
  const { data: stats } = useAIGenerationStats();
  const { data: rateLimit } = useAIRateLimit();

  const analytics = {
    totalGenerations: stats?.total_generations || 0,
    successRate: stats?.total_generations 
      ? ((stats?.successful_generations || 0) / stats.total_generations * 100).toFixed(1)
      : '0',
    avgProcessingTime: stats?.avg_processing_time_ms 
      ? Math.round(stats.avg_processing_time_ms)
      : 0,
    todayUsage: rateLimit?.current_count || 0,
    todayLimit: rateLimit?.daily_limit || 20,
    usagePercentage: rateLimit?.daily_limit 
      ? Math.round((rateLimit.current_count || 0) / rateLimit.daily_limit * 100)
      : 0,
    generationTypes: stats?.generation_types || {},
    lastGeneration: stats?.last_generation,
  };

  return analytics;
}

/**
 * Hook for prefetching AI data
 */
export function usePrefetchAIData() {
  const queryClient = useQueryClient();
  const { user } = useUniversalAuth();

  const prefetchAll = () => {
    if (!user?.id) return;

    // Prefetch rate limit
    queryClient.prefetchQuery({
      queryKey: queryKeys.ai.rateLimit(user.id),
      queryFn: () => aiQuestionService.checkRateLimit(user.id),
      staleTime: 1 * 60 * 1000,
    });

    // Prefetch stats
    queryClient.prefetchQuery({
      queryKey: queryKeys.ai.stats(user.id),
      queryFn: () => aiQuestionService.getUserStats(user.id),
      staleTime: 5 * 60 * 1000,
    });
  };

  return { prefetchAll };
}
