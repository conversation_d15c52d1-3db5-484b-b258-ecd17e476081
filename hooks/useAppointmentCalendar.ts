import { useState, useEffect, useCallback } from 'react';

export interface UseAppointmentCalendarReturn {
  currentMonth: Date;
  selectedDate: string | null;
  availableDates: string[];
  bookedDates: string[];
  availableTimeSlots: string[];
  loading: boolean;
  error: string | null;
  navigateMonth: (direction: 'prev' | 'next') => void;
  selectDate: (date: string) => void;
  goToToday: () => void;
  refetch: () => Promise<void>;
}

export function useAppointmentCalendar(): UseAppointmentCalendarReturn {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [bookedDates, setBookedDates] = useState<string[]>([]);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cache for month data to avoid repeated API calls
  const [monthCache, setMonthCache] = useState<Map<string, { available: string[], booked: string[] }>>(new Map());

  const getMonthKey = (date: Date): string => {
    return `${date.getFullYear()}-${date.getMonth()}`;
  };

  // Mock data for available time slots
  const allTimeSlots = [
    '09:00', '10:00', '11:00', '13:00', '14:00', '15:00', '16:00'
  ];

  // Generate mock available dates (next 30 days, excluding weekends for now)
  const generateMockAvailableDates = useCallback((month: Date): string[] => {
    const dates: string[] = [];
    const year = month.getFullYear();
    const monthNum = month.getMonth();
    
    // Get first and last day of the month
    const firstDay = new Date(year, monthNum, 1);
    const lastDay = new Date(year, monthNum + 1, 0);
    const today = new Date();
    
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const currentDate = new Date(year, monthNum, day);
      const dateString = currentDate.toISOString().split('T')[0];
      
      // Only include future dates and weekdays (Monday-Friday)
      if (currentDate >= today && currentDate.getDay() >= 1 && currentDate.getDay() <= 5) {
        dates.push(dateString);
      }
    }
    
    return dates;
  }, []);

  // Generate mock booked dates (some random dates from available ones)
  const generateMockBookedDates = useCallback((availableDates: string[]): string[] => {
    // Mock: book every 3rd available date
    return availableDates.filter((_, index) => index % 3 === 0);
  }, []);

  const fetchAppointmentDataForMonth = useCallback(async (month: Date) => {
    const monthKey = getMonthKey(month);
    
    // Check cache first
    if (monthCache.has(monthKey)) {
      const cached = monthCache.get(monthKey)!;
      setAvailableDates(cached.available);
      setBookedDates(cached.booked);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate mock data
      const available = generateMockAvailableDates(month);
      const booked = generateMockBookedDates(available);
      
      setAvailableDates(available);
      setBookedDates(booked);
      
      // Cache the result
      setMonthCache(prev => new Map(prev).set(monthKey, { available, booked }));
      
    } catch (err: any) {
      setError(err.message || 'Failed to load appointment data');
      console.error('Error fetching appointment data:', err);
    } finally {
      setLoading(false);
    }
  }, [monthCache, generateMockAvailableDates, generateMockBookedDates]);

  const fetchTimeSlotsForDate = useCallback(async (date: string) => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Mock: return all time slots for available dates, empty for booked dates
      if (bookedDates.includes(date)) {
        setAvailableTimeSlots([]);
      } else if (availableDates.includes(date)) {
        // Mock: randomly remove some time slots to simulate partial availability
        const availableSlots = allTimeSlots.filter(() => Math.random() > 0.3);
        setAvailableTimeSlots(availableSlots);
      } else {
        setAvailableTimeSlots([]);
      }
      
    } catch (err: any) {
      setError(err.message || 'Failed to load time slots');
      console.error('Error fetching time slots:', err);
    } finally {
      setLoading(false);
    }
  }, [availableDates, bookedDates]);

  const navigateMonth = useCallback((direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      if (direction === 'prev') {
        newMonth.setMonth(prev.getMonth() - 1);
      } else {
        newMonth.setMonth(prev.getMonth() + 1);
      }
      return newMonth;
    });
  }, []);

  const selectDate = useCallback((date: string) => {
    setSelectedDate(date);
    fetchTimeSlotsForDate(date);
  }, [fetchTimeSlotsForDate]);

  const goToToday = useCallback(() => {
    const today = new Date();
    setCurrentMonth(today);
    const todayString = today.toISOString().split('T')[0];
    
    // Only select today if it's available
    if (availableDates.includes(todayString)) {
      selectDate(todayString);
    } else {
      setSelectedDate(null);
      setAvailableTimeSlots([]);
    }
  }, [availableDates, selectDate]);

  const refetch = useCallback(async () => {
    // Clear cache and refetch current month
    setMonthCache(new Map());
    await fetchAppointmentDataForMonth(currentMonth);
    
    // Refetch time slots if there's a selected date
    if (selectedDate) {
      await fetchTimeSlotsForDate(selectedDate);
    }
  }, [currentMonth, selectedDate, fetchAppointmentDataForMonth, fetchTimeSlotsForDate]);

  // Fetch data when month changes
  useEffect(() => {
    fetchAppointmentDataForMonth(currentMonth);
  }, [currentMonth, fetchAppointmentDataForMonth]);

  // Initialize with current month
  useEffect(() => {
    const today = new Date();
    setCurrentMonth(today);
  }, []);

  return {
    currentMonth,
    selectedDate,
    availableDates,
    bookedDates,
    availableTimeSlots,
    loading,
    error,
    navigateMonth,
    selectDate,
    goToToday,
    refetch,
  };
}
