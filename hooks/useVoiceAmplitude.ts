import { useCallback, useRef, useEffect } from 'react';
import { useSharedValue, withTiming, withRepeat, withSequence } from 'react-native-reanimated';
import { Conversation } from '@elevenlabs/client';

interface VoiceAmplitudeConfig {
  smoothingFactor?: number;
  responseDuration?: number;
  peakThreshold?: number;
  fallbackAnimationIntensity?: number;
}

interface VoiceAmplitudeHook {
  voiceIntensity: any; // SharedValue
  peakValue: any; // SharedValue
  startTracking: () => void;
  stopTracking: () => void;
  isTracking: boolean;
}

export function useVoiceAmplitude(
  conversation: any, // Conversation instance
  micMuted: boolean,
  config: VoiceAmplitudeConfig = {}
): VoiceAmplitudeHook {
  const {
    smoothingFactor = 0.15, // More responsive than before (was 0.3)
    responseDuration = 30, // Faster response (was 50ms)
    peakThreshold = 0.7,
    fallbackAnimationIntensity = 0.6
  } = config;

  // Animation values
  const voiceIntensity = useSharedValue(0);
  const peakValue = useSharedValue(0);
  
  // Tracking state
  const amplitudeAnimationRef = useRef<number | null>(null);
  const isTrackingRef = useRef(false);

  // Calculate voice frequency weighted amplitude
  const calculateVoiceFrequencyAmplitude = useCallback((frequencyData: Uint8Array): number => {
    if (!frequencyData || frequencyData.length === 0) return 0;
    
    // Voice frequencies are typically between 85-255 Hz (fundamental) and 2000-4000 Hz (formants)
    // For a typical 2048 FFT at 44.1kHz, this maps to roughly bins 4-12 and 93-186
    const voiceRangeLow = Math.floor(frequencyData.length * 0.02); // ~85-300 Hz
    const voiceRangeHigh = Math.floor(frequencyData.length * 0.2); // ~2000-4000 Hz
    
    let sum = 0;
    let count = 0;
    
    // Weight voice frequencies more heavily
    for (let i = voiceRangeLow; i < Math.min(voiceRangeHigh, frequencyData.length); i++) {
      sum += frequencyData[i];
      count++;
    }
    
    if (count === 0) return 0;
    
    // Normalize to 0-1 range (255 is max value for Uint8Array)
    return Math.min((sum / count) / 255, 1);
  }, []);

  // Enhanced amplitude tracking function
  const updateAmplitude = useCallback(() => {
    if (conversation?.status === 'connected' && !micMuted && isTrackingRef.current) {
      try {
        // Get raw amplitude from Client SDK
        const rawAmplitude = conversation.getInputVolume?.();
        
        if (typeof rawAmplitude === 'number' && !isNaN(rawAmplitude)) {
          // Try to get frequency data for more sophisticated analysis
          let processedAmplitude = rawAmplitude;

          try {
            const frequencyData = conversation.getInputByteFrequencyData?.();
            if (frequencyData) {
              const voiceWeightedAmplitude = calculateVoiceFrequencyAmplitude(frequencyData);
              // Combine raw amplitude with voice-weighted amplitude
              processedAmplitude = Math.max(rawAmplitude, voiceWeightedAmplitude * 0.8);
            }
          } catch (freqError) {
            // Frequency data not available, using raw amplitude
          }
          
          // Apply exponential smoothing with improved responsiveness
          const currentValue = voiceIntensity.value;
          const newValue = Math.min(
            processedAmplitude * smoothingFactor + currentValue * (1 - smoothingFactor),
            1
          );
          
          voiceIntensity.value = withTiming(newValue, { duration: responseDuration });
          
          // Peak detection for dramatic effects
          if (processedAmplitude > peakThreshold) {
            peakValue.value = withSequence(
              withTiming(1, { duration: 100 }),
              withTiming(0, { duration: 400 })
            );
          }
          
        } else {
          console.warn('[VoiceAmplitude] Invalid amplitude value:', rawAmplitude);
          // Fallback to gentle animation
          voiceIntensity.value = withRepeat(
            withSequence(
              withTiming(fallbackAnimationIntensity, { duration: 600 }),
              withTiming(0.2, { duration: 600 })
            ),
            -1,
            false
          );
        }
        
        // Continue animation loop
        amplitudeAnimationRef.current = requestAnimationFrame(updateAmplitude);
        
      } catch (err) {
        console.error('[VoiceAmplitude] Error reading amplitude:', err);
        
        // Enhanced fallback animation - more dramatic than before
        voiceIntensity.value = withRepeat(
          withSequence(
            withTiming(fallbackAnimationIntensity, { duration: 600 }),
            withTiming(0.2, { duration: 600 })
          ),
          -1,
          false
        );
        
        // Continue trying
        amplitudeAnimationRef.current = requestAnimationFrame(updateAmplitude);
      }
    } else {
      // Not connected or muted, fade out
      voiceIntensity.value = withTiming(0, { duration: 300 });
      peakValue.value = withTiming(0, { duration: 300 });
    }
  }, [
    conversation,
    micMuted,
    smoothingFactor,
    responseDuration,
    peakThreshold,
    fallbackAnimationIntensity,
    calculateVoiceFrequencyAmplitude,
    voiceIntensity,
    peakValue
  ]);

  // Start tracking function
  const startTracking = useCallback(() => {
    if (!isTrackingRef.current && conversation?.status === 'connected') {
      isTrackingRef.current = true;
      amplitudeAnimationRef.current = requestAnimationFrame(updateAmplitude);
    }
  }, [conversation, updateAmplitude]);

  // Stop tracking function
  const stopTracking = useCallback(() => {
    isTrackingRef.current = false;
    
    if (amplitudeAnimationRef.current !== null) {
      cancelAnimationFrame(amplitudeAnimationRef.current);
      amplitudeAnimationRef.current = null;
    }
    
    // Fade out animations
    voiceIntensity.value = withTiming(0, { duration: 300 });
    peakValue.value = withTiming(0, { duration: 300 });
  }, [voiceIntensity, peakValue]);

  // Auto-start/stop based on connection and mute state
  useEffect(() => {
    if (conversation?.status === 'connected' && !micMuted) {
      startTracking();
    } else {
      stopTracking();
    }
    
    // Cleanup on unmount
    return () => {
      stopTracking();
    };
  }, [conversation?.status, micMuted, startTracking, stopTracking]);

  return {
    voiceIntensity,
    peakValue,
    startTracking,
    stopTracking,
    isTracking: isTrackingRef.current
  };
}
