import { useState, useEffect, useCallback } from 'react';
import { journalService } from '@/lib/journalService';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import type {
  JournalQuestion,
  JournalEntryWithQuestion,
  JournalEntryGroup,
  JournalEntryInput,
  UseJournalQuestionsReturn,
  UseTodayEntriesReturn,
  UseJournalHistoryReturn,
} from '@/types/journal';

/**
 * Hook for managing journal questions
 */
export function useJournalQuestions(): UseJournalQuestionsReturn {
  const [questions, setQuestions] = useState<JournalQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await journalService.getJournalQuestions();
      setQuestions(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load journal questions');
      console.error('Error fetching journal questions:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);

  return {
    questions,
    loading,
    error,
    refetch: fetchQuestions,
  };
}

/**
 * Hook for managing today's journal entries
 */
export function useTodayEntries(): UseTodayEntriesReturn {
  const { user } = useUniversalAuth();
  const [entries, setEntries] = useState<JournalEntryWithQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTodayEntries = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);
      const data = await journalService.getTodayEntries(user.id);
      setEntries(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load today\'s entries');
      console.error('Error fetching today\'s entries:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const createEntry = useCallback(async (input: JournalEntryInput) => {
    if (!user?.id) return null;

    try {
      setError(null);
      const newEntry = await journalService.createEntry(user.id, input);
      await fetchTodayEntries(); // Refresh entries
      return newEntry;
    } catch (err: any) {
      setError(err.message || 'Failed to create journal entry');
      console.error('Error creating journal entry:', err);
      return null;
    }
  }, [user?.id, fetchTodayEntries]);

  const updateEntry = useCallback(async (entryId: string, answer: string) => {
    try {
      setError(null);
      const updatedEntry = await journalService.updateEntry(entryId, answer);
      await fetchTodayEntries(); // Refresh entries
      return updatedEntry;
    } catch (err: any) {
      setError(err.message || 'Failed to update journal entry');
      console.error('Error updating journal entry:', err);
      return null;
    }
  }, [fetchTodayEntries]);

  const deleteEntry = useCallback(async (entryId: string) => {
    try {
      setError(null);
      await journalService.deleteEntry(entryId);
      await fetchTodayEntries(); // Refresh entries
      return true;
    } catch (err: any) {
      setError(err.message || 'Failed to delete journal entry');
      console.error('Error deleting journal entry:', err);
      return false;
    }
  }, [fetchTodayEntries]);

  useEffect(() => {
    fetchTodayEntries();
  }, [fetchTodayEntries]);

  return {
    entries,
    loading,
    error,
    createEntry,
    updateEntry,
    deleteEntry,
    refetch: fetchTodayEntries,
  };
}

/**
 * Hook for managing journal history
 */
export function useJournalHistory(): UseJournalHistoryReturn {
  const { user } = useUniversalAuth();
  const [historyGroups, setHistoryGroups] = useState<JournalEntryGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);

  const fetchHistory = useCallback(async (reset: boolean = false) => {
    console.log('[useJournalHistory] fetchHistory called:', {
      userId: user?.id,
      reset,
      hasUser: !!user,
      currentOffset: reset ? 0 : offset
    });

    if (!user?.id) {
      console.log('[useJournalHistory] No user ID available, skipping fetch');
      return;
    }

    // Use functional state update to get current loading state
    let shouldProceed = true;
    setLoading(currentLoading => {
      console.log('[useJournalHistory] Current loading state:', currentLoading);
      if (currentLoading && !reset) {
        console.log('[useJournalHistory] Already loading, skipping duplicate call');
        shouldProceed = false;
        return currentLoading;
      }
      console.log('[useJournalHistory] Setting loading to true');
      return true;
    });

    if (!shouldProceed) {
      console.log('[useJournalHistory] Skipping fetch due to loading state');
      return;
    }

    const currentOffset = reset ? 0 : offset;
    console.log('[useJournalHistory] Starting fetch with params:', {
      userId: user.id,
      reset,
      currentOffset,
      limit: 30
    });

    try {
      setError(null);

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000);
      });

      console.log('[useJournalHistory] Calling journalService.getJournalHistory...');
      const fetchPromise = journalService.getJournalHistory(user.id, 30, currentOffset);
      const data = await Promise.race([fetchPromise, timeoutPromise]) as any;

      console.log('[useJournalHistory] Fetch successful:', {
        dataLength: data?.length || 0,
        dataType: typeof data,
        isArray: Array.isArray(data),
        reset,
        currentOffset
      });

      if (reset) {
        setHistoryGroups(data || []);
        setOffset((data || []).length);
      } else {
        setHistoryGroups(prev => [...prev, ...(data || [])]);
        setOffset(prev => prev + (data || []).length);
      }

      // Check if there are more entries
      setHasMore((data || []).length === 30);
      setIsInitialized(true);

      console.log('[useJournalHistory] State updated successfully');
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load journal history';
      console.error('[useJournalHistory] Error fetching journal history:', {
        error: err,
        message: errorMessage,
        stack: err.stack,
        userId: user.id,
        reset,
        offset: currentOffset
      });
      setError(errorMessage);
    } finally {
      console.log('[useJournalHistory] Fetch completed, setting loading to false');
      setLoading(false);
    }
  }, [user?.id, offset]);

  const loadMore = useCallback(async () => {
    // Check loading state functionally to avoid dependency
    let canLoad = false;
    setLoading(currentLoading => {
      canLoad = !currentLoading && hasMore;
      return currentLoading;
    });

    if (!canLoad) return;
    await fetchHistory(false);
  }, [hasMore, fetchHistory]);

  const refetch = useCallback(async () => {
    console.log('[useJournalHistory] Refetch called');
    setOffset(0);
    setIsInitialized(false);
    // Call fetchHistory directly with reset=true to avoid circular dependency
    if (!user?.id) {
      console.log('[useJournalHistory] No user ID available for refetch');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000);
      });

      const fetchPromise = journalService.getJournalHistory(user.id, 30, 0);
      const data = await Promise.race([fetchPromise, timeoutPromise]) as any;

      console.log('[useJournalHistory] Refetch successful:', {
        dataLength: data.length
      });

      setHistoryGroups(data);
      setOffset(data.length);
      setHasMore(data.length === 30);
      setIsInitialized(true);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load journal history';
      console.error('[useJournalHistory] Refetch error:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    console.log('[useJournalHistory] useEffect triggered:', {
      userId: user?.id,
      hasUser: !!user,
      isInitialized,
      loading
    });

    // Only initialize once when user is available and we haven't initialized yet
    if (user?.id && !isInitialized && !loading) {
      console.log('[useJournalHistory] Initializing - calling fetchHistory directly');
      fetchHistory(true); // Call fetchHistory directly with reset=true
    }
  }, [user?.id, isInitialized, loading, fetchHistory]);

  return {
    historyGroups,
    loading,
    error,
    hasMore,
    loadMore,
    refetch,
  };
}

/**
 * Hook for managing a single journal entry with auto-save
 */
export function useJournalEntry(questionId: string, initialAnswer: string = '') {
  const { user } = useUniversalAuth();
  const [answer, setAnswer] = useState(initialAnswer);
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [entryId, setEntryId] = useState<string | null>(null);

  // Auto-save functionality
  const saveEntry = useCallback(async (text: string) => {
    if (!user?.id || !text.trim()) return;

    try {
      setSaving(true);
      setError(null);

      if (entryId) {
        // Update existing entry
        await journalService.updateEntry(entryId, text);
      } else {
        // Create new entry
        const newEntry = await journalService.createEntry(user.id, {
          question_id: questionId,
          answer: text,
        });
        setEntryId(newEntry.id);
      }

      setSaved(true);
      setTimeout(() => setSaved(false), 2000); // Hide saved indicator after 2 seconds
    } catch (err: any) {
      setError(err.message || 'Failed to save entry');
      console.error('Error saving journal entry:', err);
    } finally {
      setSaving(false);
    }
  }, [user?.id, questionId, entryId]);

  // Debounced auto-save
  useEffect(() => {
    if (!answer.trim()) return;

    const timeoutId = setTimeout(() => {
      saveEntry(answer);
    }, 1000); // Save after 1 second of inactivity

    return () => clearTimeout(timeoutId);
  }, [answer, saveEntry]);

  return {
    answer,
    setAnswer,
    saving,
    saved,
    error,
    entryId,
  };
}
