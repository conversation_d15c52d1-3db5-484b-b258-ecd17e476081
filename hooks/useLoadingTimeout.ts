import { useEffect, useRef, useState } from 'react';

interface UseLoadingTimeoutOptions {
  timeout?: number; // timeout in milliseconds
  onTimeout?: () => void;
  enabled?: boolean;
}

/**
 * Hook to detect and handle loading timeouts
 * Useful for preventing infinite loading states
 */
export const useLoadingTimeout = (
  isLoading: boolean,
  options: UseLoadingTimeoutOptions = {}
) => {
  const {
    timeout = 30000, // 30 seconds default
    onTimeout,
    enabled = true
  } = options;

  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!enabled) return;

    // Add debouncing to prevent rapid state changes
    const debounceDelay = 100; // 100ms debounce
    const debounceTimer = setTimeout(() => {
      if (isLoading) {
        // Only start tracking if not already tracking
        if (!loadingStartTime) {
          const startTime = Date.now();
          setLoadingStartTime(startTime);
          setHasTimedOut(false);

          // Set timeout
          timeoutRef.current = setTimeout(() => {
            console.warn('[useLoadingTimeout] Loading timeout reached after', timeout, 'ms');
            setHasTimedOut(true);
            onTimeout?.();
          }, timeout);

          console.log('[useLoadingTimeout] Started tracking loading at', new Date(startTime).toISOString());
        }
      } else {
        // Clear timeout when loading stops
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        if (loadingStartTime) {
          const duration = Date.now() - loadingStartTime;
          console.log('[useLoadingTimeout] Loading completed after', duration, 'ms');
        }

        setLoadingStartTime(null);
        setHasTimedOut(false);
      }
    }, debounceDelay);

    return () => {
      clearTimeout(debounceTimer);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isLoading, timeout, onTimeout, enabled, loadingStartTime]);

  const getLoadingDuration = () => {
    if (!loadingStartTime) return 0;
    return Date.now() - loadingStartTime;
  };

  const reset = () => {
    setHasTimedOut(false);
    setLoadingStartTime(null);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  return {
    hasTimedOut,
    loadingDuration: getLoadingDuration(),
    reset
  };
};
