/**
 * React Native App Focus Management for TanStack Query
 * Handles app state changes and focus management for optimal query behavior
 */

import { useEffect } from 'react';
import { AppState, Platform } from 'react-native';
import type { AppStateStatus } from 'react-native';
import { focusManager, onlineManager } from '@tanstack/react-query';

/**
 * Hook to manage app focus state for TanStack Query
 * Automatically handles:
 * - App foreground/background state changes
 * - Query refetching when app becomes active
 * - Pausing queries when app is in background
 */
export function useAppFocus() {
  useEffect(() => {
    const handleAppStateChange = (status: AppStateStatus) => {
      console.log('[useAppFocus] App state changed to:', status);
      
      // Only handle focus for native platforms
      if (Platform.OS !== 'web') {
        const isFocused = status === 'active';
        focusManager.setFocused(isFocused);
        
        // Log focus state for debugging
        console.log('[useAppFocus] Focus state set to:', isFocused);
      }
    };

    // Set initial focus state
    const currentState = AppState.currentState;
    handleAppStateChange(currentState);

    // Listen for app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Cleanup
    return () => {
      subscription.remove();
    };
  }, []);
}

/**
 * Hook to manage network status for TanStack Query
 * Note: Requires expo-network or @react-native-community/netinfo
 * This is optional and can be added later if needed
 */
export function useNetworkStatus() {
  useEffect(() => {
    // For now, we'll use a simple online/offline detection
    // You can enhance this with expo-network later if needed
    
    const handleOnline = () => {
      console.log('[useNetworkStatus] Network status: online');
      onlineManager.setOnline(true);
    };
    
    const handleOffline = () => {
      console.log('[useNetworkStatus] Network status: offline');
      onlineManager.setOnline(false);
    };

    // For web platform, use window events
    if (Platform.OS === 'web') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      // Set initial state
      onlineManager.setOnline(navigator.onLine);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
    
    // For native platforms, assume online by default
    // You can integrate with expo-network here later
    onlineManager.setOnline(true);
    
    return () => {
      // No cleanup needed for native platforms without network library
    };
  }, []);
}

/**
 * Combined hook that sets up both app focus and network status management
 * Use this in your root component for complete mobile optimization
 */
export function useMobileOptimizations() {
  useAppFocus();
  useNetworkStatus();
  
  useEffect(() => {
    console.log('[useMobileOptimizations] Mobile optimizations initialized');
  }, []);
}
