import type { 
  AIQuestionGenerationRequest, 
  AIGenerationType,
  AIQuestionGenerationResponse 
} from '@/types/journal';

export interface FormattedQuestion {
  id: string;
  text: string;
  type: AIGenerationType;
  confidence: number;
  isSelected: boolean;
}

export interface GenerationCost {
  tokensUsed: number;
  estimatedCost: number;
  currency: string;
}

export interface OptimizedPrompt {
  systemPrompt: string;
  userPrompt: string;
  modelSpecific: Record<string, string>;
}

/**
 * Build contextual prompt with user context
 */
export function buildContextualPrompt(
  baseQuestion: string, 
  context?: string,
  userPreferences?: Record<string, any>
): string {
  let prompt = `Berdasarkan pertanyaan: "${baseQuestion}"`;
  
  if (context && context.trim()) {
    prompt += `\n\nKonteks tambahan: ${context.trim()}`;
  }
  
  if (userPreferences?.tone) {
    const toneMap = {
      casual: 'santai dan bersahabat',
      formal: 'formal namun hangat',
      empathetic: 'sangat empati dan mendukung'
    };
    prompt += `\n\nGunakan tone: ${toneMap[userPreferences.tone as keyof typeof toneMap] || 'natural'}`;
  }
  
  if (userPreferences?.length) {
    const lengthMap = {
      short: 'singkat dan padat',
      medium: 'sedang dan jelas',
      long: 'detail dan mendalam'
    };
    prompt += `\n\nPanjang pertanyaan: ${lengthMap[userPreferences.length as keyof typeof lengthMap] || 'sedang'}`;
  }
  
  return prompt;
}

/**
 * Validate generated questions for safety and quality
 */
export function validateGeneratedQuestions(questions: string[]): {
  valid: string[];
  invalid: string[];
  reasons: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];
  const reasons: string[] = [];

  // Safety patterns to avoid
  const dangerousPatterns = [
    /bunuh diri|suicide/i,
    /menyakiti diri|self.?harm/i,
    /mati|death/i,
    /putus asa|hopeless/i,
    /tidak berguna|worthless/i,
    /bunuh|kill/i,
  ];

  // Quality patterns to check
  const qualityChecks = [
    {
      pattern: /^.{10,200}$/,
      message: 'Panjang pertanyaan harus 10-200 karakter'
    },
    {
      pattern: /\?$/,
      message: 'Pertanyaan harus diakhiri dengan tanda tanya'
    },
    {
      pattern: /^[A-Z\p{Lu}]/u,
      message: 'Pertanyaan harus dimulai dengan huruf kapital'
    },
    {
      pattern: /^(?!.*\b(ya|tidak|iya|nggak)\b.*\?$)/i,
      message: 'Hindari pertanyaan ya/tidak, gunakan pertanyaan terbuka'
    }
  ];

  questions.forEach((question, index) => {
    const trimmed = question.trim();
    
    // Check for dangerous content
    const hasDangerousContent = dangerousPatterns.some(pattern => pattern.test(trimmed));
    if (hasDangerousContent) {
      invalid.push(trimmed);
      reasons.push(`Pertanyaan ${index + 1}: Mengandung konten yang berpotensi berbahaya`);
      return;
    }

    // Check quality patterns
    const failedChecks = qualityChecks.filter(check => !check.pattern.test(trimmed));
    if (failedChecks.length > 0) {
      invalid.push(trimmed);
      reasons.push(`Pertanyaan ${index + 1}: ${failedChecks[0].message}`);
      return;
    }

    // Check for repetitive words
    const words = trimmed.toLowerCase().split(/\s+/);
    const wordCounts = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const hasRepetitiveWords = Object.values(wordCounts).some(count => count > 3);
    if (hasRepetitiveWords) {
      invalid.push(trimmed);
      reasons.push(`Pertanyaan ${index + 1}: Terlalu banyak kata yang berulang`);
      return;
    }

    valid.push(trimmed);
  });

  return { valid, invalid, reasons };
}

/**
 * Format questions for display with metadata
 */
export function formatQuestionsForDisplay(
  questions: string[], 
  type: AIGenerationType
): FormattedQuestion[] {
  return questions.map((question, index) => ({
    id: `${type}_${index}_${Date.now()}`,
    text: question.trim(),
    type,
    confidence: calculateQuestionConfidence(question),
    isSelected: false,
  }));
}

/**
 * Calculate confidence score for a generated question
 */
function calculateQuestionConfidence(question: string): number {
  let score = 0.5; // Base score

  // Length check (optimal 20-100 characters)
  const length = question.length;
  if (length >= 20 && length <= 100) {
    score += 0.2;
  } else if (length >= 10 && length <= 150) {
    score += 0.1;
  }

  // Question mark check
  if (question.endsWith('?')) {
    score += 0.1;
  }

  // Starts with question word
  const questionWords = ['apa', 'bagaimana', 'mengapa', 'kapan', 'dimana', 'siapa'];
  const startsWithQuestionWord = questionWords.some(word => 
    question.toLowerCase().startsWith(word)
  );
  if (startsWithQuestionWord) {
    score += 0.1;
  }

  // Contains reflective words
  const reflectiveWords = ['perasaan', 'pikiran', 'refleksi', 'makna', 'belajar', 'pengalaman'];
  const hasReflectiveWords = reflectiveWords.some(word => 
    question.toLowerCase().includes(word)
  );
  if (hasReflectiveWords) {
    score += 0.1;
  }

  // Avoid yes/no questions
  const isYesNoQuestion = /\b(apakah|sudah|belum|ya|tidak)\b/i.test(question);
  if (!isYesNoQuestion) {
    score += 0.1;
  }

  return Math.min(1.0, Math.max(0.0, score));
}

/**
 * Calculate estimated generation cost
 */
export function calculateGenerationCost(request: AIQuestionGenerationRequest): GenerationCost {
  // Rough estimation based on token usage
  const baseTokens = 100; // Base system prompt
  const questionTokens = Math.ceil(request.baseQuestion.length / 4); // ~4 chars per token
  const contextTokens = request.context ? Math.ceil(request.context.length / 4) : 0;
  const outputTokens = (request.count || 3) * 25; // ~25 tokens per generated question

  const totalTokens = baseTokens + questionTokens + contextTokens + outputTokens;
  
  // Groq pricing (approximate)
  const costPerToken = 0.0000002; // $0.0002 per 1K tokens
  const estimatedCost = totalTokens * costPerToken;

  return {
    tokensUsed: totalTokens,
    estimatedCost: estimatedCost,
    currency: 'USD',
  };
}

/**
 * Optimize prompt for specific model
 */
export function optimizePromptForModel(prompt: string, model: string): OptimizedPrompt {
  const baseSystemPrompt = prompt;
  let optimizedUserPrompt = prompt;
  const modelSpecific: Record<string, string> = {};

  // Model-specific optimizations
  if (model.includes('llama')) {
    // Llama models prefer more structured prompts
    modelSpecific.llama = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n${baseSystemPrompt}<|eot_id|>`;
  }

  if (model.includes('mixtral')) {
    // Mixtral prefers concise instructions
    optimizedUserPrompt = prompt.replace(/\n\n+/g, '\n').trim();
  }

  return {
    systemPrompt: baseSystemPrompt,
    userPrompt: optimizedUserPrompt,
    modelSpecific,
  };
}

/**
 * Analyze generation patterns for insights
 */
export function analyzeGenerationPatterns(generations: any[]): {
  averageQuality: number;
  commonThemes: string[];
  successRate: number;
  recommendations: string[];
} {
  if (generations.length === 0) {
    return {
      averageQuality: 0,
      commonThemes: [],
      successRate: 0,
      recommendations: ['Mulai generate pertanyaan untuk mendapatkan insights'],
    };
  }

  // Calculate average quality
  const validGenerations = generations.filter(g => g.success);
  const averageQuality = validGenerations.length > 0
    ? validGenerations.reduce((sum, g) => sum + (g.questions?.length || 0), 0) / validGenerations.length
    : 0;

  // Calculate success rate
  const successRate = (validGenerations.length / generations.length) * 100;

  // Extract common themes (simplified)
  const allQuestions = validGenerations.flatMap(g => g.questions || []);
  const commonWords = extractCommonWords(allQuestions);
  const commonThemes = commonWords.slice(0, 5);

  // Generate recommendations
  const recommendations: string[] = [];
  
  if (successRate < 80) {
    recommendations.push('Coba berikan konteks yang lebih spesifik untuk meningkatkan kualitas');
  }
  
  if (averageQuality < 2) {
    recommendations.push('Pertimbangkan untuk menggunakan pertanyaan dasar yang lebih detail');
  }
  
  if (commonThemes.length < 3) {
    recommendations.push('Eksplorasi topik yang lebih beragam untuk variasi yang lebih baik');
  }

  return {
    averageQuality,
    commonThemes,
    successRate,
    recommendations,
  };
}

/**
 * Extract common words from questions
 */
function extractCommonWords(questions: string[]): string[] {
  const wordCounts: Record<string, number> = {};
  const stopWords = new Set(['yang', 'dan', 'atau', 'untuk', 'dari', 'ke', 'di', 'pada', 'dengan', 'adalah', 'apa', 'bagaimana']);

  questions.forEach(question => {
    const words = question.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));

    words.forEach(word => {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    });
  });

  return Object.entries(wordCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);
}

/**
 * Generate question variations
 */
export function generateQuestionVariations(baseQuestion: string): string[] {
  const variations: string[] = [];
  
  // Simple transformations
  const questionStarters = [
    'Bagaimana perasaanmu tentang',
    'Apa yang kamu pikirkan mengenai',
    'Ceritakan pengalamanmu tentang',
    'Refleksikan tentang',
  ];

  // Extract the core topic from base question
  const coreTopic = baseQuestion.replace(/^(apa|bagaimana|mengapa|kapan|dimana|siapa)\s+/i, '').replace(/\?$/, '');

  questionStarters.forEach(starter => {
    variations.push(`${starter} ${coreTopic}?`);
  });

  return variations.filter(v => v !== baseQuestion);
}
