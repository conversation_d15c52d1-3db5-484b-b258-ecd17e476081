/**
 * Secure Error Handling Utilities
 * Prevents sensitive information leakage in error messages
 * Enhanced with Sentry integration for comprehensive error monitoring
 */

import { reportError, reportMessage, addBreadcrumb } from '@/lib/sentryConfig';

export interface SecureError {
  message: string;
  code?: string;
  statusCode: number;
  timestamp: string;
  requestId?: string;
}

export interface ErrorContext {
  userId?: string;
  action?: string;
  component?: string;
  metadata?: Record<string, any>;
}

// Generic error messages that don't expose internal details
const GENERIC_ERROR_MESSAGES = {
  AUTHENTICATION: 'Terjadi masalah dengan autentikasi. Silakan coba masuk kembali.',
  AUTHORIZATION: 'Anda tidak memiliki izin untuk melakukan tindakan ini.',
  VALIDATION: 'Data yang diberikan tidak valid. Silakan periksa kembali.',
  NETWORK: 'Terjadi masalah koneksi. Silakan coba lagi dalam beberapa saat.',
  SERVER: 'Te<PERSON><PERSON><PERSON> kesalahan server. <PERSON> kami akan segera memperbaikinya.',
  NOT_FOUND: 'Data yang diminta tidak ditemukan.',
  RATE_LIMIT: 'Terlalu banyak permintaan. Silakan tunggu sebentar sebelum mencoba lagi.',
  UNKNOWN: 'Terjadi kesalahan yang tidak terduga. Silakan coba lagi.',
};

// Sensitive patterns that should never be exposed
const SENSITIVE_PATTERNS = [
  /api[_-]?key/i,
  /secret/i,
  /password/i,
  /token/i,
  /credential/i,
  /auth/i,
  /supabase/i,
  /database/i,
  /connection/i,
  /internal/i,
];

/**
 * Sanitize error message to remove sensitive information
 */
export function sanitizeErrorMessage(error: any): string {
  if (!error) return GENERIC_ERROR_MESSAGES.UNKNOWN;
  
  let message = typeof error === 'string' ? error : error.message || error.toString();
  
  // Check for sensitive patterns
  const hasSensitiveInfo = SENSITIVE_PATTERNS.some(pattern => pattern.test(message));
  
  if (hasSensitiveInfo) {
    return GENERIC_ERROR_MESSAGES.UNKNOWN;
  }
  
  // Map common error types to user-friendly messages
  if (message.includes('network') || message.includes('fetch')) {
    return GENERIC_ERROR_MESSAGES.NETWORK;
  }
  
  if (message.includes('unauthorized') || message.includes('401')) {
    return GENERIC_ERROR_MESSAGES.AUTHENTICATION;
  }
  
  if (message.includes('forbidden') || message.includes('403')) {
    return GENERIC_ERROR_MESSAGES.AUTHORIZATION;
  }
  
  if (message.includes('not found') || message.includes('404')) {
    return GENERIC_ERROR_MESSAGES.NOT_FOUND;
  }
  
  if (message.includes('rate limit') || message.includes('429')) {
    return GENERIC_ERROR_MESSAGES.RATE_LIMIT;
  }
  
  if (message.includes('validation') || message.includes('invalid')) {
    return GENERIC_ERROR_MESSAGES.VALIDATION;
  }
  
  // For development, show more details
  if (process.env.NODE_ENV === 'development') {
    return message;
  }
  
  return GENERIC_ERROR_MESSAGES.UNKNOWN;
}

/**
 * Create a secure error object for client consumption
 */
export function createSecureError(
  error: any,
  context?: ErrorContext,
  statusCode: number = 500
): SecureError {
  const sanitizedMessage = sanitizeErrorMessage(error);
  
  return {
    message: sanitizedMessage,
    code: getErrorCode(error),
    statusCode,
    timestamp: new Date().toISOString(),
    requestId: generateRequestId(),
  };
}

/**
 * Log error securely (only in development or with sanitized info)
 * Enhanced with Sentry integration for comprehensive error monitoring
 */
export function logSecureError(
  error: any,
  context?: ErrorContext,
  level: 'error' | 'warn' | 'info' = 'error'
): void {
  const logData: any = {
    timestamp: new Date().toISOString(),
    level,
    context: context ? sanitizeContext(context) : undefined,
  };

  if (process.env.NODE_ENV === 'development') {
    // In development, log full error details
    logData.error = {
      message: error?.message || error,
      stack: error?.stack,
      name: error?.name,
    };
  } else {
    // In production, only log sanitized information
    logData.error = {
      message: sanitizeErrorMessage(error),
      code: getErrorCode(error),
    };
  }

  console[level]('[SECURE_ERROR]', logData);

  // Send to Sentry with appropriate level and context
  if (error instanceof Error) {
    // Add breadcrumb for error context
    if (context) {
      addBreadcrumb(
        `Error in ${context.component || 'unknown'}: ${context.action || 'unknown action'}`,
        'error',
        sanitizeContext(context)
      );
    }

    // Report error to Sentry
    if (level === 'error') {
      reportError(error, context);
    } else {
      reportMessage(
        sanitizeErrorMessage(error),
        level === 'warn' ? 'warning' : 'info',
        context
      );
    }
  } else {
    // Handle non-Error objects
    reportMessage(
      sanitizeErrorMessage(error),
      level === 'warn' ? 'warning' : level === 'error' ? 'error' : 'info',
      context
    );
  }
}

/**
 * Get error code from error object
 */
function getErrorCode(error: any): string | undefined {
  if (!error) return undefined;
  
  // Common error code patterns
  if (error.code) return error.code;
  if (error.status) return error.status.toString();
  if (error.statusCode) return error.statusCode.toString();
  
  return undefined;
}

/**
 * Generate unique request ID for error tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sanitize context to remove sensitive information
 */
function sanitizeContext(context: ErrorContext): Partial<ErrorContext> {
  const sanitized: Partial<ErrorContext> = {
    action: context.action,
    component: context.component,
  };
  
  // Only include user ID if it's not sensitive
  if (context.userId && !SENSITIVE_PATTERNS.some(p => p.test(context.userId!))) {
    sanitized.userId = context.userId;
  }
  
  // Sanitize metadata
  if (context.metadata) {
    sanitized.metadata = {};
    Object.entries(context.metadata).forEach(([key, value]) => {
      const isSensitive = SENSITIVE_PATTERNS.some(pattern => pattern.test(key));
      if (!isSensitive && typeof value !== 'object') {
        sanitized.metadata![key] = value;
      }
    });
  }
  
  return sanitized;
}

/**
 * Handle API errors securely
 */
export function handleApiError(error: any, context?: ErrorContext): SecureError {
  logSecureError(error, context);
  return createSecureError(error, context, error?.status || error?.statusCode || 500);
}

/**
 * Handle authentication errors securely
 */
export function handleAuthError(error: any, context?: ErrorContext): SecureError {
  logSecureError(error, { ...context, action: 'authentication' });
  return createSecureError(error, context, 401);
}

/**
 * Handle validation errors securely
 */
export function handleValidationError(error: any, context?: ErrorContext): SecureError {
  logSecureError(error, { ...context, action: 'validation' }, 'warn');
  return createSecureError(error, context, 400);
}
