/**
 * Security Headers Configuration
 * Implements comprehensive security headers for web security
 */

export interface SecurityHeadersConfig {
  csp?: {
    enabled: boolean;
    directives: Record<string, string[]>;
    reportOnly?: boolean;
  };
  hsts?: {
    enabled: boolean;
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  frameOptions?: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  contentTypeOptions?: boolean;
  xssProtection?: boolean;
  referrerPolicy?: string;
  permissionsPolicy?: Record<string, string[]>;
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: SecurityHeadersConfig = {
  csp: {
    enabled: true,
    directives: {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for React Native Web
        "'unsafe-eval'", // Required for development
        'https://cdn.jsdelivr.net',
        'https://unpkg.com',
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for React Native Web
        'https://fonts.googleapis.com',
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
        'data:',
      ],
      'img-src': [
        "'self'",
        'data:',
        'blob:',
        'https:',
      ],
      'connect-src': [
        "'self'",
        'https://smzennhnotmrjdfvwmaa.supabase.co',
        'https://api.groq.com',
        'https://api.elevenlabs.io',
        'wss://smzennhnotmrjdfvwmaa.supabase.co',
      ],
      'media-src': [
        "'self'",
        'blob:',
        'data:',
      ],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': [],
    },
    reportOnly: false,
  },
  hsts: {
    enabled: true,
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  frameOptions: 'DENY',
  contentTypeOptions: true,
  xssProtection: true,
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: {
    'camera': ["'none'"],
    'microphone': ["'self'"],
    'geolocation': ["'none'"],
    'payment': ["'none'"],
    'usb': ["'none'"],
    'magnetometer': ["'none'"],
    'gyroscope': ["'none'"],
    'accelerometer': ["'none'"],
  },
};

/**
 * Generate Content Security Policy header value
 */
export function generateCSPHeader(config: SecurityHeadersConfig['csp']): string {
  if (!config?.enabled || !config.directives) {
    return '';
  }

  const directives = Object.entries(config.directives)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');

  return directives;
}

/**
 * Generate HSTS header value
 */
export function generateHSTSHeader(config: SecurityHeadersConfig['hsts']): string {
  if (!config?.enabled) {
    return '';
  }

  let header = `max-age=${config.maxAge}`;
  
  if (config.includeSubDomains) {
    header += '; includeSubDomains';
  }
  
  if (config.preload) {
    header += '; preload';
  }
  
  return header;
}

/**
 * Generate Permissions Policy header value
 */
export function generatePermissionsPolicyHeader(
  config: SecurityHeadersConfig['permissionsPolicy']
): string {
  if (!config) {
    return '';
  }

  return Object.entries(config)
    .map(([directive, allowlist]) => {
      if (allowlist.length === 0) {
        return `${directive}=()`;
      }
      return `${directive}=(${allowlist.join(' ')})`;
    })
    .join(', ');
}

/**
 * Get all security headers as an object
 */
export function getSecurityHeaders(
  config: SecurityHeadersConfig = DEFAULT_SECURITY_CONFIG,
  isHTTPS: boolean = false
): Record<string, string> {
  const headers: Record<string, string> = {};

  // Content Security Policy
  if (config.csp?.enabled) {
    const cspValue = generateCSPHeader(config.csp);
    if (cspValue) {
      const headerName = config.csp.reportOnly 
        ? 'Content-Security-Policy-Report-Only'
        : 'Content-Security-Policy';
      headers[headerName] = cspValue;
    }
  }

  // HTTP Strict Transport Security (only for HTTPS)
  if (isHTTPS && config.hsts?.enabled) {
    const hstsValue = generateHSTSHeader(config.hsts);
    if (hstsValue) {
      headers['Strict-Transport-Security'] = hstsValue;
    }
  }

  // X-Frame-Options
  if (config.frameOptions) {
    headers['X-Frame-Options'] = config.frameOptions;
  }

  // X-Content-Type-Options
  if (config.contentTypeOptions) {
    headers['X-Content-Type-Options'] = 'nosniff';
  }

  // X-XSS-Protection
  if (config.xssProtection) {
    headers['X-XSS-Protection'] = '1; mode=block';
  }

  // Referrer Policy
  if (config.referrerPolicy) {
    headers['Referrer-Policy'] = config.referrerPolicy;
  }

  // Permissions Policy
  if (config.permissionsPolicy) {
    const permissionsValue = generatePermissionsPolicyHeader(config.permissionsPolicy);
    if (permissionsValue) {
      headers['Permissions-Policy'] = permissionsValue;
    }
  }

  // Additional security headers
  headers['X-Permitted-Cross-Domain-Policies'] = 'none';
  headers['Cross-Origin-Embedder-Policy'] = 'unsafe-none'; // Required for React Native Web
  headers['Cross-Origin-Opener-Policy'] = 'same-origin-allow-popups'; // Required for OAuth
  headers['Cross-Origin-Resource-Policy'] = 'cross-origin';

  return headers;
}

/**
 * Apply security headers to Express response
 */
export function applySecurityHeaders(
  res: any,
  config: SecurityHeadersConfig = DEFAULT_SECURITY_CONFIG,
  isHTTPS: boolean = false
): void {
  const headers = getSecurityHeaders(config, isHTTPS);
  
  Object.entries(headers).forEach(([name, value]) => {
    res.setHeader(name, value);
  });
}

/**
 * Get security headers for Supabase Edge Functions
 */
export function getEdgeFunctionSecurityHeaders(
  origin?: string
): Record<string, string> {
  const allowedOrigins = [
    'http://localhost:8081',
    'http://localhost:3000',
    'https://app.temani.co',
    'https://temani.co'
  ];

  const isAllowedOrigin = origin && allowedOrigins.includes(origin);
  
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Cross-Origin-Resource-Policy': 'cross-origin',
    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token',
  };
}

/**
 * Validate CSP configuration
 */
export function validateCSPConfig(config: SecurityHeadersConfig['csp']): {
  isValid: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  
  if (!config?.enabled) {
    return { isValid: true, warnings };
  }

  // Check for unsafe directives
  Object.entries(config.directives).forEach(([directive, sources]) => {
    if (sources.includes("'unsafe-inline'")) {
      warnings.push(`${directive} allows 'unsafe-inline' which may be risky`);
    }
    
    if (sources.includes("'unsafe-eval'")) {
      warnings.push(`${directive} allows 'unsafe-eval' which may be risky`);
    }
    
    if (sources.includes('*')) {
      warnings.push(`${directive} allows all sources (*) which is not recommended`);
    }
  });

  return {
    isValid: true,
    warnings,
  };
}
