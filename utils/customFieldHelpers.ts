import type { JournalQuestion, JournalCustomField, FieldType } from '@/types/journal';

export interface SeparatedFields {
  systemFields: JournalQuestion[];
  customQuestions: JournalCustomField[];
  customTitles: JournalCustomField[];
  allCustomFields: JournalCustomField[];
}

export interface FieldStatistics {
  totalFields: number;
  systemFields: number;
  customQuestions: number;
  customTitles: number;
  aiGeneratedFields: number;
  averageFieldLength: number;
  mostUsedFieldType: FieldType;
}

/**
 * Sort fields by their order property
 */
export function sortFieldsByOrder(fields: JournalQuestion[]): JournalQuestion[] {
  return [...fields].sort((a, b) => {
    // Handle undefined or null orders
    const orderA = a.question_order ?? 999999;
    const orderB = b.question_order ?? 999999;
    
    if (orderA === orderB) {
      // If orders are the same, sort by creation date
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    }
    
    return orderA - orderB;
  });
}

/**
 * Separate fields by type for easier management
 */
export function separateFieldsByType(fields: JournalQuestion[]): SeparatedFields {
  const systemFields: JournalQuestion[] = [];
  const customQuestions: JournalCustomField[] = [];
  const customTitles: JournalCustomField[] = [];

  fields.forEach(field => {
    const fieldType = field.field_type || 'system';
    
    switch (fieldType) {
      case 'system':
        systemFields.push(field);
        break;
      case 'custom_question':
        if (field.field_type) {
          customQuestions.push(field as JournalCustomField);
        }
        break;
      case 'custom_title':
        if (field.field_type) {
          customTitles.push(field as JournalCustomField);
        }
        break;
    }
  });

  return {
    systemFields: sortFieldsByOrder(systemFields),
    customQuestions: sortFieldsByOrder(customQuestions),
    customTitles: sortFieldsByOrder(customTitles),
    allCustomFields: sortFieldsByOrder([...customQuestions, ...customTitles]),
  };
}

/**
 * Generate appropriate order for new field
 */
export function generateFieldOrder(existingFields: JournalQuestion[], fieldType: FieldType): number {
  if (existingFields.length === 0) {
    return 1;
  }

  // For system fields, maintain existing order
  if (fieldType === 'system') {
    const systemFields = existingFields.filter(f => (f.field_type || 'system') === 'system');
    const maxSystemOrder = Math.max(...systemFields.map(f => f.question_order || 0));
    return maxSystemOrder + 1;
  }

  // For custom fields, add after all existing fields
  const maxOrder = Math.max(...existingFields.map(f => f.question_order || 0));
  return maxOrder + 1;
}

/**
 * Validate field order consistency
 */
export function validateFieldOrder(fields: JournalQuestion[]): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];

  if (fields.length === 0) {
    return { isValid: true, issues, suggestions };
  }

  const orders = fields.map(f => f.question_order || 0);
  const uniqueOrders = new Set(orders);

  // Check for duplicate orders
  if (orders.length !== uniqueOrders.size) {
    issues.push('Beberapa field memiliki urutan yang sama');
    suggestions.push('Gunakan fungsi normalizeFieldOrders untuk memperbaiki urutan');
  }

  // Check for negative orders
  const hasNegativeOrder = orders.some(order => order < 0);
  if (hasNegativeOrder) {
    issues.push('Ada field dengan urutan negatif');
    suggestions.push('Urutan field harus dimulai dari 1');
  }

  // Check for large gaps
  const sortedOrders = [...orders].sort((a, b) => a - b);
  const hasLargeGaps = sortedOrders.some((order, index) => {
    if (index === 0) return false;
    return order - sortedOrders[index - 1] > 10;
  });

  if (hasLargeGaps) {
    suggestions.push('Ada celah besar dalam urutan field, pertimbangkan untuk menormalisasi');
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions,
  };
}

/**
 * Merge system and custom fields in proper order
 */
export function mergeSystemAndCustomFields(
  systemFields: JournalQuestion[], 
  customFields: JournalCustomField[]
): JournalQuestion[] {
  const allFields = [...systemFields, ...customFields];
  return sortFieldsByOrder(allFields);
}

/**
 * Normalize field orders to remove gaps and duplicates
 */
export function normalizeFieldOrders(fields: JournalQuestion[]): Array<{ id: string; order: number }> {
  const separated = separateFieldsByType(fields);
  const result: Array<{ id: string; order: number }> = [];
  let currentOrder = 1;

  // First, add system fields
  separated.systemFields.forEach(field => {
    result.push({ id: field.id, order: currentOrder++ });
  });

  // Then add custom fields in their current relative order
  separated.allCustomFields.forEach(field => {
    result.push({ id: field.id, order: currentOrder++ });
  });

  return result;
}

/**
 * Get field statistics for analytics
 */
export function getFieldStatistics(fields: JournalQuestion[]): FieldStatistics {
  const separated = separateFieldsByType(fields);
  
  const totalFields = fields.length;
  const systemFields = separated.systemFields.length;
  const customQuestions = separated.customQuestions.length;
  const customTitles = separated.customTitles.length;
  
  const aiGeneratedFields = fields.filter(f => f.ai_generated).length;
  
  const averageFieldLength = totalFields > 0
    ? fields.reduce((sum, field) => sum + field.question_text.length, 0) / totalFields
    : 0;

  // Determine most used field type
  let mostUsedFieldType: FieldType = 'system';
  if (customQuestions > systemFields && customQuestions > customTitles) {
    mostUsedFieldType = 'custom_question';
  } else if (customTitles > systemFields && customTitles > customQuestions) {
    mostUsedFieldType = 'custom_title';
  }

  return {
    totalFields,
    systemFields,
    customQuestions,
    customTitles,
    aiGeneratedFields,
    averageFieldLength: Math.round(averageFieldLength),
    mostUsedFieldType,
  };
}

/**
 * Find duplicate or similar fields
 */
export function findSimilarFields(fields: JournalQuestion[]): Array<{
  field1: JournalQuestion;
  field2: JournalQuestion;
  similarity: number;
  reason: string;
}> {
  const similarities: Array<{
    field1: JournalQuestion;
    field2: JournalQuestion;
    similarity: number;
    reason: string;
  }> = [];

  for (let i = 0; i < fields.length; i++) {
    for (let j = i + 1; j < fields.length; j++) {
      const field1 = fields[i];
      const field2 = fields[j];
      
      const similarity = calculateTextSimilarity(field1.question_text, field2.question_text);
      
      if (similarity > 0.7) {
        similarities.push({
          field1,
          field2,
          similarity,
          reason: similarity > 0.9 ? 'Hampir identik' : 'Sangat mirip',
        });
      }
    }
  }

  return similarities.sort((a, b) => b.similarity - a.similarity);
}

/**
 * Calculate text similarity between two strings
 */
function calculateTextSimilarity(text1: string, text2: string): number {
  const normalize = (text: string) => text.toLowerCase().replace(/[^\w\s]/g, '').trim();
  
  const normalized1 = normalize(text1);
  const normalized2 = normalize(text2);
  
  if (normalized1 === normalized2) return 1.0;
  
  const words1 = new Set(normalized1.split(/\s+/));
  const words2 = new Set(normalized2.split(/\s+/));
  
  const intersection = new Set([...words1].filter(word => words2.has(word)));
  const union = new Set([...words1, ...words2]);
  
  return intersection.size / union.size;
}

/**
 * Suggest field improvements
 */
export function suggestFieldImprovements(field: JournalQuestion): string[] {
  const suggestions: string[] = [];
  const text = field.question_text;
  const fieldType = field.field_type || 'system';

  // Length suggestions
  if (text.length < 10) {
    suggestions.push('Pertanyaan terlalu pendek, pertimbangkan untuk menambah detail');
  } else if (text.length > 150) {
    suggestions.push('Pertanyaan terlalu panjang, pertimbangkan untuk mempersingkat');
  }

  // Question mark for questions
  if (fieldType === 'custom_question' && !text.includes('?')) {
    suggestions.push('Tambahkan tanda tanya (?) di akhir pertanyaan');
  }

  // Title field suggestions
  if (fieldType === 'custom_title') {
    if (text.includes('?')) {
      suggestions.push('Field judul sebaiknya tidak menggunakan tanda tanya');
    }
    if (text.length > 50) {
      suggestions.push('Judul sebaiknya lebih singkat (maksimal 50 karakter)');
    }
  }

  // Yes/no question detection
  const isYesNoQuestion = /\b(apakah|sudah|belum|ya|tidak)\b/i.test(text);
  if (isYesNoQuestion && fieldType === 'custom_question') {
    suggestions.push('Hindari pertanyaan ya/tidak, gunakan pertanyaan terbuka untuk refleksi yang lebih dalam');
  }

  // Reflective word suggestions
  const hasReflectiveWords = /\b(perasaan|pikiran|refleksi|makna|belajar|pengalaman)\b/i.test(text);
  if (!hasReflectiveWords && fieldType === 'custom_question') {
    suggestions.push('Pertimbangkan menambahkan kata-kata reflektif seperti "perasaan", "pikiran", atau "pengalaman"');
  }

  return suggestions;
}

/**
 * Generate field preview for different contexts
 */
export function generateFieldPreview(field: JournalQuestion, context: 'list' | 'form' | 'summary'): string {
  const maxLength = context === 'list' ? 50 : context === 'form' ? 100 : 200;
  const text = field.question_text;
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Export fields to JSON format
 */
export function exportFieldsToJSON(fields: JournalQuestion[]): string {
  const exportData = {
    exportDate: new Date().toISOString(),
    totalFields: fields.length,
    fields: fields.map(field => ({
      id: field.id,
      question_text: field.question_text,
      field_type: field.field_type || 'system',
      question_order: field.question_order,
      custom_config: field.custom_config,
      ai_generated: field.ai_generated,
      created_at: field.created_at,
    })),
  };
  
  return JSON.stringify(exportData, null, 2);
}

/**
 * Import fields from JSON format
 */
export function importFieldsFromJSON(jsonString: string): {
  success: boolean;
  fields: Partial<JournalQuestion>[];
  errors: string[];
} {
  try {
    const data = JSON.parse(jsonString);
    
    if (!data.fields || !Array.isArray(data.fields)) {
      return {
        success: false,
        fields: [],
        errors: ['Format JSON tidak valid: field "fields" tidak ditemukan atau bukan array'],
      };
    }
    
    const errors: string[] = [];
    const validFields: Partial<JournalQuestion>[] = [];
    
    data.fields.forEach((field: any, index: number) => {
      if (!field.question_text) {
        errors.push(`Field ${index + 1}: question_text wajib diisi`);
        return;
      }
      
      if (field.field_type && !['system', 'custom_question', 'custom_title'].includes(field.field_type)) {
        errors.push(`Field ${index + 1}: field_type tidak valid`);
        return;
      }
      
      validFields.push({
        question_text: field.question_text,
        field_type: field.field_type || 'custom_question',
        custom_config: field.custom_config || {},
        question_order: field.question_order,
      });
    });
    
    return {
      success: errors.length === 0,
      fields: validFields,
      errors,
    };
  } catch (error) {
    return {
      success: false,
      fields: [],
      errors: ['Format JSON tidak valid'],
    };
  }
}
