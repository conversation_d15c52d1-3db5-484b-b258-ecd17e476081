/**
 * Enhanced Authentication Security
 * Session management, timeout, and security event handling
 */

import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { supabase } from '@/lib/supabase';

export interface SessionSecurityConfig {
  sessionTimeout: number; // in milliseconds
  maxConcurrentSessions: number;
  forceRefreshOnSuspiciousActivity: boolean;
  trackFailedAttempts: boolean;
  maxFailedAttempts: number;
  lockoutDuration: number; // in milliseconds
}

export interface SecurityEvent {
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'suspicious_activity';
  timestamp: number;
  userAgent?: string;
  ipAddress?: string;
  metadata?: Record<string, any>;
}

export interface SessionInfo {
  sessionId: string;
  userId: string;
  createdAt: number;
  lastActivity: number;
  userAgent?: string;
  isActive: boolean;
}

// Default security configuration
export const DEFAULT_AUTH_SECURITY_CONFIG: SessionSecurityConfig = {
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  maxConcurrentSessions: 3,
  forceRefreshOnSuspiciousActivity: true,
  trackFailedAttempts: true,
  maxFailedAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
};

const SESSION_KEY = 'auth_session_info';
const FAILED_ATTEMPTS_KEY = 'failed_login_attempts';
const SECURITY_EVENTS_KEY = 'security_events';

/**
 * Session timeout manager
 */
export class SessionTimeoutManager {
  private timeoutId: NodeJS.Timeout | null = null;
  private config: SessionSecurityConfig;
  private onTimeout: () => void;

  constructor(config: SessionSecurityConfig, onTimeout: () => void) {
    this.config = config;
    this.onTimeout = onTimeout;
  }

  start(): void {
    this.reset();
  }

  reset(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.timeoutId = setTimeout(() => {
      this.onTimeout();
    }, this.config.sessionTimeout);
  }

  stop(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  extend(): void {
    this.reset();
  }
}

/**
 * Track and validate session information
 */
export async function createSessionInfo(userId: string): Promise<SessionInfo> {
  const sessionInfo: SessionInfo = {
    sessionId: generateSessionId(),
    userId,
    createdAt: Date.now(),
    lastActivity: Date.now(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    isActive: true,
  };

  await storeSessionInfo(sessionInfo);
  return sessionInfo;
}

/**
 * Update session activity
 */
export async function updateSessionActivity(sessionId: string): Promise<void> {
  try {
    const sessionInfo = await getSessionInfo();
    if (sessionInfo && sessionInfo.sessionId === sessionId) {
      sessionInfo.lastActivity = Date.now();
      await storeSessionInfo(sessionInfo);
    }
  } catch (error) {
    console.error('Failed to update session activity:', error);
  }
}

/**
 * Check if session is valid and not expired
 */
export async function validateSession(
  config: SessionSecurityConfig = DEFAULT_AUTH_SECURITY_CONFIG
): Promise<{ isValid: boolean; reason?: string }> {
  try {
    const sessionInfo = await getSessionInfo();
    
    if (!sessionInfo) {
      return { isValid: false, reason: 'No session found' };
    }

    const now = Date.now();
    const timeSinceLastActivity = now - sessionInfo.lastActivity;

    if (timeSinceLastActivity > config.sessionTimeout) {
      await clearSessionInfo();
      return { isValid: false, reason: 'Session expired' };
    }

    if (!sessionInfo.isActive) {
      return { isValid: false, reason: 'Session deactivated' };
    }

    return { isValid: true };
  } catch (error) {
    console.error('Session validation error:', error);
    return { isValid: false, reason: 'Validation error' };
  }
}

/**
 * Force session refresh on security events
 */
export async function handleSecurityEvent(
  event: SecurityEvent,
  config: SessionSecurityConfig = DEFAULT_AUTH_SECURITY_CONFIG
): Promise<void> {
  try {
    // Log security event
    await logSecurityEvent(event);

    // Handle specific event types
    switch (event.type) {
      case 'failed_login':
        await handleFailedLogin(event, config);
        break;
      
      case 'password_change':
        if (config.forceRefreshOnSuspiciousActivity) {
          await forceSessionRefresh();
        }
        break;
      
      case 'suspicious_activity':
        if (config.forceRefreshOnSuspiciousActivity) {
          await forceSessionRefresh();
          await logSecurityEvent({
            type: 'suspicious_activity',
            timestamp: Date.now(),
            metadata: { action: 'forced_session_refresh' }
          });
        }
        break;
    }
  } catch (error) {
    console.error('Error handling security event:', error);
  }
}

/**
 * Handle failed login attempts
 */
async function handleFailedLogin(
  event: SecurityEvent,
  config: SessionSecurityConfig
): Promise<void> {
  if (!config.trackFailedAttempts) return;

  try {
    const attempts = await getFailedAttempts();
    const newAttempts = [...attempts, event.timestamp];
    
    // Keep only recent attempts (within lockout duration)
    const recentAttempts = newAttempts.filter(
      timestamp => Date.now() - timestamp < config.lockoutDuration
    );

    await storeFailedAttempts(recentAttempts);

    if (recentAttempts.length >= config.maxFailedAttempts) {
      await logSecurityEvent({
        type: 'suspicious_activity',
        timestamp: Date.now(),
        metadata: { 
          reason: 'max_failed_attempts_reached',
          attempts: recentAttempts.length 
        }
      });
    }
  } catch (error) {
    console.error('Error handling failed login:', error);
  }
}

/**
 * Check if account is locked due to failed attempts
 */
export async function isAccountLocked(
  config: SessionSecurityConfig = DEFAULT_AUTH_SECURITY_CONFIG
): Promise<{ isLocked: boolean; unlockTime?: number }> {
  if (!config.trackFailedAttempts) {
    return { isLocked: false };
  }

  try {
    const attempts = await getFailedAttempts();
    const recentAttempts = attempts.filter(
      timestamp => Date.now() - timestamp < config.lockoutDuration
    );

    if (recentAttempts.length >= config.maxFailedAttempts) {
      const oldestAttempt = Math.min(...recentAttempts);
      const unlockTime = oldestAttempt + config.lockoutDuration;
      
      return {
        isLocked: Date.now() < unlockTime,
        unlockTime
      };
    }

    return { isLocked: false };
  } catch (error) {
    console.error('Error checking account lock status:', error);
    return { isLocked: false };
  }
}

/**
 * Force session refresh
 */
async function forceSessionRefresh(): Promise<void> {
  try {
    await supabase.auth.refreshSession();
    await clearSessionInfo();
  } catch (error) {
    console.error('Failed to force session refresh:', error);
  }
}

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2);
  return `sess_${timestamp}_${randomPart}`;
}

/**
 * Storage helpers
 */
async function storeSessionInfo(sessionInfo: SessionInfo): Promise<void> {
  const data = JSON.stringify(sessionInfo);
  
  if (Platform.OS === 'web') {
    localStorage.setItem(SESSION_KEY, data);
  } else {
    await SecureStore.setItemAsync(SESSION_KEY, data);
  }
}

async function getSessionInfo(): Promise<SessionInfo | null> {
  try {
    let data: string | null;
    
    if (Platform.OS === 'web') {
      data = localStorage.getItem(SESSION_KEY);
    } else {
      data = await SecureStore.getItemAsync(SESSION_KEY);
    }
    
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error getting session info:', error);
    return null;
  }
}

async function clearSessionInfo(): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      localStorage.removeItem(SESSION_KEY);
    } else {
      await SecureStore.deleteItemAsync(SESSION_KEY);
    }
  } catch (error) {
    console.error('Error clearing session info:', error);
  }
}

async function storeFailedAttempts(attempts: number[]): Promise<void> {
  const data = JSON.stringify(attempts);
  
  if (Platform.OS === 'web') {
    localStorage.setItem(FAILED_ATTEMPTS_KEY, data);
  } else {
    await SecureStore.setItemAsync(FAILED_ATTEMPTS_KEY, data);
  }
}

async function getFailedAttempts(): Promise<number[]> {
  try {
    let data: string | null;
    
    if (Platform.OS === 'web') {
      data = localStorage.getItem(FAILED_ATTEMPTS_KEY);
    } else {
      data = await SecureStore.getItemAsync(FAILED_ATTEMPTS_KEY);
    }
    
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error getting failed attempts:', error);
    return [];
  }
}

async function logSecurityEvent(event: SecurityEvent): Promise<void> {
  try {
    const events = await getSecurityEvents();
    const newEvents = [...events, event].slice(-100); // Keep last 100 events
    
    const data = JSON.stringify(newEvents);
    
    if (Platform.OS === 'web') {
      localStorage.setItem(SECURITY_EVENTS_KEY, data);
    } else {
      await SecureStore.setItemAsync(SECURITY_EVENTS_KEY, data);
    }
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}

async function getSecurityEvents(): Promise<SecurityEvent[]> {
  try {
    let data: string | null;
    
    if (Platform.OS === 'web') {
      data = localStorage.getItem(SECURITY_EVENTS_KEY);
    } else {
      data = await SecureStore.getItemAsync(SECURITY_EVENTS_KEY);
    }
    
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error getting security events:', error);
    return [];
  }
}
