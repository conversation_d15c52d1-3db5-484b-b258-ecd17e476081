/**
 * CSRF Protection Utilities
 * Implements double-submit cookie pattern for CSRF protection
 */

import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';

const CSRF_TOKEN_KEY = 'csrf_token';
const CSRF_HEADER_NAME = 'X-CSRF-Token';
const CSRF_COOKIE_NAME = 'csrf_token';

/**
 * Generate a cryptographically secure CSRF token
 */
export function generateCSRFToken(): string {
  // Generate 32 bytes of random data
  const array = new Uint8Array(32);
  
  if (Platform.OS === 'web' && typeof window !== 'undefined' && window.crypto) {
    window.crypto.getRandomValues(array);
  } else {
    // Fallback for React Native
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
  }
  
  // Convert to base64url
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Store CSRF token securely
 */
export async function storeCSRFToken(token: string): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      // Store in httpOnly cookie (simulated with localStorage for demo)
      localStorage.setItem(CSRF_TOKEN_KEY, token);
      
      // Set cookie with secure attributes
      if (typeof document !== 'undefined') {
        const isSecure = window.location.protocol === 'https:';
        document.cookie = `${CSRF_COOKIE_NAME}=${token}; Path=/; SameSite=Strict${isSecure ? '; Secure' : ''}`;
      }
    } else {
      // Store in secure storage for React Native
      await SecureStore.setItemAsync(CSRF_TOKEN_KEY, token);
    }
  } catch (error) {
    console.error('Failed to store CSRF token:', error);
    throw new Error('CSRF token storage failed');
  }
}

/**
 * Retrieve stored CSRF token
 */
export async function getCSRFToken(): Promise<string | null> {
  try {
    if (Platform.OS === 'web') {
      return localStorage.getItem(CSRF_TOKEN_KEY);
    } else {
      return await SecureStore.getItemAsync(CSRF_TOKEN_KEY);
    }
  } catch (error) {
    console.error('Failed to retrieve CSRF token:', error);
    return null;
  }
}

/**
 * Remove CSRF token
 */
export async function removeCSRFToken(): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      localStorage.removeItem(CSRF_TOKEN_KEY);
      
      // Clear cookie
      if (typeof document !== 'undefined') {
        document.cookie = `${CSRF_COOKIE_NAME}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      }
    } else {
      await SecureStore.deleteItemAsync(CSRF_TOKEN_KEY);
    }
  } catch (error) {
    console.error('Failed to remove CSRF token:', error);
  }
}

/**
 * Initialize CSRF protection for a session
 */
export async function initializeCSRFProtection(): Promise<string> {
  const token = generateCSRFToken();
  await storeCSRFToken(token);
  return token;
}

/**
 * Get CSRF token for request headers
 */
export async function getCSRFHeaders(): Promise<Record<string, string>> {
  const token = await getCSRFToken();
  
  if (!token) {
    throw new Error('CSRF token not found. Please refresh the page.');
  }
  
  return {
    [CSRF_HEADER_NAME]: token,
  };
}

/**
 * Validate CSRF token (for server-side validation)
 */
export function validateCSRFToken(headerToken: string, cookieToken: string): boolean {
  if (!headerToken || !cookieToken) {
    return false;
  }
  
  // Constant-time comparison to prevent timing attacks
  if (headerToken.length !== cookieToken.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < headerToken.length; i++) {
    result |= headerToken.charCodeAt(i) ^ cookieToken.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * CSRF-protected fetch wrapper
 */
export async function csrfFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Skip CSRF for GET requests (they should be idempotent)
  const method = options.method?.toUpperCase() || 'GET';
  if (method === 'GET' || method === 'HEAD' || method === 'OPTIONS') {
    return fetch(url, options);
  }
  
  try {
    const csrfHeaders = await getCSRFHeaders();
    
    const headers = {
      ...options.headers,
      ...csrfHeaders,
    };
    
    return fetch(url, {
      ...options,
      headers,
    });
  } catch (error) {
    console.error('CSRF fetch failed:', error);
    throw error;
  }
}

/**
 * React hook for CSRF protection
 */
export function useCSRFProtection() {
  const [csrfToken, setCSRFToken] = React.useState<string | null>(null);
  const [isInitialized, setIsInitialized] = React.useState(false);
  
  React.useEffect(() => {
    const initCSRF = async () => {
      try {
        let token = await getCSRFToken();
        
        if (!token) {
          token = await initializeCSRFProtection();
        }
        
        setCSRFToken(token);
        setIsInitialized(true);
      } catch (error) {
        console.error('CSRF initialization failed:', error);
        setIsInitialized(true); // Still mark as initialized to prevent infinite loading
      }
    };
    
    initCSRF();
  }, []);
  
  const refreshToken = async () => {
    try {
      const newToken = await initializeCSRFProtection();
      setCSRFToken(newToken);
      return newToken;
    } catch (error) {
      console.error('CSRF token refresh failed:', error);
      throw error;
    }
  };
  
  return {
    csrfToken,
    isInitialized,
    refreshToken,
    csrfFetch,
  };
}

// Re-export React for the hook
import React from 'react';
