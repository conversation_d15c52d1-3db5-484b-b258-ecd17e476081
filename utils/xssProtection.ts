/**
 * XSS Protection Utilities
 * Comprehensive XSS prevention for all user inputs
 */

import { Platform } from 'react-native';

// Import DOMPurify for web platforms
let DOMPurify: any = null;
if (Platform.OS === 'web') {
  try {
    DOMPurify = require('dompurify');
  } catch (error) {
    console.warn('DOMPurify not available:', error);
  }
}

export interface SanitizationConfig {
  allowedTags?: readonly string[];
  allowedAttributes?: Record<string, string[]>;
  stripTags?: boolean;
  maxLength?: number;
  preserveLineBreaks?: boolean;
}

// Default configuration for different content types
export const SANITIZATION_CONFIGS = {
  // For chat messages - very restrictive
  CHAT_MESSAGE: {
    allowedTags: [],
    stripTags: true,
    maxLength: 2000,
    preserveLineBreaks: true,
  },
  
  // For journal entries - allow basic formatting
  JOURNAL_ENTRY: {
    allowedTags: ['p', 'br', 'strong', 'em', 'u'],
    allowedAttributes: {},
    stripTags: false,
    maxLength: 10000,
    preserveLineBreaks: true,
  },
  
  // For user profile data - very restrictive
  PROFILE_DATA: {
    allowedTags: [],
    stripTags: true,
    maxLength: 500,
    preserveLineBreaks: false,
  },
  
  // For AI-generated content - moderate restrictions
  AI_CONTENT: {
    allowedTags: ['p', 'br', 'strong', 'em'],
    allowedAttributes: {},
    stripTags: false,
    maxLength: 5000,
    preserveLineBreaks: true,
  },
} as const;

/**
 * Sanitize HTML content using DOMPurify (web) or manual sanitization (mobile)
 */
export function sanitizeHTML(
  input: string,
  config: SanitizationConfig = SANITIZATION_CONFIGS.CHAT_MESSAGE
): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  let sanitized = input;

  // Trim and limit length
  sanitized = sanitized.trim();
  if (config.maxLength && sanitized.length > config.maxLength) {
    sanitized = sanitized.substring(0, config.maxLength);
  }

  if (Platform.OS === 'web' && DOMPurify) {
    // Use DOMPurify for web platforms
    const purifyConfig: any = {
      ALLOWED_TAGS: config.allowedTags || [],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
    };

    if (config.allowedAttributes) {
      purifyConfig.ALLOWED_ATTR = Object.keys(config.allowedAttributes);
    }

    sanitized = DOMPurify.sanitize(sanitized, purifyConfig);
  } else {
    // Manual sanitization for React Native
    sanitized = manualSanitize(sanitized, config);
  }

  // Preserve line breaks if requested
  if (config.preserveLineBreaks) {
    sanitized = sanitized.replace(/\n/g, '<br>');
  }

  return sanitized;
}

/**
 * Manual sanitization for React Native platforms
 */
function manualSanitize(input: string, config: SanitizationConfig): string {
  let sanitized = input;

  // Remove all HTML tags if stripTags is true
  if (config.stripTags) {
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  } else if (config.allowedTags) {
    // Remove disallowed HTML tags
    const allowedTagsRegex = new RegExp(
      `<(?!/?(?:${config.allowedTags.join('|')})(?:\\s|>))[^>]*>`,
      'gi'
    );
    sanitized = sanitized.replace(allowedTagsRegex, '');
  }

  // Remove potentially dangerous patterns
  sanitized = sanitized
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/data:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/<script[^>]*>.*?<\/script>/gis, '')
    .replace(/<iframe[^>]*>.*?<\/iframe>/gis, '')
    .replace(/<object[^>]*>.*?<\/object>/gis, '')
    .replace(/<embed[^>]*>/gi, '')
    .replace(/<link[^>]*>/gi, '')
    .replace(/<meta[^>]*>/gi, '')
    .replace(/<style[^>]*>.*?<\/style>/gis, '');

  // Remove null bytes and control characters
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  return sanitized;
}

/**
 * Sanitize text input (removes all HTML)
 */
export function sanitizeText(input: string, maxLength?: number): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  let sanitized = input
    .trim()
    .replace(/<[^>]*>/g, '') // Remove all HTML tags
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
    .replace(/\s+/g, ' '); // Normalize whitespace

  if (maxLength && sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }

  return sanitized;
}

/**
 * Sanitize URL to prevent XSS through href attributes
 */
export function sanitizeURL(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }

  const trimmedUrl = url.trim().toLowerCase();

  // Allow only safe protocols
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
  const hasAllowedProtocol = allowedProtocols.some(protocol => 
    trimmedUrl.startsWith(protocol)
  );

  // Block dangerous protocols
  const dangerousProtocols = ['javascript:', 'vbscript:', 'data:', 'file:'];
  const hasDangerousProtocol = dangerousProtocols.some(protocol =>
    trimmedUrl.startsWith(protocol)
  );

  if (hasDangerousProtocol || (!hasAllowedProtocol && trimmedUrl.includes(':'))) {
    return '';
  }

  return url.trim();
}

/**
 * Sanitize JSON data recursively
 */
export function sanitizeJSON(data: any, config: SanitizationConfig = SANITIZATION_CONFIGS.PROFILE_DATA): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'string') {
    return sanitizeText(data, config.maxLength);
  }

  if (typeof data === 'number' || typeof data === 'boolean') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeJSON(item, config));
  }

  if (typeof data === 'object') {
    const sanitizedObject: any = {};
    Object.keys(data).forEach(key => {
      const sanitizedKey = sanitizeText(key, 100); // Limit key length
      if (sanitizedKey) {
        sanitizedObject[sanitizedKey] = sanitizeJSON(data[key], config);
      }
    });
    return sanitizedObject;
  }

  return data;
}

/**
 * Validate and sanitize file upload names
 */
export function sanitizeFileName(fileName: string): string {
  if (!fileName || typeof fileName !== 'string') {
    return '';
  }

  return fileName
    .trim()
    .replace(/[^a-zA-Z0-9._-]/g, '') // Allow only safe characters
    .replace(/\.{2,}/g, '.') // Prevent directory traversal
    .replace(/^\.+|\.+$/g, '') // Remove leading/trailing dots
    .substring(0, 255); // Limit length
}

/**
 * Sanitize AI-generated content
 */
export function sanitizeAIContent(content: string): string {
  return sanitizeHTML(content, SANITIZATION_CONFIGS.AI_CONTENT);
}

/**
 * Sanitize chat message
 */
export function sanitizeChatMessage(message: string): string {
  return sanitizeHTML(message, SANITIZATION_CONFIGS.CHAT_MESSAGE);
}

/**
 * Sanitize journal entry
 */
export function sanitizeJournalEntry(entry: string): string {
  return sanitizeHTML(entry, SANITIZATION_CONFIGS.JOURNAL_ENTRY);
}

/**
 * Sanitize user profile data
 */
export function sanitizeProfileData(data: any): any {
  return sanitizeJSON(data, SANITIZATION_CONFIGS.PROFILE_DATA);
}

/**
 * Create a sanitization middleware for React components
 */
export function createSanitizationHook(config: SanitizationConfig) {
  return function useSanitizedInput(input: string): string {
    return sanitizeHTML(input, config);
  };
}

/**
 * Validate that content is safe after sanitization
 */
export function validateSanitizedContent(original: string, sanitized: string): {
  isSafe: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  
  if (original !== sanitized) {
    warnings.push('Content was modified during sanitization');
  }
  
  // Check for remaining suspicious patterns
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
    /on\w+\s*=/i,
  ];
  
  const hasSuspiciousContent = suspiciousPatterns.some(pattern => 
    pattern.test(sanitized)
  );
  
  if (hasSuspiciousContent) {
    warnings.push('Potentially unsafe content detected after sanitization');
  }
  
  return {
    isSafe: !hasSuspiciousContent,
    warnings,
  };
}
