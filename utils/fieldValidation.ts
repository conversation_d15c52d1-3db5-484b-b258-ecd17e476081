import type { CustomFieldInput, CustomFieldConfig, FieldType } from '@/types/journal';
import { JOURNAL_CONSTANTS } from '@/types/journal';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface FieldLimitStatus {
  currentCount: number;
  maxAllowed: number;
  canCreate: boolean;
  remaining: number;
}

/**
 * Validate custom field input data
 */
export function validateCustomFieldInput(input: CustomFieldInput): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate question text
  if (!input.question_text || input.question_text.trim().length === 0) {
    errors.push('Teks pertanyaan wajib diisi');
  } else {
    const trimmedText = input.question_text.trim();
    
    if (trimmedText.length < 3) {
      errors.push('Teks pertanyaan minimal 3 karakter');
    }
    
    if (trimmedText.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH) {
      errors.push(`Teks pertanyaan maksimal ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH} karakter`);
    }
    
    // Check for question mark for question fields
    if (input.field_type === 'custom_question' && !trimmedText.includes('?')) {
      warnings.push('Pertanyaan biasanya diakhiri dengan tanda tanya (?)');
    }
    
    // Check for appropriate length for title fields
    if (input.field_type === 'custom_title' && trimmedText.length > 50) {
      warnings.push('Judul sebaiknya tidak terlalu panjang (maksimal 50 karakter)');
    }
  }

  // Validate field type
  if (!['custom_question', 'custom_title'].includes(input.field_type)) {
    errors.push('Tipe field tidak valid');
  }

  // Validate custom configuration
  if (input.custom_config) {
    const configValidation = validateFieldConfig(input.custom_config, input.field_type);
    errors.push(...configValidation.errors);
    warnings.push(...configValidation.warnings);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate field configuration
 */
export function validateFieldConfig(config: CustomFieldConfig, fieldType: FieldType): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate placeholder
  if (config.placeholder) {
    if (config.placeholder.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH) {
      errors.push(`Placeholder maksimal ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH} karakter`);
    }
    
    if (fieldType === 'custom_title' && config.placeholder) {
      warnings.push('Placeholder tidak diperlukan untuk field judul');
    }
  }

  // Validate max length
  if (config.maxLength !== undefined) {
    if (config.maxLength < 1) {
      errors.push('Panjang maksimal harus lebih dari 0');
    }
    
    if (config.maxLength > JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH) {
      errors.push(`Panjang maksimal tidak boleh melebihi ${JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} karakter`);
    }
    
    if (fieldType === 'custom_title' && config.maxLength > 100) {
      errors.push('Panjang maksimal untuk judul tidak boleh melebihi 100 karakter');
    }
    
    if (fieldType === 'custom_question' && config.maxLength < 10) {
      warnings.push('Panjang maksimal untuk pertanyaan sebaiknya minimal 10 karakter');
    }
  }

  // Validate required field setting
  if (config.isRequired && fieldType === 'custom_title') {
    warnings.push('Field judul jarang dibuat wajib diisi');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate field text content
 */
export function validateFieldText(text: string, maxLength?: number): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!text || text.trim().length === 0) {
    errors.push('Teks tidak boleh kosong');
    return { isValid: false, errors, warnings };
  }

  const trimmedText = text.trim();
  const actualMaxLength = maxLength || JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH;

  if (trimmedText.length > actualMaxLength) {
    errors.push(`Teks terlalu panjang (maksimal ${actualMaxLength} karakter)`);
  }

  // Warning for very short text
  if (trimmedText.length < 3) {
    warnings.push('Teks sangat pendek');
  }

  // Warning for very long text approaching limit
  if (trimmedText.length > actualMaxLength * 0.9) {
    warnings.push(`Teks mendekati batas maksimal (${trimmedText.length}/${actualMaxLength})`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Sanitize field input text
 */
export function sanitizeFieldInput(input: string): string {
  if (!input) return '';

  return input
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/\n{3,}/g, '\n\n') // Replace multiple newlines with double newline
    .replace(/[^\w\s\p{L}\p{N}\p{P}\p{Z}]/gu, '') // Remove non-printable characters
    .substring(0, JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH); // Ensure max length
}

/**
 * Check if user can create more custom fields
 */
export async function checkFieldLimits(currentFieldCount: number): Promise<FieldLimitStatus> {
  const maxAllowed = JOURNAL_CONSTANTS.MAX_CUSTOM_FIELDS_PER_USER;
  const remaining = Math.max(0, maxAllowed - currentFieldCount);
  
  return {
    currentCount: currentFieldCount,
    maxAllowed,
    canCreate: remaining > 0,
    remaining,
  };
}

/**
 * Validate field order values
 */
export function validateFieldOrder(orders: Array<{ id: string; order: number }>): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for duplicate orders
  const orderValues = orders.map(item => item.order);
  const uniqueOrders = new Set(orderValues);
  
  if (orderValues.length !== uniqueOrders.size) {
    errors.push('Urutan field tidak boleh duplikat');
  }

  // Check for negative orders
  const hasNegativeOrder = orderValues.some(order => order < 0);
  if (hasNegativeOrder) {
    errors.push('Urutan field tidak boleh negatif');
  }

  // Check for gaps in ordering
  const sortedOrders = [...orderValues].sort((a, b) => a - b);
  const hasGaps = sortedOrders.some((order, index) => {
    if (index === 0) return false;
    return order - sortedOrders[index - 1] > 1;
  });
  
  if (hasGaps) {
    warnings.push('Ada celah dalam urutan field');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Generate safe field order value
 */
export function generateSafeFieldOrder(existingOrders: number[]): number {
  if (existingOrders.length === 0) return 1;
  
  const maxOrder = Math.max(...existingOrders);
  return maxOrder + 1;
}

/**
 * Normalize field orders to remove gaps
 */
export function normalizeFieldOrders(
  fields: Array<{ id: string; order: number }>
): Array<{ id: string; order: number }> {
  return fields
    .sort((a, b) => a.order - b.order)
    .map((field, index) => ({
      ...field,
      order: index + 1,
    }));
}

/**
 * Check for potentially problematic field content
 */
export function checkFieldContentSafety(text: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const lowercaseText = text.toLowerCase();

  // Check for potentially sensitive content
  const sensitivePatterns = [
    /bunuh diri|suicide/i,
    /menyakiti diri|self.?harm/i,
    /depresi berat|severe depression/i,
    /ingin mati|want to die/i,
  ];

  const hasSensitiveContent = sensitivePatterns.some(pattern => pattern.test(text));
  if (hasSensitiveContent) {
    warnings.push('Konten mungkin sensitif - pertimbangkan untuk menggunakan bahasa yang lebih positif');
  }

  // Check for very personal information
  const personalPatterns = [
    /password|kata sandi/i,
    /nomor rekening|account number/i,
    /alamat lengkap|full address/i,
  ];

  const hasPersonalInfo = personalPatterns.some(pattern => pattern.test(text));
  if (hasPersonalInfo) {
    warnings.push('Hindari menyimpan informasi pribadi sensitif dalam jurnal');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
