/**
 * Password Security Utilities
 * Implements strong password policies and validation
 */

export interface PasswordValidationResult {
  isValid: boolean;
  score: number; // 0-100
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface PasswordPolicy {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  minUniqueChars: number;
  preventCommonPasswords: boolean;
  preventPersonalInfo: boolean;
}

// Default strong password policy
export const DEFAULT_PASSWORD_POLICY: PasswordPolicy = {
  minLength: 12,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  minUniqueChars: 8,
  preventCommonPasswords: true,
  preventPersonalInfo: true,
};

// Common weak passwords (subset for demo - in production use a larger list)
const COMMON_PASSWORDS = [
  'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
  'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',
  'qwerty123', 'admin123', 'root', 'toor', 'pass', 'test', 'guest',
  'user', 'demo', 'sample', 'temp', 'default', 'changeme', 'secret',
];

// Indonesian common passwords
const INDONESIAN_COMMON_PASSWORDS = [
  'indonesia', 'jakarta', 'surabaya', 'bandung', 'medan', 'semarang',
  'makassar', 'palembang', 'tangerang', 'depok', 'bekasi', 'bogor',
  'sayang', 'cinta', 'kamu', 'aku', 'love', 'cantik', 'ganteng',
];

/**
 * Validate password strength according to policy
 */
export function validatePassword(
  password: string,
  policy: PasswordPolicy = DEFAULT_PASSWORD_POLICY,
  userInfo?: { email?: string; name?: string; }
): PasswordValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];
  let score = 0;

  // Length validation
  if (password.length < policy.minLength) {
    errors.push(`Kata sandi minimal ${policy.minLength} karakter`);
  } else if (password.length >= policy.minLength) {
    score += 20;
  }

  if (password.length > policy.maxLength) {
    errors.push(`Kata sandi maksimal ${policy.maxLength} karakter`);
  }

  // Character type requirements
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);
  const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

  if (policy.requireUppercase && !hasUppercase) {
    errors.push('Kata sandi harus mengandung huruf besar');
    suggestions.push('Tambahkan huruf besar (A-Z)');
  } else if (hasUppercase) {
    score += 15;
  }

  if (policy.requireLowercase && !hasLowercase) {
    errors.push('Kata sandi harus mengandung huruf kecil');
    suggestions.push('Tambahkan huruf kecil (a-z)');
  } else if (hasLowercase) {
    score += 15;
  }

  if (policy.requireNumbers && !hasNumbers) {
    errors.push('Kata sandi harus mengandung angka');
    suggestions.push('Tambahkan angka (0-9)');
  } else if (hasNumbers) {
    score += 15;
  }

  if (policy.requireSpecialChars && !hasSpecialChars) {
    errors.push('Kata sandi harus mengandung karakter khusus');
    suggestions.push('Tambahkan karakter khusus (!@#$%^&*)');
  } else if (hasSpecialChars) {
    score += 15;
  }

  // Unique characters
  const uniqueChars = new Set(password.toLowerCase()).size;
  if (uniqueChars < policy.minUniqueChars) {
    warnings.push(`Gunakan lebih banyak karakter unik (minimal ${policy.minUniqueChars})`);
  } else {
    score += 10;
  }

  // Common password check
  if (policy.preventCommonPasswords) {
    const lowerPassword = password.toLowerCase();
    const isCommon = COMMON_PASSWORDS.some(common => 
      lowerPassword.includes(common) || common.includes(lowerPassword)
    );
    const isIndonesianCommon = INDONESIAN_COMMON_PASSWORDS.some(common =>
      lowerPassword.includes(common) || common.includes(lowerPassword)
    );

    if (isCommon || isIndonesianCommon) {
      errors.push('Kata sandi terlalu umum dan mudah ditebak');
      suggestions.push('Gunakan kombinasi kata yang tidak umum');
    } else {
      score += 10;
    }
  }

  // Personal information check
  if (policy.preventPersonalInfo && userInfo) {
    const lowerPassword = password.toLowerCase();
    
    if (userInfo.email) {
      const emailParts = userInfo.email.toLowerCase().split('@')[0];
      if (lowerPassword.includes(emailParts) || emailParts.includes(lowerPassword)) {
        errors.push('Kata sandi tidak boleh mengandung email Anda');
      }
    }

    if (userInfo.name) {
      const nameParts = userInfo.name.toLowerCase().split(' ');
      const containsName = nameParts.some(part => 
        part.length > 2 && (lowerPassword.includes(part) || part.includes(lowerPassword))
      );
      if (containsName) {
        errors.push('Kata sandi tidak boleh mengandung nama Anda');
      }
    }
  }

  // Pattern detection
  if (hasRepeatingPatterns(password)) {
    warnings.push('Hindari pola berulang (123, abc, aaa)');
  }

  if (hasKeyboardPatterns(password)) {
    warnings.push('Hindari pola keyboard (qwerty, asdf)');
  }

  // Bonus points for length
  if (password.length >= 16) {
    score += 10;
  }

  // Cap score at 100
  score = Math.min(score, 100);

  return {
    isValid: errors.length === 0,
    score,
    errors,
    warnings,
    suggestions,
  };
}

/**
 * Generate a secure password suggestion
 */
export function generateSecurePassword(length: number = 16): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = uppercase + lowercase + numbers + specialChars;
  
  let password = '';
  
  // Ensure at least one character from each category
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Check for repeating patterns
 */
function hasRepeatingPatterns(password: string): boolean {
  // Check for repeated characters (aaa, 111)
  if (/(.)\1{2,}/.test(password)) {
    return true;
  }
  
  // Check for sequential patterns (123, abc)
  for (let i = 0; i < password.length - 2; i++) {
    const char1 = password.charCodeAt(i);
    const char2 = password.charCodeAt(i + 1);
    const char3 = password.charCodeAt(i + 2);
    
    if (char2 === char1 + 1 && char3 === char2 + 1) {
      return true;
    }
  }
  
  return false;
}

/**
 * Check for keyboard patterns
 */
function hasKeyboardPatterns(password: string): boolean {
  const keyboardPatterns = [
    'qwerty', 'asdf', 'zxcv', 'qaz', 'wsx', 'edc',
    '123456', '098765', 'qwertyuiop', 'asdfghjkl'
  ];
  
  const lowerPassword = password.toLowerCase();
  
  return keyboardPatterns.some(pattern => 
    lowerPassword.includes(pattern) || lowerPassword.includes(pattern.split('').reverse().join(''))
  );
}

/**
 * Get password strength description
 */
export function getPasswordStrengthDescription(score: number): {
  level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  description: string;
  color: string;
} {
  if (score < 20) {
    return {
      level: 'very-weak',
      description: 'Sangat Lemah',
      color: '#ff4444'
    };
  } else if (score < 40) {
    return {
      level: 'weak',
      description: 'Lemah',
      color: '#ff8800'
    };
  } else if (score < 60) {
    return {
      level: 'fair',
      description: 'Cukup',
      color: '#ffaa00'
    };
  } else if (score < 80) {
    return {
      level: 'good',
      description: 'Baik',
      color: '#88cc00'
    };
  } else {
    return {
      level: 'strong',
      description: 'Kuat',
      color: '#00aa00'
    };
  }
}
