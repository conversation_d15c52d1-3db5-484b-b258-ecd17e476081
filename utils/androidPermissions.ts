import { Platform, Alert, Linking } from 'react-native';
import { AudioModule, setAudioModeAsync } from 'expo-audio';

export interface PermissionResult {
  granted: boolean;
  canAskAgain: boolean;
  error?: string;
}

/**
 * Platform-specific audio configurations using modern expo-audio APIs
 * Updated to use the new AudioMode structure from expo-audio
 */
const AUDIO_CONFIGURATIONS = {
  android: {
    recording: {
      shouldPlayInBackground: false,
      interruptionModeAndroid: 'doNotMix',
      shouldRouteThroughEarpiece: false,
    },
    cleanup: {
      shouldPlayInBackground: false,
      interruptionModeAndroid: 'duckOthers',
      shouldRouteThroughEarpiece: false,
    }
  },
  ios: {
    recording: {
      allowsRecording: true,
      shouldPlayInBackground: false,
      interruptionMode: 'doNotMix',
      playsInSilentMode: true,
    },
    cleanup: {
      allowsRecording: false,
      shouldPlayInBackground: false,
      interruptionMode: 'mixWithOthers',
      playsInSilentMode: false,
    }
  }
};

export class AndroidPermissionManager {
  private static instance: AndroidPermissionManager;

  public static getInstance(): AndroidPermissionManager {
    if (!AndroidPermissionManager.instance) {
      AndroidPermissionManager.instance = new AndroidPermissionManager();
    }
    return AndroidPermissionManager.instance;
  }

  /**
   * Get Android-specific audio configuration to prevent cross-platform property errors
   * This method only returns Android configurations to prevent iOS properties on Android
   */
  private getAndroidAudioConfig(type: 'recording' | 'cleanup'): any {
    const config = AUDIO_CONFIGURATIONS.android[type];

    if (!config) {
      console.warn(`[AndroidPermissions] No Android audio config found for type: ${type}`);
      return {};
    }

    console.log(`[AndroidPermissions] Using Android ${type} audio config:`, config);
    return config;
  }

  /**
   * Request microphone permission for Android using expo-audio
   */
  async requestMicrophonePermission(): Promise<PermissionResult> {
    try {
      console.log('[AndroidPermissions] Requesting microphone permission...');

      if (Platform.OS !== 'android') {
        console.log('[AndroidPermissions] Not Android platform, skipping permission request');
        return { granted: true, canAskAgain: true };
      }

      // First check current permission status
      const currentStatus = await AudioModule.getRecordingPermissionsAsync();
      console.log('[AndroidPermissions] Current permission status:', currentStatus);

      if (currentStatus.granted) {
        console.log('[AndroidPermissions] Permission already granted');
        return { granted: true, canAskAgain: true };
      }

      // Request permission
      const permissionResponse = await AudioModule.requestRecordingPermissionsAsync();
      console.log('[AndroidPermissions] Permission response:', permissionResponse);

      if (permissionResponse.granted) {
        console.log('[AndroidPermissions] Permission granted successfully');
        return { granted: true, canAskAgain: true };
      } else {
        console.log('[AndroidPermissions] Permission denied');
        return {
          granted: false,
          canAskAgain: permissionResponse.canAskAgain,
          error: 'Izin mikrofon ditolak'
        };
      }
    } catch (error: any) {
      console.error('[AndroidPermissions] Error requesting microphone permission:', error);
      return {
        granted: false,
        canAskAgain: true,
        error: `Gagal meminta izin mikrofon: ${error.message}`
      };
    }
  }

  /**
   * Check if microphone permission is currently granted
   */
  async checkMicrophonePermission(): Promise<boolean> {
    try {
      if (Platform.OS !== 'android') {
        return true;
      }

      const status = await AudioModule.getRecordingPermissionsAsync();
      console.log('[AndroidPermissions] Current microphone permission status:', status.granted);
      return status.granted;
    } catch (error) {
      console.error('[AndroidPermissions] Error checking microphone permission:', error);
      return false;
    }
  }

  /**
   * Show permission denied dialog with options to retry or go to settings
   */
  showPermissionDeniedDialog(canAskAgain: boolean): Promise<'retry' | 'settings' | 'cancel'> {
    return new Promise((resolve) => {
      const buttons = [];
      
      if (canAskAgain) {
        buttons.push({
          text: 'Coba Lagi',
          onPress: () => resolve('retry')
        });
      } else {
        buttons.push({
          text: 'Buka Pengaturan',
          onPress: () => {
            Linking.openSettings();
            resolve('settings');
          }
        });
      }
      
      buttons.push({
        text: 'Batal',
        style: 'cancel' as const,
        onPress: () => resolve('cancel')
      });

      Alert.alert(
        'Izin Mikrofon Diperlukan',
        canAskAgain 
          ? 'Temani memerlukan akses mikrofon untuk fitur panggilan suara. Silakan berikan izin untuk melanjutkan.'
          : 'Izin mikrofon telah ditolak secara permanen. Silakan buka pengaturan aplikasi dan berikan izin mikrofon secara manual.',
        buttons
      );
    });
  }

  /**
   * Show microphone error dialog
   */
  showMicrophoneErrorDialog(error: string): void {
    Alert.alert(
      'Kesalahan Mikrofon',
      `Terjadi kesalahan saat mengakses mikrofon: ${error}`,
      [{ text: 'OK' }]
    );
  }

  /**
   * Complete permission flow with user-friendly dialogs
   */
  async requestPermissionWithDialog(): Promise<boolean> {
    try {
      const result = await this.requestMicrophonePermission();
      
      if (result.granted) {
        return true;
      }

      if (result.error) {
        this.showMicrophoneErrorDialog(result.error);
        return false;
      }

      // Permission denied, show dialog
      const userChoice = await this.showPermissionDeniedDialog(result.canAskAgain);
      
      if (userChoice === 'retry' && result.canAskAgain) {
        // Recursive call for retry
        return this.requestPermissionWithDialog();
      }
      
      return false;
    } catch (error: any) {
      console.error('[AndroidPermissions] Error in permission flow:', error);
      this.showMicrophoneErrorDialog(error.message || 'Kesalahan tidak diketahui');
      return false;
    }
  }

  /**
   * Initialize audio session for Android
   */
  async initializeAudioSession(): Promise<boolean> {
    try {
      if (Platform.OS !== 'android') {
        console.log('[AndroidPermissions] Not Android platform, skipping audio session initialization');
        return true;
      }

      console.log('[AndroidPermissions] Initializing audio session for Android...');

      // Use Android-specific configuration to prevent iOS property errors
      const audioConfig = this.getAndroidAudioConfig('recording');
      await setAudioModeAsync(audioConfig);

      console.log('[AndroidPermissions] ✅ Audio session initialized successfully for Android');
      return true;
    } catch (error: any) {
      console.error('[AndroidPermissions] ❌ Error initializing audio session:', error);
      console.error('[AndroidPermissions] Error details:', {
        message: error.message,
        stack: error.stack,
        platform: Platform.OS
      });
      return false;
    }
  }

  /**
   * Cleanup audio session
   */
  async cleanupAudioSession(): Promise<void> {
    try {
      if (Platform.OS !== 'android') {
        console.log('[AndroidPermissions] Not Android platform, skipping audio session cleanup');
        return;
      }

      console.log('[AndroidPermissions] Cleaning up audio session for Android...');

      // Use Android-specific cleanup configuration to prevent iOS property errors
      const cleanupConfig = this.getAndroidAudioConfig('cleanup');
      await setAudioModeAsync(cleanupConfig);

      console.log('[AndroidPermissions] ✅ Audio session cleaned up successfully for Android');
    } catch (error: any) {
      console.error('[AndroidPermissions] ❌ Error cleaning up audio session:', error);
      console.error('[AndroidPermissions] Error details:', {
        message: error.message,
        stack: error.stack,
        platform: Platform.OS
      });
    }
  }
}

// Export singleton instance
export const androidPermissions = AndroidPermissionManager.getInstance();
