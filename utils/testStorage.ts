/**
 * Test utility to verify the custom storage adapter works correctly
 * across different platforms and environments.
 */

import { supabaseStorage } from '../lib/storage';

export const testStorageAdapter = async (): Promise<{
  success: boolean;
  results: string[];
  errors: string[];
}> => {
  const results: string[] = [];
  const errors: string[] = [];
  let success = true;

  try {
    // Test 1: Check storage availability
    results.push(`Storage type: ${supabaseStorage.getStorageType()}`);
    
    const isAvailable = await supabaseStorage.isAvailable();
    results.push(`Storage available: ${isAvailable}`);

    if (!isAvailable) {
      errors.push('Storage is not available');
      success = false;
      return { success, results, errors };
    }

    // Test 2: Test setItem
    const testKey = 'test-storage-key';
    const testValue = 'test-storage-value';
    
    await supabaseStorage.setItem(testKey, testValue);
    results.push(`✅ setItem: Successfully stored "${testKey}"`);

    // Test 3: Test getItem
    const retrievedValue = await supabaseStorage.getItem(testKey);
    
    if (retrievedValue === testValue) {
      results.push(`✅ getItem: Successfully retrieved "${testKey}" = "${retrievedValue}"`);
    } else {
      errors.push(`❌ getItem: Expected "${testValue}", got "${retrievedValue}"`);
      success = false;
    }

    // Test 4: Test removeItem
    await supabaseStorage.removeItem(testKey);
    results.push(`✅ removeItem: Successfully removed "${testKey}"`);

    // Test 5: Verify item was removed
    const removedValue = await supabaseStorage.getItem(testKey);
    
    if (removedValue === null) {
      results.push(`✅ Verification: Item "${testKey}" was successfully removed`);
    } else {
      errors.push(`❌ Verification: Item "${testKey}" still exists with value "${removedValue}"`);
      success = false;
    }

    // Test 6: Test with null/undefined values
    await supabaseStorage.setItem('null-test', '');
    const emptyValue = await supabaseStorage.getItem('null-test');
    
    if (emptyValue === '') {
      results.push(`✅ Empty value test: Successfully handled empty string`);
    } else {
      errors.push(`❌ Empty value test: Expected empty string, got "${emptyValue}"`);
      success = false;
    }

    // Cleanup
    await supabaseStorage.removeItem('null-test');

  } catch (error) {
    errors.push(`❌ Test failed with error: ${error instanceof Error ? error.message : String(error)}`);
    success = false;
  }

  return { success, results, errors };
};

/**
 * Run storage tests and log results to console
 */
export const runStorageTests = async (): Promise<boolean> => {
  console.log('\n🧪 Running Storage Adapter Tests...\n');
  
  const { success, results, errors } = await testStorageAdapter();
  
  // Log results
  results.forEach(result => console.log(result));
  
  // Log errors
  if (errors.length > 0) {
    console.log('\n❌ Errors:');
    errors.forEach(error => console.error(error));
  }
  
  console.log(`\n${success ? '✅ All tests passed!' : '❌ Some tests failed!'}\n`);
  
  return success;
};

/**
 * Test Supabase integration with the custom storage
 */
export const testSupabaseIntegration = async (): Promise<{
  success: boolean;
  message: string;
}> => {
  try {
    // Import Supabase client
    const { supabase } = await import('../lib/supabase');
    
    // Test getting session (this should not throw an error)
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      return {
        success: false,
        message: `Supabase error: ${error.message}`
      };
    }
    
    return {
      success: true,
      message: 'Supabase client initialized successfully with custom storage'
    };
    
  } catch (error) {
    return {
      success: false,
      message: `Integration test failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
};
