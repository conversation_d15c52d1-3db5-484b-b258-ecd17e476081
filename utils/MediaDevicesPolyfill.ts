import { Platform } from 'react-native';

// Conditional import for expo-audio - only available on React Native platforms
let AudioModule: any = null;
let useAudioRecorder: any = null;
let RecordingPresets: any = null;
let setAudioModeAsync: any = null;

// Only import expo-audio on non-web platforms
if (Platform.OS !== 'web') {
  try {
    const expoAudio = require('expo-audio');
    AudioModule = expoAudio.AudioModule;
    useAudioRecorder = expoAudio.useAudioRecorder;
    RecordingPresets = expoAudio.RecordingPresets;
    setAudioModeAsync = expoAudio.setAudioModeAsync;
  } catch (error) {
    console.warn('[MediaDevicesPolyfill] expo-audio not available:', error);
  }
}

// Immediate polyfill installation to ensure availability before any other code runs
if (typeof navigator !== 'undefined' && !navigator.mediaDevices) {
  console.log('[MediaDevicesPolyfill] Installing immediate polyfill...');
  (navigator as any).mediaDevices = {
    getUserMedia: null, // Will be replaced with proper implementation
    enumerateDevices: async () => [],
    getSupportedConstraints: () => ({})
  };
}

// Add global error handler to catch property access errors
if (typeof global !== 'undefined') {
  const originalConsoleError = console.error;
  console.error = function(...args: any[]) {
    const message = args.join(' ');
    if (message.includes('Cannot read property') && message.includes('includes')) {
      console.log('[MediaDevicesPolyfill] CAUGHT INCLUDES ERROR:', message);
      console.log('[MediaDevicesPolyfill] Stack trace:', new Error().stack);
    }
    originalConsoleError.apply(console, args);
  };
}

// Mock MediaStreamTrack class with complete API
class MockMediaStreamTrack {
  kind: string = 'audio';
  id: string;
  label: string = 'Default Microphone';
  enabled: boolean = true;
  muted: boolean = false;
  readyState: 'live' | 'ended' = 'live';

  private recorder: any = null;

  constructor(id: string) {
    this.id = String(id || `audio_track_${Math.random().toString(36).substr(2, 9)}`);
    this.kind = String(this.kind);
    this.label = String(this.label);

    // Ensure all string properties are proper strings with includes method
    console.log('[MediaDevicesPolyfill] Track created with properties:', {
      id: this.id,
      kind: this.kind,
      label: this.label,
      idHasIncludes: typeof this.id.includes === 'function',
      kindHasIncludes: typeof this.kind.includes === 'function',
      labelHasIncludes: typeof this.label.includes === 'function'
    });
  }

  // Required MediaStreamTrack methods with defensive programming
  getSettings() {
    const settings = {
      deviceId: 'default',
      groupId: 'default_group',
      autoGainControl: true,
      channelCount: 1,
      echoCancellation: true,
      noiseSuppression: true,
      sampleRate: 44100,
      sampleSize: 16,
      volume: 1.0
    };

    // Ensure all string properties have includes method
    Object.keys(settings).forEach(key => {
      const value = (settings as any)[key];
      if (typeof value === 'string' && !value.includes) {
        console.warn(`[MediaDevicesPolyfill] String property ${key} missing includes method, fixing...`);
        (settings as any)[key] = String(value);
      }
    });

    return settings;
  }

  getCapabilities() {
    const capabilities = {
      deviceId: 'default',
      groupId: 'default_group',
      autoGainControl: [true, false],
      channelCount: { min: 1, max: 2 },
      echoCancellation: [true, false],
      noiseSuppression: [true, false],
      sampleRate: { min: 8000, max: 96000 },
      sampleSize: { min: 8, max: 32 },
      volume: { min: 0, max: 1 }
    };

    // Ensure all string properties have includes method
    Object.keys(capabilities).forEach(key => {
      const value = (capabilities as any)[key];
      if (typeof value === 'string' && !value.includes) {
        console.warn(`[MediaDevicesPolyfill] String property ${key} missing includes method, fixing...`);
        (capabilities as any)[key] = String(value);
      }
    });

    return capabilities;
  }

  getConstraints() {
    const constraints = {
      deviceId: 'default',
      autoGainControl: true,
      echoCancellation: true,
      noiseSuppression: true
    };

    // Ensure all string properties have includes method
    Object.keys(constraints).forEach(key => {
      const value = (constraints as any)[key];
      if (typeof value === 'string' && !value.includes) {
        console.warn(`[MediaDevicesPolyfill] String property ${key} missing includes method, fixing...`);
        (constraints as any)[key] = String(value);
      }
    });

    return constraints;
  }

  applyConstraints(constraints: any) {
    return Promise.resolve();
  }

  stop() {
    console.log('[MediaDevicesPolyfill] MediaStreamTrack.stop() called');
    this.readyState = 'ended';
    if (this.recorder) {
      this.recorder.stop().catch(console.error);
      this.recorder = null;
    }
  }

  clone() {
    const cloned = new MockMediaStreamTrack(this.id + '_clone');
    cloned.kind = this.kind;
    cloned.label = this.label;
    cloned.enabled = this.enabled;
    return cloned;
  }

  addEventListener() {}
  removeEventListener() {}
  dispatchEvent() { return true; }

  setRecorder(recorder: any) {
    this.recorder = recorder;
  }
}

// Mock MediaStream class with complete API
class MockMediaStream {
  id: string;
  active: boolean = true;
  private tracks: MockMediaStreamTrack[] = [];

  constructor() {
    this.id = `mock_stream_${Math.random().toString(36).substr(2, 9)}`;
    // Create a mock audio track with proper ID and wrap it in a logging proxy
    const audioTrack = new MockMediaStreamTrack(`audio_track_${Math.random().toString(36).substr(2, 9)}`);
    const proxiedTrack = this.createLoggingProxy(audioTrack);
    this.tracks.push(proxiedTrack);
  }

  private createLoggingProxy(track: MockMediaStreamTrack): MockMediaStreamTrack {
    return new Proxy(track, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver);

        // Log property access for debugging
        if (typeof prop === 'string' && !prop.startsWith('_')) {
          console.log(`[MediaDevicesPolyfill] Track property accessed: ${prop} = ${typeof value} ${value === undefined ? '(UNDEFINED!)' : ''}`);

          // Special logging for methods that might be called
          if (typeof value === 'function') {
            return function(...args: any[]) {
              console.log(`[MediaDevicesPolyfill] Track method called: ${prop}(${args.map(a => JSON.stringify(a)).join(', ')})`);
              const result = value.apply(target, args);
              console.log(`[MediaDevicesPolyfill] Track method ${prop} returned:`, result);
              return result;
            };
          }
        }

        return value;
      }
    });
  }

  getTracks() {
    return [...this.tracks]; // Return copy to prevent external modification
  }

  getAudioTracks() {
    return this.tracks.filter(track => track.kind === 'audio');
  }

  getVideoTracks() {
    return this.tracks.filter(track => track.kind === 'video');
  }

  getTrackById(trackId: string) {
    return this.tracks.find(track => track.id === trackId) || null;
  }

  addTrack(track: MockMediaStreamTrack) {
    if (!this.tracks.includes(track)) {
      this.tracks.push(track);
      this.updateActiveState();
    }
  }

  removeTrack(track: MockMediaStreamTrack) {
    const index = this.tracks.indexOf(track);
    if (index > -1) {
      this.tracks.splice(index, 1);
      this.updateActiveState();
    }
  }

  clone() {
    const clonedStream = new MockMediaStream();
    clonedStream.id = `${this.id}_clone`;
    clonedStream.tracks = this.tracks.map(track => track.clone());
    return clonedStream;
  }

  private updateActiveState() {
    this.active = this.tracks.some(track => track.readyState === 'live');
  }

  addEventListener() {}
  removeEventListener() {}
  dispatchEvent() { return true; }

  setRecorder(recorder: any) {
    this.getAudioTracks().forEach(track => {
      if (track instanceof MockMediaStreamTrack) {
        track.setRecorder(recorder);
      }
    });
  }
}

// Helper function to ensure string properties have includes method
function ensureStringWithIncludes(value: any): string {
  if (typeof value === 'string') {
    // If it's already a string with includes method, return as is
    if (typeof value.includes === 'function') {
      return value;
    }
    // If it's a string without includes method, create a new string
    return String(value);
  }
  // If it's not a string, convert to string
  return String(value || '');
}

// Helper function to wrap objects with string property protection
function wrapObjectWithStringProtection(obj: any): any {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const wrapped = { ...obj };

  // Ensure all string properties have includes method
  Object.keys(wrapped).forEach(key => {
    const value = wrapped[key];
    if (typeof value === 'string') {
      wrapped[key] = ensureStringWithIncludes(value);
    }
  });

  return wrapped;
}

// Mock getUserMedia function with enhanced error handling and string protection
async function mockGetUserMedia(constraints: MediaStreamConstraints): Promise<MockMediaStream> {
  console.log('[MediaDevicesPolyfill] mockGetUserMedia called with constraints:', JSON.stringify(constraints));

  if (!constraints || !constraints.audio) {
    const error = new Error('Audio constraint is required');
    error.name = 'NotSupportedError';
    throw error;
  }

  try {
    // Request audio recording permission using Expo Audio
    console.log('[MediaDevicesPolyfill] Requesting audio recording permission...');
    const { status } = await AudioModule.requestRecordingPermissionsAsync();

    if (status !== 'granted') {
      const error = new Error('Permission to access microphone was denied');
      error.name = 'NotAllowedError';
      throw error;
    }

    console.log('[MediaDevicesPolyfill] Audio permission granted, setting up recording...');

    // Configure audio mode for recording
    await setAudioModeAsync({
      allowsRecording: true,
      playsInSilentMode: true,
      shouldPlayInBackground: false,
      shouldRouteThroughEarpiece: false,
      interruptionMode: 'doNotMix',
      interruptionModeAndroid: 'doNotMix',
    });

    // Create a mock MediaStream with proper validation
    const mockStream = new MockMediaStream();

    // Wrap the stream with string protection
    const protectedStream = new Proxy(mockStream, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver);

        // Log property access for debugging
        if (typeof prop === 'string') {
          console.log(`[MediaDevicesPolyfill] Stream property accessed: ${prop}`);
        }

        // If it's a method, wrap it to protect returned objects
        if (typeof value === 'function') {
          return function(...args: any[]) {
            const result = value.apply(target, args);

            // If the method returns an array (like getTracks), protect each item
            if (Array.isArray(result)) {
              return result.map(item => {
                if (item && typeof item === 'object') {
                  return new Proxy(item, {
                    get(itemTarget, itemProp, itemReceiver) {
                      const itemValue = Reflect.get(itemTarget, itemProp, itemReceiver);

                      // Log track property access
                      if (typeof itemProp === 'string') {
                        console.log(`[MediaDevicesPolyfill] Track property accessed: ${itemProp} = ${typeof itemValue}`);
                      }

                      // If it's a string, ensure it has includes method
                      if (typeof itemValue === 'string') {
                        return ensureStringWithIncludes(itemValue);
                      }

                      // If it's a method that returns an object, protect the returned object
                      if (typeof itemValue === 'function') {
                        return function(...methodArgs: any[]) {
                          const methodResult = itemValue.apply(itemTarget, methodArgs);
                          return wrapObjectWithStringProtection(methodResult);
                        };
                      }

                      return itemValue;
                    }
                  });
                }
                return item;
              });
            }

            return result;
          };
        }

        return value;
      }
    });

    console.log('[MediaDevicesPolyfill] Mock MediaStream created successfully');
    return protectedStream as MockMediaStream;

  } catch (error: any) {
    console.error('[MediaDevicesPolyfill] Error in mockGetUserMedia:', error);

    // Ensure error has proper name for ElevenLabs SDK
    if (!error.name) {
      error.name = 'NotReadableError';
    }

    throw error;
  }
}

// Polyfill initialization with platform-specific approach
export function initializeMediaDevicesPolyfill() {
  console.log('[MediaDevicesPolyfill] Initializing polyfill...');
  console.log('[MediaDevicesPolyfill] Platform.OS:', Platform.OS);
  console.log('[MediaDevicesPolyfill] navigator available:', typeof navigator !== 'undefined');
  console.log('[MediaDevicesPolyfill] navigator.mediaDevices available:', typeof navigator !== 'undefined' && navigator.mediaDevices);

  if (typeof navigator === 'undefined') {
    console.log('[MediaDevicesPolyfill] Navigator not available, skipping polyfill');
    return;
  }

  // Skip polyfill on web platforms - use native web APIs
  if (Platform.OS === 'web') {
    console.log('[MediaDevicesPolyfill] Web platform detected, skipping polyfill - using native web APIs');
    return;
  }

  // Only proceed with polyfill on React Native platforms
  if (!AudioModule) {
    console.log('[MediaDevicesPolyfill] expo-audio not available, cannot initialize polyfill');
    return;
  }

  // Create comprehensive mediaDevices polyfill
  const mediaDevicesPolyfill = {
    getUserMedia: mockGetUserMedia,
    enumerateDevices: async () => {
      return [
        {
          deviceId: 'default',
          kind: 'audioinput' as const,
          label: 'Default Microphone',
          groupId: 'default_group',
          toJSON: function() {
            return {
              deviceId: this.deviceId,
              kind: this.kind,
              label: this.label,
              groupId: this.groupId
            };
          }
        },
        {
          deviceId: 'communications',
          kind: 'audioinput' as const,
          label: 'Communications Microphone',
          groupId: 'default_group',
          toJSON: function() {
            return {
              deviceId: this.deviceId,
              kind: this.kind,
              label: this.label,
              groupId: this.groupId
            };
          }
        },
        {
          deviceId: 'default_speaker',
          kind: 'audiooutput' as const,
          label: 'Default Speaker',
          groupId: 'default_group',
          toJSON: function() {
            return {
              deviceId: this.deviceId,
              kind: this.kind,
              label: this.label,
              groupId: this.groupId
            };
          }
        }
      ];
    },
    getSupportedConstraints: () => {
      return {
        deviceId: true,
        groupId: true,
        autoGainControl: true,
        channelCount: true,
        echoCancellation: true,
        noiseSuppression: true,
        sampleRate: true,
        sampleSize: true,
        volume: true
      };
    }
  };

  // Force install the polyfill regardless of existing state
  console.log('[MediaDevicesPolyfill] Force installing mediaDevices polyfill...');
  (navigator as any).mediaDevices = mediaDevicesPolyfill;

  // Also ensure it's available on the global object and window
  if (typeof global !== 'undefined') {
    (global as any).navigator = (global as any).navigator || {};
    (global as any).navigator.mediaDevices = mediaDevicesPolyfill;
  }

  if (typeof window !== 'undefined') {
    (window as any).navigator = (window as any).navigator || {};
    (window as any).navigator.mediaDevices = mediaDevicesPolyfill;
  }

  // Also try to set it on globalThis
  if (typeof globalThis !== 'undefined') {
    (globalThis as any).navigator = (globalThis as any).navigator || {};
    (globalThis as any).navigator.mediaDevices = mediaDevicesPolyfill;
  }

  // Create a property descriptor that always returns our polyfill
  try {
    Object.defineProperty(navigator, 'mediaDevices', {
      get: function() {
        console.log('[MediaDevicesPolyfill] mediaDevices property accessed, returning polyfill');
        return mediaDevicesPolyfill;
      },
      set: function(value) {
        console.log('[MediaDevicesPolyfill] Attempt to set mediaDevices, ignoring and keeping polyfill');
        // Ignore attempts to overwrite our polyfill
      },
      configurable: true,
      enumerable: true
    });
    console.log('[MediaDevicesPolyfill] ✅ Property descriptor installed successfully');
  } catch (error) {
    console.warn('[MediaDevicesPolyfill] Could not install property descriptor:', error);
  }

  // Verify installation
  const hasGetUserMedia = navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function';
  console.log('[MediaDevicesPolyfill] Verification - getUserMedia available:', hasGetUserMedia);

  if (hasGetUserMedia) {
    console.log('[MediaDevicesPolyfill] ✅ Polyfill installed and verified successfully');
  } else {
    console.error('[MediaDevicesPolyfill] ❌ Polyfill installation failed');
  }

  // Set up a periodic check to ensure polyfill stays installed
  const checkInterval = setInterval(() => {
    if (!navigator.mediaDevices || typeof navigator.mediaDevices.getUserMedia !== 'function') {
      console.warn('[MediaDevicesPolyfill] Polyfill was lost, reinstalling...');
      (navigator as any).mediaDevices = mediaDevicesPolyfill;
    }
  }, 1000);

  // Clear the interval after 30 seconds to avoid infinite checking
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('[MediaDevicesPolyfill] Periodic check stopped');
  }, 30000);
}

// Check if polyfill is needed - platform-aware
export function isPolyfillNeeded(): boolean {
  if (typeof navigator === 'undefined') {
    console.log('[MediaDevicesPolyfill] Navigator not available');
    return false;
  }

  // On web platforms, don't use polyfill - rely on native web APIs
  if (Platform.OS === 'web') {
    console.log('[MediaDevicesPolyfill] Web platform - polyfill not needed, using native APIs');
    return false;
  }

  const hasMediaDevices = navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function';
  console.log('[MediaDevicesPolyfill] Has mediaDevices:', hasMediaDevices);

  return !hasMediaDevices;
}

export { MockMediaStream, MockMediaStreamTrack };
