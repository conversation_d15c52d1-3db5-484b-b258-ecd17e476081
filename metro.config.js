const path = require('path');
const {
  getSentryExpoConfig
} = require("@sentry/react-native/metro");

// Find the project and workspace directories
const projectRoot = __dirname;

const config = getSentryExpoConfig(projectRoot);

// Resolver options for Metro
config.resolver.extraNodeModules = {
  '@': path.resolve(__dirname),
};

// Add support for path aliases
config.resolver.sourceExts = ['js', 'jsx', 'ts', 'tsx', 'cjs', 'mjs', 'json'];

// Ensure that import aliases are properly resolved
config.transformer.assetPlugins = ['expo-asset/tools/hashAssetFiles'];

module.exports = config;