# Migration Summary: expo-av to expo-audio + DOM Components

## Overview

This migration addresses two critical issues:
1. **WebRTC Issue**: `Cannot read property 'getUserMedia' of undefined` on Expo Go Android
2. **Deprecation**: `expo-av` will be removed in SDK 54, requiring migration to `expo-audio`

## Solution Implemented

### 1. DOM Components Approach
- **Purpose**: Resolve WebRTC compatibility issues on Expo Go Android
- **Method**: Use ElevenLabs official DOM components with `'use dom'` directive
- **Benefit**: Web APIs available in webview context

### 2. expo-audio Migration
- **Purpose**: Future-proof the codebase for SDK 54+
- **Method**: Replace all `expo-av` APIs with modern `expo-audio` equivalents
- **Benefit**: Continued compatibility with future Expo versions

## Files Modified

### Core Files
1. **`package.json`**
   - Replaced `expo-av` with `expo-audio`
   - Added `react-native-webview`, `react-native-web`
   - Added development scripts for tunnel mode

2. **`app.json`**
   - Added `expo-audio` plugin configuration
   - Configured microphone permissions

3. **`utils/androidPermissions.ts`**
   - Migrated from `expo-av` to `expo-audio` APIs
   - Updated audio configuration structure
   - Changed permission methods

### New Components
4. **`components/VoiceInterface/VoiceDOMComponent.tsx`** (NEW)
   - DOM component with `'use dom'` directive
   - ElevenLabs React SDK integration
   - WebRTC-compatible microphone handling

5. **`components/VoiceInterface/VoiceContainer.tsx`**
   - Added conditional DOM component usage
   - Platform-specific rendering logic
   - DOM component event handlers

### Testing & Documentation
6. **`scripts/testAndroidVoiceFixes.ts`**
   - Updated to use `expo-audio` APIs

7. **`docs/ANDROID_VOICE_FIXES.md`**
   - Updated documentation for expo-audio
   - Added DOM components information

## API Migration Map

| expo-av API | expo-audio API | Notes |
|-------------|----------------|-------|
| `Audio.getPermissionsAsync()` | `AudioModule.getRecordingPermissionsAsync()` | Recording-specific |
| `Audio.requestPermissionsAsync()` | `AudioModule.requestRecordingPermissionsAsync()` | Recording-specific |
| `Audio.setAudioModeAsync(config)` | `setAudioModeAsync(mode)` | Different structure |
| `Audio.InterruptionModeAndroid.DoNotMix` | `'doNotMix'` | String literals |
| `Audio.InterruptionModeAndroid.DuckOthers` | `'duckOthers'` | String literals |

## Configuration Changes

### Audio Mode (Before - expo-av)
```typescript
const audioConfig = {
  staysActiveInBackground: false,
  shouldDuckAndroid: true,
  interruptionModeAndroid: 1, // DoNotMix
  playThroughEarpieceAndroid: false,
};
```

### Audio Mode (After - expo-audio)
```typescript
const audioConfig = {
  shouldPlayInBackground: false,
  interruptionModeAndroid: 'doNotMix',
  shouldRouteThroughEarpiece: false,
};
```

## Platform Strategy

### Android (Expo Go)
- **Uses**: DOM Components approach
- **Reason**: WebRTC APIs not available in React Native context
- **Requirement**: Tunnel mode for HTTPS (microphone access)

### Web
- **Uses**: DOM Components approach
- **Reason**: Consistent cross-platform experience
- **Benefit**: Native web API access

### iOS
- **Uses**: Traditional approach (fallback)
- **Reason**: Native APIs work correctly
- **Future**: Can migrate to DOM components for consistency

## Development Workflow

### Development Mode
```bash
# Start with tunnel mode (required for DOM components)
npm run start:tunnel
```

### Production Build
```bash
# Prebuild for native features
npm run prebuild

# Run on device
npm run android:device
npm run ios:device
```

## Testing Strategy

1. **Expo Go + Tunnel Mode**: Test DOM components functionality
2. **Prebuild + Device**: Test full native functionality
3. **Cross-Platform**: Verify no regressions on web/iOS

## Benefits Achieved

### Immediate
- ✅ **Fixed**: WebRTC `getUserMedia` error on Expo Go Android
- ✅ **Future-Proof**: No more deprecated expo-av dependency
- ✅ **Cross-Platform**: Single codebase approach

### Long-term
- ✅ **Maintainability**: Modern APIs with better documentation
- ✅ **Performance**: Optimized for each platform
- ✅ **Compatibility**: Ready for SDK 54+ migration

## Next Steps

1. **Install Dependencies**: Run `npm install` to install new packages
2. **Test Tunnel Mode**: Use `npm run start:tunnel` for development
3. **Prebuild Testing**: Test on physical devices with `npm run prebuild`
4. **Production Deploy**: Update deployment scripts for new dependencies

## Rollback Plan

If issues arise, the migration can be rolled back by:
1. Reverting `package.json` to use `expo-av`
2. Reverting `utils/androidPermissions.ts` changes
3. Removing DOM component usage in `VoiceContainer.tsx`
4. Removing `VoiceDOMComponent.tsx`

However, this should be temporary as `expo-av` will be removed in SDK 54.
