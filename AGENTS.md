# AGENTS.md

## Build/Lint/Test Commands
- `npm run dev` - Start development server
- `npm run lint` - Run ESLint
- `npm test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run test:deeplink:ios` - Test iOS deep links
- `npm run test:deeplink:android` - Test Android deep links
- `npm test -- SelfReflection.test.ts` - Run single test file
- `npm test -- -t "should generate questions"` - Run specific test by name

## Code Style Guidelines
- **Imports**: Use `@/` prefix for absolute imports (configured in tsconfig.json)
- **Types**: Strict TypeScript enabled, use explicit types
- **Naming**: PascalCase for components, camelCase for variables/functions
- **Formatting**: ESLint with expo-config, no semicolons required
- **Error Handling**: Use try-catch blocks, return meaningful error messages
- **Testing**: Jest with React Native preset, tests in `__tests__/` or `*.test.ts(x)`
- **Environment**: React Native/Expo project with TypeScript