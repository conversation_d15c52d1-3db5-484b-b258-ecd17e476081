/*
  Test Script for Voice Credits Database Fix
  
  Run these queries in your Supabase SQL editor to verify the fix worked correctly.
*/

-- Test 1: Check if the function exists and has the correct signature
SELECT 
  p.proname as function_name,
  pg_get_function_result(p.oid) as return_type,
  pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'get_user_voice_credits'
  AND n.nspname = 'public';

-- Test 2: Test the function with a dummy user ID
SELECT 
  'Function Test' as test_name,
  credits_remaining,
  credits_used,
  total_sessions,
  last_session,
  last_credit_reset,
  CASE 
    WHEN last_credit_reset IS NOT NULL THEN 'PASS'
    ELSE 'FAIL'
  END as test_result
FROM get_user_voice_credits('00000000-0000-0000-0000-000000000000');

-- Test 3: Check if the view exists and includes the new field
SELECT 
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns
WHERE table_name = 'user_voice_usage'
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Test 4: Test the view (will return empty if no users, but should not error)
SELECT 
  'View Test' as test_name,
  COUNT(*) as user_count,
  'PASS' as test_result
FROM user_voice_usage
LIMIT 1;

-- Test 5: Check if all required tables exist
SELECT 
  table_name,
  CASE 
    WHEN table_name IN ('user_profiles', 'voice_call_sessions') THEN 'Required'
    ELSE 'Optional'
  END as importance
FROM information_schema.tables
WHERE table_schema = 'public'
  AND table_name IN ('user_profiles', 'voice_call_sessions', 'user_voice_usage')
ORDER BY table_name;
