/**
 * <PERSON><PERSON><PERSON> to seed self-reflection questions
 * Run this script to populate the database with initial question configuration
 */

import { supabase } from '../lib/supabase';

export const seedSelfReflectionQuestions = async () => {
  try {
    console.log('🌱 Seeding self-reflection questions...');

    // Check if questions already exist
    const { data: existingQuestionSets, error: checkError } = await supabase
      .from('question_sets')
      .select('id')
      .eq('name', 'Temani Self-Reflection Check-In')
      .eq('version', 'v1.0');

    if (checkError) {
      throw new Error(`Error checking existing questions: ${checkError.message}`);
    }

    if (existingQuestionSets && existingQuestionSets.length > 0) {
      console.log('✅ Questions already exist, skipping seed');
      return;
    }

    // The questions are already seeded via the migration file
    // This script can be used for additional question sets or updates
    console.log('✅ Questions should be seeded via migration files');
    console.log('   Run: npx supabase db reset (in development)');
    console.log('   Or apply migrations to your database');

  } catch (error) {
    console.error('❌ Error seeding questions:', error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  seedSelfReflectionQuestions()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
