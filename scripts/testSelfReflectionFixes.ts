/**
 * Test script to verify Self-Reflection fixes
 * Run this to test the import and authentication fixes
 */

import { QuestionConfigService } from '../lib/questionConfigService';
import { ScoringEngine } from '../lib/scoringEngine';
import { SelfReflectionService } from '../lib/selfReflectionService';

export const testSelfReflectionFixes = async () => {
  console.log('🧪 Testing Self-Reflection fixes...');

  try {
    // Test 1: Check if error classes are properly imported
    console.log('\n1. Testing error class imports...');
    
    try {
      // This should not throw "QuestionConfigError is not defined"
      throw new (await import('../types/selfReflection')).QuestionConfigError('Test error');
    } catch (error) {
      if (error.name === 'QuestionConfigError') {
        console.log('✅ QuestionConfigError import working');
      } else {
        console.log('❌ QuestionConfigError import failed:', error.message);
      }
    }

    try {
      // This should not throw "SelfReflectionError is not defined"
      throw new (await import('../types/selfReflection')).SelfReflectionError('Test error', 'TEST_CODE');
    } catch (error) {
      if (error.name === 'SelfReflectionError') {
        console.log('✅ SelfReflectionError import working');
      } else {
        console.log('❌ SelfReflectionError import failed:', error.message);
      }
    }

    try {
      // This should not throw "ScoringError is not defined"
      throw new (await import('../types/selfReflection')).ScoringError('Test error');
    } catch (error) {
      if (error.name === 'ScoringError') {
        console.log('✅ ScoringError import working');
      } else {
        console.log('❌ ScoringError import failed:', error.message);
      }
    }

    // Test 2: Test question loading (this will test authentication)
    console.log('\n2. Testing question loading...');
    
    try {
      const questionSet = await QuestionConfigService.getActiveQuestionSet('id');
      console.log('✅ Question set loaded successfully');
      console.log(`   - Questions: ${questionSet.questions.length}`);
      console.log(`   - Risk rules: ${questionSet.riskAssessmentRules.length}`);
    } catch (error) {
      if (error.message.includes('AUTHENTICATION_REQUIRED') || error.message.includes('PGRST116')) {
        console.log('⚠️  Authentication required for question loading (expected in test environment)');
        console.log('   This should work when user is properly authenticated in the app');
      } else {
        console.log('❌ Question loading failed:', error.message);
      }
    }

    // Test 3: Test scoring engine
    console.log('\n3. Testing scoring engine...');
    
    const mockQuestions = [
      {
        id: '1',
        questionSetId: 'test',
        questionKey: 'test_question',
        questionText: { id: 'Test' },
        questionType: 'multiple_choice' as const,
        options: [
          { id: 'option1', text: { id: 'Option 1' }, score: 1 },
          { id: 'option2', text: { id: 'Option 2' }, score: 2 },
        ],
        scoringConfig: { method: 'direct' as const, maxScore: 2 },
        orderIndex: 1,
        isRequired: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    ];

    const mockResponses = { test_question: 'option1' };
    
    try {
      const result = ScoringEngine.calculateScore(mockResponses, mockQuestions);
      console.log('✅ Scoring engine working');
      console.log(`   - Total score: ${result.totalScore}`);
      console.log(`   - Risk level: ${result.riskLevel}`);
    } catch (error) {
      console.log('❌ Scoring engine failed:', error.message);
    }

    console.log('\n🎉 Self-Reflection fixes test completed!');
    console.log('\nNext steps:');
    console.log('1. Test in the actual app with authenticated user');
    console.log('2. Navigate to onboarding → self-reflection');
    console.log('3. Complete the 5 questions');
    console.log('4. Verify risk assessment works');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Run if called directly
if (require.main === module) {
  testSelfReflectionFixes()
    .then(() => {
      console.log('✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}
