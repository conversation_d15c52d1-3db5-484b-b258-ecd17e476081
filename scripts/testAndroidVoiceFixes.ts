/**
 * Test script for Android voice functionality fixes
 * This script helps validate that the Android microphone permission
 * and voice functionality is working correctly.
 */

import { Platform, Alert } from 'react-native';
import { AudioModule } from 'expo-audio';
import { androidPermissions } from '../utils/androidPermissions';

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class AndroidVoiceTestSuite {
  private results: TestResult[] = [];

  /**
   * Run all Android voice tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Android Voice Test Suite...');
    
    this.results = [];
    
    // Only run tests on Android
    if (Platform.OS !== 'android') {
      console.log('⚠️ Skipping Android tests - not running on Android platform');
      return [{
        testName: 'Platform Check',
        passed: false,
        error: 'Not running on Android platform'
      }];
    }

    await this.testPermissionManager();
    await this.testMicrophonePermissionCheck();
    await this.testMicrophonePermissionRequest();
    await this.testAudioSessionInitialization();
    await this.testAudioSessionCleanup();
    await this.testPermissionFlow();

    this.printResults();
    return this.results;
  }

  /**
   * Test 1: Permission Manager Initialization
   */
  private async testPermissionManager(): Promise<void> {
    try {
      console.log('🔍 Test 1: Permission Manager Initialization');
      
      const manager = androidPermissions;
      if (!manager) {
        throw new Error('Permission manager not initialized');
      }

      this.addResult('Permission Manager Initialization', true);
    } catch (error: any) {
      this.addResult('Permission Manager Initialization', false, error.message);
    }
  }

  /**
   * Test 2: Microphone Permission Check
   */
  private async testMicrophonePermissionCheck(): Promise<void> {
    try {
      console.log('🔍 Test 2: Microphone Permission Check');
      
      const hasPermission = await androidPermissions.checkMicrophonePermission();
      console.log('Current microphone permission status:', hasPermission);
      
      this.addResult('Microphone Permission Check', true, undefined, { hasPermission });
    } catch (error: any) {
      this.addResult('Microphone Permission Check', false, error.message);
    }
  }

  /**
   * Test 3: Microphone Permission Request
   */
  private async testMicrophonePermissionRequest(): Promise<void> {
    try {
      console.log('🔍 Test 3: Microphone Permission Request');
      
      const result = await androidPermissions.requestMicrophonePermission();
      console.log('Permission request result:', result);
      
      if (result.granted) {
        this.addResult('Microphone Permission Request', true, undefined, result);
      } else {
        this.addResult('Microphone Permission Request', false, result.error || 'Permission denied', result);
      }
    } catch (error: any) {
      this.addResult('Microphone Permission Request', false, error.message);
    }
  }

  /**
   * Test 4: Audio Session Initialization
   */
  private async testAudioSessionInitialization(): Promise<void> {
    try {
      console.log('🔍 Test 4: Audio Session Initialization');
      
      const initialized = await androidPermissions.initializeAudioSession();
      
      if (initialized) {
        this.addResult('Audio Session Initialization', true);
      } else {
        this.addResult('Audio Session Initialization', false, 'Failed to initialize audio session');
      }
    } catch (error: any) {
      this.addResult('Audio Session Initialization', false, error.message);
    }
  }

  /**
   * Test 5: Audio Session Cleanup
   */
  private async testAudioSessionCleanup(): Promise<void> {
    try {
      console.log('🔍 Test 5: Audio Session Cleanup');
      
      await androidPermissions.cleanupAudioSession();
      this.addResult('Audio Session Cleanup', true);
    } catch (error: any) {
      this.addResult('Audio Session Cleanup', false, error.message);
    }
  }

  /**
   * Test 6: Complete Permission Flow
   */
  private async testPermissionFlow(): Promise<void> {
    try {
      console.log('🔍 Test 6: Complete Permission Flow');
      
      // Note: This test doesn't actually show dialogs to avoid interrupting automated testing
      // It just tests the permission request logic
      const result = await androidPermissions.requestMicrophonePermission();
      
      if (result.granted) {
        this.addResult('Complete Permission Flow', true, undefined, result);
      } else {
        this.addResult('Complete Permission Flow', false, result.error || 'Permission flow failed', result);
      }
    } catch (error: any) {
      this.addResult('Complete Permission Flow', false, error.message);
    }
  }

  /**
   * Test 7: Expo Audio API Direct Test
   */
  private async testExpoAudioAPI(): Promise<void> {
    try {
      console.log('🔍 Test 7: Expo Audio API Direct Test');

      const status = await AudioModule.getRecordingPermissionsAsync();
      console.log('Expo Audio permission status:', status);

      this.addResult('Expo Audio API Direct Test', true, undefined, status);
    } catch (error: any) {
      this.addResult('Expo Audio API Direct Test', false, error.message);
    }
  }

  /**
   * Add test result
   */
  private addResult(testName: string, passed: boolean, error?: string, details?: any): void {
    this.results.push({
      testName,
      passed,
      error,
      details
    });
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('\n📊 Android Voice Test Results:');
    console.log('================================');
    
    let passedCount = 0;
    let totalCount = this.results.length;
    
    this.results.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${result.testName}: ${status}`);
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
      
      if (result.passed) {
        passedCount++;
      }
    });
    
    console.log('================================');
    console.log(`📈 Summary: ${passedCount}/${totalCount} tests passed`);
    
    if (passedCount === totalCount) {
      console.log('🎉 All tests passed! Android voice functionality should work correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the errors above.');
    }
  }

  /**
   * Show test results in an alert (for manual testing)
   */
  showResultsAlert(): void {
    const passedCount = this.results.filter(r => r.passed).length;
    const totalCount = this.results.length;
    
    const message = `Android Voice Tests: ${passedCount}/${totalCount} passed\n\n` +
      this.results.map(r => `${r.passed ? '✅' : '❌'} ${r.testName}`).join('\n');
    
    Alert.alert('Test Results', message, [{ text: 'OK' }]);
  }
}

// Export the test suite
export const androidVoiceTestSuite = new AndroidVoiceTestSuite();

// Helper function to run tests manually
export const runAndroidVoiceTests = async (): Promise<TestResult[]> => {
  return await androidVoiceTestSuite.runAllTests();
};

// Helper function to run tests and show alert
export const runAndroidVoiceTestsWithAlert = async (): Promise<void> => {
  await androidVoiceTestSuite.runAllTests();
  androidVoiceTestSuite.showResultsAlert();
};
