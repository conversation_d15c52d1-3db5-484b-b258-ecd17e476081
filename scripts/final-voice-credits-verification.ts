/**
 * Final Voice Credits System Verification
 * Comprehensive testing after all fixes have been applied
 */

import { supabase } from '../lib/supabase';
import { voiceCreditsService } from '../lib/voiceCreditsService';
import { VOICE_CREDITS_CONFIG, calculateCreditsFromDuration } from '../types/voiceCredits';

interface TestResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  details?: any;
}

class FinalVoiceCreditsVerifier {
  private results: TestResult[] = [];

  private addResult(category: string, test: string, status: 'PASS' | 'FAIL' | 'SKIP', message: string, details?: any) {
    this.results.push({ category, test, status, message, details });
    const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️';
    console.log(`${icon} [${category}] ${test}: ${message}`);
    if (details) {
      console.log('   Details:', JSON.stringify(details, null, 2));
    }
  }

  async verifyDatabaseFunction(): Promise<void> {
    console.log('\n🗄️ Verifying Database Function Fix...');

    try {
      // Test the updated get_user_voice_credits function
      const testUserId = '00000000-0000-0000-0000-000000000000';
      const { data, error } = await supabase
        .rpc('get_user_voice_credits', { target_user_id: testUserId });

      if (error) {
        this.addResult('Database', 'Function Call', 'FAIL', 'Function call failed', error);
        return;
      }

      // Check if the function returns the expected fields
      if (data && data.length > 0) {
        const result = data[0];
        const expectedFields = ['credits_remaining', 'credits_used', 'total_sessions', 'last_session', 'last_credit_reset'];
        const actualFields = Object.keys(result);
        
        const missingFields = expectedFields.filter(field => !actualFields.includes(field));
        
        if (missingFields.length === 0) {
          this.addResult('Database', 'Function Fields', 'PASS', 'All expected fields present', { fields: actualFields });
        } else {
          this.addResult('Database', 'Function Fields', 'FAIL', 'Missing fields', { missing: missingFields, actual: actualFields });
        }

        // Test last_credit_reset specifically
        if (result.last_credit_reset !== undefined) {
          this.addResult('Database', 'Last Credit Reset', 'PASS', 'last_credit_reset field is present');
        } else {
          this.addResult('Database', 'Last Credit Reset', 'FAIL', 'last_credit_reset field is missing');
        }
      } else {
        this.addResult('Database', 'Function Response', 'PASS', 'Function returns empty result for non-existent user (expected)');
      }

    } catch (error) {
      this.addResult('Database', 'Function Test', 'FAIL', 'Error testing database function', error);
    }
  }

  async verifyServiceLayer(): Promise<void> {
    console.log('\n⚙️ Verifying Service Layer...');

    try {
      // Test credit calculation
      const testCases = [
        { duration: 5, expected: 1 },
        { duration: 10, expected: 1 },
        { duration: 15, expected: 2 },
        { duration: 25, expected: 3 },
        { duration: 60, expected: 6 }
      ];

      let allPassed = true;
      for (const testCase of testCases) {
        const calculated = calculateCreditsFromDuration(testCase.duration);
        if (calculated !== testCase.expected) {
          allPassed = false;
          this.addResult('Service', 'Credit Calculation', 'FAIL', 
            `${testCase.duration}s should be ${testCase.expected} credits, got ${calculated}`);
          break;
        }
      }

      if (allPassed) {
        this.addResult('Service', 'Credit Calculation', 'PASS', 'All credit calculations correct', testCases);
      }

      // Test service instantiation
      if (voiceCreditsService) {
        this.addResult('Service', 'Service Instance', 'PASS', 'VoiceCreditsService instance available');
      } else {
        this.addResult('Service', 'Service Instance', 'FAIL', 'VoiceCreditsService instance not available');
      }

      // Test constants
      const configTests = [
        { name: 'INITIAL_CREDITS', value: VOICE_CREDITS_CONFIG.INITIAL_CREDITS, expected: 10 },
        { name: 'SECONDS_PER_CREDIT', value: VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT, expected: 10 },
        { name: 'MINIMUM_CREDIT_DEDUCTION', value: VOICE_CREDITS_CONFIG.MINIMUM_CREDIT_DEDUCTION, expected: 1 },
      ];

      const configPassed = configTests.every(test => test.value === test.expected);
      
      if (configPassed) {
        this.addResult('Service', 'Configuration', 'PASS', 'All configuration constants correct', configTests);
      } else {
        this.addResult('Service', 'Configuration', 'FAIL', 'Configuration constants incorrect', configTests);
      }

    } catch (error) {
      this.addResult('Service', 'Service Layer', 'FAIL', 'Error verifying service layer', error);
    }
  }

  async verifyImportFixes(): Promise<void> {
    console.log('\n📦 Verifying Import Fixes...');

    try {
      // Test if we can import all necessary modules without conflicts
      const { formatCreditsDisplay } = await import('../types/voiceCredits');
      
      if (typeof formatCreditsDisplay === 'function') {
        this.addResult('Imports', 'Types Import', 'PASS', 'formatCreditsDisplay function available from types');
        
        // Test the function
        const testResult = formatCreditsDisplay(5);
        if (typeof testResult === 'string' && testResult.includes('5 credits')) {
          this.addResult('Imports', 'Function Test', 'PASS', 'formatCreditsDisplay function works correctly', { result: testResult });
        } else {
          this.addResult('Imports', 'Function Test', 'FAIL', 'formatCreditsDisplay function not working', { result: testResult });
        }
      } else {
        this.addResult('Imports', 'Types Import', 'FAIL', 'formatCreditsDisplay not available from types');
      }

    } catch (error) {
      this.addResult('Imports', 'Import Test', 'FAIL', 'Error testing imports', error);
    }
  }

  async verifyInterfaceConsistency(): Promise<void> {
    console.log('\n🔍 Verifying Interface Consistency...');

    try {
      // Test with a mock user ID to see if service returns data matching interface
      const mockUserId = '12345678-1234-1234-1234-123456789012';
      
      try {
        const credits = await voiceCreditsService.getUserCredits(mockUserId);
        
        // Check if all required fields are present
        const requiredFields = ['creditsRemaining', 'creditsUsed', 'totalSessions', 'lastCreditReset'];
        const missingFields = requiredFields.filter(field => credits[field] === undefined);
        
        if (missingFields.length === 0) {
          this.addResult('Interface', 'Required Fields', 'PASS', 'All required fields present in service response', credits);
        } else {
          this.addResult('Interface', 'Required Fields', 'FAIL', 'Missing required fields', { missing: missingFields, actual: credits });
        }

        // Check field types
        const typeChecks = [
          { field: 'creditsRemaining', type: 'number', value: credits.creditsRemaining },
          { field: 'creditsUsed', type: 'number', value: credits.creditsUsed },
          { field: 'totalSessions', type: 'number', value: credits.totalSessions },
          { field: 'lastCreditReset', type: 'object', value: credits.lastCreditReset }, // Date object
        ];

        const typeErrors = typeChecks.filter(check => 
          check.type === 'object' ? !(check.value instanceof Date) : typeof check.value !== check.type
        );

        if (typeErrors.length === 0) {
          this.addResult('Interface', 'Field Types', 'PASS', 'All field types correct');
        } else {
          this.addResult('Interface', 'Field Types', 'FAIL', 'Incorrect field types', typeErrors);
        }

      } catch (serviceError) {
        this.addResult('Interface', 'Service Call', 'FAIL', 'Error calling service method', serviceError);
      }

    } catch (error) {
      this.addResult('Interface', 'Interface Test', 'FAIL', 'Error verifying interface consistency', error);
    }
  }

  async runAllTests(): Promise<TestResult[]> {
    console.log('🚀 Starting Final Voice Credits System Verification...\n');

    await this.verifyDatabaseFunction();
    await this.verifyServiceLayer();
    await this.verifyImportFixes();
    await this.verifyInterfaceConsistency();

    console.log('\n📋 Final Test Summary:');
    
    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category);
      const passed = categoryResults.filter(r => r.status === 'PASS').length;
      const failed = categoryResults.filter(r => r.status === 'FAIL').length;
      const skipped = categoryResults.filter(r => r.status === 'SKIP').length;
      
      console.log(`\n${category}:`);
      console.log(`  ✅ Passed: ${passed}`);
      console.log(`  ❌ Failed: ${failed}`);
      console.log(`  ⏭️ Skipped: ${skipped}`);
    });

    const totalPassed = this.results.filter(r => r.status === 'PASS').length;
    const totalFailed = this.results.filter(r => r.status === 'FAIL').length;
    const totalSkipped = this.results.filter(r => r.status === 'SKIP').length;

    console.log(`\n🎯 Overall Results:`);
    console.log(`✅ Total Passed: ${totalPassed}`);
    console.log(`❌ Total Failed: ${totalFailed}`);
    console.log(`⏭️ Total Skipped: ${totalSkipped}`);
    console.log(`📊 Total Tests: ${this.results.length}`);

    if (totalFailed === 0) {
      console.log('\n🎉 All tests passed! Voice Credits system is production-ready.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }

    return this.results;
  }
}

// Export for use in other files
export { FinalVoiceCreditsVerifier };

// If running directly
if (require.main === module) {
  const verifier = new FinalVoiceCreditsVerifier();
  verifier.runAllTests().catch(console.error);
}
