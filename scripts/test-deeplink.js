#!/usr/bin/env node

/**
 * Test script for deep linking functionality
 * Run this script to test if deep links are working properly
 */

const { execSync } = require('child_process');

const testUrls = [
  'temani://auth/callback?access_token=test&refresh_token=test',
  'temani://auth/callback?error=access_denied&error_description=User%20cancelled',
  'temani://reset-password',
];

function testDeepLink(url, platform = 'ios') {
  console.log(`\n🔗 Testing deep link: ${url}`);
  console.log(`📱 Platform: ${platform}`);
  
  try {
    const command = `npx uri-scheme open "${url}" --${platform}`;
    console.log(`⚡ Running: ${command}`);
    
    execSync(command, { stdio: 'inherit' });
    console.log('✅ Deep link test completed');
  } catch (error) {
    console.error('❌ Deep link test failed:', error.message);
  }
}

function main() {
  console.log('🚀 Temani Deep Link Test Script');
  console.log('================================');
  
  const platform = process.argv[2] || 'ios';
  
  if (!['ios', 'android'].includes(platform)) {
    console.error('❌ Invalid platform. Use "ios" or "android"');
    process.exit(1);
  }
  
  console.log(`\n📋 Testing ${testUrls.length} deep links on ${platform}...`);
  
  testUrls.forEach((url, index) => {
    console.log(`\n--- Test ${index + 1}/${testUrls.length} ---`);
    testDeepLink(url, platform);
    
    // Wait a bit between tests
    if (index < testUrls.length - 1) {
      console.log('⏳ Waiting 3 seconds before next test...');
      execSync('sleep 3');
    }
  });
  
  console.log('\n🎉 All deep link tests completed!');
  console.log('\n📝 Notes:');
  console.log('- Make sure your development build is installed');
  console.log('- Check the app logs for deep link handling');
  console.log('- Verify the callback screen appears correctly');
}

if (require.main === module) {
  main();
}

module.exports = { testDeepLink };
