#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files to update (excluding the ones we already updated)
const filesToUpdate = [
  'app/(tabs)/index.tsx',
  'app/(tabs)/mood.tsx',
  'app/(tabs)/journal.tsx',
  'app/(tabs)/chat.tsx',
  'app/(onboarding)/index.tsx',
  'app/(onboarding)/self-reflection.tsx',
  'app/(auth)/register.tsx',
  'app/(auth)/reset-password.tsx',
  'app/(auth)/forgot-password.tsx',
  'app/(auth)/callback.tsx',
  'app/(settings)/index.tsx',
  'app/(settings)/change-password.tsx',
  'components/journal/SimpleAIQuestionHelper.tsx',
  'components/AuthFlowTester.tsx',
  'components/SelfReflectionCheckIn.tsx',
  'components/DebugAuthStatus.tsx',
  'components/mood/MoodAnalyticsView.tsx',
  'components/VoiceCreditsVerification.tsx',
  'hooks/useVoiceTranscript.ts',
  'hooks/useUserProfile.ts',
  'hooks/useAIQuestionGenerator.ts',
  'hooks/useMoodQuery.ts',
  'hooks/useCustomFields.ts',
  'hooks/useJournal.ts',
  'hooks/useVoiceCredits.ts',
  'hooks/useJournalQuery.ts',
  'hooks/useJournalCalendar.ts'
];

function updateFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Replace the import statement
    const oldImport = "import { useAuth } from '@/context/AuthContext';";
    const newImport = "import { useUniversalAuth } from '@/hooks/useUniversalAuth';";
    
    if (content.includes(oldImport)) {
      content = content.replace(oldImport, newImport);
      
      // Replace all useAuth() calls with useUniversalAuth()
      content = content.replace(/useAuth\(\)/g, 'useUniversalAuth()');
      
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
}

console.log('🔄 Updating auth imports...\n');

filesToUpdate.forEach(updateFile);

console.log('\n✨ Auth import update complete!');
console.log('\nNote: Please review the changes and test the application.');
