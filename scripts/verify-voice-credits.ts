/**
 * Voice Credits System Verification Script
 * Comprehensive testing of the voice credits implementation
 */

import { supabase } from '../lib/supabase';
import { voiceCreditsService } from '../lib/voiceCreditsService';
import { VOICE_CREDITS_CONFIG } from '../types/voiceCredits';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  details?: any;
}

class VoiceCreditsVerifier {
  private results: TestResult[] = [];
  private testUserId: string | null = null;

  private addResult(test: string, status: 'PASS' | 'FAIL' | 'SKIP', message: string, details?: any) {
    this.results.push({ test, status, message, details });
    console.log(`${status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️'} ${test}: ${message}`);
    if (details) {
      console.log('   Details:', details);
    }
  }

  async verifyDatabaseSchema(): Promise<void> {
    console.log('\n🗄️ Verifying Database Schema...');

    try {
      // Check if voice_credits columns exist in user_profiles
      const { data: profileColumns, error: profileError } = await supabase
        .rpc('get_table_columns', { table_name: 'user_profiles' });

      if (profileError) {
        this.addResult('Database Schema', 'FAIL', 'Cannot query user_profiles columns', profileError);
        return;
      }

      const hasVoiceCredits = profileColumns?.some((col: any) => col.column_name === 'voice_credits');
      const hasVoiceCreditsUsed = profileColumns?.some((col: any) => col.column_name === 'voice_credits_used');
      const hasLastCreditReset = profileColumns?.some((col: any) => col.column_name === 'last_credit_reset');

      if (hasVoiceCredits && hasVoiceCreditsUsed && hasLastCreditReset) {
        this.addResult('User Profiles Schema', 'PASS', 'All voice credits columns exist');
      } else {
        this.addResult('User Profiles Schema', 'FAIL', 'Missing voice credits columns', {
          hasVoiceCredits,
          hasVoiceCreditsUsed,
          hasLastCreditReset
        });
      }

      // Check if voice_call_sessions table exists
      const { data: sessionTable, error: sessionError } = await supabase
        .from('voice_call_sessions')
        .select('id')
        .limit(1);

      if (sessionError && sessionError.code === '42P01') {
        this.addResult('Voice Sessions Table', 'FAIL', 'voice_call_sessions table does not exist');
      } else {
        this.addResult('Voice Sessions Table', 'PASS', 'voice_call_sessions table exists');
      }

    } catch (error) {
      this.addResult('Database Schema', 'FAIL', 'Error verifying schema', error);
    }
  }

  async verifyDatabaseFunctions(): Promise<void> {
    console.log('\n🔧 Verifying Database Functions...');

    try {
      // Test get_user_voice_credits function
      const { data, error } = await supabase
        .rpc('get_user_voice_credits', { target_user_id: '00000000-0000-0000-0000-000000000000' });

      if (error) {
        this.addResult('Database Functions', 'FAIL', 'get_user_voice_credits function error', error);
      } else {
        this.addResult('Database Functions', 'PASS', 'get_user_voice_credits function works');
      }
    } catch (error) {
      this.addResult('Database Functions', 'FAIL', 'Error testing database functions', error);
    }
  }

  async verifyServiceLayer(): Promise<void> {
    console.log('\n⚙️ Verifying Service Layer...');

    try {
      // Test credit calculation
      const testDuration = 25; // seconds
      const expectedCredits = Math.ceil(testDuration / VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT);
      
      if (expectedCredits === 3) {
        this.addResult('Credit Calculation', 'PASS', `25 seconds = ${expectedCredits} credits`);
      } else {
        this.addResult('Credit Calculation', 'FAIL', `Expected 3 credits, got ${expectedCredits}`);
      }

      // Test service instantiation
      const service = voiceCreditsService;
      if (service) {
        this.addResult('Service Instantiation', 'PASS', 'VoiceCreditsService instance created');
      } else {
        this.addResult('Service Instantiation', 'FAIL', 'Cannot create VoiceCreditsService instance');
      }

    } catch (error) {
      this.addResult('Service Layer', 'FAIL', 'Error verifying service layer', error);
    }
  }

  async verifyWithTestUser(): Promise<void> {
    console.log('\n👤 Testing with Mock User...');

    try {
      // Create a test user ID (not actually creating a user, just testing the service)
      this.testUserId = '12345678-1234-1234-1234-123456789012';

      // Test getUserCredits with non-existent user (should return defaults)
      const credits = await voiceCreditsService.getUserCredits(this.testUserId);
      
      if (credits.creditsRemaining === VOICE_CREDITS_CONFIG.INITIAL_CREDITS) {
        this.addResult('Default Credits', 'PASS', `New user gets ${VOICE_CREDITS_CONFIG.INITIAL_CREDITS} credits`);
      } else {
        this.addResult('Default Credits', 'FAIL', `Expected ${VOICE_CREDITS_CONFIG.INITIAL_CREDITS}, got ${credits.creditsRemaining}`);
      }

      // Test credit checking
      const creditCheck = await voiceCreditsService.checkCredits(this.testUserId);
      
      if (creditCheck.hasCredits && creditCheck.creditsRemaining === VOICE_CREDITS_CONFIG.INITIAL_CREDITS) {
        this.addResult('Credit Checking', 'PASS', 'Credit checking works correctly');
      } else {
        this.addResult('Credit Checking', 'FAIL', 'Credit checking failed', creditCheck);
      }

    } catch (error) {
      this.addResult('Test User Operations', 'FAIL', 'Error testing with mock user', error);
    }
  }

  async verifyConstants(): Promise<void> {
    console.log('\n📊 Verifying Constants...');

    const config = VOICE_CREDITS_CONFIG;
    
    if (config.INITIAL_CREDITS === 10) {
      this.addResult('Initial Credits', 'PASS', 'Initial credits set to 10');
    } else {
      this.addResult('Initial Credits', 'FAIL', `Expected 10, got ${config.INITIAL_CREDITS}`);
    }

    if (config.SECONDS_PER_CREDIT === 10) {
      this.addResult('Seconds Per Credit', 'PASS', 'Seconds per credit set to 10');
    } else {
      this.addResult('Seconds Per Credit', 'FAIL', `Expected 10, got ${config.SECONDS_PER_CREDIT}`);
    }

    if (config.MINIMUM_CREDIT_DEDUCTION === 1) {
      this.addResult('Minimum Deduction', 'PASS', 'Minimum credit deduction set to 1');
    } else {
      this.addResult('Minimum Deduction', 'FAIL', `Expected 1, got ${config.MINIMUM_CREDIT_DEDUCTION}`);
    }
  }

  async runAllTests(): Promise<TestResult[]> {
    console.log('🚀 Starting Voice Credits System Verification...\n');

    await this.verifyConstants();
    await this.verifyDatabaseSchema();
    await this.verifyDatabaseFunctions();
    await this.verifyServiceLayer();
    await this.verifyWithTestUser();

    console.log('\n📋 Test Summary:');
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const skipped = this.results.filter(r => r.status === 'SKIP').length;

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log(`📊 Total: ${this.results.length}`);

    if (failed === 0) {
      console.log('\n🎉 All tests passed! Voice Credits system is ready for production.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }

    return this.results;
  }
}

// Export for use in other files
export { VoiceCreditsVerifier };

// If running directly
if (require.main === module) {
  const verifier = new VoiceCreditsVerifier();
  verifier.runAllTests().catch(console.error);
}
