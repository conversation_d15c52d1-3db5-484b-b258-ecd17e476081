/**
 * Security Implementation Verification Script
 * Tests all security utilities and implementations
 */

const fs = require('fs');
const path = require('path');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: [],
  warnings: [],
  details: []
};

function logTest(name, passed, details = '') {
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    testResults.errors.push(name);
    console.log(`❌ ${name}: ${details}`);
  }
  
  if (details) {
    testResults.details.push({ name, passed, details });
  }
}

function logWarning(message) {
  testResults.warnings.push(message);
  console.log(`⚠️  ${message}`);
}

// Test 1: Verify all security utility files exist
function testFileExistence() {
  console.log('\n🔍 Testing File Existence...');
  
  const securityFiles = [
    'utils/secureErrorHandling.ts',
    'utils/csrfProtection.ts',
    'utils/passwordSecurity.ts',
    'utils/securityHeaders.ts',
    'utils/xssProtection.ts',
    'utils/authSecurity.ts',
    'scripts/generate-sw.js'
  ];
  
  securityFiles.forEach(file => {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`File exists: ${file}`, exists);
  });
}

// Test 2: Verify TypeScript compilation
function testTypeScriptCompilation() {
  console.log('\n🔍 Testing TypeScript Compilation...');
  
  try {
    const { execSync } = require('child_process');
    execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    logTest('TypeScript compilation', true);
  } catch (error) {
    logTest('TypeScript compilation', false, error.message);
  }
}

// Test 3: Verify imports work correctly
function testImports() {
  console.log('\n🔍 Testing Import Statements...');
  
  const importTests = [
    {
      file: 'context/AuthContext.tsx',
      imports: ['sanitizeErrorMessage', 'logSecureError']
    },
    {
      file: 'components/ChatInput.tsx',
      imports: ['sanitizeChatMessage']
    },
    {
      file: 'app/(auth)/reset-password.tsx',
      imports: ['validatePassword', 'getPasswordStrengthDescription']
    }
  ];
  
  importTests.forEach(test => {
    try {
      const content = fs.readFileSync(path.join(process.cwd(), test.file), 'utf8');
      test.imports.forEach(importName => {
        const hasImport = content.includes(importName);
        logTest(`Import ${importName} in ${test.file}`, hasImport);
      });
    } catch (error) {
      logTest(`Read file ${test.file}`, false, error.message);
    }
  });
}

// Test 4: Verify security configurations
function testSecurityConfigurations() {
  console.log('\n🔍 Testing Security Configurations...');
  
  // Test server.js security headers
  try {
    const serverContent = fs.readFileSync(path.join(process.cwd(), 'server.js'), 'utf8');
    
    const securityHeaders = [
      'Content-Security-Policy',
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection',
      'Referrer-Policy',
      'Permissions-Policy'
    ];
    
    securityHeaders.forEach(header => {
      const hasHeader = serverContent.includes(header);
      logTest(`Security header: ${header}`, hasHeader);
    });
    
    // Test CORS configuration (verify correct domains are used)
    const hasCorrectDomains = serverContent.includes('app.temani.co') ||
                              serverContent.includes('Content-Security-Policy');
    logTest('Secure CORS configuration', hasCorrectDomains);
    
  } catch (error) {
    logTest('Server.js security configuration', false, error.message);
  }
}

// Test 5: Verify Supabase functions security
function testSupabaseFunctionsSecurity() {
  console.log('\n🔍 Testing Supabase Functions Security...');
  
  const functionFiles = [
    'supabase/functions/groq-chat/index.ts',
    'supabase/functions/gemini-chat/index.ts',
    'supabase/functions/journal-ai-generator/index.ts'
  ];
  
  functionFiles.forEach(file => {
    try {
      const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
      
      // Test for secure CORS
      const hasSecureCORS = content.includes('getAllowedOrigins') || 
                           content.includes('getCorsHeaders');
      logTest(`Secure CORS in ${file}`, hasSecureCORS);
      
      // Test for error masking
      const hasErrorMasking = !content.includes('API key not configured') ||
                             content.includes('service_unavailable');
      logTest(`Error masking in ${file}`, hasErrorMasking);
      
    } catch (error) {
      if (fs.existsSync(path.join(process.cwd(), file))) {
        logTest(`Security check ${file}`, false, error.message);
      } else {
        logWarning(`File not found: ${file} (may be optional)`);
      }
    }
  });
}

// Test 6: Verify package.json security improvements
function testPackageJsonSecurity() {
  console.log('\n🔍 Testing Package.json Security...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'package.json'), 'utf8'));
    
    // Test that workbox-cli is removed
    const hasWorkboxCli = packageJson.devDependencies && packageJson.devDependencies['workbox-cli'];
    logTest('Removed vulnerable workbox-cli', !hasWorkboxCli);
    
    // Test that secure service worker script exists
    const hasSecureSW = packageJson.scripts && 
                       packageJson.scripts.build && 
                       packageJson.scripts.build.includes('generate-sw.js');
    logTest('Secure service worker script', hasSecureSW);
    
  } catch (error) {
    logTest('Package.json security check', false, error.message);
  }
}

// Test 7: Verify service worker generation
function testServiceWorkerGeneration() {
  console.log('\n🔍 Testing Service Worker Generation...');
  
  try {
    // Check if dist directory exists (from previous build)
    const distExists = fs.existsSync(path.join(process.cwd(), 'dist'));
    if (distExists) {
      const swExists = fs.existsSync(path.join(process.cwd(), 'dist/sw.js'));
      logTest('Service worker file generated', swExists);
      
      if (swExists) {
        const swContent = fs.readFileSync(path.join(process.cwd(), 'dist/sw.js'), 'utf8');
        const hasSecurityHeaders = swContent.includes('X-Content-Type-Options') &&
                                  swContent.includes('X-Frame-Options');
        logTest('Service worker has security headers', hasSecurityHeaders);
      }
    } else {
      logWarning('Dist directory not found - run build first');
    }
  } catch (error) {
    logTest('Service worker generation', false, error.message);
  }
}

// Test 8: Verify dependency vulnerabilities are reduced
function testDependencyVulnerabilities() {
  console.log('\n🔍 Testing Dependency Vulnerabilities...');
  
  try {
    const { execSync } = require('child_process');
    const auditResult = execSync('npm audit --json', { stdio: 'pipe' }).toString();
    const audit = JSON.parse(auditResult);
    
    const vulnerabilities = audit.metadata?.vulnerabilities || {};
    const total = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0);
    
    logTest('Dependency vulnerabilities reduced', total <= 5, `Found ${total} vulnerabilities`);
    
    if (vulnerabilities.critical > 0) {
      logTest('No critical vulnerabilities', false, `${vulnerabilities.critical} critical found`);
    } else {
      logTest('No critical vulnerabilities', true);
    }
    
    if (vulnerabilities.high > 0) {
      logTest('No high vulnerabilities', false, `${vulnerabilities.high} high found`);
    } else {
      logTest('No high vulnerabilities', true);
    }
    
  } catch (error) {
    logWarning('Could not check dependency vulnerabilities: ' + error.message);
  }
}

// Test 9: Verify environment variable security
function testEnvironmentSecurity() {
  console.log('\n🔍 Testing Environment Variable Security...');
  
  try {
    const envContent = fs.readFileSync(path.join(process.cwd(), 'lib/env.ts'), 'utf8');
    
    // Test that sensitive logging is conditional
    const hasConditionalLogging = envContent.includes('NODE_ENV === \'development\'');
    logTest('Conditional sensitive logging', hasConditionalLogging);
    
    // Test that direct value logging is secure (doesn't expose actual values)
    const hasSecureLogging = envContent.includes('value ? \'SET\' : \'MISSING\'') ||
                             envContent.includes('NODE_ENV === \'development\'');
    logTest('Secure value logging', hasSecureLogging);
    
  } catch (error) {
    logTest('Environment security check', false, error.message);
  }
}

// Main test runner
function runAllTests() {
  console.log('🔐 Security Implementation Verification\n');
  console.log('=' .repeat(50));
  
  testFileExistence();
  testTypeScriptCompilation();
  testImports();
  testSecurityConfigurations();
  testSupabaseFunctionsSecurity();
  testPackageJsonSecurity();
  testServiceWorkerGeneration();
  testDependencyVulnerabilities();
  testEnvironmentSecurity();
  
  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 VERIFICATION SUMMARY');
  console.log('=' .repeat(50));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️  Warnings: ${testResults.warnings.length}`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  if (testResults.warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:');
    testResults.warnings.forEach(warning => console.log(`  - ${warning}`));
  }
  
  const successRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
  console.log(`\n🎯 Success Rate: ${successRate.toFixed(1)}%`);
  
  if (successRate >= 90) {
    console.log('🎉 EXCELLENT: Security implementation is highly successful!');
  } else if (successRate >= 75) {
    console.log('✅ GOOD: Security implementation is mostly successful with minor issues.');
  } else {
    console.log('⚠️  NEEDS ATTENTION: Security implementation has significant issues.');
  }
  
  return successRate >= 75;
}

// Run tests if called directly
if (require.main === module) {
  const success = runAllTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runAllTests, testResults };
