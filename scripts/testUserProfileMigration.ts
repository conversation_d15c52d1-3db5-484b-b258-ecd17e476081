/**
 * Test script for User Profile Migration (High Priority Features)
 * Tests database migration, completion status, and user profiles
 */

import { UserProfileService } from '../lib/userProfileService';
import { SelfReflectionService } from '../lib/selfReflectionService';

export const testUserProfileMigration = async () => {
  console.log('🧪 Testing User Profile Migration (High Priority)...');

  try {
    // Test 1: UserProfileService import and error classes
    console.log('\n1. Testing UserProfileService imports...');
    
    try {
      // This should not throw import errors
      const service = UserProfileService;
      console.log('✅ UserProfileService imported successfully');
      
      // Test error class
      const { UserProfileError } = await import('../lib/userProfileService');
      const testError = new UserProfileError('Test error', 'TEST_CODE');
      if (testError.name === 'UserProfileError') {
        console.log('✅ UserProfileError class working');
      } else {
        console.log('❌ UserProfileError class failed');
      }
    } catch (error) {
      console.log('❌ UserProfileService import failed:', error.message);
    }

    // Test 2: Database functions (requires authentication)
    console.log('\n2. Testing database functions...');
    
    try {
      // Note: These will fail without proper authentication in test environment
      // But we can test the function calls don't throw import errors
      
      console.log('⚠️  Database function tests require authenticated user');
      console.log('   These should be tested in the actual app with real user authentication');
      
      // Test function signatures exist
      if (typeof UserProfileService.getCompletionStatus === 'function') {
        console.log('✅ getCompletionStatus function exists');
      }
      
      if (typeof UserProfileService.updateCompletionStatus === 'function') {
        console.log('✅ updateCompletionStatus function exists');
      }
      
      if (typeof UserProfileService.getOrCreateProfile === 'function') {
        console.log('✅ getOrCreateProfile function exists');
      }
      
    } catch (error) {
      console.log('❌ Database function test failed:', error.message);
    }

    // Test 3: SelfReflectionService enhancements
    console.log('\n3. Testing SelfReflectionService enhancements...');
    
    try {
      if (typeof SelfReflectionService.hasCompletedSelfReflection === 'function') {
        console.log('✅ hasCompletedSelfReflection function exists');
      }
      
      if (typeof SelfReflectionService.hasRecentSession === 'function') {
        console.log('✅ hasRecentSession function exists');
      }
      
    } catch (error) {
      console.log('❌ SelfReflectionService enhancement test failed:', error.message);
    }

    // Test 4: Type definitions
    console.log('\n4. Testing type definitions...');
    
    try {
      const { UserProfile, CompletionStatus } = await import('../types/userProfile');
      console.log('✅ UserProfile types imported successfully');
      
      // Test type structure
      const mockProfile: Partial<UserProfile> = {
        userId: 'test-user',
        onboardingCompleted: false,
        selfReflectionCompleted: false
      };
      
      const mockStatus: CompletionStatus = {
        onboardingCompleted: false,
        selfReflectionCompleted: false
      };
      
      console.log('✅ Type definitions working correctly');
      
    } catch (error) {
      console.log('❌ Type definition test failed:', error.message);
    }

    // Test 5: Migration logic simulation
    console.log('\n5. Testing migration logic simulation...');
    
    try {
      // Simulate the migration process (without actual database calls)
      const mockUserId = 'test-user-123';
      const mockLocalOnboarding = true;
      const mockLocalSelfReflection = false;
      
      console.log('📝 Simulating migration for user:', mockUserId);
      console.log('   Local onboarding completed:', mockLocalOnboarding);
      console.log('   Local self-reflection completed:', mockLocalSelfReflection);
      
      // This would normally call UserProfileService.migrateFromLocalStorage
      console.log('✅ Migration logic simulation completed');
      console.log('   In real app: would migrate local storage → database');
      
    } catch (error) {
      console.log('❌ Migration simulation failed:', error.message);
    }

    console.log('\n🎉 High Priority User Profile Migration tests completed!');
    console.log('\n📋 Next steps for real testing:');
    console.log('1. Apply database migration: npx supabase db push');
    console.log('2. Test with authenticated user in the app');
    console.log('3. Sign up new user → should go to self-reflection');
    console.log('4. Complete self-reflection → should update database');
    console.log('5. Sign out and sign back in → should remember completion');
    console.log('6. Check user_profiles table for data');

    console.log('\n🔍 Database verification queries:');
    console.log('-- Check if user_profiles table exists');
    console.log('SELECT * FROM user_profiles;');
    console.log('');
    console.log('-- Check if functions exist');
    console.log('SELECT * FROM pg_proc WHERE proname LIKE \'%user_profile%\';');
    console.log('');
    console.log('-- Test completion status for a user');
    console.log('SELECT has_completed_self_reflection(\'user-id-here\');');

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
};

// Helper function to test database connectivity (for manual testing)
export const testDatabaseConnectivity = async () => {
  console.log('🔌 Testing database connectivity...');
  
  try {
    const { supabase } = await import('../lib/supabase');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('question_sets')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Database connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
    
  } catch (error) {
    console.log('❌ Database connectivity test failed:', error);
    return false;
  }
};

// Helper function to test user profile functions (requires auth)
export const testUserProfileFunctions = async (userId: string) => {
  console.log(`🧪 Testing user profile functions for user: ${userId}`);
  
  try {
    // Test completion status
    const status = await UserProfileService.getCompletionStatus(userId);
    console.log('✅ Completion status retrieved:', status);
    
    // Test profile creation
    const profile = await UserProfileService.getOrCreateProfile(userId);
    console.log('✅ Profile retrieved/created:', {
      id: profile.id,
      onboardingCompleted: profile.onboardingCompleted,
      selfReflectionCompleted: profile.selfReflectionCompleted
    });
    
    return true;
    
  } catch (error) {
    console.log('❌ User profile function test failed:', error);
    return false;
  }
};

// Run if called directly
if (require.main === module) {
  testUserProfileMigration()
    .then(() => {
      console.log('✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}
