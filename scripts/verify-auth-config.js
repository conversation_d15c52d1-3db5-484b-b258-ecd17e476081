#!/usr/bin/env node

/**
 * Configuration verification script for Google Auth setup
 * This script checks if all necessary configurations are in place
 */

const fs = require('fs');
const path = require('path');

function checkFile(filePath, description) {
  const fullPath = path.join(process.cwd(), filePath);
  const exists = fs.existsSync(fullPath);
  
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  
  if (exists) {
    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      return { exists: true, content };
    } catch (error) {
      console.log(`   ⚠️  Could not read file: ${error.message}`);
      return { exists: true, content: null };
    }
  }
  
  return { exists: false, content: null };
}

function checkEnvironmentVariables() {
  console.log('\n📋 Environment Variables Check:');
  
  const envFile = checkFile('.env.local', 'Environment file');
  
  if (envFile.exists && envFile.content) {
    const hasSupabaseUrl = envFile.content.includes('EXPO_PUBLIC_SUPABASE_URL');
    const hasSupabaseKey = envFile.content.includes('EXPO_PUBLIC_SUPABASE_ANON_KEY');
    
    console.log(`${hasSupabaseUrl ? '✅' : '❌'} EXPO_PUBLIC_SUPABASE_URL defined`);
    console.log(`${hasSupabaseKey ? '✅' : '❌'} EXPO_PUBLIC_SUPABASE_ANON_KEY defined`);
    
    return hasSupabaseUrl && hasSupabaseKey;
  }
  
  return false;
}

function checkAppConfig() {
  console.log('\n📱 App Configuration Check:');
  
  const appConfig = checkFile('app.json', 'App configuration');
  
  if (appConfig.exists && appConfig.content) {
    try {
      const config = JSON.parse(appConfig.content);
      const expo = config.expo;
      
      const hasScheme = expo.scheme === 'temani';
      const hasIosBundleId = expo.ios?.bundleIdentifier === 'id.temani.app';
      const hasAndroidPackage = expo.android?.package === 'id.temani.app';
      const hasIosUrlTypes = expo.ios?.infoPlist?.CFBundleURLTypes?.length > 0;
      const hasAndroidIntentFilters = expo.android?.intentFilters?.length > 0;
      
      console.log(`${hasScheme ? '✅' : '❌'} URL scheme set to 'temani'`);
      console.log(`${hasIosBundleId ? '✅' : '❌'} iOS bundle identifier set`);
      console.log(`${hasAndroidPackage ? '✅' : '❌'} Android package name set`);
      console.log(`${hasIosUrlTypes ? '✅' : '❌'} iOS URL types configured`);
      console.log(`${hasAndroidIntentFilters ? '✅' : '❌'} Android intent filters configured`);
      
      return hasScheme && hasIosBundleId && hasAndroidPackage;
    } catch (error) {
      console.log(`   ❌ Could not parse app.json: ${error.message}`);
      return false;
    }
  }
  
  return false;
}

function checkAuthContext() {
  console.log('\n🔐 Auth Context Check:');

  const authContext = checkFile('context/AuthContext.tsx', 'Auth context');

  if (authContext.exists && authContext.content) {
    const hasCorrectScheme = authContext.content.includes('temani://auth/callback');
    const hasResetScheme = authContext.content.includes('temani://reset-password');
    const hasGoogleSignIn = authContext.content.includes('signInWithGoogle');
    const hasAppStateHandling = authContext.content.includes('AppState.addEventListener');
    const hasWebBrowserOptimization = authContext.content.includes('WebBrowser.warmUpAsync');

    console.log(`${hasCorrectScheme ? '✅' : '❌'} OAuth callback URL uses 'temani' scheme`);
    console.log(`${hasResetScheme ? '✅' : '❌'} Password reset URL uses 'temani' scheme`);
    console.log(`${hasGoogleSignIn ? '✅' : '❌'} Google sign-in method exists`);
    console.log(`${hasAppStateHandling ? '✅' : '❌'} AppState handling for token refresh`);
    console.log(`${hasWebBrowserOptimization ? '✅' : '❌'} WebBrowser optimization for Android`);

    return hasCorrectScheme && hasResetScheme && hasGoogleSignIn;
  }

  return false;
}

function checkCallbackRoute() {
  console.log('\n🔄 OAuth Callback Route Check:');
  
  const callbackRoute = checkFile('app/(auth)/callback.tsx', 'OAuth callback route');
  
  if (callbackRoute.exists && callbackRoute.content) {
    const hasTokenHandling = callbackRoute.content.includes('access_token');
    const hasErrorHandling = callbackRoute.content.includes('error');
    const hasSupabaseSession = callbackRoute.content.includes('setSession');
    
    console.log(`${hasTokenHandling ? '✅' : '❌'} Token handling implemented`);
    console.log(`${hasErrorHandling ? '✅' : '❌'} Error handling implemented`);
    console.log(`${hasSupabaseSession ? '✅' : '❌'} Supabase session handling`);
    
    return hasTokenHandling && hasErrorHandling && hasSupabaseSession;
  }
  
  return false;
}

function checkEasConfig() {
  console.log('\n🏗️  EAS Configuration Check:');
  
  const easConfig = checkFile('eas.json', 'EAS configuration');
  
  if (easConfig.exists && easConfig.content) {
    try {
      const config = JSON.parse(easConfig.content);
      const hasDevelopmentProfile = config.build?.development?.developmentClient === true;
      const hasIosBundleId = config.build?.development?.ios?.bundleIdentifier;
      const hasAndroidPackage = config.build?.development?.android?.package;
      
      console.log(`${hasDevelopmentProfile ? '✅' : '❌'} Development client profile configured`);
      console.log(`${hasIosBundleId ? '✅' : '❌'} iOS bundle identifier in EAS config`);
      console.log(`${hasAndroidPackage ? '✅' : '❌'} Android package in EAS config`);
      
      return hasDevelopmentProfile;
    } catch (error) {
      console.log(`   ❌ Could not parse eas.json: ${error.message}`);
      return false;
    }
  }
  
  return false;
}

function main() {
  console.log('🔍 Temani Google Auth Configuration Verification');
  console.log('===============================================');
  
  function checkSupabaseConfig() {
    console.log('\n🗄️  Supabase Configuration Check:');

    const supabaseConfig = checkFile('lib/supabase.ts', 'Supabase configuration');

    if (supabaseConfig.exists && supabaseConfig.content) {
      const hasAsyncStorage = supabaseConfig.content.includes('AsyncStorage');
      const hasUrlPolyfill = supabaseConfig.content.includes('react-native-url-polyfill');
      const hasDetectSessionFalse = supabaseConfig.content.includes('detectSessionInUrl: false');

      console.log(`${hasAsyncStorage ? '✅' : '❌'} AsyncStorage for session persistence`);
      console.log(`${hasUrlPolyfill ? '✅' : '❌'} URL polyfill for React Native`);
      console.log(`${hasDetectSessionFalse ? '✅' : '❌'} detectSessionInUrl set to false`);

      return hasAsyncStorage && hasUrlPolyfill && hasDetectSessionFalse;
    }

    return false;
  }

  const checks = [
    checkEnvironmentVariables(),
    checkAppConfig(),
    checkAuthContext(),
    checkCallbackRoute(),
    checkEasConfig(),
    checkSupabaseConfig(),
  ];
  
  const passedChecks = checks.filter(Boolean).length;
  const totalChecks = checks.length;
  
  console.log('\n📊 Summary:');
  console.log(`${passedChecks}/${totalChecks} configuration checks passed`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 All configurations look good!');
    console.log('\n📝 Next steps:');
    console.log('1. Create a development build: npm run build:dev:ios or npm run build:dev:android');
    console.log('2. Install the development build on your device');
    console.log('3. Test Google OAuth flow');
    console.log('4. Use npm run test:deeplink:ios to test deep linking');
  } else {
    console.log('⚠️  Some configurations need attention. Please review the failed checks above.');
  }
  
  console.log('\n📚 For detailed setup instructions, see: docs/GOOGLE_AUTH_SETUP.md');
}

if (require.main === module) {
  main();
}
