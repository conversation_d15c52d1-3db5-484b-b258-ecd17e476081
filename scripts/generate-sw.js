/**
 * Secure Service Worker Generator
 * Replaces workbox-cli to avoid dependency vulnerabilities
 */

const fs = require('fs');
const path = require('path');

const SW_TEMPLATE = `
// Secure Service Worker for Temani PWA
const CACHE_NAME = 'temani-v1';
const STATIC_CACHE = 'temani-static-v1';

// Security headers for cached responses
const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin'
};

// Files to cache
const STATIC_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/logo192.png',
  '/logo512.png'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => cache.addAll(STATIC_FILES))
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache with security headers
self.addEventListener('fetch', (event) => {
  // Only handle GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip non-HTTP requests
  if (!event.request.url.startsWith('http')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          // Add security headers to cached response
          const newHeaders = new Headers(response.headers);
          Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
            newHeaders.set(key, value);
          });
          
          return new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: newHeaders
          });
        }

        // Fetch from network with timeout
        return Promise.race([
          fetch(event.request),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Network timeout')), 10000)
          )
        ]).then((response) => {
          // Cache successful responses
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then((cache) => cache.put(event.request, responseClone));
          }
          return response;
        }).catch(() => {
          // Return offline page for navigation requests
          if (event.request.mode === 'navigate') {
            return caches.match('/offline.html');
          }
          throw new Error('Network error and no cache available');
        });
      })
  );
});

// Security: Prevent unauthorized access to service worker
self.addEventListener('message', (event) => {
  // Only respond to messages from same origin
  if (event.origin !== self.location.origin) {
    return;
  }
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
`;

function generateServiceWorker() {
  try {
    const distPath = path.join(process.cwd(), 'dist');
    const swPath = path.join(distPath, 'sw.js');
    
    // Ensure dist directory exists
    if (!fs.existsSync(distPath)) {
      console.error('❌ dist directory not found. Run expo export first.');
      process.exit(1);
    }
    
    // Write service worker
    fs.writeFileSync(swPath, SW_TEMPLATE.trim());
    
    console.log('✅ Secure service worker generated successfully');
    console.log(`📁 Location: ${swPath}`);
    
    // Generate service worker map for debugging
    const mapPath = path.join(distPath, 'sw.js.map');
    const sourceMap = {
      version: 3,
      file: 'sw.js',
      sources: ['../scripts/generate-sw.js'],
      names: [],
      mappings: ''
    };
    fs.writeFileSync(mapPath, JSON.stringify(sourceMap, null, 2));
    
    console.log('✅ Service worker source map generated');
    
  } catch (error) {
    console.error('❌ Failed to generate service worker:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  generateServiceWorker();
}

module.exports = { generateServiceWorker };
