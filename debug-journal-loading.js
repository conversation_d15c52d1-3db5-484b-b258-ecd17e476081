// Debug script to test journal history loading
// Run this in the browser console to test the journal loading

console.log('🔍 Starting Journal History Loading Debug...');

// Test 1: Check if user is authenticated
const checkAuth = () => {
  console.log('1. Checking authentication...');
  const user = window.localStorage.getItem('sb-vpochaefgqqosnibwyys-auth-token');
  if (user) {
    try {
      const parsed = JSON.parse(user);
      console.log('✅ User authenticated:', parsed.user?.id);
      return parsed.user?.id;
    } catch (e) {
      console.log('❌ Error parsing auth token:', e);
      return null;
    }
  } else {
    console.log('❌ No auth token found');
    return null;
  }
};

// Test 2: Direct Supabase query test
const testSupabaseQuery = async (userId) => {
  console.log('2. Testing direct Supabase query...');
  
  try {
    // This would need to be adapted based on your Supabase client setup
    console.log('📝 Would query journal_entries for user:', userId);
    console.log('📝 Query: SELECT * FROM journal_entries WHERE user_id = ?');
    console.log('📝 Check browser Network tab for actual requests');
  } catch (error) {
    console.error('❌ Supabase query error:', error);
  }
};

// Test 3: Check for React component state
const checkReactState = () => {
  console.log('3. Checking React component state...');
  
  // Look for loading indicators
  const loadingElements = document.querySelectorAll('[data-testid*="loading"], .loading, [class*="loading"]');
  console.log('🔄 Loading elements found:', loadingElements.length);
  
  // Look for error messages
  const errorElements = document.querySelectorAll('[data-testid*="error"], .error, [class*="error"]');
  console.log('❌ Error elements found:', errorElements.length);
  
  // Look for journal content
  const journalElements = document.querySelectorAll('[data-testid*="journal"], [class*="journal"], [class*="history"]');
  console.log('📖 Journal elements found:', journalElements.length);
};

// Test 4: Monitor console logs
const monitorLogs = () => {
  console.log('4. Monitoring console logs for journal-related messages...');
  console.log('👀 Watch for:');
  console.log('   - [useJournalHistory] messages');
  console.log('   - [JournalService] messages');
  console.log('   - [useLoadingTimeout] messages');
  console.log('   - Any error messages');
};

// Run all tests
const runDebug = async () => {
  console.log('🚀 Running Journal History Debug Tests...');
  console.log('=====================================');
  
  const userId = checkAuth();
  if (userId) {
    await testSupabaseQuery(userId);
  }
  
  checkReactState();
  monitorLogs();
  
  console.log('=====================================');
  console.log('✅ Debug tests completed!');
  console.log('💡 Next steps:');
  console.log('   1. Navigate to journal history tab');
  console.log('   2. Watch console for new messages');
  console.log('   3. Check Network tab for API requests');
  console.log('   4. Look for any error messages');
};

// Auto-run the debug
runDebug();

// Export for manual testing
window.debugJournalLoading = {
  checkAuth,
  testSupabaseQuery,
  checkReactState,
  monitorLogs,
  runDebug
};
