[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "22"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  NODE_ENV = "production"
  # Add production environment variables here if needed
  # EXPO_PUBLIC_SUPABASE_URL = "your-production-supabase-url"
  # EXPO_PUBLIC_SUPABASE_ANON_KEY = "your-production-supabase-key"

[context.deploy-preview.environment]
  NODE_ENV = "staging"