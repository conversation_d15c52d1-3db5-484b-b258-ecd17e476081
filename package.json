{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "dev:client": "EXPO_NO_TELEMETRY=1 expo start --dev-client", "build": "expo export --platform web && node scripts/generate-sw.js", "build:web": "expo export --platform web && node scripts/generate-sw.js", "build:dev:ios": "eas build --profile development --platform ios", "build:dev:android": "eas build --profile development --platform android", "run:ios": "expo run:ios", "run:android": "expo run:android", "test:deeplink:ios": "node scripts/test-deeplink.js ios", "test:deeplink:android": "node scripts/test-deeplink.js android", "verify:auth": "node scripts/verify-auth-config.js", "install:deps": "npx expo install @react-native-async-storage/async-storage expo-auth-session", "lint": "npx expo lint", "start": "npx serve dist -s -l ${PORT:-8081}", "start:server": "node server.js", "start:dev": "npx expo start --no-dev --minify --web", "start:tunnel": "npx expo start --tunnel", "prebuild": "npx expo prebuild --clean", "ios:device": "npx expo run:ios --device", "android:device": "npx expo run:android --device", "android": "expo run:android", "ios": "expo run:ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@auth/core": "^0.37.0", "@convex-dev/auth": "^0.0.87", "@elevenlabs/client": "^0.2.0", "@elevenlabs/react": "^0.2.0", "@expo/vector-icons": "^14.0.4", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@sentry/react-native": "^6.17.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-query-persist-client": "^5.83.0", "babel-plugin-module-resolver": "^5.0.2", "convex": "^1.25.4", "dompurify": "^3.2.0", "expo": "53.0.15", "expo-audio": "~0.4.8", "expo-auth-session": "~6.0.3", "expo-blur": "^14.0.3", "expo-clipboard": "~7.1.5", "expo-constants": "^17.0.5", "expo-dev-client": "~5.2.2", "expo-font": "^13.0.3", "expo-haptics": "^14.0.1", "expo-linear-gradient": "^14.0.2", "expo-linking": "^7.0.5", "expo-router": "^5.1.2", "expo-secure-store": "~14.2.3", "expo-speech-recognition": "^2.1.1", "expo-splash-screen": "~0.30.9", "expo-status-bar": "^2.0.1", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "^14.0.2", "express": "^4.19.2", "groq-sdk": "^0.7.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.4.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "serve": "^14.2.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/config-plugins": "~10.0.0", "@expo/prebuild-config": "~9.0.0", "@react-native-async-storage/async-storage": "^2.2.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "eslint": "^9.0.0", "eslint-config-expo": "^9.2.0", "jest": "^30.0.4", "metro": "^0.82.0", "metro-config": "^0.82.0", "metro-resolver": "^0.82.0", "react-test-renderer": "^19.0.0", "ts-jest": "^29.4.0", "typescript": "^5.3.3", "uuid": "^11.1.0", "workbox-webpack-plugin": "^7.3.0"}}