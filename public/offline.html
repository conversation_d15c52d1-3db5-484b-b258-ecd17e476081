<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temani - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #87C4E5 0%, #4AACF4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #1F2937;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #87C4E5, #4AACF4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 36px;
            color: white;
        }

        h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            color: #1F2937;
        }

        .subtitle {
            font-size: 18px;
            color: #6B7280;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .crisis-section {
            background: #FEF2F2;
            border: 2px solid #FECACA;
            border-radius: 12px;
            padding: 24px;
            margin: 32px 0;
            text-align: left;
        }

        .crisis-title {
            font-size: 20px;
            font-weight: 600;
            color: #DC2626;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .crisis-contacts {
            list-style: none;
            margin: 16px 0;
        }

        .crisis-contacts li {
            margin: 8px 0;
            padding: 12px;
            background: white;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contact-name {
            font-weight: 600;
            color: #1F2937;
        }

        .contact-number {
            color: #DC2626;
            font-weight: 600;
            text-decoration: none;
        }

        .breathing-section {
            background: #F0F9FF;
            border: 2px solid #BAE6FD;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }

        .breathing-title {
            font-size: 18px;
            font-weight: 600;
            color: #0369A1;
            margin-bottom: 16px;
        }

        .breathing-exercise {
            text-align: center;
            padding: 20px;
        }

        .breathing-circle {
            width: 120px;
            height: 120px;
            border: 4px solid #0369A1;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
            color: #0369A1;
            animation: breathe 4s infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .breathing-instructions {
            color: #0369A1;
            font-size: 16px;
            line-height: 1.5;
        }

        .tips-section {
            background: #F9FAFB;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
        }

        .tips-title {
            font-size: 18px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
        }

        .tips-list {
            list-style: none;
        }

        .tips-list li {
            margin: 12px 0;
            padding-left: 24px;
            position: relative;
            color: #4B5563;
            line-height: 1.5;
        }

        .tips-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10B981;
            font-weight: bold;
        }

        .retry-button {
            background: linear-gradient(135deg, #87C4E5, #4AACF4);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 24px;
            transition: transform 0.2s;
        }

        .retry-button:hover {
            transform: translateY(-2px);
        }

        .status {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }

        .status.offline {
            background: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }

        .status.online {
            background: #F0FDF4;
            color: #16A34A;
            border: 1px solid #BBF7D0;
        }

        @media (max-width: 480px) {
            .container {
                padding: 24px;
                margin: 10px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .subtitle {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            🌙
        </div>
        
        <h1>Temani Offline</h1>
        <p class="subtitle">
            Anda sedang offline, tetapi Temani tetap di sini untuk Anda. 
            Berikut beberapa sumber daya yang dapat membantu.
        </p>

        <!-- Crisis Support Section -->
        <div class="crisis-section">
            <div class="crisis-title">
                🚨 Bantuan Darurat
            </div>
            <p style="color: #7F1D1D; margin-bottom: 16px;">
                Jika Anda mengalami krisis atau membutuhkan bantuan segera:
            </p>
            <ul class="crisis-contacts">
                <li>
                    <span class="contact-name">Hotline Nasional</span>
                    <a href="tel:119" class="contact-number">119</a>
                </li>
                <li>
                    <span class="contact-name">Halo Kemkes</span>
                    <a href="tel:1500567" class="contact-number">1500-567</a>
                </li>
                <li>
                    <span class="contact-name">LSM Jangan Bunuh Diri</span>
                    <a href="tel:+6281389854754" class="contact-number">0813-8985-4754</a>
                </li>
            </ul>
        </div>

        <!-- Breathing Exercise Section -->
        <div class="breathing-section">
            <div class="breathing-title">🫁 Latihan Pernapasan</div>
            <div class="breathing-exercise">
                <div class="breathing-circle">
                    Bernapas
                </div>
                <div class="breathing-instructions">
                    Ikuti lingkaran yang bergerak:<br>
                    Tarik napas saat membesar<br>
                    Buang napas saat mengecil
                </div>
            </div>
        </div>

        <!-- Coping Tips Section -->
        <div class="tips-section">
            <div class="tips-title">💡 Tips Mengatasi Stres</div>
            <ul class="tips-list">
                <li>Ambil napas dalam-dalam selama 5 menit</li>
                <li>Tuliskan 3 hal yang Anda syukuri hari ini</li>
                <li>Lakukan peregangan ringan atau jalan di tempat</li>
                <li>Dengarkan musik yang menenangkan</li>
                <li>Hubungi teman atau keluarga yang Anda percayai</li>
                <li>Minum air putih dan istirahat sejenak</li>
            </ul>
        </div>

        <button class="retry-button" onclick="checkConnection()">
            Coba Sambung Kembali
        </button>

        <div id="status" class="status offline">
            📡 Tidak ada koneksi internet
        </div>
    </div>

    <script>
        function checkConnection() {
            if (navigator.onLine) {
                document.getElementById('status').className = 'status online';
                document.getElementById('status').innerHTML = '✅ Terhubung! Memuat ulang...';
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                document.getElementById('status').className = 'status offline';
                document.getElementById('status').innerHTML = '📡 Masih tidak ada koneksi';
            }
        }

        // Auto-check connection every 30 seconds
        setInterval(checkConnection, 30000);

        // Listen for online event
        window.addEventListener('online', () => {
            document.getElementById('status').className = 'status online';
            document.getElementById('status').innerHTML = '✅ Terhubung! Memuat ulang...';
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        window.addEventListener('offline', () => {
            document.getElementById('status').className = 'status offline';
            document.getElementById('status').innerHTML = '📡 Koneksi terputus';
        });
    </script>
</body>
</html>
