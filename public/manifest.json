{"short_name": "<PERSON><PERSON>", "name": "Temani - AI Companion un<PERSON><PERSON>", "icons": [{"src": "favicon.ico", "sizes": "64x64 32x32 24x24 16x16", "type": "image/x-icon"}, {"src": "logo192.png", "type": "image/png", "sizes": "192x192", "purpose": "any maskable"}, {"src": "logo512.png", "type": "image/png", "sizes": "512x512", "purpose": "any maskable"}], "start_url": "/", "display": "standalone", "orientation": "portrait", "theme_color": "#2B7EFF", "background_color": "#ffffff", "description": "AI companion untuk dukungan emosional dan kesehatan mental remaja Indonesia. Fitur chat AI, self-reflection, dan journal untuk kesejahteraan mental.", "categories": ["health", "lifestyle", "medical", "wellness"], "lang": "id", "dir": "ltr", "scope": "/", "shortcuts": [{"name": "<PERSON><PERSON>", "short_name": "Cha<PERSON>", "description": "<PERSON><PERSON> dengan AI companion <PERSON><PERSON>", "url": "/(tabs)/chat", "icons": [{"src": "logo192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "Self Reflection", "short_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Lakukan check-in harian untuk kesehatan mental", "url": "/(tabs)/self-reflection", "icons": [{"src": "logo192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "Journal", "short_name": "Jurnal", "description": "<PERSON><PERSON> dalam jurnal pribadi Anda", "url": "/(tabs)/journal", "icons": [{"src": "logo192.png", "sizes": "192x192", "type": "image/png"}]}]}