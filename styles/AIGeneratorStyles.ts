import { StyleSheet, Platform } from 'react-native';

export const aiGeneratorStyles = StyleSheet.create({
  // Main container
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    ...Platform.select({
      web: {
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 5,
      },
    }),
  },

  // Header styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },

  headerIcon: {
    marginRight: 12,
  },

  headerText: {
    flex: 1,
  },

  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
  },

  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
  },

  // Generation type selector
  typeSelector: {
    marginBottom: 20,
  },

  typeSelectorLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },

  typeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },

  typeOption: {
    flex: 1,
    minWidth: '30%',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 12,
    margin: 4,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },

  typeOptionSelected: {
    backgroundColor: '#FFF8FC',
    borderColor: '#DD76B9',
  },

  typeOptionIcon: {
    marginBottom: 6,
  },

  typeOptionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
    textAlign: 'center',
  },

  typeOptionTextSelected: {
    color: '#DD76B9',
  },

  typeOptionDescription: {
    fontSize: 10,
    color: '#999999',
    textAlign: 'center',
    marginTop: 2,
  },

  // Context input
  contextSection: {
    marginBottom: 20,
  },

  contextLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },

  contextInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 12,
    fontSize: 14,
    color: '#333333',
    backgroundColor: '#FFFFFF',
    minHeight: 60,
    textAlignVertical: 'top',
  },

  contextInputFocused: {
    borderColor: '#DD76B9',
    backgroundColor: '#FFF8FC',
  },

  contextHint: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
  },

  // Rate limit indicator
  rateLimitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },

  rateLimitWarning: {
    backgroundColor: '#FFF3CD',
  },

  rateLimitError: {
    backgroundColor: '#F8D7DA',
  },

  rateLimitText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 8,
    flex: 1,
  },

  rateLimitWarningText: {
    color: '#856404',
  },

  rateLimitErrorText: {
    color: '#721C24',
  },

  // Generate button
  generateButton: {
    backgroundColor: '#DD76B9',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 8px rgba(221, 118, 185, 0.3)',
      },
      default: {
        shadowColor: '#DD76B9',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 4,
      },
    }),
  },

  generateButtonDisabled: {
    backgroundColor: '#E0E0E0',
    ...Platform.select({
      web: {
        boxShadow: 'none',
      },
      default: {
        shadowOpacity: 0,
        elevation: 0,
      },
    }),
  },

  generateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  generateButtonTextDisabled: {
    color: '#999999',
  },

  // Loading state
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },

  loadingText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 12,
  },

  // Generated questions
  questionsContainer: {
    marginTop: 20,
  },

  questionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },

  questionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },

  regenerateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
  },

  regenerateButtonText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },

  // Question item
  questionItem: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },

  questionItemSelected: {
    backgroundColor: '#FFF8FC',
    borderColor: '#DD76B9',
  },

  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },

  questionText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
    flex: 1,
    marginRight: 12,
  },

  questionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  questionActionButton: {
    padding: 6,
    marginLeft: 4,
    borderRadius: 6,
  },

  // Question preview
  questionPreview: {
    backgroundColor: '#E8F5E8',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },

  questionPreviewText: {
    fontSize: 12,
    color: '#2D5A2D',
    fontStyle: 'italic',
  },

  // Action buttons
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },

  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },

  actionButtonPrimary: {
    backgroundColor: '#DD76B9',
  },

  actionButtonSecondary: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },

  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },

  actionButtonTextPrimary: {
    color: '#FFFFFF',
  },

  actionButtonTextSecondary: {
    color: '#666666',
  },

  // Empty state
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },

  emptyStateIcon: {
    marginBottom: 12,
  },

  emptyStateText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },

  // Error state
  errorContainer: {
    backgroundColor: '#F8D7DA',
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  errorText: {
    fontSize: 12,
    color: '#721C24',
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },

  // Success state
  successContainer: {
    backgroundColor: '#D4EDDA',
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },

  successText: {
    fontSize: 12,
    color: '#155724',
    marginLeft: 8,
    flex: 1,
  },

  // Modal styles (for full-screen AI generator)
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60, // Account for status bar
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },

  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
  },

  modalCloseButton: {
    padding: 8,
  },

  modalContent: {
    flex: 1,
    padding: 20,
  },

  // Base question display
  baseQuestionContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#DD76B9',
  },

  baseQuestionLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  baseQuestionText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
});
