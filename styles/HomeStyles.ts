import { StyleSheet, Platform } from 'react-native';

export const homeStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingBottom: 40,
    paddingHorizontal: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginRight: 16,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    ...Platform.select({
      web: {
        boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.15)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      },
    }),
    elevation: 4,
  },
  userDetails: {
    flex: 1,
  },
  welcomeText: {
    color: 'rgba(255, 255, 255, 0.85)',
    fontSize: 15,
    marginBottom: 4,
    fontWeight: '500',
  },
  userName: {
    color: '#fff',
    fontSize: 22,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  signOutButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 14,
    padding: 14,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  content: {
    padding: 24,
  },
  profileCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    marginBottom: 28,
    ...Platform.select({
      web: {
        boxShadow: '0px 8px 16px rgba(0, 0, 0, 0.08)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 8,
        },
        shadowOpacity: 0.08,
        shadowRadius: 16,
      },
    }),
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.02)',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A202C',
    marginBottom: 20,
    letterSpacing: 0.2,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  profileItemContent: {
    marginLeft: 16,
    flex: 1,
  },
  profileItemLabel: {
    fontSize: 13,
    color: '#64748B',
    marginBottom: 4,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  profileItemValue: {
    fontSize: 16,
    color: '#1A202C',
    fontWeight: '600',
    lineHeight: 22,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 28,
  },
  featureCard: {
    backgroundColor: '#fff',
    borderRadius: 18,
    padding: 20,
    width: '48%',
    marginBottom: 16,
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.06)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.06,
        shadowRadius: 12,
      },
    }),
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.02)',
  },
  featureImage: {
    width: '100%',
    height: 90,
    borderRadius: 14,
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1A202C',
    marginBottom: 6,
    letterSpacing: 0.2,
  },
  featureDescription: {
    fontSize: 13,
    color: '#64748B',
    lineHeight: 18,
    fontWeight: '500',
  },
  quoteCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    borderLeftWidth: 5,
    borderLeftColor: '#8EC8FF',
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.06)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.06,
        shadowRadius: 12,
      },
    }),
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.02)',
  },
  quoteText: {
    fontSize: 17,
    color: '#2D3748',
    fontStyle: 'italic',
    lineHeight: 26,
    marginBottom: 12,
    fontWeight: '500',
  },
  quoteAuthor: {
    fontSize: 15,
    color: '#2B7EFF',
    fontWeight: '700',
    letterSpacing: 0.3,
  },
});