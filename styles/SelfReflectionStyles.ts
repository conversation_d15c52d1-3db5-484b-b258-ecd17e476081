/**
 * Self-Reflection Check-In Styles
 * Following user preferences: corner radius 10, gradient fills, specific colors
 */

import { StyleSheet, Platform, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const selfReflectionStyles = StyleSheet.create({
  // Main Container Styles
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Let background image show through
  },

  safeArea: {
    flex: 1,
    backgroundColor: '#FEFEFE',
  },

  // Background Styles (same as onboarding)
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },

  backgroundImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },

  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },

  // Header Styles (Match Tabs)
  headerContainer: {
    paddingHorizontal: 24,  // Match tabs
    paddingTop: 24,         // Match tabs
    paddingBottom: 16,      // Match tabs
    alignItems: 'center',
  },

  headerTitle: {
    fontSize: 18,           // Match tabs size
    fontWeight: '600',      // Match tabs weight
    color: '#1C1C1E',       // Match tabs color
    textAlign: 'center',
  },

  // Progress Indicator (Green & Visible)
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,        // Reduce from 30
    paddingHorizontal: 24,   // Match tabs padding
  },

  progressBar: {
    flex: 1,
    height: 8,               // Increase from 6 to 8
    backgroundColor: '#E5E7EB',
    borderRadius: 10,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    borderRadius: 10,
    backgroundColor: '#10B981', // Green instead of yellow
    // Remove gradient, use solid green
  },

  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#454459',
    marginLeft: 12,
    minWidth: 40,
    textAlign: 'center',
  },

  // Introduction Screen Styles
  introContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },

  introHeroSection: {
    alignItems: 'center',
    marginBottom: 32,
  },

  introEmoji: {
    fontSize: 64,
    textAlign: 'center',
  },

  introTextContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },

  introMessage: {
    fontSize: 16,
    lineHeight: 24,
    color: '#454459',
    textAlign: 'center',
    maxWidth: 300,
  },

  introButtonsContainer: {
    width: '100%',
    gap: 16,
  },

  introButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 52,
  },

  introPrimaryButton: {
    backgroundColor: '#A08CFB',
  },

  introSecondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },

  introPrimaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  introSecondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },

  // Question Card Styles
  questionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 24,
    marginBottom: 20,
    ...Platform.select({
      web: {
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.08)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.08,
        shadowRadius: 12,
        elevation: 4,
      },
    }),
  },

  questionText: {
    flex: 1,
    fontSize: 16,
    color: '#454459',
    fontWeight: '500',
    lineHeight: 22,
  },

  questionDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginTop: 8,
  },

  // Multiple Choice Options
  optionsContainer: {
    gap: 12,
  },

  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    ...Platform.select({
      web: {
        transition: 'all 0.2s ease',
      },
    }),
  },

  optionButtonSelected: {
    backgroundColor: '#FFFFFF',
    borderColor: '#A08CFB',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 8px rgba(160, 140, 251, 0.2)',
      },
      default: {
        shadowColor: '#A08CFB',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 3,
      },
    }),
  },

  optionEmoji: {
    fontSize: 24,
    marginRight: 12,
  },

  optionText: {
    flex: 1,
    fontSize: 16,
    color: '#454459',
    fontWeight: '500',
    lineHeight: 22,
  },

  optionTextSelected: {
    color: '#A08CFB',
    fontWeight: '600',
  },

  // Scale Question Styles
  scaleContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },

  scaleLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
  },

  scaleLabel: {
    fontSize: 14,
    color: '#707070',
    fontWeight: '500',
  },

  scaleSlider: {
    width: '100%',
    height: 40,
  },

  scaleValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#A08CFB',
    marginTop: 16,
  },

  // Navigation Buttons
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: '#FEFEFE',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },

  navButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    minWidth: 100,
    alignItems: 'center',
  },

  navButtonPrimary: {
    backgroundColor: '#A08CFB',
    ...Platform.select({
      web: {
        boxShadow: '0px 4px 12px rgba(160, 140, 251, 0.3)',
      },
      default: {
        shadowColor: '#A08CFB',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 4,
      },
    }),
  },

  navButtonSecondary: {
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },

  navButtonDisabled: {
    backgroundColor: '#F3F4F6',
    borderColor: '#E5E7EB',
  },

  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },

  navButtonTextPrimary: {
    color: '#FFFFFF',
  },

  navButtonTextSecondary: {
    color: '#454459',
  },

  navButtonTextDisabled: {
    color: '#9CA3AF',
  },

  // Results Display Styles
  resultsContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },

  riskLevelCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 32,
    alignItems: 'center',
    marginBottom: 30,
    width: '100%',
    maxWidth: 400,
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 20px rgba(0, 0, 0, 0.1)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.1,
        shadowRadius: 20,
        elevation: 6,
      },
    }),
  },

  riskLevelIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },

  riskLevelIconText: {
    fontSize: 32,
  },

  riskLevelTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },

  riskLevelMessage: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 20,
    color: '#6B7280',
  },

  riskLevelScore: {
    fontSize: 14,
    fontWeight: '600',
    color: '#9CA3AF',
  },

  // Risk Level Colors
  riskGreen: {
    backgroundColor: '#F0F9F4',
    borderColor: '#4EAF64',
  },

  riskGreenText: {
    color: '#4EAF64',
  },

  riskYellow: {
    backgroundColor: '#FFFBEB',
    borderColor: '#FFCE5C',
  },

  riskYellowText: {
    color: '#D97706',
  },

  riskRed: {
    backgroundColor: '#FEF2F2',
    borderColor: '#ED7E1C',
  },

  riskRedText: {
    color: '#DC2626',
  },

  riskEmergency: {
    backgroundColor: '#FEF2F2',
    borderColor: '#EF4444',
  },

  riskEmergencyText: {
    color: '#DC2626',
  },

  // Safety Protocol Styles
  safetyContainer: {
    backgroundColor: '#FEF2F2',
    borderRadius: 10,
    padding: 24,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#FECACA',
  },

  safetyIcon: {
    fontSize: 48,
    textAlign: 'center',
    marginBottom: 16,
  },

  safetyTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#DC2626',
    textAlign: 'center',
    marginBottom: 12,
  },

  safetyMessage: {
    fontSize: 16,
    lineHeight: 24,
    color: '#7F1D1D',
    textAlign: 'center',
    marginBottom: 24,
  },

  safetyButtonsContainer: {
    gap: 12,
  },

  safetyButton: {
    backgroundColor: '#DC2626',
    borderRadius: 10,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },

  safetyButtonSecondary: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#DC2626',
  },

  safetyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  safetyButtonTextSecondary: {
    color: '#DC2626',
  },

  // Loading States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FEFEFE',
  },

  loadingText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 16,
  },

  // Error States
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderRadius: 10,
    padding: 20,
    margin: 24,
    alignItems: 'center',
  },

  errorText: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
    lineHeight: 22,
  },

  // Responsive adjustments
  smallScreen: {
    paddingHorizontal: 16,
  },

  largeScreen: {
    paddingHorizontal: 32,
    maxWidth: 600,
    alignSelf: 'center',
  },
});

// Helper function to get responsive styles
export const getResponsiveStyles = () => {
  const isSmallScreen = width < 375;
  const isLargeScreen = width > 768;

  return {
    isSmallScreen,
    isLargeScreen,
    containerStyle: isLargeScreen 
      ? selfReflectionStyles.largeScreen 
      : isSmallScreen 
        ? selfReflectionStyles.smallScreen 
        : {},
  };
};
