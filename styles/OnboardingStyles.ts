import { StyleSheet, Platform, Dimensions } from 'react-native';

// Default screen dimensions for responsive calculations
const { width, height } = Dimensions.get('window');

export const onboardingStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEFEFE',
  },
  mainContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    paddingTop: 80, // Added padding to the top of the main container
  },
  unifiedContentContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 30,
    marginTop: 20,
  },
  textContainer: {
    alignItems: 'center',
    paddingTop: 5,
    marginBottom: 20,
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'flex-start',
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  heroSection: {
    width: '100%',
    height: height * 0.4, // Responsive height based on screen height
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 0,
    paddingBottom: 0,
    overflow: 'hidden',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  // contentSection is replaced by topContent and bottomContent
  title: {
    fontSize: 32,
    fontWeight: '900',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 14,
    color: '#707070',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
    fontWeight: '400',
    maxWidth: 300,
  },
  responsiveFeaturesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30, // Slightly reduced margin
    width: '100%',
    gap: width < 350 ? 8 : 12, // Smaller gaps between pills
  },
  featureTag: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 25, // Smaller border radius
    paddingHorizontal: 16, // Base padding adjusted in dynamic styles
    paddingVertical: 8, // Smaller vertical padding
    borderWidth: 1,
    borderColor: '#E0E4E8',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.05)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.05,
        shadowRadius: 6,
      },
    }),
    elevation: 2,
  },
  featureTagText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
  },
  ctaButton: {
    backgroundColor: '#FF7F6E', // Coral/peach color from screenshot
    borderRadius: 30,
    paddingHorizontal: 50,
    paddingVertical: 14,
    minWidth: 180,
    marginTop: 10,
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 16px rgba(255, 127, 110, 0.3)',
      },
      default: {
        shadowColor: '#FF7F6E',
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.3,
        shadowRadius: 16,
      },
    }),
    elevation: 6,
  },
  ctaButtonPressed: {
    transform: [{ scale: 0.98 }],
  },
  ctaButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  // Additional responsive styles are now handled dynamically
});