import { StyleSheet } from 'react-native';

export const headerStyles = StyleSheet.create({
  // Standard header container
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  
  // Side elements (left/right)
  sideElement: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Center content container
  centerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Header icon
  icon: {
    marginRight: 8,
  },
  
  // Header title
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#5B3E31',
  },
  
  // Button styles for header actions
  actionButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
});

// Header configuration constants
export const HEADER_CONFIGS = {
  appointment: {
    iconName: 'calendar' as const,
    iconColor: '#B5A136',
    title: '<PERSON><PERSON><PERSON> Konsultasi',
  },
  chat: {
    iconName: 'chatbubble' as const,
    iconColor: '#B5A136',
    title: 'Temani Chat',
  },
  chatVoice: {
    iconName: 'call' as const,
    iconColor: '#4A55A2',
    title: 'Temani Call',
  },
  mood: {
    iconName: 'heart' as const,
    iconColor: '#4EAF64',
    title: 'Mood Kamu Minggu Ini',
  },
  moodLogging: {
    iconName: 'heart' as const,
    iconColor: '#4EAF64',
    title: 'Mood Tracker',
  },
  journal: {
    iconName: 'book' as const,
    iconColor: '#DD76B9',
    title: 'Journal Anda',
  },
  journalHistory: {
    iconName: 'book' as const,
    iconColor: '#DD76B9',
    title: 'Riwayat Journal',
  },
  voice: {
    iconName: 'call' as const,
    iconColor: '#4A55A2',
    title: 'Temani Call',
  },
} as const;
