import { StyleSheet, Platform } from 'react-native';

export const customFieldStyles = StyleSheet.create({
  // Main container styles
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  // Field manager styles
  managerContainer: {
    flex: 1,
    padding: 20,
  },

  managerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },

  managerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#DD76B9', // Journal theme primary color
  },

  addButton: {
    backgroundColor: '#DD76B9',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 8px rgba(221, 118, 185, 0.3)',
      },
      default: {
        shadowColor: '#DD76B9',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 4,
      },
    }),
  },

  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },

  addButtonDisabled: {
    backgroundColor: '#E0E0E0',
    ...Platform.select({
      web: {
        boxShadow: 'none',
      },
      default: {
        shadowOpacity: 0,
        elevation: 0,
      },
    }),
  },

  // Field list styles
  fieldsList: {
    flex: 1,
  },

  fieldItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 3,
      },
    }),
  },

  fieldItemDragging: {
    opacity: 0.8,
    transform: [{ scale: 1.02 }],
    borderColor: '#DD76B9',
    borderWidth: 2,
  },

  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },

  fieldContent: {
    flex: 1,
    marginRight: 12,
  },

  fieldText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    lineHeight: 22,
  },

  fieldType: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  fieldTypeQuestion: {
    color: '#DD76B9',
  },

  fieldTypeTitle: {
    color: '#4EAF64',
  },

  fieldActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  actionButton: {
    padding: 8,
    marginLeft: 4,
    borderRadius: 8,
  },

  dragHandle: {
    padding: 8,
    marginRight: 4,
  },

  // Empty state styles
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },

  emptyStateIcon: {
    marginBottom: 16,
  },

  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 8,
  },

  emptyStateDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },

  emptyStateButton: {
    backgroundColor: '#DD76B9',
    borderRadius: 24,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },

  emptyStateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },

  // Field creator modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },

  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    ...Platform.select({
      web: {
        boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.2)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.2,
        shadowRadius: 30,
        elevation: 10,
      },
    }),
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },

  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
  },

  closeButton: {
    padding: 4,
  },

  // Form styles
  formSection: {
    marginBottom: 20,
  },

  formLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },

  formInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#FFFFFF',
  },

  formInputFocused: {
    borderColor: '#DD76B9',
    backgroundColor: '#FFF8FC',
  },

  formInputError: {
    borderColor: '#FF6B6B',
    backgroundColor: '#FFF5F5',
  },

  formTextArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },

  formError: {
    fontSize: 12,
    color: '#FF6B6B',
    marginTop: 4,
  },

  formHint: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
  },

  // Field type selector styles
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 20,
  },

  typeOption: {
    flex: 1,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
  },

  typeOptionSelected: {
    borderColor: '#DD76B9',
    backgroundColor: '#FFF8FC',
  },

  typeOptionIcon: {
    marginBottom: 8,
  },

  typeOptionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 4,
  },

  typeOptionDescription: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 16,
  },

  // Modal actions
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },

  modalButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 12,
  },

  modalButtonPrimary: {
    backgroundColor: '#DD76B9',
  },

  modalButtonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },

  modalButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  modalButtonTextSecondary: {
    color: '#666666',
  },

  modalButtonDisabled: {
    backgroundColor: '#E0E0E0',
  },

  // Loading states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },

  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 12,
  },

  // Limit indicator
  limitIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },

  limitText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 8,
  },

  limitWarning: {
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
  },

  limitWarningText: {
    color: '#856404',
  },
});
