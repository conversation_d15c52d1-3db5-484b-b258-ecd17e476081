import { StyleSheet, Platform } from 'react-native';

export const voiceChatStyles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  voiceButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#2B7EFF',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 12px rgba(43, 126, 255, 0.3)',
      },
      default: {
        shadowColor: '#2B7EFF',
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.3,
        shadowRadius: 12,
      },
    }),
    elevation: 8,
    position: 'relative',
  },
  voiceButtonActive: {
    backgroundColor: '#EF4444',
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 12px rgba(239, 68, 68, 0.3)',
      },
      default: {
        shadowColor: '#EF4444',
      },
    }),
  },
  voiceButtonDisabled: {
    backgroundColor: '#CBD5E0',
    ...Platform.select({
      web: {
        boxShadow: 'none',
      },
      default: {
        shadowOpacity: 0,
      },
    }),
    elevation: 0,
  },
  voiceButtonError: {
    backgroundColor: '#EF4444',
    ...Platform.select({
      web: {
        boxShadow: '0px 6px 12px rgba(239, 68, 68, 0.3)',
      },
      default: {
        shadowColor: '#EF4444',
      },
    }),
  },
  pulseRing: {
    position: 'absolute',
    width: 90,
    height: 90,
    borderRadius: 45,
    backgroundColor: '#EF4444',
    top: -9,
    left: -9,
  },
  iconContainer: {
    zIndex: 1,
  },
  statusContainer: {
    alignItems: 'center',
    marginTop: 16,
    minHeight: 50,
    maxWidth: 280,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  instructionText: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 16,
    marginTop: 4,
  },
  retryButton: {
    backgroundColor: '#EF4444',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});