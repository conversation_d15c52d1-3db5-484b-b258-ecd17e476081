// Mock Expo modules
jest.mock('expo-constants', () => ({
  default: {
    expoConfig: {
      name: 'test-app',
    },
  },
}));

jest.mock('expo-linking', () => ({
  createURL: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
}));

// Create a comprehensive chainable mock for Supabase
const createChainableMock = () => {
  const chainable = {
    select: jest.fn(() => chainable),
    insert: jest.fn(() => chainable),
    update: jest.fn(() => chainable),
    upsert: jest.fn(() => chainable),
    delete: jest.fn(() => chainable),
    eq: jest.fn(() => Promise.resolve({ data: [], error: null })),
    neq: jest.fn(() => Promise.resolve({ data: [], error: null })),
    gt: jest.fn(() => Promise.resolve({ data: [], error: null })),
    gte: jest.fn(() => Promise.resolve({ data: [], error: null })),
    lt: jest.fn(() => Promise.resolve({ data: [], error: null })),
    lte: jest.fn(() => Promise.resolve({ data: [], error: null })),
    like: jest.fn(() => Promise.resolve({ data: [], error: null })),
    ilike: jest.fn(() => Promise.resolve({ data: [], error: null })),
    is: jest.fn(() => Promise.resolve({ data: [], error: null })),
    in: jest.fn(() => Promise.resolve({ data: [], error: null })),
    contains: jest.fn(() => Promise.resolve({ data: [], error: null })),
    containedBy: jest.fn(() => Promise.resolve({ data: [], error: null })),
    rangeGt: jest.fn(() => Promise.resolve({ data: [], error: null })),
    rangeGte: jest.fn(() => Promise.resolve({ data: [], error: null })),
    rangeLt: jest.fn(() => Promise.resolve({ data: [], error: null })),
    rangeLte: jest.fn(() => Promise.resolve({ data: [], error: null })),
    rangeAdjacent: jest.fn(() => Promise.resolve({ data: [], error: null })),
    overlaps: jest.fn(() => Promise.resolve({ data: [], error: null })),
    textSearch: jest.fn(() => Promise.resolve({ data: [], error: null })),
    match: jest.fn(() => Promise.resolve({ data: [], error: null })),
    not: jest.fn(() => chainable),
    or: jest.fn(() => chainable),
    filter: jest.fn(() => chainable),
    order: jest.fn(() => chainable),
    limit: jest.fn(() => chainable),
    range: jest.fn(() => chainable),
    single: jest.fn(() => Promise.resolve({ data: null, error: null })),
    maybeSingle: jest.fn(() => Promise.resolve({ data: null, error: null })),
    csv: jest.fn(() => Promise.resolve({ data: '', error: null })),
    geojson: jest.fn(() => Promise.resolve({ data: null, error: null })),
    explain: jest.fn(() => Promise.resolve({ data: null, error: null })),
    rollback: jest.fn(() => Promise.resolve({ data: null, error: null })),
    returns: jest.fn(() => chainable),
    then: jest.fn((resolve) => resolve({ data: [], error: null })),
  };
  return chainable;
};

// Mock Supabase with proper chaining and all methods
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => createChainableMock()),
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: null }, error: null })),
      signInWithPassword: jest.fn(() => Promise.resolve({ data: { user: null }, error: null })),
      signUp: jest.fn(() => Promise.resolve({ data: { user: null }, error: null })),
      signOut: jest.fn(() => Promise.resolve({ error: null })),
      onAuthStateChange: jest.fn(() => ({ data: { subscription: { unsubscribe: jest.fn() } } })),
    },
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(() => Promise.resolve({ data: null, error: null })),
        download: jest.fn(() => Promise.resolve({ data: null, error: null })),
        remove: jest.fn(() => Promise.resolve({ data: null, error: null })),
        list: jest.fn(() => Promise.resolve({ data: [], error: null })),
        getPublicUrl: jest.fn(() => ({ data: { publicUrl: 'test-url' } })),
      })),
    },
  },
}));

// Create a smart mock for UserProfileService that tracks state
let mockProfile = {
  id: 'test-profile-id',
  user_id: 'test-uuid-123',
  onboardingCompleted: false,
  selfReflectionCompleted: false,
  preferences: { theme: 'light' },
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Mock UserProfileService methods with state tracking
jest.mock('@/lib/userProfileService', () => ({
  UserProfileService: {
    getOrCreateProfile: jest.fn(() => Promise.resolve({ ...mockProfile })),
    updateCompletionStatus: jest.fn((userId, updates) => {
      mockProfile = { ...mockProfile, ...updates, updatedAt: new Date() };
      return Promise.resolve({ ...mockProfile });
    }),
    updatePreferences: jest.fn((userId, preferences) => {
      mockProfile = { 
        ...mockProfile, 
        preferences: { ...mockProfile.preferences, ...preferences },
        updatedAt: new Date() 
      };
      return Promise.resolve({ ...mockProfile });
    }),
  },
}));

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-123'),
}));

// Silence console warnings during tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};

// Reset mock profile before each test
beforeEach(() => {
  mockProfile = {
    id: 'test-profile-id',
    user_id: 'test-uuid-123',
    onboardingCompleted: false,
    selfReflectionCompleted: false,
    preferences: { theme: 'light' },
    createdAt: new Date(),
    updatedAt: new Date(),
  };
});
