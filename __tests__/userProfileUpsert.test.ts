/**
 * User Profile Upsert Test
 * Tests the fix for unique constraint violation in UserProfileService
 */

import { UserProfileService } from '@/lib/userProfileService';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

// Mock user ID for testing
const TEST_USER_ID = uuidv4();

describe('UserProfileService conflict handling', () => {
  beforeAll(async () => {
    // Clean up any existing test user profile
    await supabase
      .from('user_profiles')
      .delete()
      .eq('user_id', TEST_USER_ID);
  });

  afterAll(async () => {
    // Clean up test data
    await supabase
      .from('user_profiles')
      .delete()
      .eq('user_id', TEST_USER_ID);
  });

  test('handles concurrent upsert operations without unique constraint violations', async () => {
    // Simulate multiple concurrent update operations
    const updates = [
      { onboardingCompleted: true },
      { selfReflectionCompleted: true },
      { preferences: { theme: 'dark' } }
    ];
    
    // Create promises for concurrent operations
    const updatePromises = updates.map(update => 
      UserProfileService.updateCompletionStatus(TEST_USER_ID, update)
    );
    
    // All should resolve without errors
    await expect(Promise.all(updatePromises)).resolves.not.toThrow();
    
    // Verify the final state
    const profile = await UserProfileService.getOrCreateProfile(TEST_USER_ID);
    
    expect(profile).toBeDefined();
    expect(profile.onboardingCompleted).toBe(true);
    expect(profile.selfReflectionCompleted).toBe(true);
    expect(profile.preferences.theme).toBe('dark');
  });

  test('handles multiple preference updates without conflicts', async () => {
    const preferences = [
      { theme: 'light' },
      { language: 'en' },
      { notifications: { selfReflectionReminders: false } }
    ];
    
    // Create promises for concurrent operations
    const updatePromises = preferences.map(pref => 
      UserProfileService.updatePreferences(TEST_USER_ID, pref)
    );
    
    // All should resolve without errors
    await expect(Promise.all(updatePromises)).resolves.not.toThrow();
    
    // Verify the final state
    const profile = await UserProfileService.getOrCreateProfile(TEST_USER_ID);
    
    expect(profile).toBeDefined();
    expect(profile.preferences.theme).toBe('light');
    expect(profile.preferences.language).toBe('en');
    expect(profile.preferences.notifications?.selfReflectionReminders).toBe(false);
  });
});
