/**
 * Tests for Self-Reflection System
 * Basic unit tests for scoring engine and question validation
 */

import { ScoringEngine } from '../lib/scoringEngine';
import type { Question, QuestionOption } from '../types/selfReflection';

// Mock question data for testing
const mockQuestions: Question[] = [
  {
    id: '1',
    questionSetId: 'test-set',
    questionKey: 'general_feeling',
    questionText: { id: 'Test question' },
    questionType: 'multiple_choice',
    options: [
      { id: 'good', text: { id: 'Good' }, score: 1 },
      { id: 'neutral', text: { id: 'Neutral' }, score: 2 },
      { id: 'bad', text: { id: 'Bad' }, score: 3 },
    ],
    scoringConfig: { method: 'direct', maxScore: 3 },
    orderIndex: 1,
    isRequired: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    questionSetId: 'test-set',
    questionKey: 'safety_check',
    questionText: { id: 'Safety question' },
    questionType: 'multiple_choice',
    options: [
      { id: 'no', text: { id: 'No' }, score: 0 },
      { id: 'yes', text: { id: 'Yes' }, score: 10, critical: true },
    ],
    scoringConfig: { method: 'direct', maxScore: 10, critical: true },
    orderIndex: 2,
    isRequired: true,
    metadata: { isSafetyQuestion: true },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

describe('ScoringEngine', () => {
  describe('calculateScore', () => {
    it('should calculate scores correctly for multiple choice questions', () => {
      const responses = {
        general_feeling: 'neutral',
        safety_check: 'no',
      };

      const result = ScoringEngine.calculateScore(responses, mockQuestions);

      expect(result.questionScores.general_feeling).toBe(2);
      expect(result.questionScores.safety_check).toBe(0);
      expect(result.totalScore).toBe(2);
      expect(result.maxPossibleScore).toBe(13);
      expect(result.riskLevel).toBe('green');
      expect(result.criticalFlags).toHaveLength(0);
    });

    it('should detect critical responses', () => {
      const responses = {
        general_feeling: 'bad',
        safety_check: 'yes',
      };

      const result = ScoringEngine.calculateScore(responses, mockQuestions);

      expect(result.questionScores.general_feeling).toBe(3);
      expect(result.questionScores.safety_check).toBe(10);
      expect(result.totalScore).toBe(13);
      expect(result.riskLevel).toBe('emergency');
      expect(result.criticalFlags).toContain('safety_check');
      expect(result.criticalFlags).toContain('safety_check_critical');
    });

    it('should handle missing responses', () => {
      const responses = {
        general_feeling: 'good',
        // safety_check missing
      };

      const result = ScoringEngine.calculateScore(responses, mockQuestions);

      expect(result.questionScores.general_feeling).toBe(1);
      expect(result.questionScores.safety_check).toBe(0);
      expect(result.totalScore).toBe(1);
      expect(result.riskLevel).toBe('green');
    });

    it('should determine correct risk levels', () => {
      // Test green level (0-4)
      let responses = { general_feeling: 'good', safety_check: 'no' };
      let result = ScoringEngine.calculateScore(responses, mockQuestions);
      expect(result.riskLevel).toBe('green');

      // Test yellow level (5-7)
      responses = { general_feeling: 'bad', safety_check: 'no' };
      result = ScoringEngine.calculateScore(responses, mockQuestions);
      expect(result.riskLevel).toBe('green'); // Still green because total is 3

      // Test emergency level (critical flag)
      responses = { general_feeling: 'good', safety_check: 'yes' };
      result = ScoringEngine.calculateScore(responses, mockQuestions);
      expect(result.riskLevel).toBe('emergency');
    });
  });

  describe('validateScoringConfig', () => {
    it('should validate correct scoring configuration', () => {
      const result = ScoringEngine.validateScoringConfig(mockQuestions);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid scoring configuration', () => {
      const invalidQuestions: Question[] = [
        {
          ...mockQuestions[0],
          scoringConfig: { method: 'direct', maxScore: -1 }, // Invalid maxScore
        },
      ];

      const result = ScoringEngine.validateScoringConfig(invalidQuestions);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('getAITonePreference', () => {
    it('should map support preferences to AI tones', () => {
      expect(ScoringEngine.getAITonePreference({ support_preference: 'light_chat' }))
        .toBe('casual_friendly');
      
      expect(ScoringEngine.getAITonePreference({ support_preference: 'validation' }))
        .toBe('empathetic_supportive');
      
      expect(ScoringEngine.getAITonePreference({ support_preference: 'unknown' }))
        .toBe('warm_supportive');
    });
  });

  describe('calculateMaxPossibleScore', () => {
    it('should calculate maximum possible score correctly', () => {
      const maxScore = ScoringEngine.calculateMaxPossibleScore(mockQuestions);
      expect(maxScore).toBe(13); // 3 + 10
    });
  });
});

// Mock data validation tests
describe('Question Configuration Validation', () => {
  it('should validate question structure', () => {
    const question = mockQuestions[0];
    
    expect(question.id).toBeDefined();
    expect(question.questionKey).toBeDefined();
    expect(question.questionText).toBeDefined();
    expect(question.questionType).toBeDefined();
    expect(question.scoringConfig).toBeDefined();
    expect(question.orderIndex).toBeGreaterThan(0);
  });

  it('should validate multiple choice options', () => {
    const question = mockQuestions[0];
    
    expect(question.options).toBeDefined();
    expect(question.options!.length).toBeGreaterThan(0);
    
    question.options!.forEach(option => {
      expect(option.id).toBeDefined();
      expect(option.text).toBeDefined();
      expect(typeof option.score).toBe('number');
    });
  });
});

export {};
