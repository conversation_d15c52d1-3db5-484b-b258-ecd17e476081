/**
 * TypeScript types for User Profile system
 * Defines interfaces for user completion status and preferences
 */

export interface UserProfile {
  id: string;
  userId: string;
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
  latestSessionId?: string;
  latestRiskLevel?: 'green' | 'yellow' | 'red' | 'emergency';
  latestCompletedAt?: Date;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  // UI preferences
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
  timezone?: string; // IANA timezone identifier (e.g., 'Asia/Jakarta')

  // Notification preferences
  notifications?: {
    selfReflectionReminders?: boolean;
    weeklyCheckIns?: boolean;
    riskLevelAlerts?: boolean;
  };

  // Self-reflection preferences
  selfReflection?: {
    reminderFrequency?: 'daily' | 'weekly' | 'monthly' | 'never';
    preferredTime?: string; // HH:MM format
    skipWeekends?: boolean;
  };

  // AI interaction preferences
  aiInteraction?: {
    preferredTone?: string;
    supportType?: string;
    conversationStyle?: 'casual' | 'formal' | 'empathetic';
  };

  // Privacy preferences
  privacy?: {
    shareAnonymousData?: boolean;
    dataRetentionDays?: number;
  };

  // Custom preferences (extensible)
  [key: string]: any;
}

export interface CompletionStatus {
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
}

export interface CompletionUpdates {
  onboardingCompleted?: boolean;
  selfReflectionCompleted?: boolean;
  preferences?: Partial<UserPreferences>;
}

// Database row type (matches Supabase schema)
export interface UserProfileRow {
  id: string;
  user_id: string;
  onboarding_completed: boolean;
  self_reflection_completed: boolean;
  latest_session_id?: string;
  latest_risk_level?: string;
  latest_completed_at?: string;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// View type for user completion status
export interface UserCompletionStatusView {
  user_id: string;
  email?: string;
  onboarding_completed: boolean;
  self_reflection_completed: boolean;
  latest_risk_level?: string;
  latest_completed_at?: string;
  profile_created_at?: string;
  profile_updated_at?: string;
}

// Migration types
export interface MigrationData {
  userId: string;
  localOnboardingCompleted: boolean;
  localSelfReflectionCompleted: boolean;
  migrationTimestamp: Date;
}

// Error types
export class UserProfileError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'UserProfileError';
  }
}

// Service response types
export interface UserProfileServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: UserProfileError;
}

// Cache types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Analytics types (for future use)
export interface UserProfileAnalytics {
  userId: string;
  profileCreatedAt: Date;
  onboardingCompletedAt?: Date;
  selfReflectionCompletedAt?: Date;
  totalSelfReflectionSessions: number;
  averageRiskLevel: number;
  lastActiveAt: Date;
  preferenceChanges: number;
}

// Utility types
export type UserProfileKeys = keyof UserProfile;
export type UserPreferenceKeys = keyof UserPreferences;
export type CompletionStatusKeys = keyof CompletionStatus;

// Constants
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'auto',
  language: 'id',
  timezone: 'Asia/Jakarta', // Default timezone for Indonesian users
  notifications: {
    selfReflectionReminders: true,
    weeklyCheckIns: false,
    riskLevelAlerts: true,
  },
  selfReflection: {
    reminderFrequency: 'weekly',
    preferredTime: '19:00',
    skipWeekends: false,
  },
  aiInteraction: {
    preferredTone: 'warm_supportive',
    supportType: 'balanced',
    conversationStyle: 'empathetic',
  },
  privacy: {
    shareAnonymousData: true,
    dataRetentionDays: 365,
  },
};

export const CACHE_DURATION = {
  USER_PROFILE: 5 * 60 * 1000, // 5 minutes
  COMPLETION_STATUS: 2 * 60 * 1000, // 2 minutes
  PREFERENCES: 10 * 60 * 1000, // 10 minutes
} as const;

// Helper functions
export const isValidRiskLevel = (level: string): level is 'green' | 'yellow' | 'red' | 'emergency' => {
  return ['green', 'yellow', 'red', 'emergency'].includes(level);
};

export const isValidTheme = (theme: string): theme is 'light' | 'dark' | 'auto' => {
  return ['light', 'dark', 'auto'].includes(theme);
};

export const isValidLanguage = (language: string): boolean => {
  // Add more languages as needed
  return ['id', 'en'].includes(language);
};

export const isValidTimezone = (timezone: string): boolean => {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
};

// Validation functions
export const validateUserPreferences = (preferences: Partial<UserPreferences>): string[] => {
  const errors: string[] = [];

  if (preferences.theme && !isValidTheme(preferences.theme)) {
    errors.push('Invalid theme value');
  }

  if (preferences.language && !isValidLanguage(preferences.language)) {
    errors.push('Invalid language value');
  }

  if (preferences.timezone && !isValidTimezone(preferences.timezone)) {
    errors.push('Invalid timezone value');
  }

  if (preferences.selfReflection?.preferredTime) {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(preferences.selfReflection.preferredTime)) {
      errors.push('Invalid preferred time format (should be HH:MM)');
    }
  }

  return errors;
};

export const sanitizeUserPreferences = (preferences: Partial<UserPreferences>): UserPreferences => {
  const sanitized = { ...DEFAULT_USER_PREFERENCES };
  
  // Merge with provided preferences, ensuring valid values
  if (preferences.theme && isValidTheme(preferences.theme)) {
    sanitized.theme = preferences.theme;
  }
  
  if (preferences.language && isValidLanguage(preferences.language)) {
    sanitized.language = preferences.language;
  }
  
  // Deep merge nested objects
  if (preferences.notifications) {
    sanitized.notifications = { ...sanitized.notifications, ...preferences.notifications };
  }
  
  if (preferences.selfReflection) {
    sanitized.selfReflection = { ...sanitized.selfReflection, ...preferences.selfReflection };
  }
  
  if (preferences.aiInteraction) {
    sanitized.aiInteraction = { ...sanitized.aiInteraction, ...preferences.aiInteraction };
  }
  
  if (preferences.privacy) {
    sanitized.privacy = { ...sanitized.privacy, ...preferences.privacy };
  }
  
  return sanitized;
};
