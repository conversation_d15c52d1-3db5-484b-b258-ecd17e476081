interface Client {
  id: string                           // UUID (matches auth.users.id)
  email: string                        // Unique email
  full_name: string                    // Required
  phone?: string                       // Optional
  usia?: number                        // Age (0-150)
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  emergency_contact_name?: string
  emergency_contact_phone?: string
  assigned_psychologist_id?: string    // UUID reference
  status: 'active' | 'inactive' | 'discharged'
  intake_date: string                  // ISO timestamp
  notes?: string                       // Psychologist notes
  created_at: string                   // ISO timestamp
  updated_at: string                   // ISO timestamp
  created_by?: string                  // UUID of creator
}

export type { Client };
