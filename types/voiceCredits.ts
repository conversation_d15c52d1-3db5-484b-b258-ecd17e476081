/**
 * Voice Credits System Types
 * Defines interfaces for the credits-based rate limiting system
 */

export interface VoiceCredits {
  creditsRemaining: number;
  creditsUsed: number;
  totalSessions: number;
  lastSession?: Date;
  lastCreditReset: Date;
}

export interface VoiceCallSession {
  id: string;
  userId: string;
  sessionStart: Date;
  sessionEnd?: Date;
  durationSeconds: number;
  creditsUsed: number;
  agentId?: string;
  sessionStatus: VoiceSessionStatus;
  sessionMetadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export type VoiceSessionStatus = 'active' | 'completed' | 'interrupted' | 'failed';

export interface VoiceUsageStats {
  userId: string;
  email: string;
  creditsRemaining: number;
  creditsUsed: number;
  totalSessions: number;
  totalCallTimeSeconds: number;
  lastCallTime?: Date;
  lastCreditReset: Date;
}

export interface CreditCheckResult {
  hasCredits: boolean;
  creditsRemaining: number;
  availableCredits: number;
  requiredCredits: number;
  estimatedCallTime: number; // in seconds
  message?: string;
}

export interface SessionStartResult {
  sessionId: string;
  creditsReserved: number;
  maxCallDuration: number; // in seconds
}

export interface SessionEndResult {
  sessionId: string;
  durationSeconds: number;
  creditsUsed: number;
  creditsRemaining: number;
}

export interface VoiceCreditsError extends Error {
  code: 'INSUFFICIENT_CREDITS' | 'SESSION_NOT_FOUND' | 'INVALID_DURATION' | 'DATABASE_ERROR';
  details?: any;
}

// Constants
export const VOICE_CREDITS_CONFIG = {
  INITIAL_CREDITS: 12,
  SECONDS_PER_CREDIT: 10,
  MINIMUM_CREDIT_DEDUCTION: 1,
  MAX_SESSION_DURATION: 120, // 2 minutes = 12 credits max
  WEEKLY_RECHARGE_AMOUNT: 12, // Credits recharged weekly
} as const;

// Helper functions
export const calculateCreditsFromDuration = (durationSeconds: number): number => {
  return Math.max(
    VOICE_CREDITS_CONFIG.MINIMUM_CREDIT_DEDUCTION,
    Math.ceil(durationSeconds / VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT)
  );
};

export const calculateMaxDurationFromCredits = (credits: number): number => {
  return credits * VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT;
};

export const formatCallDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
};

export const formatCreditsDisplay = (credits: number): string => {
  const totalSeconds = calculateMaxDurationFromCredits(credits);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  
  if (minutes > 0) {
    return `${credits} credits (${minutes}m ${seconds}s)`;
  }
  return `${credits} credits (${seconds}s)`;
};
