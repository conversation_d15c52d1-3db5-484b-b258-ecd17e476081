export interface Thread {
  id: string;
  user_id: string;
  title?: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  message_id: string;
  thread_id: string;
  sender_type: 'user' | 'ai';
  content: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  metadata: Record<string, any>;
  transcription?: string; // Voice transcription for voice messages
}

export interface ChatState {
  messages: Message[];
  loading: boolean;
  error: string | null;
  isTyping: boolean;
  hasMore: boolean;
}