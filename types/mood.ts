/**
 * TypeScript types for the mood tracking feature
 */

// Days of the week in Indonesian abbreviations
export type DayOfWeek = 'Sen' | 'Sel' | 'Rab' | 'Kam' | 'Jum' | 'Sab' | 'Min';

// Mood levels (1-5 scale)
export type MoodLevel = 1 | 2 | 3 | 4 | 5;

// Stress levels (1-5 scale)
export type StressLevel = 1 | 2 | 3 | 4 | 5;

// Daily feeling levels (1-4 scale for step 4)
export type DailyFeelingLevel = 1 | 2 | 3 | 4;

// Physical health status
export type PhysicalHealthStatus = boolean; // true = healthy, false = sick

// Sleep hours (0-24 hours)
export type SleepHours = number;

// Sleep quality levels (0-10 scale for better granularity)
export type SleepQualityLevel = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;

// Daily mood data structure
export interface DailyMoodData {
  mood: MoodLevel | null;
  physicalHealth: PhysicalHealthStatus | null;
  sleepHours: SleepHours | null;
  sleepQuality: SleepQualityLevel | null;
  stressLevel: StressLevel | null;
  dailyFeeling: DailyFeelingLevel | null;
}

// Weekly mood data structure
export interface WeeklyMoodData {
  Sen: DailyMoodData;
  Sel: DailyMoodData;
  Rab: DailyMoodData;
  Kam: DailyMoodData;
  Jum: DailyMoodData;
  Sab: DailyMoodData;
  Min: DailyMoodData;
}

// Mood tracking state for React components
export interface MoodTrackingState {
  weeklyData: WeeklyMoodData;
  selectedWeek: string; // ISO date string for the Monday of the week
  loading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
}

// Database types for mood tracking
export interface MoodEntry {
  id: string;
  user_id: string;
  entry_date: string;
  day_of_week: DayOfWeek;
  mood_level: MoodLevel | null;
  physical_health: PhysicalHealthStatus | null;
  sleep_hours: SleepHours | null;
  sleep_quality: SleepQualityLevel | null;
  stress_level: StressLevel | null;
  daily_feeling: DailyFeelingLevel | null;
  created_at: string;
  updated_at: string;
}

// Input type for creating mood entries
export interface MoodEntryInput {
  entry_date: string;
  day_of_week: DayOfWeek;
  mood_level?: MoodLevel | null;
  physical_health?: PhysicalHealthStatus | null;
  sleep_hours?: SleepHours | null;
  sleep_quality?: SleepQualityLevel | null;
  stress_level?: StressLevel | null;
  daily_feeling?: DailyFeelingLevel | null;
}

// Update type for mood entries
export interface MoodEntryUpdate {
  mood_level?: MoodLevel | null;
  physical_health?: PhysicalHealthStatus | null;
  sleep_hours?: SleepHours | null;
  sleep_quality?: SleepQualityLevel | null;
  stress_level?: StressLevel | null;
  daily_feeling?: DailyFeelingLevel | null;
}

// Analytics types
export interface MoodAnalytics {
  id: string;
  user_id: string;
  period_type: 'week' | 'month';
  period_start: string;
  period_end: string;
  avg_mood: number | null;
  avg_sleep_hours: number | null;
  avg_sleep_quality: number | null;
  avg_stress_level: number | null;
  avg_daily_feeling: number | null;
  healthy_days: number;
  sick_days: number;
  total_entries: number;
  calculated_at: string;
  created_at: string;
  updated_at: string;
}

// Mood trend analysis
export interface MoodTrend {
  period: string;
  avgMood: number;
  avgSleepHours: number;
  avgSleepQuality: number;
  avgStressLevel: number;
  avgDailyFeeling: number;
  healthyDays: number;
  totalEntries: number;
}

// Comprehensive mood insights
export interface MoodInsights {
  totalEntries: number;
  averages: {
    mood: number;
    sleepHours: number;
    sleepQuality: number;
    stressLevel: number;
    dailyFeeling: number;
  };
  trends: {
    moodTrend: 'improving' | 'declining' | 'stable';
    sleepTrend: 'improving' | 'declining' | 'stable';
    stressTrend: 'improving' | 'declining' | 'stable';
  };
  insights: string[];
}

// Error handling types
export interface MoodError {
  code: string;
  message: string;
  details?: any;
}

export interface MoodValidationResult {
  isValid: boolean;
  errors: string[];
}

// Props for mood tracking components
export interface MoodSelectorProps {
  selectedMood: MoodLevel | null;
  onMoodSelect: (mood: MoodLevel) => void;
  day: DayOfWeek;
}

export interface PhysicalHealthTrackerProps {
  isHealthy: PhysicalHealthStatus | null;
  onHealthToggle: (isHealthy: boolean) => void;
  day: DayOfWeek;
}

export interface SleepTrackerProps {
  sleepHours: SleepHours | null;
  onSleepHoursChange: (hours: number) => void;
  day: DayOfWeek;
}

export interface StressLevelTrackerProps {
  stressLevel: StressLevel | null;
  onStressLevelSelect: (level: StressLevel) => void;
  day: DayOfWeek;
}

export interface SemicircularMoodPickerProps {
  selectedMood: MoodLevel | null;
  onMoodSelect: (mood: MoodLevel) => void;
  colors: Record<MoodLevel, string>;
}

// Utility types for component props
export interface DayTrackerRowProps {
  weeklyData: WeeklyMoodData;
  onDataUpdate: (day: DayOfWeek, field: keyof DailyMoodData, value: any) => void;
}

// Constants for mood tracking
export const DAYS_OF_WEEK: DayOfWeek[] = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'];

export const MOOD_LEVELS: MoodLevel[] = [1, 2, 3, 4, 5];

export const STRESS_LEVELS: StressLevel[] = [1, 2, 3, 4, 5];

export const DAILY_FEELING_LEVELS: DailyFeelingLevel[] = [1, 2, 3, 4];

export const SLEEP_QUALITY_LEVELS: SleepQualityLevel[] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// Default empty daily mood data
export const DEFAULT_DAILY_MOOD_DATA: DailyMoodData = {
  mood: null,
  physicalHealth: null,
  sleepHours: null,
  sleepQuality: null,
  stressLevel: null,
  dailyFeeling: null,
};

// Default empty weekly mood data (used for unauthenticated users)
// Note: All fields are null, encouraging users to sign in and track their mood
export const DEFAULT_WEEKLY_MOOD_DATA: WeeklyMoodData = {
  Sen: { ...DEFAULT_DAILY_MOOD_DATA },
  Sel: { ...DEFAULT_DAILY_MOOD_DATA },
  Rab: { ...DEFAULT_DAILY_MOOD_DATA },
  Kam: { ...DEFAULT_DAILY_MOOD_DATA },
  Jum: { ...DEFAULT_DAILY_MOOD_DATA },
  Sab: { ...DEFAULT_DAILY_MOOD_DATA },
  Min: { ...DEFAULT_DAILY_MOOD_DATA },
};

// Mood asset mapping (corresponds to assets/images/mood/1.png, etc.)
export const MOOD_ASSET_MAP: Record<MoodLevel, any> = {
  1: require('../assets/images/mood/1.png'),
  2: require('../assets/images/mood/2.png'),
  3: require('../assets/images/mood/3.png'),
  4: require('../assets/images/mood/4.png'),
  5: require('../assets/images/mood/5.png'),
};

// Color schemes for different components
export const MOOD_COLORS = {
  primary: '#4EAF64',
  secondary: '#C6FFDD',
  background: '#F8F8F8',
  text: '#5B3E31',
  border: '#E2E8F0',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
};

// Stress level colors (light to dark)
export const STRESS_LEVEL_COLORS: Record<StressLevel, string> = {
  1: '#FFCB9D', // Light peach
  2: '#FFA95E', // Medium orange
  3: '#F18232', // Orange
  4: '#D66400', // Dark orange
  5: '#873800', // Dark brown
};

// Background color for stress level circles
export const STRESS_LEVEL_BACKGROUND = '#FFDFD2';

// Background color for daily feeling circles
export const DAILY_FEELING_BACKGROUND = '#F3F4F6';

// Mood level colors for semicircular picker
export const MOOD_LEVEL_COLORS: Record<MoodLevel, string> = {
  1: '#926247', // Very Sad - Brown
  2: '#ED7E1C', // Sad - Orange
  3: '#FFCE5C', // Neutral - Yellow
  4: '#9BB168', // Happy - Green
  5: '#A694F5', // Very Happy - Purple
};

// Daily feeling colors (4-point scale with intuitive color psychology)
export const DAILY_FEELING_COLORS: Record<DailyFeelingLevel, string> = {
  1: '#4EAF64', // Green - Tenang & Terkendali (positive)
  2: '#FFCE5C', // Yellow - Sedikit Terbebani (neutral)
  3: '#ED7E1C', // Orange - Kewalahan & Emosional (negative)
  4: '#DC2626', // Red - Lagi di Titik Terendah (very negative)
};

// Enhanced mood section configuration for new design
export interface MoodSection {
  id: MoodLevel;
  label: string;
  emoji: string;
  color: string;
  responseText: string;
  angle: {
    start: number;
    end: number;
  };
  svgIcon?: string; // Optional SVG icon string
}

// Circular mood sections configuration (360° full circle) - Updated with SVG icons
export const CIRCULAR_MOOD_SECTIONS: MoodSection[] = [
  {
    id: 1,
    label: 'Sangat Buruk',
    emoji: '😢', // Keep emoji for backward compatibility
    color: '#C2B1FF', // Purple - Very Bad (matches SVG background)
    responseText: 'Saya merasa sangat buruk hari ini.',
    angle: { start: 0, end: 72 },
    svgIcon: `<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="31" height="31" rx="15.5" fill="#C2B1FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.7533 8.12832C20.2577 7.62389 21.0756 7.62389 21.58 8.12832L25.455 12.0033C25.9594 12.5077 25.9594 13.3256 25.455 13.83C24.9506 14.3344 24.1327 14.3344 23.6283 13.83L19.7533 9.95501C19.2489 9.45059 19.2489 8.63275 19.7533 8.12832Z" fill="#3C357C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.4547 8.12832C25.9591 8.63275 25.9591 9.45059 25.4547 9.95501L21.5797 13.83C21.0753 14.3344 20.2574 14.3344 19.753 13.83C19.2486 13.3256 19.2486 12.5077 19.753 12.0033L23.628 8.12832C24.1324 7.62389 24.9503 7.62389 25.4547 8.12832Z" fill="#3C357C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.54531 8.12832C6.04974 7.62389 6.86758 7.62389 7.37201 8.12832L11.247 12.0033C11.7514 12.5077 11.7514 13.3256 11.247 13.83C10.7426 14.3344 9.92474 14.3344 9.42031 13.83L5.54531 9.95501C5.04089 9.45059 5.04089 8.63275 5.54531 8.12832Z" fill="#3C357C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.2467 8.12832C11.7511 8.63275 11.7511 9.45059 11.2467 9.95501L7.37168 13.83C6.86725 14.3344 6.04941 14.3344 5.54499 13.83C5.04056 13.3256 5.04056 12.5077 5.54499 12.0033L9.41999 8.12832C9.92441 7.62389 10.7423 7.62389 11.2467 8.12832Z" fill="#3C357C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7821 18.393C12.1356 17.3544 13.7939 16.7915 15.5 16.7915C17.206 16.7915 18.8644 17.3544 20.2179 18.393C21.5714 19.4316 22.5444 20.8878 22.9859 22.5357C23.1705 23.2247 22.7616 23.933 22.0726 24.1176C21.3835 24.3023 20.6752 23.8933 20.4906 23.2043C20.1962 22.1057 19.5476 21.1349 18.6453 20.4425C17.7429 19.7501 16.6373 19.3748 15.5 19.3748C14.3626 19.3748 13.257 19.7501 12.3547 20.4425C11.4524 21.1349 10.8037 22.1057 10.5094 23.2043C10.3247 23.8933 9.61647 24.3023 8.92741 24.1176C8.23835 23.933 7.82943 23.2247 8.01406 22.5357C8.45562 20.8877 9.42859 19.4316 10.7821 18.393Z" fill="#3C357C"/>
</svg>`
  },
  {
    id: 2,
    label: 'Buruk',
    emoji: '😔',
    color: '#ED7E1C', // Orange - Bad (matches SVG background)
    responseText: 'Saya merasa buruk.',
    angle: { start: 72, end: 144 },
    svgIcon: `<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="31" height="31" rx="15.5" fill="#ED7E1C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7821 18.393C12.1356 17.3544 13.7939 16.7915 15.5 16.7915C17.206 16.7915 18.8644 17.3544 20.2179 18.393C21.5714 19.4316 22.5444 20.8878 22.9859 22.5357C23.1705 23.2247 22.7616 23.933 22.0726 24.1176C21.3835 24.3023 20.6752 23.8933 20.4906 23.2043C20.1962 22.1057 19.5476 21.1349 18.6453 20.4425C17.7429 19.7501 16.6373 19.3748 15.5 19.3748C14.3626 19.3748 13.257 19.7501 12.3547 20.4425C11.4524 21.1349 10.8037 22.1057 10.5094 23.2043C10.3247 23.8933 9.61647 24.3023 8.92741 24.1176C8.23835 23.933 7.82943 23.2247 8.01406 22.5357C8.45562 20.8877 9.42859 19.4316 10.7821 18.393Z" fill="#663600"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.74967 9.0415C8.46304 9.0415 9.04134 9.6198 9.04134 10.3332V14.2082C9.04134 14.9215 8.46304 15.4998 7.74967 15.4998C7.03631 15.4998 6.45801 14.9215 6.45801 14.2082V10.3332C6.45801 9.6198 7.03631 9.0415 7.74967 9.0415Z" fill="#663600"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.2497 9.0415C23.963 9.0415 24.5413 9.6198 24.5413 10.3332V14.2082C24.5413 14.9215 23.963 15.4998 23.2497 15.4998C22.5363 15.4998 21.958 14.9215 21.958 14.2082V10.3332C21.958 9.6198 22.5363 9.0415 23.2497 9.0415Z" fill="#663600"/>
</svg>`
  },
  {
    id: 3,
    label: 'Netral',
    emoji: '😐',
    color: '#C0A091', // Brown - Neutral (matches SVG background)
    responseText: 'Saya biasa aja.',
    angle: { start: 144, end: 216 },
    svgIcon: `<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="31" height="31" rx="15.5" fill="#C0A091"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.2497 9.0415C23.963 9.0415 24.5413 9.6198 24.5413 10.3332L24.5413 14.2082C24.5413 14.9215 23.963 15.4998 23.2497 15.4998C22.5363 15.4998 21.958 14.9215 21.958 14.2082L21.958 10.3332C21.958 9.6198 22.5363 9.0415 23.2497 9.0415Z" fill="#4F3422"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.74967 9.0415C8.46304 9.0415 9.04134 9.6198 9.04134 10.3332L9.04134 14.2082C9.04134 14.9215 8.46304 15.4998 7.74967 15.4998C7.03631 15.4998 6.45801 14.9215 6.45801 14.2082L6.45801 10.3332C6.45801 9.6198 7.03631 9.0415 7.74967 9.0415Z" fill="#4F3422"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.45801 20.6667C6.45801 19.9533 7.03631 19.375 7.74967 19.375H23.2497C23.963 19.375 24.5413 19.9533 24.5413 20.6667C24.5413 21.38 23.963 21.9583 23.2497 21.9583H7.74967C7.03631 21.9583 6.45801 21.38 6.45801 20.6667Z" fill="#4F3422"/>
</svg>`
  },
  {
    id: 4,
    label: 'Baik',
    emoji: '🙂',
    color: '#FFCE5C', // Yellow - Good (matches SVG background)
    responseText: 'Saya merasa cukup baik.',
    angle: { start: 216, end: 288 },
    svgIcon: `<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="31" height="31" rx="15.5" fill="#FFCE5C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.2497 9.0415C23.963 9.0415 24.5413 9.6198 24.5413 10.3332L24.5413 14.2082C24.5413 14.9215 23.963 15.4998 23.2497 15.4998C22.5363 15.4998 21.958 14.9215 21.958 14.2082L21.958 10.3332C21.958 9.6198 22.5363 9.0415 23.2497 9.0415Z" fill="#705600"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.74967 9.0415C8.46304 9.0415 9.04134 9.6198 9.04134 10.3332L9.04134 14.2082C9.04134 14.9215 8.46304 15.4998 7.74967 15.4998C7.03631 15.4998 6.45801 14.9215 6.45801 14.2082L6.45801 10.3332C6.45801 9.6198 7.03631 9.0415 7.74967 9.0415Z" fill="#705600"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.0726 18.5072C22.7616 18.6919 23.1705 19.4001 22.9859 20.0892C22.5444 21.7371 21.5714 23.1933 20.2179 24.2318C18.8644 25.2704 17.206 25.8333 15.5 25.8333C13.7939 25.8333 12.1356 25.2704 10.7821 24.2318C9.42859 23.1933 8.45562 21.7371 8.01406 20.0892C7.82943 19.4001 8.23835 18.6919 8.92741 18.5072C9.61647 18.3226 10.3247 18.7315 10.5094 19.4206C10.8037 20.5192 11.4524 21.49 12.3547 22.1823C13.257 22.8747 14.3626 23.25 15.5 23.25C16.6373 23.25 17.7429 22.8747 18.6453 22.1823C19.5476 21.49 20.1962 20.5192 20.4906 19.4206C20.6752 18.7315 21.3835 18.3226 22.0726 18.5072Z" fill="#705600"/>
</svg>`
  },
  {
    id: 5,
    label: 'Sangat Baik',
    emoji: '😊',
    color: '#9BB168', // Green - Very Good (matches SVG background)
    responseText: 'Saya merasa sangat baik!',
    angle: { start: 288, end: 360 },
    svgIcon: `<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="31" height="31" rx="15.5" fill="#9BB168"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.4751 10.6285C20.9452 10.4337 21.4491 10.3335 21.958 10.3335C22.4669 10.3335 22.9708 10.4337 23.4409 10.6285C23.911 10.8232 24.3382 11.1086 24.698 11.4685C25.0579 11.8283 25.3433 12.2555 25.538 12.7256C25.7328 13.1957 25.833 13.6996 25.833 14.2085C25.833 14.9219 25.2547 15.5002 24.5413 15.5002C23.828 15.5002 23.2497 14.9219 23.2497 14.2085C23.2497 14.0389 23.2163 13.8709 23.1514 13.7142C23.0864 13.5575 22.9913 13.4151 22.8714 13.2951C22.7514 13.1752 22.609 13.0801 22.4523 13.0152C22.2956 12.9502 22.1276 12.9168 21.958 12.9168C21.7884 12.9168 21.6204 12.9502 21.4637 13.0152C21.307 13.0801 21.1646 13.1752 21.0447 13.2952C20.9247 13.4151 20.8296 13.5575 20.7647 13.7142C20.6998 13.8709 20.6663 14.0389 20.6663 14.2085C20.6663 14.9219 20.088 15.5002 19.3747 15.5002C18.6613 15.5002 18.083 14.9219 18.083 14.2085C18.083 13.6996 18.1832 13.1957 18.378 12.7256C18.5727 12.2555 18.8581 11.8283 19.218 11.4685C19.5778 11.1086 20.005 10.8232 20.4751 10.6285Z" fill="#3D4A26"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.0726 18.5072C22.7616 18.6919 23.1705 19.4001 22.9859 20.0892C22.5444 21.7371 21.5714 23.1933 20.2179 24.2318C18.8644 25.2704 17.206 25.8333 15.5 25.8333C13.7939 25.8333 12.1356 25.2704 10.7821 24.2318C9.42859 23.1933 8.45562 21.7371 8.01406 20.0892C7.82943 19.4001 8.23835 18.6919 8.92741 18.5072C9.61647 18.3226 10.3247 18.7315 10.5094 19.4206C10.8037 20.5192 11.4524 21.49 12.3547 22.1823C13.257 22.8747 14.3626 23.25 15.5 23.25C16.6373 23.25 17.7429 22.8747 18.6453 22.1823C19.5476 21.49 20.1962 20.5192 20.4906 19.4206C20.6752 18.7315 21.3835 18.3226 22.0726 18.5072Z" fill="#3D4A26"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.55909 10.6285C8.02923 10.4337 8.53312 10.3335 9.04199 10.3335C9.55086 10.3335 10.0548 10.4337 10.5249 10.6285C10.995 10.8232 11.4222 11.1086 11.782 11.4685C12.1419 11.8283 12.4273 12.2555 12.622 12.7256C12.8168 13.1957 12.917 13.6996 12.917 14.2085C12.917 14.9219 12.3387 15.5002 11.6253 15.5002C10.912 15.5002 10.3337 14.9219 10.3337 14.2085C10.3337 14.0389 10.3002 13.8709 10.2353 13.7142C10.1704 13.5575 10.0753 13.4151 9.95534 13.2951C9.8354 13.1752 9.693 13.0801 9.53629 13.0152C9.37958 12.9502 9.21162 12.9168 9.04199 12.9168C8.87237 12.9168 8.70441 12.9502 8.54769 13.0152C8.39098 13.0801 8.24859 13.1752 8.12865 13.2952C8.0087 13.4151 7.91356 13.5575 7.84865 13.7142C7.78374 13.8709 7.75033 14.0389 7.75033 14.2085C7.75033 14.9219 7.17203 15.5002 6.45866 15.5002C5.74529 15.5002 5.16699 14.9219 5.16699 14.2085C5.16699 13.6996 5.26722 13.1957 5.46196 12.7256C5.6567 12.2555 5.94213 11.8283 6.30195 11.4685C6.66178 11.1086 7.08896 10.8232 7.55909 10.6285Z" fill="#3D4A26"/>
</svg>`
  }
];

// Keep the old one for backward compatibility
export const ENHANCED_MOOD_SECTIONS: MoodSection[] = CIRCULAR_MOOD_SECTIONS;

// Mood response text mapping (keeping for backward compatibility)
export const MOOD_RESPONSE_TEXTS: Record<MoodLevel | 'null', string> = {
  1: 'Saya merasa sangat sedih hari ini.',
  2: 'Saya merasa agak sedih.',
  3: 'Saya biasa aja.',
  4: 'Saya merasa cukup baik.',
  5: 'Saya merasa sangat bahagia!',
  null: 'Saya biasa aja.',
};

// Helper function to get mood response text
export const getMoodResponseText = (mood: MoodLevel | null): string => {
  return MOOD_RESPONSE_TEXTS[mood || 'null'];
};

// Daily feeling text mapping
export const DAILY_FEELING_TEXTS: Record<DailyFeelingLevel, string> = {
  1: 'Tenang & Terkendali',
  2: 'Sedikit Terbebani',
  3: 'Kewalahan & Emosional',
  4: 'Lagi di Titik Terendah',
};

// Helper function to get daily feeling text
export const getDailyFeelingText = (feeling: DailyFeelingLevel | null): string => {
  return feeling ? DAILY_FEELING_TEXTS[feeling] : '';
};

// Sleep quality level mapping to mood assets (1-10 scale mapped to 1-5 mood assets)
export const getSleepQualityMoodAsset = (sleepQuality: SleepQualityLevel): any => {
  // Map 10 levels to 5 mood assets
  if (sleepQuality <= 2) return MOOD_ASSET_MAP[1]; // Very bad sleep -> sad face
  if (sleepQuality <= 4) return MOOD_ASSET_MAP[2]; // Bad sleep -> slightly sad face
  if (sleepQuality <= 6) return MOOD_ASSET_MAP[3]; // Average sleep -> neutral face
  if (sleepQuality <= 8) return MOOD_ASSET_MAP[4]; // Good sleep -> happy face
  return MOOD_ASSET_MAP[5]; // Excellent sleep -> very happy face
};

// Sleep quality labels in Indonesian (updated for 0-10 scale)
export const SLEEP_QUALITY_LABELS: Record<SleepQualityLevel, string> = {
  0: 'Sangat Buruk',
  1: 'Buruk Sekali',
  2: 'Buruk',
  3: 'Kurang',
  4: 'Di Bawah Rata-rata',
  5: 'Cukup',
  6: 'Lumayan',
  7: 'Bagus',
  8: 'Bagus Sekali',
  9: 'Sangat Bagus',
  10: 'Sempurna',
};

// Sleep quality time indicators (updated for 0-10 scale)
export const SLEEP_QUALITY_TIME_LABELS: Record<SleepQualityLevel, string> = {
  0: '<3 JAM',
  1: '3-4 JAM',
  2: '4-5 JAM',
  3: '5 JAM',
  4: '5-6 JAM',
  5: '6 JAM',
  6: '6-7 JAM',
  7: '7 JAM',
  8: '7-8 JAM',
  9: '8 JAM',
  10: '8-9 JAM',
};

// Sleep quality options for timeline design
export interface SleepQualityOption {
  id: SleepQualityLevel;
  label: string;
  duration: string;
  emoji: string;
  color: string;
}

export const SLEEP_QUALITY_OPTIONS: SleepQualityOption[] = [
  {
    id: 5,
    label: 'Bagus Sekali',
    duration: '7-9 JAM',
    emoji: '😊',
    color: '#8BC34A',
  },
  {
    id: 4,
    label: 'Bagus',
    duration: '6-7 JAM',
    emoji: '🙂',
    color: '#FFC107',
  },
  {
    id: 3,
    label: 'Biasa Saja',
    duration: '5 JAM',
    emoji: '😐',
    color: '#8D6E63',
  },
  {
    id: 2,
    label: 'Buruk',
    duration: '3-4 JAM',
    emoji: '😞',
    color: '#FF9800',
  },
  {
    id: 1,
    label: 'Buruk Sekali',
    duration: '<3 JAM',
    emoji: '😵',
    color: '#9C27B0',
  },
];

// Sleep quality options for 0-10 scale (based on medical research)
// References: PSQI, National Sleep Foundation, CDC/AASM recommendations
// 7-9 hours optimal for adults, <7 hours linked to health risks
export interface SleepQualityOptionExtended extends SleepQualityOption {
  healthImpact: string;
  shortLabel: string; // For overflow prevention
}

export const SLEEP_QUALITY_OPTIONS_0_10: SleepQualityOptionExtended[] = [
  {
    id: 10,
    label: 'Sempurna',
    shortLabel: 'Sempurna',
    duration: '8-9 JAM',
    emoji: '😴',
    color: '#4CAF50',
    healthImpact: 'Peak Optimal',
  },
  {
    id: 9,
    label: 'Sangat Bagus',
    shortLabel: 'Sangat Bagus',
    duration: '8 JAM',
    emoji: '😊',
    color: '#66BB6A',
    healthImpact: 'Peak Optimal',
  },
  {
    id: 8,
    label: 'Bagus Sekali',
    shortLabel: 'Bagus Sekali',
    duration: '7-8 JAM',
    emoji: '🙂',
    color: '#8BC34A',
    healthImpact: 'Optimal',
  },
  {
    id: 7,
    label: 'Bagus',
    shortLabel: 'Bagus',
    duration: '7 JAM',
    emoji: '😌',
    color: '#9CCC65',
    healthImpact: 'Optimal',
  },
  {
    id: 6,
    label: 'Lumayan',
    shortLabel: 'Lumayan',
    duration: '6-7 JAM',
    emoji: '🙂',
    color: '#CDDC39',
    healthImpact: 'Mendekati Optimal',
  },
  {
    id: 5,
    label: 'Cukup',
    shortLabel: 'Cukup',
    duration: '6 JAM',
    emoji: '😐',
    color: '#FFC107',
    healthImpact: 'Risiko Ringan',
  },
  {
    id: 4,
    label: 'Di Bawah Rata-rata',
    shortLabel: 'Kurang',
    duration: '5-6 JAM',
    emoji: '😕',
    color: '#FF9800',
    healthImpact: 'Risiko Ringan',
  },
  {
    id: 3,
    label: 'Kurang',
    shortLabel: 'Kurang',
    duration: '5 JAM',
    emoji: '😞',
    color: '#FF7043',
    healthImpact: 'Risiko Sedang',
  },
  {
    id: 2,
    label: 'Buruk',
    shortLabel: 'Buruk',
    duration: '4-5 JAM',
    emoji: '😫',
    color: '#F44336',
    healthImpact: 'Risiko Sedang',
  },
  {
    id: 1,
    label: 'Buruk Sekali',
    shortLabel: 'Buruk Sekali',
    duration: '3-4 JAM',
    emoji: '😵',
    color: '#E91E63',
    healthImpact: 'Risiko Tinggi',
  },
  {
    id: 0,
    label: 'Sangat Buruk',
    shortLabel: 'Sangat Buruk',
    duration: '<3 JAM',
    emoji: '🥱',
    color: '#9C27B0',
    healthImpact: 'Risiko Tinggi',
  },
];

// Sleep hours color scheme
export const SLEEP_CHART_COLORS = {
  bar: '#FBBF24', // Yellow/orange for bars
  text: '#1F2937', // Dark text
  background: '#F9FAFB', // Light background
  grid: '#E5E7EB', // Grid lines
};

// Mood service constants
export const MOOD_CONSTANTS = {
  MAX_SLEEP_HOURS: 24,
  MIN_SLEEP_HOURS: 0,
  MAX_MOOD_LEVEL: 5,
  MIN_MOOD_LEVEL: 1,
  MAX_STRESS_LEVEL: 5,
  MIN_STRESS_LEVEL: 1,
  MAX_SLEEP_QUALITY: 10,
  MIN_SLEEP_QUALITY: 0,
  MAX_DAILY_FEELING: 4,
  MIN_DAILY_FEELING: 1,
  HISTORY_PAGE_SIZE: 30,
  ANALYTICS_CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
} as const;
