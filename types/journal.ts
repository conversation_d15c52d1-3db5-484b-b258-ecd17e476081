/**
 * TypeScript types for the journal feature
 */

// Field type enum for custom fields
export type FieldType = 'system' | 'custom_question' | 'custom_title';

// AI generation types
export type AIGenerationType = 'follow_up' | 'alternative' | 'related' | 'mixed';

// Custom field configuration
export interface CustomFieldConfig {
  placeholder?: string;
  maxLength?: number;
  isRequired?: boolean;
  aiGenerated?: boolean;
  parentFieldId?: string;
  description?: string;
  icon?: string;
}

export interface JournalQuestion {
  id: string;
  question_text: string;
  question_order: number;
  is_active: boolean;
  created_at: string;
  // New fields for custom field support
  field_type?: FieldType;
  user_id?: string;
  custom_config?: CustomFieldConfig;
  parent_field_id?: string;
  ai_generated?: boolean;
  is_archived?: boolean;
}

export interface JournalEntry {
  id: string;
  user_id: string;
  question_id: string;
  answer: string;
  entry_date: string;
  created_at: string;
  updated_at: string;
  // Optional joined question data
  question?: JournalQuestion;
}

export interface JournalEntryInput {
  question_id: string;
  answer: string;
  entry_date?: string; // Defaults to today if not provided
}

export interface JournalEntryUpdate {
  answer: string;
}

// For grouped journal entries by date (used in history view)
export interface JournalEntryGroup {
  date: string;
  entries: JournalEntryWithQuestion[];
}

// Journal entry with question data included
export interface JournalEntryWithQuestion extends JournalEntry {
  question: JournalQuestion;
}

// API response types
export interface JournalApiResponse<T> {
  data: T | null;
  error: string | null;
  loading: boolean;
}

// Journal state for React components
export interface JournalState {
  questions: JournalQuestion[];
  todayEntries: JournalEntry[];
  historyEntries: JournalEntryGroup[];
  loading: boolean;
  error: string | null;
  saving: boolean;
}

// Hook return types
export interface UseJournalQuestionsReturn {
  questions: JournalQuestion[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseTodayEntriesReturn {
  entries: JournalEntryWithQuestion[];
  loading: boolean;
  error: string | null;
  createEntry: (input: JournalEntryInput) => Promise<JournalEntry | null>;
  updateEntry: (entryId: string, answer: string) => Promise<JournalEntry | null>;
  deleteEntry: (entryId: string) => Promise<boolean>;
  refetch: () => Promise<void>;
}

export interface UseJournalHistoryReturn {
  historyGroups: JournalEntryGroup[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refetch: () => Promise<void>;
}

export interface UseJournalCalendarReturn {
  currentMonth: Date;
  selectedDate: string | null;
  entryDates: string[];
  entriesForDate: JournalEntryWithQuestion[];
  loading: boolean;
  error: string | null;
  navigateMonth: (direction: 'prev' | 'next') => void;
  selectDate: (date: string) => void;
  goToToday: () => void;
  refetch: () => Promise<void>;
}

// Error types
export interface JournalError {
  code: string;
  message: string;
  details?: any;
}

// Validation types
export interface JournalValidationResult {
  isValid: boolean;
  errors: string[];
}

// Custom field specific interfaces
export interface JournalCustomField extends JournalQuestion {
  field_type: FieldType;
  user_id: string;
  custom_config: CustomFieldConfig;
}

// AI Question Generation interfaces
export interface AIQuestionGenerationRequest {
  baseQuestion: string;
  baseQuestionId?: string;
  context?: string;
  questionType: AIGenerationType;
  count?: number;
  userPreferences?: {
    tone?: 'casual' | 'formal' | 'empathetic';
    length?: 'short' | 'medium' | 'long';
  };
}

export interface AIQuestionGenerationResponse {
  success: boolean;
  questions: string[];
  model?: string;
  processingTime?: number;
  error?: string;
  generationId?: string;
}

export interface AIGenerationLog {
  id: string;
  user_id: string;
  base_question_id?: string;
  generation_type: AIGenerationType;
  prompt_used: string;
  generated_questions: string[];
  selected_questions: string[];
  generation_context: Record<string, any>;
  model_used?: string;
  processing_time_ms?: number;
  success: boolean;
  error_message?: string;
  user_feedback?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AIRateLimit {
  allowed: boolean;
  current_count: number;
  daily_limit: number;
  remaining?: number;
  reset_time?: string;
}

// Custom field creation input
export interface CustomFieldInput {
  question_text: string;
  field_type: Exclude<FieldType, 'system'>;
  custom_config?: CustomFieldConfig;
  question_order?: number;
}

// Custom field update input
export interface CustomFieldUpdate {
  question_text?: string;
  custom_config?: CustomFieldConfig;
  question_order?: number;
  is_active?: boolean;
}

// Constants
export const JOURNAL_CONSTANTS = {
  MAX_ANSWER_LENGTH: 2000,
  MIN_ANSWER_LENGTH: 1,
  HISTORY_PAGE_SIZE: 30,
  // Custom field constants
  MAX_CUSTOM_FIELDS_PER_USER: 20,
  MAX_CUSTOM_FIELD_TEXT_LENGTH: 200,
  MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH: 100,
  // AI generation constants
  AI_DAILY_GENERATION_LIMIT: 20,
  AI_MAX_QUESTIONS_PER_REQUEST: 5,
  AI_REQUEST_TIMEOUT_MS: 30000,
} as const;
