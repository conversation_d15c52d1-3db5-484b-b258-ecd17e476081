/**
 * TypeScript types for the extensible Self-Reflection Check-In system
 * Supports dynamic question management and flexible scoring
 */

// Question Types - Extensible enum for different input methods
export type QuestionType = 
  | 'multiple_choice' 
  | 'scale' 
  | 'boolean' 
  | 'text_input'
  | 'multi_select';

// Risk Levels with color coding
export type RiskLevel = 'green' | 'yellow' | 'red' | 'emergency';

// Scoring Methods
export type ScoringMethod = 'direct' | 'weighted' | 'conditional' | 'none';

// Multi-language text support
export interface MultiLanguageText {
  id: string; // Indonesian (primary)
  en?: string; // English (optional)
  [key: string]: string | undefined;
}

// Question Option Configuration
export interface QuestionOption {
  id: string;
  text: MultiLanguageText;
  emoji?: string;
  score?: number;
  color?: string;
  preference?: string; // For preference-based questions
  critical?: boolean; // For safety-critical options
  metadata?: Record<string, any>;
}

// Flexible Scoring Configuration
export interface ScoringConfig {
  method: ScoringMethod;
  maxScore: number;
  weights?: Record<string, number>;
  conditions?: ScoringCondition[];
  critical?: boolean; // For safety questions
}

// Conditional Logic for Smart Question Flow
export interface ConditionalLogic {
  showIf?: ConditionalRule[];
  hideIf?: ConditionalRule[];
}

export interface ConditionalRule {
  questionKey: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

// Scoring Conditions for Complex Scoring
export interface ScoringCondition {
  if: ConditionalRule[];
  then: {
    score?: number;
    multiplier?: number;
    addScore?: number;
  };
}

// Question Definition
export interface Question {
  id: string;
  questionSetId: string;
  questionKey: string;
  questionText: MultiLanguageText;
  questionType: QuestionType;
  options?: QuestionOption[];
  scoringConfig: ScoringConfig;
  orderIndex: number;
  isRequired: boolean;
  conditionalLogic?: ConditionalLogic;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Risk Assessment Rule Configuration
export interface RiskAssessmentRule {
  id: string;
  questionSetId: string;
  ruleName: string;
  scoreRanges: {
    green: ScoreRange;
    yellow: ScoreRange;
    red: ScoreRange;
    emergency: ScoreRange;
  };
  actions: {
    green: RiskLevelAction;
    yellow: RiskLevelAction;
    red: RiskLevelAction;
    emergency: RiskLevelAction;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ScoreRange {
  min: number;
  max: number;
  label: string;
}

export interface RiskLevelAction {
  aiTone: string;
  message: string;
  actions: string[];
}

// Question Set Management
export interface QuestionSet {
  id: string;
  name: string;
  version: string;
  description?: string;
  isActive: boolean;
  language: string;
  questions: Question[];
  riskAssessmentRules: RiskAssessmentRule[];
  createdAt: Date;
  updatedAt: Date;
}

// User Response Session
export interface SelfReflectionSession {
  id: string;
  userId: string;
  questionSetId: string;
  responses: Record<string, any>; // Flexible response storage
  calculatedScores: Record<string, number>; // Individual question scores
  totalScore: number;
  riskLevel: RiskLevel;
  aiTonePreference?: string;
  supportPreference?: string;
  metadata?: Record<string, any>;
  completedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Component Props Interfaces
export interface SelfReflectionCheckInProps {
  onComplete: (session: SelfReflectionSession) => void;
  onSkip?: () => void;
  language?: string;
}

export interface QuestionRendererProps {
  question: Question;
  value: any;
  onChange: (value: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirst?: boolean;
  isLast?: boolean;
}

export interface MultipleChoiceQuestionProps {
  question: Question;
  selectedOption: string | null;
  onOptionSelect: (optionId: string) => void;
}

export interface ScaleQuestionProps {
  question: Question;
  value: number | null;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
}

export interface BooleanQuestionProps {
  question: Question;
  value: boolean | null;
  onChange: (value: boolean) => void;
}

export interface SafetyProtocolProps {
  session: SelfReflectionSession;
  onContinue: () => void;
  onSeekHelp: () => void;
}

export interface ResultsDisplayProps {
  session: SelfReflectionSession;
  riskAssessmentRule: RiskAssessmentRule;
  onContinue: () => void;
}

// Utility Types
export interface ScoringResult {
  questionScores: Record<string, number>;
  totalScore: number;
  maxPossibleScore: number;
  riskLevel: RiskLevel;
  criticalFlags: string[];
}

export interface RiskAssessment {
  level: RiskLevel;
  score: number;
  maxScore: number;
  percentage: number;
  message: string;
  aiTone: string;
  recommendedActions: string[];
  requiresImmediateAction: boolean;
}

// Constants
export const RISK_LEVELS: RiskLevel[] = ['green', 'yellow', 'red', 'emergency'];

export const QUESTION_TYPES: QuestionType[] = [
  'multiple_choice',
  'scale', 
  'boolean',
  'text_input',
  'multi_select'
];

export const SCORING_METHODS: ScoringMethod[] = [
  'direct',
  'weighted',
  'conditional',
  'none'
];

// Risk Level Colors (matching user preferences)
export const RISK_LEVEL_COLORS: Record<RiskLevel, string> = {
  green: '#4EAF64',   // Success green
  yellow: '#FFCE5C',  // Warning yellow
  red: '#ED7E1C',     // Error orange
  emergency: '#EF4444' // Critical red
};

// Risk Level Background Colors
export const RISK_LEVEL_BACKGROUNDS: Record<RiskLevel, string> = {
  green: '#F0F9F4',   // Light green
  yellow: '#FFFBEB',  // Light yellow
  red: '#FEF2F2',     // Light red
  emergency: '#FEF2F2' // Light red
};

// Default Question Configuration
export const DEFAULT_SCORING_CONFIG: ScoringConfig = {
  method: 'direct',
  maxScore: 4,
  critical: false
};

// Helper Functions
export const getRiskLevelColor = (level: RiskLevel): string => {
  return RISK_LEVEL_COLORS[level];
};

export const getRiskLevelBackground = (level: RiskLevel): string => {
  return RISK_LEVEL_BACKGROUNDS[level];
};

export const getQuestionText = (question: Question, language: string = 'id'): string => {
  return question.questionText[language] || question.questionText.id || '';
};

export const getOptionText = (option: QuestionOption, language: string = 'id'): string => {
  return option.text[language] || option.text.id || '';
};

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface QuestionValidation {
  question: Question;
  result: ValidationResult;
}

// Error Types
export class SelfReflectionError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'SelfReflectionError';
  }
}

export class QuestionConfigError extends SelfReflectionError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'QUESTION_CONFIG_ERROR', context);
    this.name = 'QuestionConfigError';
  }
}

export class ScoringError extends SelfReflectionError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'SCORING_ERROR', context);
    this.name = 'ScoringError';
  }
}
