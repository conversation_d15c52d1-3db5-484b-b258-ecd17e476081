{"expo": {"name": "<PERSON><PERSON>", "slug": "temani", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "temani", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "id.temani.app", "infoPlist": {"NSMicrophoneUsageDescription": "Temani memerlukan akses mikrofon untuk fitur panggilan suara dengan AI companion. Ini memungkinkan Anda berbicara langsung dengan Temani untuk dukungan emosional yang lebih personal.", "CFBundleURLTypes": [{"CFBundleURLName": "temani-auth", "CFBundleURLSchemes": ["temani"]}]}}, "android": {"permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"], "package": "id.temani.app", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "temani"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "display": "standalone", "orientation": "portrait", "startUrl": "/", "backgroundColor": "#ffffff", "themeColor": "#2B7EFF", "preferRelatedApplications": false, "icons": [{"src": "./assets/images/icon.png", "sizes": "192x192", "type": "image/png"}, {"src": "./assets/images/icon.png", "sizes": "512x512", "type": "image/png"}], "name": "<PERSON><PERSON>", "shortName": "<PERSON><PERSON>", "description": "AI companion untuk dukungan emosional dan kesehatan mental remaja Indonesia", "categories": ["health", "lifestyle", "medical"], "lang": "id", "dir": "ltr"}, "plugins": ["expo-router", ["expo-audio", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone for voice calls."}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "temani", "organization": "prasai-z0"}], "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "d5a6eb4e-8111-4180-b894-0b06fdbc8313"}}, "owner": "prastheking"}}