# Data Documentation

This directory contains data-related documentation and utilities.

## Mood Data Structure

The mood tracking system uses a structured data format for daily and weekly mood entries.

```typescript
interface DailyMoodData {
  mood: MoodLevel | null;           // 1-5 scale
  physicalHealth: boolean | null;   // true = healthy, false = sick
  sleepHours: number | null;        // 0-24 hours
  sleepQuality: SleepQualityLevel | null; // 1-10 scale
  stressLevel: StressLevel | null;  // 1-5 scale
  dailyFeeling: DailyFeelingLevel | null; // 1-4 scale (Step 4 mood tracker)
}

interface WeeklyMoodData {
  Sen: DailyMoodData; // Monday
  Sel: DailyMoodData; // Tuesday
  Rab: DailyMoodData; // Wednesday
  Kam: DailyMoodData; // Thursday
  Jum: DailyMoodData; // Friday
  Sab: DailyMoodData; // Saturday
  Min: DailyMoodData; // Sunday
}
```

### Data Storage

Mood data is stored in Supabase with the following features:

- **Real-time sync**: Data syncs across devices
- **Offline support**: Local storage with sync when online
- **Data validation**: Server-side validation for data integrity
- **Analytics**: Aggregated insights and trends
