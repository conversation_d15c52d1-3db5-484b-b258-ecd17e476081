# Authentication Flow Test Plan

## Test Scenarios

### Scenario 1: First-time User
1. Clear all local storage/secure store
2. Open app
3. **Expected**: Should show onboarding screen
4. Click "Yuk Curhat" 
5. **Expected**: Should navigate to auth screen and save onboarding completion

### Scenario 2: Returning User - Failed Login
1. Complete Scenario 1 first (so onboarding is completed)
2. Try to login with incorrect credentials
3. **Expected**: Should stay on auth screen, NOT redirect to onboarding
4. **Expected**: Should show error message

### Scenario 3: Returning User - Successful Login
1. Complete Scenario 1 first
2. Login with correct credentials
3. **Expected**: Should navigate to self-reflection or main app

### Scenario 4: Explicit Sign Out
1. Complete successful login
2. Go to settings and sign out
3. **Expected**: Should clear onboarding status and redirect to onboarding

## Key Changes Made

1. **AuthContext Changes**:
   - Added `isExplicitSignOut` flag to distinguish explicit sign out vs auth failures
   - Modified `SIGNED_OUT` event handler to only reset onboarding status on explicit sign out
   - Added logic to load onboarding status from local storage for unauthenticated users

2. **Root Layout Changes**:
   - Improved navigation logic to prioritize auth screen for users who have completed onboarding
   - Added better comments explaining the routing decisions

3. **Onboarding Screen Changes**:
   - Added call to `completeOnboarding()` when user clicks "Yuk Curhat"
   - This ensures onboarding completion is saved to persistent storage

## Testing Commands

```bash
# Clear browser local storage (in browser console)
localStorage.clear()

# Or for React Native, you'd need to clear SecureStore
# This would be done programmatically or by uninstalling/reinstalling the app
```

## Expected Behavior After Fix

- ✅ First-time users see onboarding
- ✅ Failed login attempts stay on auth screen
- ✅ Returning users go directly to auth screen
- ✅ Explicit sign out resets onboarding status
- ✅ Onboarding completion persists across authentication failures
