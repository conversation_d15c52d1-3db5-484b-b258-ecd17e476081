const express = require('express');
const path = require('path');
const app = express();
const port = process.env.PORT || 8081;

// Security middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Comprehensive security headers middleware
app.use((req, res, next) => {
  const isHTTPS = req.secure || req.headers['x-forwarded-proto'] === 'https';

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com data:",
    "img-src 'self' data: blob: https:",
    "connect-src 'self' https://smzennhnotmrjdfvwmaa.supabase.co https://api.groq.com https://api.elevenlabs.io wss://smzennhnotmrjdfvwmaa.supabase.co https://app.temani.co https://temani.co",
    "media-src 'self' blob: data:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; ');

  res.setHeader('Content-Security-Policy', csp);

  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  res.setHeader('Cross-Origin-Embedder-Policy', 'unsafe-none');
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin-allow-popups');
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');

  // Permissions Policy
  const permissionsPolicy = [
    "camera=()",
    "microphone=(self)",
    "geolocation=()",
    "payment=()",
    "usb=()",
    "magnetometer=()",
    "gyroscope=()",
    "accelerometer=()"
  ].join(', ');
  res.setHeader('Permissions-Policy', permissionsPolicy);

  // HSTS for HTTPS
  if (isHTTPS) {
    res.setHeader('Strict-Transport-Security', 'max-age=********; includeSubDomains; preload');
  }

  next();
});

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Handle client-side routing - send all requests to index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, () => {
  console.log(`🚀 Temani app is running on port ${port}`);
  console.log(`📱 Access your app at: http://localhost:${port}`);
});
