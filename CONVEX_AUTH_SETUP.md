# Convex Auth Migration Setup Guide

## Current Status ✅

The following steps have been completed:

1. ✅ **Convex Auth Installation**: `@convex-dev/auth` and `@auth/core` installed
2. ✅ **Schema Configuration**: Updated `convex/schema.ts` with auth tables and user profiles
3. ✅ **Auth Provider Setup**: Configured Google OAuth and Password providers in `convex/auth.ts`
4. ✅ **User Profile Management**: Created `convex/userProfiles.ts` with CRUD operations
5. ✅ **React Context**: Created `context/ConvexAuthContext.tsx` for frontend integration
6. ✅ **Feature Flag System**: Added `EXPO_PUBLIC_USE_CONVEX_AUTH` flag for gradual migration
7. ✅ **Layout Architecture**: Created separate layouts for Supabase and Convex auth systems

## Next Steps 🚀

### 1. Configure Google OAuth (Required)

You need to set up Google OAuth credentials in your Google Cloud Console:

#### A. Google Cloud Console Setup:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Choose "Web Application"
6. Add these URLs:
   - **Authorized JavaScript origins**: `http://localhost:8081` (development)
   - **Authorized redirect URIs**: `https://disciplined-butterfly-269.convex.site/api/auth/callback/google`

#### B. Set Environment Variables:
```bash
# Set Google OAuth credentials in Convex
npx convex env set AUTH_GOOGLE_ID your_google_client_id_here
npx convex env set AUTH_GOOGLE_SECRET your_google_client_secret_here
```

### 2. Enable Convex Auth (Testing)

To test the Convex Auth system, set the feature flag:

```bash
# In your .env.local file, add:
EXPO_PUBLIC_USE_CONVEX_AUTH=true
```

### 3. Test the Migration

1. **Start Convex Dev Server**:
   ```bash
   npx convex dev
   ```

2. **Start Expo Dev Server**:
   ```bash
   npm run dev
   ```

3. **Test Authentication**:
   - Try Google OAuth sign-in
   - Try email/password registration
   - Verify user profile creation
   - Test onboarding flow

### 4. Production Deployment

When ready for production:

1. **Update Google OAuth URLs**:
   - Add your production domain to authorized origins
   - Update redirect URI to your production Convex site

2. **Set Production Environment Variables**:
   ```bash
   npx convex env set --prod AUTH_GOOGLE_ID your_prod_google_client_id
   npx convex env set --prod AUTH_GOOGLE_SECRET your_prod_google_client_secret
   ```

3. **Deploy Convex**:
   ```bash
   npx convex deploy
   ```

## Architecture Overview

### Feature Flag System
- `EXPO_PUBLIC_USE_CONVEX_AUTH=false` → Uses Supabase Auth (current system)
- `EXPO_PUBLIC_USE_CONVEX_AUTH=true` → Uses Convex Auth (new system)

### File Structure
```
app/
├── _layout.tsx              # Feature flag router
├── _layout.supabase.tsx     # Original Supabase auth layout
└── _layout.convex.tsx       # New Convex auth layout

context/
├── AuthContext.tsx          # Original Supabase auth context
└── ConvexAuthContext.tsx    # New Convex auth context

convex/
├── schema.ts               # Database schema with auth tables
├── auth.ts                 # Auth configuration and helpers
└── userProfiles.ts         # User profile management
```

### Data Migration Strategy

The current implementation creates a parallel authentication system. User data migration will need to be handled separately:

1. **User Profiles**: Automatically created when users sign in with Convex Auth
2. **Existing Data**: Will need manual migration scripts for:
   - Journal entries
   - Mood tracking data
   - Self-reflection sessions
   - Voice credits

## Troubleshooting

### Common Issues:

1. **"Invalid redirect URI"**: Make sure your Google OAuth redirect URI matches exactly:
   `https://disciplined-butterfly-269.convex.site/api/auth/callback/google`

2. **"Convex URL not found"**: Ensure `EXPO_PUBLIC_CONVEX_URL` is set in your environment

3. **"User profile not found"**: The system automatically creates profiles, but there might be a race condition. Check the `getOrCreateUserProfile` function.

### Debug Mode:
Enable detailed logging by checking the console for:
- "Auth system selection" logs
- "Convex Auth Routing decision" logs
- User profile creation logs

## Rollback Plan

If issues occur, you can immediately rollback by setting:
```bash
EXPO_PUBLIC_USE_CONVEX_AUTH=false
```

This will revert to the original Supabase authentication system.

## Support

For issues with this migration:
1. Check the console logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure Google OAuth is configured properly
4. Test in development before production deployment
