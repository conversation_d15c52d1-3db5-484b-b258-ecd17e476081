import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { journalService } from '@/lib/journalService';
import type {
  JournalQuestion,
  JournalEntryWithQuestion,
  JournalEntryGroup,
  JournalEntry,
  JournalEntryInput,
  JournalCustomField,
  CustomFieldInput,
  CustomFieldUpdate
} from '@/types/journal';
import { customFieldService } from '@/lib/customFieldService';

interface JournalContextType {
  // Today's entries (shared between homepage and journal page)
  todayEntries: JournalEntryWithQuestion[];
  todayLoading: boolean;
  todayError: string | null;

  // Journal questions (system only)
  questions: JournalQuestion[];
  questionsLoading: boolean;
  questionsError: string | null;

  // User fields (system + custom)
  userFields: JournalQuestion[];
  userFieldsLoading: boolean;
  userFieldsError: string | null;

  // Custom fields only
  customFields: JournalCustomField[];
  customFieldsLoading: boolean;
  customFieldsError: string | null;
  
  // History entries (for journal page)
  historyGroups: JournalEntryGroup[];
  historyLoading: boolean;
  historyError: string | null;
  historyHasMore: boolean;
  
  // Actions
  refreshTodayEntries: () => Promise<void>;
  refreshQuestions: () => Promise<void>;
  refreshUserFields: () => Promise<void>;
  refreshCustomFields: () => Promise<void>;
  refreshHistory: () => Promise<void>;
  loadMoreHistory: () => Promise<void>;
  createEntry: (input: JournalEntryInput) => Promise<JournalEntry | null>;
  updateEntry: (entryId: string, answer: string) => Promise<JournalEntry | null>;
  deleteEntry: (entryId: string) => Promise<boolean>;

  // Custom field actions
  createCustomField: (input: CustomFieldInput) => Promise<JournalCustomField | null>;
  updateCustomField: (fieldId: string, update: CustomFieldUpdate) => Promise<JournalCustomField | null>;
  archiveCustomField: (fieldId: string) => Promise<boolean>;
}

const JournalContext = createContext<JournalContextType | undefined>(undefined);

export const useJournal = () => {
  const context = useContext(JournalContext);
  if (context === undefined) {
    throw new Error('useJournal must be used within a JournalProvider');
  }
  return context;
};

interface JournalProviderProps {
  children: ReactNode;
}

export const JournalProvider: React.FC<JournalProviderProps> = ({ children }) => {
  const { user, selfReflectionCompleted, selfReflectionSkipped } = useUniversalAuth();
  
  // Today's entries state
  const [todayEntries, setTodayEntries] = useState<JournalEntryWithQuestion[]>([]);
  const [todayLoading, setTodayLoading] = useState(true);
  const [todayError, setTodayError] = useState<string | null>(null);
  
  // Questions state (system only)
  const [questions, setQuestions] = useState<JournalQuestion[]>([]);
  const [questionsLoading, setQuestionsLoading] = useState(true);
  const [questionsError, setQuestionsError] = useState<string | null>(null);

  // User fields state (system + custom)
  const [userFields, setUserFields] = useState<JournalQuestion[]>([]);
  const [userFieldsLoading, setUserFieldsLoading] = useState(true);
  const [userFieldsError, setUserFieldsError] = useState<string | null>(null);

  // Custom fields state
  const [customFields, setCustomFields] = useState<JournalCustomField[]>([]);
  const [customFieldsLoading, setCustomFieldsLoading] = useState(true);
  const [customFieldsError, setCustomFieldsError] = useState<string | null>(null);
  
  // History state
  const [historyGroups, setHistoryGroups] = useState<JournalEntryGroup[]>([]);
  const [historyLoading, setHistoryLoading] = useState(true);
  const [historyError, setHistoryError] = useState<string | null>(null);
  const [historyHasMore, setHistoryHasMore] = useState(true);
  const [historyOffset, setHistoryOffset] = useState(0);

  // Fetch today's entries
  const refreshTodayEntries = useCallback(async () => {
    if (!user?.id) return;

    try {
      setTodayLoading(true);
      setTodayError(null);
      const data = await journalService.getTodayEntries(user.id);
      setTodayEntries(data);
    } catch (err: any) {
      setTodayError(err.message || 'Failed to load today\'s entries');
      console.error('Error fetching today\'s entries:', err);
    } finally {
      setTodayLoading(false);
    }
  }, [user?.id]);

  // Fetch journal questions
  const refreshQuestions = useCallback(async () => {
    try {
      setQuestionsLoading(true);
      setQuestionsError(null);
      const data = await journalService.getJournalQuestions();
      setQuestions(data);
    } catch (err: any) {
      setQuestionsError(err.message || 'Failed to load journal questions');
      console.error('Error fetching journal questions:', err);
    } finally {
      setQuestionsLoading(false);
    }
  }, []);

  // Fetch user fields (system + custom)
  const refreshUserFields = useCallback(async () => {
    if (!user?.id) {
      console.log('[JournalContext] refreshUserFields: No user ID available');
      return;
    }

    try {
      setUserFieldsLoading(true);
      setUserFieldsError(null);
      const data = await journalService.getUserJournalFields(user.id);
      setUserFields(data);
    } catch (err: any) {
      setUserFieldsError(err.message || 'Failed to load user fields');
      console.error('Error fetching user fields:', err);
    } finally {
      setUserFieldsLoading(false);
    }
  }, [user?.id]);

  // Fetch custom fields only
  const refreshCustomFields = useCallback(async () => {
    if (!user?.id) {
      console.log('[JournalContext] refreshCustomFields: No user ID available');
      return;
    }

    try {
      setCustomFieldsLoading(true);
      setCustomFieldsError(null);
      const data = await customFieldService.getCustomFields(user.id);
      setCustomFields(data);
    } catch (err: any) {
      setCustomFieldsError(err.message || 'Failed to load custom fields');
      console.error('Error fetching custom fields:', err);
    } finally {
      setCustomFieldsLoading(false);
    }
  }, [user?.id]);

  // Fetch journal history
  const refreshHistory = useCallback(async () => {
    if (!user?.id) {
      console.log('[JournalContext] refreshHistory: No user ID available');
      return;
    }

    // Prevent multiple simultaneous calls
    if (historyLoading) {
      console.log('[JournalContext] refreshHistory: Already loading, skipping');
      return;
    }

    console.log('[JournalContext] refreshHistory: Starting fetch for user:', user.id);

    try {
      setHistoryLoading(true);
      setHistoryError(null);
      const data = await journalService.getJournalHistory(user.id, 30, 0);
      console.log('[JournalContext] refreshHistory: Success, got', data.length, 'groups');
      setHistoryGroups(data);
      setHistoryOffset(data.length);
      setHistoryHasMore(data.length === 30);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load journal history';
      console.error('[JournalContext] refreshHistory: Error:', err);
      setHistoryError(errorMessage);
    } finally {
      console.log('[JournalContext] refreshHistory: Setting loading to false');
      setHistoryLoading(false);
    }
  }, [user?.id, historyLoading]);

  // Load more history entries
  const loadMoreHistory = useCallback(async () => {
    if (!user?.id || !historyHasMore || historyLoading) return;

    try {
      setHistoryLoading(true);
      setHistoryError(null);
      const data = await journalService.getJournalHistory(user.id, 30, historyOffset);
      setHistoryGroups(prev => [...prev, ...data]);
      setHistoryOffset(prev => prev + data.length);
      setHistoryHasMore(data.length === 30);
    } catch (err: any) {
      setHistoryError(err.message || 'Failed to load more history');
      console.error('Error loading more history:', err);
    } finally {
      setHistoryLoading(false);
    }
  }, [user?.id, historyHasMore, historyLoading, historyOffset]);

  // Create new journal entry
  const createEntry = useCallback(async (input: JournalEntryInput): Promise<JournalEntry | null> => {
    if (!user?.id) return null;

    try {
      setTodayError(null);
      const newEntry = await journalService.createEntry(user.id, input);
      
      // Refresh today's entries to sync across all components
      await refreshTodayEntries();
      
      // Also refresh history if it's loaded
      if (historyGroups.length > 0) {
        await refreshHistory();
      }
      
      return newEntry;
    } catch (err: any) {
      setTodayError(err.message || 'Failed to create journal entry');
      console.error('Error creating journal entry:', err);
      return null;
    }
  }, [user?.id, refreshTodayEntries, refreshHistory, historyGroups.length]);

  // Update existing journal entry
  const updateEntry = useCallback(async (entryId: string, answer: string): Promise<JournalEntry | null> => {
    try {
      setTodayError(null);
      const updatedEntry = await journalService.updateEntry(entryId, answer);
      
      // Refresh today's entries to sync across all components
      await refreshTodayEntries();
      
      // Also refresh history if it's loaded
      if (historyGroups.length > 0) {
        await refreshHistory();
      }
      
      return updatedEntry;
    } catch (err: any) {
      setTodayError(err.message || 'Failed to update journal entry');
      console.error('Error updating journal entry:', err);
      return null;
    }
  }, [refreshTodayEntries, refreshHistory, historyGroups.length]);

  // Delete journal entry
  const deleteEntry = useCallback(async (entryId: string): Promise<boolean> => {
    try {
      setTodayError(null);
      await journalService.deleteEntry(entryId);
      
      // Refresh today's entries to sync across all components
      await refreshTodayEntries();
      
      // Also refresh history if it's loaded
      if (historyGroups.length > 0) {
        await refreshHistory();
      }
      
      return true;
    } catch (err: any) {
      setTodayError(err.message || 'Failed to delete journal entry');
      console.error('Error deleting journal entry:', err);
      return false;
    }
  }, [refreshTodayEntries, refreshHistory, historyGroups.length]);

  // Custom field operations
  const createCustomField = useCallback(async (input: CustomFieldInput): Promise<JournalCustomField | null> => {
    if (!user?.id) {
      console.log('[JournalContext] createCustomField: No user ID available');
      return null;
    }

    try {
      setCustomFieldsError(null);
      const newField = await customFieldService.createCustomField(user.id, input);

      // Refresh both custom fields and user fields
      await refreshCustomFields();
      await refreshUserFields();

      return newField;
    } catch (err: any) {
      setCustomFieldsError(err.message || 'Failed to create custom field');
      console.error('Error creating custom field:', err);
      return null;
    }
  }, [user?.id, refreshCustomFields, refreshUserFields]);

  const updateCustomField = useCallback(async (fieldId: string, update: CustomFieldUpdate): Promise<JournalCustomField | null> => {
    if (!user?.id) {
      console.log('[JournalContext] updateCustomField: No user ID available');
      return null;
    }

    try {
      setCustomFieldsError(null);
      const updatedField = await customFieldService.updateCustomField(user.id, fieldId, update);

      // Refresh both custom fields and user fields
      await refreshCustomFields();
      await refreshUserFields();

      return updatedField;
    } catch (err: any) {
      setCustomFieldsError(err.message || 'Failed to update custom field');
      console.error('Error updating custom field:', err);
      return null;
    }
  }, [user?.id, refreshCustomFields, refreshUserFields]);

  const archiveCustomField = useCallback(async (fieldId: string): Promise<boolean> => {
    if (!user?.id) {
      console.log('[JournalContext] archiveCustomField: No user ID available');
      return false;
    }

    try {
      setCustomFieldsError(null);
      await customFieldService.archiveCustomField(user.id, fieldId);

      // Refresh both custom fields and user fields
      await refreshCustomFields();
      await refreshUserFields();

      return true;
    } catch (err: any) {
      setCustomFieldsError(err.message || 'Failed to archive custom field');
      console.error('Error archiving custom field:', err);
      return false;
    }
  }, [user?.id, refreshCustomFields, refreshUserFields]);

  // Initialize data when user changes and has completed/skipped self-reflection
  useEffect(() => {
    if (user?.id && (selfReflectionCompleted || selfReflectionSkipped)) {
      // User is ready for journal features - load data
      refreshTodayEntries();
      refreshQuestions();
      refreshUserFields();
      refreshCustomFields();
      // Don't auto-load history - let journal page load it when needed
    } else {
      // Clear data when user logs out or hasn't completed self-reflection
      setTodayEntries([]);
      setQuestions([]);
      setUserFields([]);
      setCustomFields([]);
      setHistoryGroups([]);
      setHistoryOffset(0);
    }
  }, [user?.id, selfReflectionCompleted, selfReflectionSkipped, refreshTodayEntries, refreshQuestions, refreshUserFields, refreshCustomFields]);

  const value: JournalContextType = {
    // Today's entries
    todayEntries,
    todayLoading,
    todayError,

    // Questions (system only)
    questions,
    questionsLoading,
    questionsError,

    // User fields (system + custom)
    userFields,
    userFieldsLoading,
    userFieldsError,

    // Custom fields
    customFields,
    customFieldsLoading,
    customFieldsError,

    // History
    historyGroups,
    historyLoading,
    historyError,
    historyHasMore,

    // Actions
    refreshTodayEntries,
    refreshQuestions,
    refreshUserFields,
    refreshCustomFields,
    refreshHistory,
    loadMoreHistory,
    createEntry,
    updateEntry,
    deleteEntry,

    // Custom field actions
    createCustomField,
    updateCustomField,
    archiveCustomField,
  };

  return (
    <JournalContext.Provider value={value}>
      {children}
    </JournalContext.Provider>
  );
};
