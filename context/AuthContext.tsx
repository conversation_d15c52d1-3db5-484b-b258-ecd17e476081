import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { UserProfileService } from '@/lib/userProfileService';
import { voiceCreditsService } from '@/lib/voiceCreditsService';
import type { User, Session, AuthError } from '@supabase/supabase-js';
import type { Client } from '@/types/client';
import { Platform, AppState } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import * as WebBrowser from 'expo-web-browser';
import { isGoogleSigninEnabled, isSignupEnabled } from '@/lib/authConfig';
import { sanitizeErrorMessage, logSecureError } from '@/utils/secureErrorHandling';
import { setUserContext, clearUserContext, addBreadcrumb, startTransaction } from '@/lib/sentryConfig';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  userStatusLoading: boolean;
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
  selfReflectionSkipped: boolean;
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithEmail: (email: string, password: string, displayName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (fullName: string, avatarUrl?: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  completeSelfReflection: () => Promise<void>;
  skipSelfReflection: () => void;
  resetPassword: (email: string) => Promise<void>;
  error: string | null;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

const ONBOARDING_KEY = 'onboardingCompleted';
const SELF_REFLECTION_KEY = 'selfReflectionCompleted';

// Helper function to get user-specific keys
const getUserSpecificKey = (baseKey: string, userId?: string): string => {
  return userId ? `${baseKey}_${userId}` : baseKey;
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [userStatusLoading, setUserStatusLoading] = useState(true);
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);
  const [selfReflectionCompleted, setSelfReflectionCompleted] = useState(false);
  const [selfReflectionSkipped, setSelfReflectionSkipped] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExplicitSignOut, setIsExplicitSignOut] = useState(false);

  const clearError = () => setError(null);

  const handleAuthError = (error: AuthError | Error) => {
    logSecureError(error, { component: 'AuthContext' });
    const sanitizedMessage = sanitizeErrorMessage(error);
    setError(sanitizedMessage);
  };

  // Helper functions for onboarding status
  const saveOnboardingStatus = async (userId?: string) => {
    try {
      if (userId) {
        // Use database for authenticated users
        await UserProfileService.updateCompletionStatus(userId, {
          onboardingCompleted: true
        });
      } else {
        // Fallback to local storage for unauthenticated state
        const key = getUserSpecificKey(ONBOARDING_KEY, userId);
        if (Platform.OS === 'web') {
          localStorage.setItem(key, 'true');
        } else {
          await SecureStore.setItemAsync(key, 'true');
        }
      }
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      // Fallback to local storage on database error
      try {
        const key = getUserSpecificKey(ONBOARDING_KEY, userId);
        if (Platform.OS === 'web') {
          localStorage.setItem(key, 'true');
        } else {
          await SecureStore.setItemAsync(key, 'true');
        }
      } catch (fallbackError) {
        console.error('Fallback save also failed:', fallbackError);
      }
    }
  };

  const getOnboardingStatus = async (userId?: string): Promise<boolean> => {
    try {
      if (userId) {
        // Use database for authenticated users
        const status = await UserProfileService.getCompletionStatus(userId);
        return status.onboardingCompleted;
      } else {
        // Fallback to local storage for unauthenticated state
        const key = getUserSpecificKey(ONBOARDING_KEY, userId);
        if (Platform.OS === 'web') {
          return localStorage.getItem(key) === 'true';
        } else {
          const status = await SecureStore.getItemAsync(key);
          return status === 'true';
        }
      }
    } catch (error) {
      console.error('Error getting onboarding status:', error);
      // Fallback to local storage on database error
      try {
        const key = getUserSpecificKey(ONBOARDING_KEY, userId);
        if (Platform.OS === 'web') {
          return localStorage.getItem(key) === 'true';
        } else {
          const status = await SecureStore.getItemAsync(key);
          return status === 'true';
        }
      } catch (fallbackError) {
        console.error('Fallback get also failed:', fallbackError);
        return false;
      }
    }
  };

  // Helper functions for self-reflection status
  const saveSelfReflectionStatus = async (userId?: string) => {
    try {
      if (userId) {
        // Use database for authenticated users
        await UserProfileService.updateCompletionStatus(userId, {
          selfReflectionCompleted: true
        });
      } else {
        // Fallback to local storage for unauthenticated state
        const key = getUserSpecificKey(SELF_REFLECTION_KEY, userId);
        if (Platform.OS === 'web') {
          localStorage.setItem(key, 'true');
        } else {
          await SecureStore.setItemAsync(key, 'true');
        }
      }
    } catch (error) {
      console.error('Error saving self-reflection status:', error);
      // Fallback to local storage on database error
      try {
        const key = getUserSpecificKey(SELF_REFLECTION_KEY, userId);
        if (Platform.OS === 'web') {
          localStorage.setItem(key, 'true');
        } else {
          await SecureStore.setItemAsync(key, 'true');
        }
      } catch (fallbackError) {
        console.error('Fallback save also failed:', fallbackError);
      }
    }
  };

  const getSelfReflectionStatus = async (userId?: string): Promise<boolean> => {
    try {
      if (userId) {
        // Use database for authenticated users
        const status = await UserProfileService.getCompletionStatus(userId);
        return status.selfReflectionCompleted;
      } else {
        // Fallback to local storage for unauthenticated state
        const key = getUserSpecificKey(SELF_REFLECTION_KEY, userId);
        if (Platform.OS === 'web') {
          return localStorage.getItem(key) === 'true';
        } else {
          const status = await SecureStore.getItemAsync(key);
          return status === 'true';
        }
      }
    } catch (error) {
      console.error('Error getting self-reflection status:', error);
      // Fallback to local storage on database error
      try {
        const key = getUserSpecificKey(SELF_REFLECTION_KEY, userId);
        if (Platform.OS === 'web') {
          return localStorage.getItem(key) === 'true';
        } else {
          const status = await SecureStore.getItemAsync(key);
          return status === 'true';
        }
      } catch (fallbackError) {
        console.error('Fallback get also failed:', fallbackError);
        return false;
      }
    }
  };

  const completeOnboarding = async () => {
    try {
      await saveOnboardingStatus(user?.id);
      setOnboardingCompleted(true);
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  const completeSelfReflection = async () => {
    try {
      await saveSelfReflectionStatus(user?.id);
      setSelfReflectionCompleted(true);
    } catch (error) {
      console.error('Error completing self-reflection:', error);
    }
  };

  const skipSelfReflection = () => {
    // For skip operations, DON'T update database or completion status
    // User will see self-reflection again next time they log in
    // But set session-based skip state to allow access to tabs
    console.log('Setting selfReflectionSkipped to true for current session');
    setSelfReflectionSkipped(true);
  };

  // Helper function to create client record for new users
  const createClientRecordIfNeeded = async (user: User) => {
    try {
      // Check if client record already exists
      const { data: existingClient, error: checkError } = await supabase
        .from('clients')
        .select('id')
        .eq('id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected for new users
        console.error('Error checking existing client:', checkError);
        return;
      }

      if (existingClient) {
        // Client record already exists
        console.log('Client record already exists for user:', user.id);
        return;
      }

      // Extract metadata from user.user_metadata
      const metadata = user.user_metadata || {};
      const registrationMethod = metadata.registration_method || 'unknown';
      const createdByRole = metadata.created_by_role || 'system';

      // Create metadata object for notes field
      const clientMetadata = {
        registration_method: registrationMethod,
        created_by_role: createdByRole,
        signup_timestamp: new Date().toISOString(),
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
      };

      // Create new client record with enhanced metadata
      const now = new Date().toISOString();
      const clientData: Partial<Client> = {
        id: user.id,
        email: user.email || '',
        full_name: metadata.full_name || user.email?.split('@')[0] || 'User',
        status: 'active',
        intake_date: now,
        created_at: now,
        updated_at: now,
        created_by: createdByRole === 'system' ? 'system' : user.id, // Store creator role info
        notes: JSON.stringify(clientMetadata) // Store registration metadata as JSON
      };

      const { error: clientError } = await supabase
        .from('clients')
        .insert(clientData);

      if (clientError) {
        console.error('Failed to create client record:', clientError);
      } else {
        console.log('Client record created successfully for user:', user.id, 'with metadata:', clientMetadata);

        // Initialize voice credits for new user
        try {
          await voiceCreditsService.initializeUserCredits(user.id);
          console.log('Voice credits initialized for user:', user.id);
        } catch (creditsError) {
          console.error('Failed to initialize voice credits:', creditsError);
          // Don't throw - client record was created successfully
        }
      }

    } catch (error) {
      console.error('Error in createClientRecordIfNeeded:', error);
    }
  };

  const signInWithGoogle = async () => {
    // Check if Google sign-in is enabled
    if (!isGoogleSigninEnabled()) {
      setError('Google sign-in is temporarily disabled');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Warm up browser on Android for better performance
      if (Platform.OS === 'android') {
        await WebBrowser.warmUpAsync();
      }

      const redirectUrl = Platform.select({
        web: window.location.origin,
        default: 'temani://auth/callback',
      });

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            prompt: 'select_account',
          },
          // Add client role metadata for Google OAuth users
          data: {
            role: 'client',
            created_by_role: 'system',
            registration_method: 'google_oauth'
          }
        },
      });

      if (error) {
        handleAuthError(error);
      }
    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);

      // Cool down browser on Android to free memory
      if (Platform.OS === 'android') {
        WebBrowser.coolDownAsync();
      }
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        handleAuthError(error);
      }
    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmail = async (email: string, password: string, displayName?: string) => {
    // Check if sign-up is enabled
    if (!isSignupEnabled()) {
      setError('User registration is temporarily disabled');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 1. Create auth user with client role metadata
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: displayName,
            role: 'client',
            created_by_role: 'system',
            registration_method: 'email_signup'
          }
        }
      });

      if (authError) {
        handleAuthError(authError);
        return;
      }

      if (!authData.user) {
        throw new Error('User creation failed');
      }

      // 2. Automatically sign in the user after successful signup
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (signInError) {
        console.error('Auto sign-in after signup failed:', signInError);
        // Don't throw - user was created successfully, they can manually sign in
        handleAuthError(signInError);
        return;
      }

      console.log('User successfully signed up and automatically signed in:', authData.user.id);
      // Client record creation will be handled by the onAuthStateChange listener

    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (fullName: string, avatarUrl?: string) => {
    try {
      setLoading(true);
      setError(null);

      const updates: { full_name: string; avatar_url?: string } = {
        full_name: fullName,
      };

      if (avatarUrl) {
        updates.avatar_url = avatarUrl;
      }

      const { error } = await supabase.auth.updateUser({
        data: updates,
      });

      if (error) {
        handleAuthError(error);
      }
    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  const updatePassword = async (newPassword: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        handleAuthError(error);
      }
    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setLoading(true);
      setError(null);

      // Get base URL for redirection after password reset
      const baseUrl = Platform.OS === 'web' ?
        window.location.origin :
        'temani://reset-password';

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${baseUrl}/reset-password`,
      });

      if (error) {
        handleAuthError(error);
      }
    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      setIsExplicitSignOut(true); // Mark this as an explicit sign out

      // Reset session-based skip state on sign out
      setSelfReflectionSkipped(false);

      const { error } = await supabase.auth.signOut();

      if (error) {
        handleAuthError(error);
      }
    } catch (error) {
      handleAuthError(error as Error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Handle app state changes for token refresh
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        supabase.auth.startAutoRefresh();
      } else {
        supabase.auth.stopAutoRefresh();
      }
    };

    // Add app state listener
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Get initial session and onboarding status
    const getInitialState = async () => {
      try {
        // Get session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
        } else {
          setSession(session);
          setUser(session?.user ?? null);
        }

        // Get onboarding and self-reflection status
        setUserStatusLoading(true);
        try {
          if (session?.user?.id) {
            // For authenticated users, get status from database
            const onboardingStatus = await getOnboardingStatus(session.user.id);
            const selfReflectionStatus = await getSelfReflectionStatus(session.user.id);
            setOnboardingCompleted(onboardingStatus);
            setSelfReflectionCompleted(selfReflectionStatus);
          } else {
            // For unauthenticated users, check local storage for onboarding status
            // This prevents returning users from being sent back to onboarding on failed login
            const localOnboardingStatus = await getOnboardingStatus();
            setOnboardingCompleted(localOnboardingStatus);
            setSelfReflectionCompleted(false); // Always false for unauthenticated users
          }
        } catch (error) {
          console.error('Error loading initial user status:', error);
          setOnboardingCompleted(false);
          setSelfReflectionCompleted(false);
        } finally {
          setUserStatusLoading(false);
        }
      } catch (error) {
        console.error('Error in getInitialState:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    getInitialState();

    // Listen for auth changes
    const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
      async (event: any, session: any) => {
        console.log('[AuthContext] Auth state change:', {
          event,
          hasSession: !!session,
          userId: session?.user?.id || null
        });

        // Add Sentry breadcrumb for auth state changes
        addBreadcrumb(
          `Auth state changed: ${event}`,
          'auth',
          {
            event,
            hasSession: !!session,
            // Don't include actual user ID for privacy
            hasUser: !!session?.user
          }
        );

        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Handle successful authentication
        if (event === 'SIGNED_IN' && session) {
          // Set user context in Sentry (privacy-safe)
          setUserContext(session.user.id, {
            authMethod: session.user.app_metadata?.provider || 'email',
            signInTime: new Date().toISOString(),
          });
          setUserStatusLoading(true);

          try {
            // Create client record for new users (handles both email signup and Google OAuth)
            await createClientRecordIfNeeded(session.user);

            // Check if we need to migrate from local storage
            const localOnboardingKey = getUserSpecificKey(ONBOARDING_KEY, session.user.id);
            const localSelfReflectionKey = getUserSpecificKey(SELF_REFLECTION_KEY, session.user.id);

            let localOnboardingStatus = false;
            let localSelfReflectionStatus = false;

            // Get local storage values for migration
            try {
              if (Platform.OS === 'web') {
                localOnboardingStatus = localStorage.getItem(localOnboardingKey) === 'true';
                localSelfReflectionStatus = localStorage.getItem(localSelfReflectionKey) === 'true';
              } else {
                const onboardingLocal = await SecureStore.getItemAsync(localOnboardingKey);
                const selfReflectionLocal = await SecureStore.getItemAsync(localSelfReflectionKey);
                localOnboardingStatus = onboardingLocal === 'true';
                localSelfReflectionStatus = selfReflectionLocal === 'true';
              }
            } catch (localError) {
              console.warn('Could not read local storage for migration:', localError);
            }

            // Migrate local data to database if needed
            if (localOnboardingStatus || localSelfReflectionStatus) {
              await UserProfileService.migrateFromLocalStorage(
                session.user.id,
                localOnboardingStatus,
                localSelfReflectionStatus
              );

              // Clean up local storage after successful migration
              try {
                if (Platform.OS === 'web') {
                  localStorage.removeItem(localOnboardingKey);
                  localStorage.removeItem(localSelfReflectionKey);
                } else {
                  await SecureStore.deleteItemAsync(localOnboardingKey);
                  await SecureStore.deleteItemAsync(localSelfReflectionKey);
                }
              } catch (cleanupError) {
                console.warn('Could not clean up local storage:', cleanupError);
              }
            }

            // Load user-specific status from database
            const userOnboardingStatus = await getOnboardingStatus(session.user.id);
            const userSelfReflectionStatus = await getSelfReflectionStatus(session.user.id);
            setOnboardingCompleted(userOnboardingStatus);
            setSelfReflectionCompleted(userSelfReflectionStatus);

            // Reset session-based skip state on new sign in
            setSelfReflectionSkipped(false);

          } catch (error) {
            console.error('Error loading user status:', error);
            // Fallback to false values for new users
            setOnboardingCompleted(false);
            setSelfReflectionCompleted(false);
            setSelfReflectionSkipped(false);
          } finally {
            setUserStatusLoading(false);
          }

          // Clear any URL parameters after OAuth callback
          if (Platform.OS === 'web' && window.location.hash) {
            window.history.replaceState({}, document.title, window.location.pathname);
          }
        }

        // Handle sign out
        if (event === 'SIGNED_OUT') {
          // Clear user context from Sentry
          clearUserContext();

          // Only reset completion status on explicit sign out, not on authentication failures
          if (isExplicitSignOut) {
            setOnboardingCompleted(false);
            setSelfReflectionCompleted(false);
            setSelfReflectionSkipped(false);

            // Clear local storage on explicit sign out
            try {
              const onboardingKey = getUserSpecificKey(ONBOARDING_KEY);
              const selfReflectionKey = getUserSpecificKey(SELF_REFLECTION_KEY);

              if (Platform.OS === 'web') {
                localStorage.removeItem(onboardingKey);
                localStorage.removeItem(selfReflectionKey);
              } else {
                await SecureStore.deleteItemAsync(onboardingKey);
                await SecureStore.deleteItemAsync(selfReflectionKey);
              }
            } catch (error) {
              console.warn('Could not clear local storage on sign out:', error);
            }

            setIsExplicitSignOut(false); // Reset the flag
          }
          setUserStatusLoading(true); // Reset to loading state
        }
      }
    );

    return () => {
      subscription?.remove();
      authSubscription.unsubscribe();
    };
  }, []);

  const value: AuthContextType = {
    user,
    session,
    loading,
    userStatusLoading,
    onboardingCompleted,
    selfReflectionCompleted,
    selfReflectionSkipped,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    updateProfile,
    updatePassword,
    completeOnboarding,
    completeSelfReflection,
    skipSelfReflection,
    resetPassword,
    error,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};