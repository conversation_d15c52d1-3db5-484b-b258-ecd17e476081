import React, { createContext, useContext, useState, ReactNode } from 'react';

interface VoiceModeContextType {
  voiceMode: boolean;
  setVoiceMode: (mode: boolean) => void;
}

const VoiceModeContext = createContext<VoiceModeContextType | undefined>(undefined);

interface VoiceModeProviderProps {
  children: ReactNode;
}

export function VoiceModeProvider({ children }: VoiceModeProviderProps) {
  const [voiceMode, setVoiceMode] = useState(false);

  return (
    <VoiceModeContext.Provider value={{ voiceMode, setVoiceMode }}>
      {children}
    </VoiceModeContext.Provider>
  );
}

export function useVoiceMode() {
  const context = useContext(VoiceModeContext);
  if (context === undefined) {
    throw new Error('useVoiceMode must be used within a VoiceModeProvider');
  }
  return context;
}
