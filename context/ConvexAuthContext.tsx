import React, { createContext, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { useConvexAuth, useMutation, useQuery } from "convex/react";
import { useAuthActions } from "@convex-dev/auth/react";
import { api } from "../convex/_generated/api";
import { sanitizeErrorMessage, logSecureError } from '@/utils/secureErrorHandling';
import { addBreadcrumb, setUserContext } from '@/lib/sentryConfig';
import * as SecureStore from 'expo-secure-store';

interface ConvexAuthContextType {
  user: any | null;
  loading: boolean;
  userStatusLoading: boolean;
  connectionError: string | null;
  onboardingCompleted: boolean;
  selfReflectionCompleted: boolean;
  selfReflectionSkipped: boolean;
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithEmail: (email: string, password: string, displayName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (fullName: string, avatarUrl?: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  completeOnboarding: () => Promise<void>;
  completeSelfReflection: () => Promise<void>;
  skipSelfReflection: () => void;
  resetPassword: (email: string) => Promise<void>;
  resetOptimisticState: () => void;
  retryConnection: () => void;
  error: string | null;
  clearError: () => void;
}

const ConvexAuthContext = createContext<ConvexAuthContextType | undefined>(undefined);

// localStorage utilities for unauthenticated onboarding state
const ONBOARDING_KEY = 'onboarding_completed';

const getUserSpecificKey = (baseKey: string, userId?: string) => {
  return userId ? `${baseKey}_${userId}` : baseKey;
};

const saveOnboardingToLocalStorage = async (userId?: string) => {
  try {
    const key = getUserSpecificKey(ONBOARDING_KEY, userId);
    if (Platform.OS === 'web') {
      localStorage.setItem(key, 'true');
    } else {
      await SecureStore.setItemAsync(key, 'true');
    }
    console.log('💾 [LocalStorage] Onboarding status saved to localStorage');
  } catch (error) {
    console.error('❌ [LocalStorage] Failed to save onboarding status:', error);
  }
};

const getOnboardingFromLocalStorage = async (userId?: string): Promise<boolean> => {
  try {
    const key = getUserSpecificKey(ONBOARDING_KEY, userId);
    if (Platform.OS === 'web') {
      return localStorage.getItem(key) === 'true';
    } else {
      const status = await SecureStore.getItemAsync(key);
      return status === 'true';
    }
  } catch (error) {
    console.error('❌ [LocalStorage] Failed to get onboarding status:', error);
    return false;
  }
};

export const ConvexAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoading, isAuthenticated } = useConvexAuth();
  const { signIn, signOut: authSignOut } = useAuthActions();
  const [error, setError] = useState<string | null>(null);
  const [userStatusLoading, setUserStatusLoading] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  // Optimistic state to prevent race conditions
  const [optimisticOnboardingCompleted, setOptimisticOnboardingCompleted] = useState<boolean | null>(null);
  const [optimisticSelfReflectionCompleted, setOptimisticSelfReflectionCompleted] = useState<boolean | null>(null);
  const [optimisticSelfReflectionSkipped, setOptimisticSelfReflectionSkipped] = useState<boolean | null>(null);

  // Local state for unauthenticated onboarding status
  const [localOnboardingCompleted, setLocalOnboardingCompleted] = useState(false);

  // Connection monitoring and retry logic
  const retryConnection = () => {
    setConnectionError(null);
    // The Convex client will automatically attempt to reconnect
  };

  // Retry logic for mutations with exponential backoff
  const retryMutation = async (
    mutationFn: () => Promise<any>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<any> => {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await mutationFn();
      } catch (error: any) {
        const isLastAttempt = attempt === maxRetries - 1;
        const isConnectionError = error.message?.includes('WebSocket') ||
                                 error.message?.includes('network') ||
                                 error.message?.includes('connection');

        if (isLastAttempt || !isConnectionError) {
          throw error;
        }

        // Exponential backoff: wait 1s, 2s, 4s, etc.
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    throw new Error('Max retries exceeded');
  };

  // Get current user data
  const user = useQuery(api.auth.getCurrentUser);
  const userProfile = useQuery(api.userProfiles.getCurrentUserProfile);

  // Mutations
  const createUserProfile = useMutation(api.userProfiles.createUserProfile);
  const updateUserProfile = useMutation(api.userProfiles.updateUserProfile);
  const updateUserAuth = useMutation(api.auth.updateUser);
  const getOrCreateProfile = useMutation(api.userProfiles.getOrCreateUserProfile);

  const handleAuthError = (error: any) => {
    logSecureError(error, { component: 'ConvexAuthContext' });
    const sanitizedMessage = sanitizeErrorMessage(error);
    setError(sanitizedMessage);
  };

  const signInWithGoogle = async () => {
    try {
      setError(null);
      addBreadcrumb('Google sign-in initiated', 'auth');
      await signIn("google");
    } catch (error) {
      handleAuthError(error);
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      setError(null);
      addBreadcrumb('Email sign-in initiated', 'auth', { email });
      await signIn("password", { email, password, flow: "signIn" });
    } catch (error) {
      handleAuthError(error);
    }
  };

  const signUpWithEmail = async (email: string, password: string, displayName?: string) => {
    try {
      setError(null);
      addBreadcrumb('Email sign-up initiated', 'auth', { email, hasDisplayName: !!displayName });
      await signIn("password", {
        email,
        password,
        name: displayName || "User",
        flow: "signUp"
      });
    } catch (error) {
      handleAuthError(error);
    }
  };

  const signOut = async () => {
    try {
      setError(null);
      addBreadcrumb('Sign-out initiated', 'auth');
      await authSignOut();
    } catch (error) {
      handleAuthError(error);
    }
  };

  const updateProfile = async (fullName: string, avatarUrl?: string) => {
    try {
      setError(null);
      addBreadcrumb('Profile update initiated', 'user');
      await updateUserAuth({
        name: fullName,
        image: avatarUrl,
      });
    } catch (error) {
      handleAuthError(error);
    }
  };

  const updatePassword = async (newPassword: string) => {
    try {
      setError(null);
      // Note: Password updates in Convex Auth might need to be handled differently
      // This is a placeholder - you may need to implement this via the Password provider
      throw new Error("Password update not yet implemented in Convex Auth");
    } catch (error) {
      handleAuthError(error);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setError(null);
      // Note: Password reset in Convex Auth might need to be handled differently
      // This is a placeholder - you may need to implement this via the Password provider
      throw new Error("Password reset not yet implemented in Convex Auth");
    } catch (error) {
      handleAuthError(error);
    }
  };

  // Reset optimistic state (useful for testing and error recovery)
  const resetOptimisticState = () => {
    setOptimisticOnboardingCompleted(null);
    setOptimisticSelfReflectionCompleted(null);
    setOptimisticSelfReflectionSkipped(null);
  };

  const completeOnboarding = async () => {
    // Prevent multiple simultaneous calls
    if (optimisticOnboardingCompleted === true) {
      console.log('🔄 [Onboarding] Already completing, skipping...');
      return;
    }

    try {
      addBreadcrumb('Onboarding completion initiated', 'user');

      if (isAuthenticated && user) {
        // Authenticated user: Save to database
        console.log('🔐 [Onboarding] Authenticated user - saving to database');

        // Set optimistic state immediately
        setOptimisticOnboardingCompleted(true);

        // Ensure profile exists before updating
        await retryMutation(() => getOrCreateProfile());

        // Update database
        await retryMutation(() => updateUserProfile({
          onboardingCompleted: true,
        }));

        // Clear optimistic state after successful update
        setOptimisticOnboardingCompleted(null);
        console.log('✅ [Onboarding] Database update completed');
      } else {
        // Unauthenticated user: Save to localStorage
        console.log('🌐 [Onboarding] Unauthenticated user - saving to localStorage');

        await saveOnboardingToLocalStorage();
        setLocalOnboardingCompleted(true);

        console.log('✅ [Onboarding] localStorage update completed');
      }
    } catch (error: any) {
      if (isAuthenticated && user) {
        // Revert optimistic state on error for authenticated users
        setOptimisticOnboardingCompleted(null);

        // If profile not found, try to create it first
        if (error.message?.includes('not found')) {
          try {
            await retryMutation(() => getOrCreateProfile());
            await retryMutation(() => updateUserProfile({
              onboardingCompleted: true,
            }));
            setOptimisticOnboardingCompleted(null);
          } catch (retryError) {
            handleAuthError(retryError);
          }
        } else {
          handleAuthError(error);
        }
      } else {
        // For unauthenticated users, just log the error
        console.error('❌ [Onboarding] Failed to save to localStorage:', error);
      }
    }
  };

  const completeSelfReflection = async () => {
    try {
      // Check if user is authenticated before proceeding
      if (!isAuthenticated || !user) {
        console.log('Cannot complete self-reflection - user not authenticated yet');
        return; // Skip silently instead of throwing error
      }

      addBreadcrumb('Self-reflection completion initiated', 'user');

      // Set optimistic state immediately
      setOptimisticSelfReflectionCompleted(true);

      // Ensure profile exists before updating
      await retryMutation(() => getOrCreateProfile());

      // Update database
      await retryMutation(() => updateUserProfile({
        selfReflectionCompleted: true,
      }));

      // Clear optimistic state after successful update
      setOptimisticSelfReflectionCompleted(null);
    } catch (error: any) {
      // Revert optimistic state on error
      setOptimisticSelfReflectionCompleted(null);

      // If profile not found, try to create it first
      if (error.message?.includes('not found')) {
        try {
          await retryMutation(() => getOrCreateProfile());
          await retryMutation(() => updateUserProfile({
            selfReflectionCompleted: true,
          }));
          setOptimisticSelfReflectionCompleted(null);
        } catch (retryError) {
          handleAuthError(retryError);
        }
      } else {
        handleAuthError(error);
      }
    }
  };

  const skipSelfReflection = () => {
    // Check if user is authenticated before proceeding
    if (!isAuthenticated || !user) {
      console.log('Cannot skip self-reflection - user not authenticated yet');
      return; // Skip silently instead of throwing error
    }

    addBreadcrumb('Self-reflection skipped', 'user');

    // Set optimistic state immediately
    setOptimisticSelfReflectionSkipped(true);

    // Update database
    updateUserProfile({
      selfReflectionSkipped: true,
    }).then(() => {
      // Clear optimistic state after successful update
      setOptimisticSelfReflectionSkipped(null);
    }).catch(error => {
      // Revert optimistic state on error
      setOptimisticSelfReflectionSkipped(null);
      handleAuthError(error);
    });
  };

  // Monitor connection state changes
  useEffect(() => {
    if (isAuthenticated) {
      setConnectionError(null);
    }
  }, [isAuthenticated, isLoading]);

  // Create user profile on first sign-in
  useEffect(() => {
    if (isAuthenticated && user && !userProfile && !userStatusLoading) {
      setUserStatusLoading(true);
      addBreadcrumb('Creating user profile for new user', 'user', { userId: user._id });

      // Add a small delay to ensure the auth session is fully established
      const timer = setTimeout(() => {
        // Add timeout to prevent hanging
        const profileCreationTimeout = setTimeout(() => {
          console.error('Profile creation timeout - forcing completion');
          setUserStatusLoading(false);
          setConnectionError('Profile creation timed out. Please refresh the page.');
        }, 15000); // Increased to 15 second timeout

        retryMutation(() => getOrCreateProfile())
          .then(() => {
            clearTimeout(profileCreationTimeout);
            addBreadcrumb('User profile created/retrieved successfully', 'user');
            setConnectionError(null);
            console.log('✅ [ConvexAuth] Profile creation completed successfully');
          })
          .catch(error => {
            clearTimeout(profileCreationTimeout);
            console.error('❌ [ConvexAuth] Profile creation failed:', error);

            // Check if it's a connection error
            const isConnectionError = error.message?.includes('WebSocket') ||
                                     error.message?.includes('network') ||
                                     error.message?.includes('connection') ||
                                     error.message?.includes('1006');

            if (isConnectionError) {
              setConnectionError('Connection failed. Please check your internet connection and try again.');
            } else if (!error.message?.includes('Unauthorized')) {
              setConnectionError('Profile creation failed. Please refresh the page.');
              handleAuthError(error);
            }
          })
          .finally(() => {
            console.log('🔄 [ConvexAuth] Profile creation process finished - resetting userStatusLoading');
            setUserStatusLoading(false);
          });
      }, 500); // Increased delay to 500ms for better stability

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, user, userProfile, userStatusLoading, getOrCreateProfile]);

  // Initialize localStorage onboarding status for unauthenticated users
  useEffect(() => {
    const initializeOnboardingStatus = async () => {
      if (!isAuthenticated && !isLoading) {
        console.log('🔄 [LocalStorage] Initializing onboarding status from localStorage');
        const localStatus = await getOnboardingFromLocalStorage();
        setLocalOnboardingCompleted(localStatus);
        console.log(`📖 [LocalStorage] Onboarding status loaded: ${localStatus}`);
      }
    };
    initializeOnboardingStatus();
  }, [isAuthenticated, isLoading]);

  // Reset optimistic state when authentication changes
  useEffect(() => {
    if (!isAuthenticated) {
      // Clear optimistic state when user logs out
      resetOptimisticState();
    }
  }, [isAuthenticated]);

  // Set Sentry user context when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      setUserContext(user._id, {
        authMethod: userProfile?.registrationMethod || 'unknown',
        signInTime: new Date().toISOString(),
      });
    }
  }, [isAuthenticated, user, userProfile]);

  const clearError = () => setError(null);

  const value: ConvexAuthContextType = {
    user,
    loading: isLoading,
    userStatusLoading,
    connectionError,
    // For authenticated users: use optimistic state or database state
    // For unauthenticated users: use localStorage state
    onboardingCompleted: isAuthenticated
      ? (optimisticOnboardingCompleted ?? userProfile?.onboardingCompleted ?? false)
      : localOnboardingCompleted,
    selfReflectionCompleted: optimisticSelfReflectionCompleted ?? userProfile?.selfReflectionCompleted ?? false,
    selfReflectionSkipped: optimisticSelfReflectionSkipped ?? userProfile?.selfReflectionSkipped ?? false,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    updateProfile,
    updatePassword,
    completeOnboarding,
    completeSelfReflection,
    skipSelfReflection,
    resetPassword,
    resetOptimisticState,
    retryConnection,
    error,
    clearError,
  };

  return (
    <ConvexAuthContext.Provider value={value}>
      {children}
    </ConvexAuthContext.Provider>
  );
};

export const useConvexAuthContext = () => {
  const context = useContext(ConvexAuthContext);
  if (context === undefined) {
    throw new Error('useConvexAuthContext must be used within a ConvexAuthProvider');
  }
  return context;
};
