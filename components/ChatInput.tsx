import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, Platform } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { chatStyles } from '@/styles/ChatStyles';
import { sanitizeChatMessage } from '@/utils/xssProtection';

interface ChatInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSend,
  disabled = false,
  placeholder = "Ketik pesan Anda..."
}) => {
  const [message, setMessage] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const sanitizeInput = (input: string): string => {
    return sanitizeChatMessage(input);
  };

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage || disabled) return;

    const sanitizedMessage = sanitizeInput(trimmedMessage);
    if (sanitizedMessage) {
      onSend(sanitizedMessage);
      setMessage('');
    }
  };

  const handleKeyPress = (event: any) => {
    if (Platform.OS === 'web' && event.nativeEvent.key === 'Enter' && !event.nativeEvent.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <View style={chatStyles.inputContainer}>
      <View style={[
        chatStyles.inputWrapper,
        isFocused && chatStyles.inputWrapperFocused
      ]}>
        <TextInput
          style={chatStyles.textInput}
          value={message}
          onChangeText={setMessage}
          placeholder={placeholder}
          placeholderTextColor="#94A3B8"
          multiline
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyPress={handleKeyPress}
          editable={!disabled}
          maxLength={1000}
        />
        
        <TouchableOpacity
          style={[
            chatStyles.sendButton,
            !canSend && chatStyles.sendButtonDisabled
          ]}
          onPress={handleSend}
          disabled={!canSend}
          activeOpacity={0.8}
        >
          <Feather 
            name="send" 
            size={18} 
            color={canSend ? "#fff" : "#94A3B8"} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};