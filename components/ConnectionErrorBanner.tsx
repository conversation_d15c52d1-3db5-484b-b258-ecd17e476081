import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useConvexAuthContext } from '@/context/ConvexAuthContext';

interface ConnectionErrorBannerProps {
  visible?: boolean;
}

export const ConnectionErrorBanner: React.FC<ConnectionErrorBannerProps> = ({ 
  visible = true 
}) => {
  const { connectionState, connectionError, retryConnection } = useConvexAuthContext();

  if (!visible || connectionState === 'connected' || !connectionError) {
    return null;
  }

  return (
    <View style={styles.banner}>
      <View style={styles.content}>
        <Text style={styles.errorText}>{connectionError}</Text>
        <TouchableOpacity 
          style={styles.retryButton} 
          onPress={retryConnection}
          activeOpacity={0.7}
        >
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  banner: {
    backgroundColor: '#FEF2F2',
    borderBottomWidth: 1,
    borderBottomColor: '#FECACA',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  errorText: {
    flex: 1,
    fontSize: 14,
    color: '#DC2626',
    marginRight: 12,
  },
  retryButton: {
    backgroundColor: '#A08CFB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
