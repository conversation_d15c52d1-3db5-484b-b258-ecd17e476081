import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { chatStyles } from '@/styles/ChatStyles';

interface ConfirmationModalProps {
  isVisible: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonColor?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isVisible,
  title,
  message,
  confirmText = 'Konfirmasi',
  cancelText = 'Batal',
  confirmButtonColor = '#EF4444',
  onConfirm,
  onCancel,
}) => {
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <TouchableWithoutFeedback onPress={onCancel}>
        <View style={chatStyles.confirmationOverlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={chatStyles.confirmationModal}>
              {/* Header */}
              <View style={chatStyles.confirmationHeader}>
                <Feather name="alert-triangle" size={24} color="#F59E0B" />
                <Text style={chatStyles.confirmationTitle}>{title}</Text>
              </View>

              {/* Message */}
              <Text style={chatStyles.confirmationMessage}>{message}</Text>

              {/* Action Buttons */}
              <View style={chatStyles.confirmationActions}>
                <TouchableOpacity
                  style={chatStyles.confirmationCancelButton}
                  onPress={onCancel}
                  activeOpacity={0.8}
                >
                  <Text style={chatStyles.confirmationCancelButtonText}>
                    {cancelText}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    chatStyles.confirmationConfirmButton,
                    { backgroundColor: confirmButtonColor }
                  ]}
                  onPress={onConfirm}
                  activeOpacity={0.8}
                >
                  <Text style={chatStyles.confirmationConfirmButtonText}>
                    {confirmText}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};