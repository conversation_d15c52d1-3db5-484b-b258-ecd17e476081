import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { chatStyles } from '@/styles/ChatStyles';
import type { Message } from '@/types/chat';

interface ChatMessageProps {
  message: Message;
  onCopy?: (content: string) => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, onCopy }) => {
  const isUser = message.sender_type === 'user';
  const timestamp = new Date(message.timestamp).toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const handleCopy = () => {
    if (onCopy) {
      onCopy(message.content);
    }
  };

  const getStatusIcon = () => {
    switch (message.status) {
      case 'sent':
        return <Feather name="check" size={12} color="#64748B" />;
      case 'delivered':
        return <Feather name="check-circle" size={12} color="#10B981" />;
      case 'read':
        return <Feather name="check-circle" size={12} color="#2B7EFF" />;
      default:
        return null;
    }
  };

  return (
    <View style={[
      chatStyles.messageWrapper,
      isUser && chatStyles.userMessageWrapper
    ]}>
      <TouchableOpacity
        style={[
          chatStyles.messageBubble,
          isUser ? chatStyles.userMessage : chatStyles.aiMessage
        ]}
        onLongPress={handleCopy}
        activeOpacity={0.8}
      >
        <Text style={[
          chatStyles.messageText,
          isUser ? chatStyles.userMessageText : chatStyles.aiMessageText
        ]}>
          {message.content}
        </Text>
      </TouchableOpacity>
      
      <View style={chatStyles.messageInfo}>
        <Text style={chatStyles.timestamp}>{timestamp}</Text>
        {isUser && (
          <View style={chatStyles.statusIndicator}>
            {getStatusIcon()}
          </View>
        )}
      </View>
    </View>
  );
};