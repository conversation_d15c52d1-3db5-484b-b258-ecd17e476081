import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useConvexAuthContext } from '@/context/ConvexAuthContext';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';

/**
 * Test component for Convex Auth functionality
 * This component helps verify that the Convex Auth migration is working correctly
 */
export const ConvexAuthTest: React.FC = () => {
  const {
    user,
    loading,
    userStatusLoading,
    onboardingCompleted,
    selfReflectionCompleted,
    selfReflectionSkipped,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    updateProfile,
    completeOnboarding,
    completeSelfReflection,
    skipSelfReflection,
    resetOptimisticState,
    error,
    clearError,
  } = useConvexAuthContext();

  // Convex mutations
  const resetSelfReflection = useMutation(api.userProfiles.resetSelfReflectionStatus);

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
      Alert.alert('Success', 'Google sign-in initiated');
    } catch (error) {
      Alert.alert('Error', 'Google sign-in failed');
    }
  };

  const handleEmailSignIn = async () => {
    try {
      await signInWithEmail('<EMAIL>', 'TestPassword123!');
      Alert.alert('Success', 'Email sign-in initiated');
    } catch (error) {
      Alert.alert('Error', 'Email sign-in failed');
    }
  };

  const handleEmailSignUp = async () => {
    try {
      await signUpWithEmail('<EMAIL>', 'TestPassword123!', 'Test User');
      Alert.alert('Success', 'Email sign-up initiated');
    } catch (error) {
      Alert.alert('Error', 'Email sign-up failed');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Success', 'Signed out successfully');
    } catch (error) {
      Alert.alert('Error', 'Sign-out failed');
    }
  };

  const handleUpdateProfile = async () => {
    try {
      await updateProfile('Updated Test User', 'https://example.com/avatar.jpg');
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Profile update failed');
    }
  };

  const handleCompleteOnboarding = async () => {
    try {
      await completeOnboarding();
      Alert.alert('Success', 'Onboarding completed');
    } catch (error) {
      Alert.alert('Error', 'Failed to complete onboarding');
    }
  };

  const handleCompleteSelfReflection = async () => {
    try {
      await completeSelfReflection();
      Alert.alert('Success', 'Self-reflection completed');
    } catch (error) {
      Alert.alert('Error', 'Failed to complete self-reflection');
    }
  };

  const handleSkipSelfReflection = () => {
    skipSelfReflection();
    Alert.alert('Success', 'Self-reflection skipped');
  };

  const handleResetSelfReflection = async () => {
    try {
      await resetSelfReflection();
      Alert.alert('Success', 'Self-reflection status reset! Refresh the app to see changes.');
    } catch (error) {
      Alert.alert('Error', 'Failed to reset self-reflection status');
    }
  };

  const handleResetOptimisticState = () => {
    resetOptimisticState();
    Alert.alert('Success', 'Optimistic state reset! This should fix any stuck routing issues.');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Convex Auth Test</Text>
      
      {/* Status Display */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusTitle}>Current Status:</Text>
        <Text style={styles.statusText}>Loading: {loading ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>User Status Loading: {userStatusLoading ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>User: {user ? `${user.name || user.email || user._id}` : 'Not signed in'}</Text>
        <Text style={styles.statusText}>Onboarding: {onboardingCompleted ? 'Completed' : 'Not completed'}</Text>
        <Text style={styles.statusText}>Self-reflection: {selfReflectionCompleted ? 'Completed' : selfReflectionSkipped ? 'Skipped' : 'Not completed'}</Text>
        {error && <Text style={styles.errorText}>Error: {error}</Text>}
      </View>

      {/* Authentication Actions */}
      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>Authentication Actions:</Text>
        <Text style={styles.infoText}>
          Password Requirements: At least 8 characters with uppercase, lowercase, and number
        </Text>
        
        <TouchableOpacity style={styles.button} onPress={handleGoogleSignIn}>
          <Text style={styles.buttonText}>Sign In with Google</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleEmailSignIn}>
          <Text style={styles.buttonText}>Sign In with Email</Text>
          <Text style={styles.buttonSubtext}><EMAIL> / TestPassword123!</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleEmailSignUp}>
          <Text style={styles.buttonText}>Sign Up with Email</Text>
          <Text style={styles.buttonSubtext}><EMAIL> / TestPassword123!</Text>
        </TouchableOpacity>

        {user && (
          <TouchableOpacity style={[styles.button, styles.signOutButton]} onPress={handleSignOut}>
            <Text style={styles.buttonText}>Sign Out</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Profile Actions */}
      {user && (
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Profile Actions:</Text>
          
          <TouchableOpacity style={styles.button} onPress={handleUpdateProfile}>
            <Text style={styles.buttonText}>Update Profile</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={handleCompleteOnboarding}>
            <Text style={styles.buttonText}>Complete Onboarding</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={handleCompleteSelfReflection}>
            <Text style={styles.buttonText}>Complete Self-reflection</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={handleSkipSelfReflection}>
            <Text style={styles.buttonText}>Skip Self-reflection</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.button, styles.resetButton]} onPress={handleResetSelfReflection}>
            <Text style={styles.buttonText}>🔄 Reset Self-reflection Status</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.button, styles.optimisticResetButton]} onPress={handleResetOptimisticState}>
            <Text style={styles.buttonText}>⚡ Reset Optimistic State</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Clear Error */}
      {error && (
        <TouchableOpacity style={[styles.button, styles.clearErrorButton]} onPress={clearError}>
          <Text style={styles.buttonText}>Clear Error</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  errorText: {
    fontSize: 14,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  actionsContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 15,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#A08CFB',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  signOutButton: {
    backgroundColor: '#e74c3c',
  },
  clearErrorButton: {
    backgroundColor: '#f39c12',
  },
  resetButton: {
    backgroundColor: '#ff9500',
  },
  optimisticResetButton: {
    backgroundColor: '#8e44ad',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonSubtext: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
    marginTop: 2,
  },
});
