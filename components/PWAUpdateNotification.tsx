/**
 * PWA Update Notification Component
 * Research-based implementation for seamless app updates
 * Prevents disruption during therapy sessions
 */

import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Platform, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface PWAUpdateNotificationProps {
  onUpdateApplied?: () => void;
  onUpdateDismissed?: () => void;
}

export default function PWAUpdateNotification({ 
  onUpdateApplied, 
  onUpdateDismissed 
}: PWAUpdateNotificationProps) {
  const [showUpdate, setShowUpdate] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);
  const slideAnim = new Animated.Value(-100);

  useEffect(() => {
    // Only run on web platform
    if (Platform.OS !== 'web' || typeof navigator === 'undefined') return;

    const handleServiceWorkerUpdate = () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          // Reload the page when new service worker takes control
          window.location.reload();
        });

        navigator.serviceWorker.ready.then((registration) => {
          // Check for waiting service worker
          if (registration.waiting) {
            setWaitingWorker(registration.waiting);
            showUpdateNotification();
          }

          // Listen for new service worker installing
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setWaitingWorker(newWorker);
                  showUpdateNotification();
                }
              });
            }
          });
        });
      }
    };

    handleServiceWorkerUpdate();
  }, []);

  const showUpdateNotification = () => {
    setShowUpdate(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const hideUpdateNotification = () => {
    Animated.timing(slideAnim, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowUpdate(false);
    });
  };

  const handleUpdateClick = async () => {
    if (!waitingWorker) return;

    setIsUpdating(true);
    
    try {
      // Tell the waiting service worker to skip waiting
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
      
      // Track update acceptance
      onUpdateApplied?.();
      
      // The page will reload automatically when the new SW takes control
    } catch (error) {
      console.error('Error applying update:', error);
      setIsUpdating(false);
    }
  };

  const handleDismiss = () => {
    hideUpdateNotification();
    onUpdateDismissed?.();
    
    // Don't show again for this session
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('pwa-update-dismissed', 'true');
    }
  };

  // Don't show if dismissed this session
  if (typeof window !== 'undefined' && sessionStorage.getItem('pwa-update-dismissed')) {
    return null;
  }

  if (!showUpdate) return null;

  return (
    <Animated.View 
      style={[
        styles.container,
        { transform: [{ translateY: slideAnim }] }
      ]}
    >
      <View style={styles.notification}>
        <View style={styles.iconContainer}>
          <Ionicons 
            name="refresh-circle" 
            size={24} 
            color="#4AACF4" 
          />
        </View>
        
        <View style={styles.content}>
          <Text style={styles.title}>Update Available</Text>
          <Text style={styles.message}>
            Pembaruan Temani tersedia dengan fitur dan perbaikan terbaru
          </Text>
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity 
            style={styles.updateButton}
            onPress={handleUpdateClick}
            disabled={isUpdating}
          >
            <Text style={styles.updateButtonText}>
              {isUpdating ? 'Updating...' : 'Update'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.dismissButton}
            onPress={handleDismiss}
            disabled={isUpdating}
          >
            <Ionicons name="close" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = {
  container: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  notification: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#4AACF4',
  },
  iconContainer: {
    marginRight: 12,
  },
  content: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  actions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
  },
  updateButton: {
    backgroundColor: '#4AACF4',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  updateButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600' as const,
  },
  dismissButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
};
