import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface JournalCalendarProps {
  currentMonth: Date;
  selectedDate: string | null;
  entryDates: string[];
  loading?: boolean;
  onDateSelect: (date: string) => void;
  onNavigateMonth: (direction: 'prev' | 'next') => void;
  onGoToToday: () => void;
}

const JournalCalendar: React.FC<JournalCalendarProps> = ({
  currentMonth,
  selectedDate,
  entryDates,
  loading = false,
  onDateSelect,
  onNavigateMonth,
  onGoToToday,
}) => {
  const monthNames = [
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  const dayNames = ['Min', 'Sen', 'Se<PERSON>', '<PERSON>b', '<PERSON><PERSON>', '<PERSON>m', 'Sab'];

  const today = new Date();
  const todayString = today.toISOString().split('T')[0];

  // Get first day of month and number of days
  const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
  const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startingDayOfWeek = firstDay.getDay();

  // Generate calendar days
  const calendarDays: (number | null)[] = [];
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null);
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  const formatDateString = (day: number): string => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth() + 1;
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  };

  const hasEntry = (day: number): boolean => {
    const dateString = formatDateString(day);
    return entryDates.includes(dateString);
  };

  const isToday = (day: number): boolean => {
    const dateString = formatDateString(day);
    return dateString === todayString;
  };

  const isSelected = (day: number): boolean => {
    const dateString = formatDateString(day);
    return dateString === selectedDate;
  };

  const renderDay = (day: number | null, index: number) => {
    if (day === null) {
      return <View key={index} style={styles.emptyDay} />;
    }

    const dateString = formatDateString(day);
    const dayHasEntry = hasEntry(day);
    const dayIsToday = isToday(day);
    const dayIsSelected = isSelected(day);

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.dayCell,
          dayIsToday && styles.todayCell,
          dayIsSelected && styles.selectedCell,
        ]}
        onPress={() => onDateSelect(dateString)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.dayText,
            dayIsToday && styles.todayText,
            dayIsSelected && styles.selectedText,
          ]}
        >
          {day}
        </Text>
        {dayHasEntry && (
          <View
            style={[
              styles.entryIndicator,
              dayIsSelected && styles.selectedEntryIndicator,
            ]}
          />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Calendar Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => onNavigateMonth('prev')}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={20} color="#DD76B9" />
        </TouchableOpacity>

        <View style={styles.monthYearContainer}>
          <Text style={styles.monthYearText}>
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.navButton}
          onPress={() => onNavigateMonth('next')}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-forward" size={20} color="#DD76B9" />
        </TouchableOpacity>
      </View>

      {/* Today Button */}
      <TouchableOpacity
        style={styles.todayButton}
        onPress={onGoToToday}
        activeOpacity={0.7}
      >
        <Text style={styles.todayButtonText}>Hari Ini</Text>
      </TouchableOpacity>

      {/* Calendar Grid */}
      <View style={styles.calendar}>
        {/* Day Headers */}
        <View style={styles.dayHeaderRow}>
          {dayNames.map((dayName, index) => (
            <View key={index} style={styles.dayHeader}>
              <Text style={styles.dayHeaderText}>{dayName}</Text>
            </View>
          ))}
        </View>

        {/* Calendar Days */}
        <View style={styles.daysGrid}>
          {calendarDays.map((day, index) => renderDay(day, index))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 6,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  navButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FFEBF9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  monthYearContainer: {
    flex: 1,
    alignItems: 'center',
  },
  monthYearText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#5B3E31',
  },
  todayButton: {
    backgroundColor: '#DD76B9',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'center',
    marginBottom: 8,
  },
  todayButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  calendar: {
    backgroundColor: '#FAFAFA',
    borderRadius: 12,
    padding: 4,
  },
  dayHeaderRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  dayHeader: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 4,
  },
  dayHeaderText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#9C2B6C',
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayCell: {
    width: '14.28%', // 100% / 7 days
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 2,
  },
  emptyDay: {
    width: '14.28%',
    height: 32,
  },
  dayText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#4B3425',
  },
  todayCell: {
    backgroundColor: '#E3F2FD',
    borderRadius: 6,
    borderWidth: 1.5,
    borderColor: '#4285F4',
  },
  todayText: {
    color: '#4285F4',
    fontWeight: '700',
  },
  selectedCell: {
    backgroundColor: '#DD76B9',
    borderRadius: 6,
  },
  selectedText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  entryIndicator: {
    position: 'absolute',
    bottom: 1,
    width: 5,
    height: 5,
    borderRadius: 2.5,
    backgroundColor: '#DD76B9',
  },
  selectedEntryIndicator: {
    backgroundColor: '#FFFFFF',
  },
});

export default JournalCalendar;
