/**
 * Voice Credits Display Component
 * Shows user's current credit balance and usage information
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useVoiceCredits } from '@/hooks/useVoiceCredits';

interface VoiceCreditsDisplayProps {
  showDetails?: boolean;
  onPress?: () => void;
  style?: any;
}

export const VoiceCreditsDisplay: React.FC<VoiceCreditsDisplayProps> = ({
  showDetails = false,
  onPress,
  style,
}) => {
  const { credits, loading, error, formatCreditsDisplay: formatDisplay, forceRefreshCredits } = useVoiceCredits();

  if (loading) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.loadingContainer}>
          <Ionicons name="time-outline" size={16} color="#666" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Ionicons name="warning-outline" size={16} color="#FF6B6B" />
        <Text style={styles.errorText}>Credits unavailable</Text>
      </View>
    );
  }

  if (!credits) {
    return null;
  }

  const isLowCredits = credits.creditsRemaining <= 2;
  const isNoCredits = credits.creditsRemaining === 0;

  const containerStyle = [
    styles.container,
    isNoCredits && styles.noCreditsContainer,
    isLowCredits && !isNoCredits && styles.lowCreditsContainer,
    style,
  ];

  const textStyle = [
    styles.creditsText,
    isNoCredits && styles.noCreditsText,
    isLowCredits && !isNoCredits && styles.lowCreditsText,
  ];

  const iconName = isNoCredits ? 'close-circle' : isLowCredits ? 'warning' : 'checkmark-circle';
  const iconColor = isNoCredits ? '#FF6B6B' : isLowCredits ? '#FFA500' : '#4CAF50';

  const content = (
    <View style={containerStyle}>
      <View style={styles.mainRow}>
        <Ionicons name={iconName} size={16} color={iconColor} />
        <Text style={textStyle}>
          {formatDisplay()}
        </Text>
        <TouchableOpacity
          onPress={forceRefreshCredits}
          style={styles.refreshButton}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh-outline" size={14} color="#666" />
        </TouchableOpacity>
        {onPress && (
          <Ionicons name="information-circle-outline" size={16} color="#666" />
        )}
      </View>
      
      {showDetails && (
        <View style={styles.detailsContainer}>
          <Text style={styles.detailText}>
            Used: {credits.creditsUsed} credits
          </Text>
          <Text style={styles.detailText}>
            Sessions: {credits.totalSessions}
          </Text>
          {credits.lastSession && (
            <Text style={styles.detailText}>
              Last call: {credits.lastSession.toLocaleDateString()}
            </Text>
          )}
        </View>
      )}
      
      {isNoCredits && (
        <Text style={styles.warningText}>
          No credits remaining for voice calls
        </Text>
      )}
      
      {isLowCredits && !isNoCredits && (
        <Text style={styles.warningText}>
          Low credits - only {credits.creditsRemaining} remaining
        </Text>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
  },
  errorContainer: {
    backgroundColor: '#FFF5F5',
    borderColor: '#FED7D7',
  },
  errorText: {
    fontSize: 14,
    color: '#FF6B6B',
    marginLeft: 8,
  },
  lowCreditsContainer: {
    backgroundColor: '#FFFBF0',
    borderColor: '#FED7AA',
  },
  noCreditsContainer: {
    backgroundColor: '#FFF5F5',
    borderColor: '#FED7D7',
  },
  mainRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  creditsText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    flex: 1,
  },
  lowCreditsText: {
    color: '#D69E2E',
  },
  noCreditsText: {
    color: '#E53E3E',
  },
  detailsContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  warningText: {
    fontSize: 12,
    color: '#E53E3E',
    marginTop: 4,
    fontStyle: 'italic',
  },
  refreshButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: 'transparent',
  },
});

export default VoiceCreditsDisplay;
