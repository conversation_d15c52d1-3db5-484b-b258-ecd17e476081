import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  Extrapolation
} from 'react-native-reanimated';

interface VoiceAnimationRingsProps {
  isConnected: boolean;
  voiceIntensity: any; // SharedValue
  peakValue: any; // SharedValue
  pulseAnimation: any; // SharedValue
  size?: number;
  children?: React.ReactNode;
}

export function VoiceAnimationRings({
  isConnected,
  voiceIntensity,
  peakValue,
  pulseAnimation,
  size = 140,
  children
}: VoiceAnimationRingsProps) {
  
  // Base pulse animation style (continuous when connected)
  const basePulseStyle = useAnimatedStyle(() => {
    if (!isConnected) {
      return {
        transform: [{ scale: 1 }],
        opacity: 0,
      };
    }
    
    const scale = interpolate(
      pulseAnimation.value,
      [0, 1],
      [1.0, 1.1],
      Extrapolation.CLAMP
    );
    
    const opacity = interpolate(
      pulseAnimation.value,
      [0, 1],
      [0.2, 0.4],
      Extrapolation.CLAMP
    );
    
    return {
      transform: [{ scale }],
      opacity,
    };
  });

  // Voice intensity animation style (reactive to microphone)
  const voiceIntensityStyle = useAnimatedStyle(() => {
    if (!isConnected) {
      return {
        transform: [{ scale: 1 }],
        opacity: 0,
      };
    }
    
    // Dramatically increased scale range: 1.0x to 2.0x (was 1.1x to 1.3x)
    const scale = interpolate(
      voiceIntensity.value,
      [0, 1],
      [1.0, 2.0],
      Extrapolation.CLAMP
    );
    
    // Improved opacity range: 0.2 to 1.0 (was 0.5 to 0.8)
    const opacity = interpolate(
      voiceIntensity.value,
      [0, 1],
      [0.2, 1.0],
      Extrapolation.CLAMP
    );
    
    return {
      transform: [{ scale }],
      opacity,
    };
  });

  // Peak detection animation style (for loud sounds)
  const peakPulseStyle = useAnimatedStyle(() => {
    if (!isConnected) {
      return {
        transform: [{ scale: 1 }],
        opacity: 0,
      };
    }
    
    // Dramatic peak effect: up to 2.5x scale
    const scale = interpolate(
      peakValue.value,
      [0, 1],
      [1.0, 2.5],
      Extrapolation.CLAMP
    );
    
    const opacity = interpolate(
      peakValue.value,
      [0, 1],
      [0, 0.8],
      Extrapolation.CLAMP
    );
    
    return {
      transform: [{ scale }],
      opacity,
    };
  });

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {/* Layer 1: Base pulse ring (soft blue, continuous) */}
      <Animated.View
        style={[
          styles.animationRing,
          styles.basePulseRing,
          { width: size, height: size, borderRadius: size / 2 },
          basePulseStyle
        ]}
      />
      
      {/* Layer 2: Voice intensity ring (bright green, reactive) */}
      <Animated.View
        style={[
          styles.animationRing,
          styles.voiceIntensityRing,
          { width: size, height: size, borderRadius: size / 2 },
          voiceIntensityStyle
        ]}
      />
      
      {/* Layer 3: Peak detection ring (bright yellow, dramatic) */}
      <Animated.View
        style={[
          styles.animationRing,
          styles.peakPulseRing,
          { width: size, height: size, borderRadius: size / 2 },
          peakPulseStyle
        ]}
      />
      
      {/* Content (mic button, etc.) */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  animationRing: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  basePulseRing: {
    backgroundColor: 'rgba(135, 196, 229, 0.3)', // Soft blue
  },
  voiceIntensityRing: {
    backgroundColor: 'rgba(34, 197, 94, 0.4)', // Bright green
  },
  peakPulseRing: {
    backgroundColor: 'rgba(251, 191, 36, 0.5)', // Bright yellow
  },
  content: {
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default VoiceAnimationRings;
