import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface StandardContinueButtonProps {
  onPress: () => void;
  disabled?: boolean;
  text?: string;
}

export const StandardContinueButton: React.FC<StandardContinueButtonProps> = ({
  onPress,
  disabled = false,
  text = "Lanjut"
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.continueButton,
        disabled ? styles.continueButtonInactive : styles.continueButtonActive
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={[
        styles.continueButtonText,
        disabled ? styles.continueButtonTextInactive : styles.continueButtonTextActive
      ]}>
        {text}
      </Text>
      <Ionicons 
        name="arrow-forward" 
        size={20} 
        color={disabled ? "#9CA3AF" : "#FFFFFF"} 
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    width: '100%',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
      },
      default: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      },
    }),
  },
  continueButtonActive: {
    backgroundColor: '#4EAF64',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 4px rgba(78, 175, 100, 0.1)',
      },
      default: {
        shadowColor: '#4EAF64',
      },
    }),
  },
  continueButtonInactive: {
    backgroundColor: '#F3F4F6',
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
      },
      default: {
        shadowColor: '#000',
      },
    }),
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
  continueButtonTextActive: {
    color: '#FFFFFF',
  },
  continueButtonTextInactive: {
    color: '#9CA3AF',
  },
});
