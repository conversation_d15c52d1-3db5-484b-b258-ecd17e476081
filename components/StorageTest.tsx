import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { runStorageTests, testSupabaseIntegration } from '../utils/testStorage';

interface TestResult {
  storageTest: {
    success: boolean;
    message: string;
  };
  supabaseTest: {
    success: boolean;
    message: string;
  };
}

export const StorageTest: React.FC = () => {
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setTestResult(null);

    try {
      // Run storage tests
      const storageSuccess = await runStorageTests();
      
      // Run Supabase integration test
      const supabaseResult = await testSupabaseIntegration();

      setTestResult({
        storageTest: {
          success: storageSuccess,
          message: storageSuccess ? 'Storage adapter tests passed' : 'Storage adapter tests failed'
        },
        supabaseTest: supabaseR<PERSON>ult
      });

    } catch (error) {
      setTestResult({
        storageTest: {
          success: false,
          message: `Test error: ${error instanceof Error ? error.message : String(error)}`
        },
        supabaseTest: {
          success: false,
          message: 'Could not run Supabase test due to storage test failure'
        }
      });
    } finally {
      setIsRunning(false);
    }
  };

  // Auto-run tests on component mount
  useEffect(() => {
    runTests();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Storage Adapter Test</Text>
      
      <TouchableOpacity 
        style={[styles.button, isRunning && styles.buttonDisabled]} 
        onPress={runTests}
        disabled={isRunning}
      >
        <Text style={styles.buttonText}>
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </Text>
      </TouchableOpacity>

      {testResult && (
        <ScrollView style={styles.resultsContainer}>
          <View style={styles.testSection}>
            <Text style={styles.sectionTitle}>Storage Adapter Test</Text>
            <View style={[
              styles.resultBox, 
              testResult.storageTest.success ? styles.successBox : styles.errorBox
            ]}>
              <Text style={[
                styles.resultText,
                testResult.storageTest.success ? styles.successText : styles.errorText
              ]}>
                {testResult.storageTest.success ? '✅' : '❌'} {testResult.storageTest.message}
              </Text>
            </View>
          </View>

          <View style={styles.testSection}>
            <Text style={styles.sectionTitle}>Supabase Integration Test</Text>
            <View style={[
              styles.resultBox, 
              testResult.supabaseTest.success ? styles.successBox : styles.errorBox
            ]}>
              <Text style={[
                styles.resultText,
                testResult.supabaseTest.success ? styles.successText : styles.errorText
              ]}>
                {testResult.supabaseTest.success ? '✅' : '❌'} {testResult.supabaseTest.message}
              </Text>
            </View>
          </View>

          <View style={styles.infoSection}>
            <Text style={styles.infoTitle}>Platform Information</Text>
            <Text style={styles.infoText}>
              Environment: {typeof window !== 'undefined' ? 'Web' : 'React Native'}
            </Text>
            <Text style={styles.infoText}>
              Window available: {typeof window !== 'undefined' ? 'Yes' : 'No'}
            </Text>
            <Text style={styles.infoText}>
              LocalStorage available: {typeof window !== 'undefined' && typeof window.localStorage !== 'undefined' ? 'Yes' : 'No'}
            </Text>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
  },
  testSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  resultBox: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
  },
  successBox: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
  },
  errorBox: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
  },
  resultText: {
    fontSize: 14,
    fontWeight: '500',
  },
  successText: {
    color: '#155724',
  },
  errorText: {
    color: '#721c24',
  },
  infoSection: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#e9ecef',
    borderRadius: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#495057',
  },
  infoText: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 5,
  },
});

export default StorageTest;
