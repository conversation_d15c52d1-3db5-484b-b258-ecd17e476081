import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { customFieldStyles } from '@/styles/CustomFieldStyles';
import type { FieldType } from '@/types/journal';

interface FieldTypeSelectorProps {
  selectedType: Exclude<FieldType, 'system'>;
  onTypeSelect: (type: Exclude<FieldType, 'system'>) => void;
  disabled?: boolean;
}

const fieldTypeOptions = [
  {
    type: 'custom_question' as const,
    icon: 'help-circle-outline' as const,
    title: 'Pertanyaan',
    description: 'Pertanyaan reflektif yang membutuhkan jawaban tertulis',
    color: '#DD76B9',
  },
  {
    type: 'custom_title' as const,
    icon: 'text-outline' as const,
    title: 'Judul',
    description: 'Header atau kategori untuk mengorganisir jurnal',
    color: '#4EAF64',
  },
];

export function FieldTypeSelector({ 
  selectedType, 
  onTypeSelect, 
  disabled = false 
}: FieldTypeSelectorProps) {
  return (
    <View style={customFieldStyles.typeSelector}>
      {fieldTypeOptions.map((option) => {
        const isSelected = selectedType === option.type;
        
        return (
          <TouchableOpacity
            key={option.type}
            style={[
              customFieldStyles.typeOption,
              isSelected && customFieldStyles.typeOptionSelected,
            ]}
            onPress={() => !disabled && onTypeSelect(option.type)}
            disabled={disabled}
            activeOpacity={0.7}
          >
            <View style={customFieldStyles.typeOptionIcon}>
              <Ionicons
                name={option.icon}
                size={24}
                color={isSelected ? option.color : '#666666'}
              />
            </View>
            
            <Text
              style={[
                customFieldStyles.typeOptionTitle,
                isSelected && { color: option.color },
              ]}
            >
              {option.title}
            </Text>
            
            <Text style={customFieldStyles.typeOptionDescription}>
              {option.description}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

export default FieldTypeSelector;
