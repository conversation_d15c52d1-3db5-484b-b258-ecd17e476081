import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  TextInput,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import type { JournalEntryWithQuestion, FieldType } from '@/types/journal';

interface EnhancedJournalHistoryProps {
  entries: JournalEntryWithQuestion[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

type FilterType = 'all' | 'system' | 'custom_question' | 'custom_title';

export function EnhancedJournalHistory({
  entries,
  loading = false,
  onLoadMore,
  hasMore = false,
}: EnhancedJournalHistoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [expandedEntries, setExpandedEntries] = useState<Set<string>>(new Set());

  const filterOptions = [
    { key: 'all' as const, label: 'Semua', icon: 'list-outline' },
    { key: 'system' as const, label: 'Sistem', icon: 'document-text-outline' },
    { key: 'custom_question' as const, label: 'Pertanyaan', icon: 'help-circle-outline' },
    { key: 'custom_title' as const, label: 'Judul', icon: 'text-outline' },
  ];

  // Filter and search entries
  const filteredEntries = useMemo(() => {
    let filtered = entries;

    // Apply field type filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(entry => {
        const fieldType = entry.question?.field_type || 'system';
        return fieldType === selectedFilter;
      });
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry => 
        entry.answer.toLowerCase().includes(query) ||
        entry.question?.question_text.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [entries, selectedFilter, searchQuery]);

  const toggleEntryExpansion = (entryId: string) => {
    const newExpanded = new Set(expandedEntries);
    if (newExpanded.has(entryId)) {
      newExpanded.delete(entryId);
    } else {
      newExpanded.add(entryId);
    }
    setExpandedEntries(newExpanded);
  };

  const getFieldTypeColor = (fieldType?: FieldType) => {
    switch (fieldType) {
      case 'custom_question':
        return '#DD76B9';
      case 'custom_title':
        return '#4EAF64';
      default:
        return '#666666';
    }
  };

  const getFieldTypeIcon = (fieldType?: FieldType) => {
    switch (fieldType) {
      case 'custom_question':
        return 'help-circle-outline';
      case 'custom_title':
        return 'text-outline';
      default:
        return 'document-text-outline';
    }
  };

  const renderFilterButton = (filter: typeof filterOptions[0]) => {
    const isSelected = selectedFilter === filter.key;
    const count = filter.key === 'all' 
      ? entries.length 
      : entries.filter(entry => (entry.question?.field_type || 'system') === filter.key).length;

    return (
      <TouchableOpacity
        key={filter.key}
        style={[
          styles.filterButton,
          isSelected && styles.filterButtonSelected,
        ]}
        onPress={() => setSelectedFilter(filter.key)}
      >
        <Ionicons
          name={filter.icon as any}
          size={16}
          color={isSelected ? '#FFFFFF' : '#666666'}
        />
        <Text style={[
          styles.filterButtonText,
          isSelected && styles.filterButtonTextSelected,
        ]}>
          {filter.label} ({count})
        </Text>
      </TouchableOpacity>
    );
  };

  const renderHistoryEntry = ({ item }: { item: JournalEntryWithQuestion }) => {
    const isExpanded = expandedEntries.has(item.id);
    const fieldType = item.question?.field_type || 'system';
    const fieldColor = getFieldTypeColor(fieldType);
    const shouldTruncate = item.answer.length > 150;
    const displayAnswer = isExpanded || !shouldTruncate 
      ? item.answer 
      : `${item.answer.substring(0, 150)}...`;

    return (
      <View style={styles.entryCard}>
        {/* Entry Header */}
        <View style={styles.entryHeader}>
          <View style={styles.entryHeaderLeft}>
            <Ionicons 
              name={getFieldTypeIcon(fieldType)} 
              size={16} 
              color={fieldColor} 
            />
            <Text style={[styles.entryQuestion, { color: fieldColor }]}>
              {item.question?.question_text || 'Unknown Question'}
            </Text>
          </View>
          <Text style={styles.entryDate}>
            {new Date(item.created_at).toLocaleDateString('id-ID', {
              day: 'numeric',
              month: 'short',
              year: 'numeric',
            })}
          </Text>
        </View>

        {/* Entry Content */}
        <TouchableOpacity
          onPress={() => shouldTruncate && toggleEntryExpansion(item.id)}
          disabled={!shouldTruncate}
        >
          <Text style={styles.entryAnswer}>
            {displayAnswer}
          </Text>
          {shouldTruncate && (
            <Text style={styles.expandButton}>
              {isExpanded ? 'Tampilkan lebih sedikit' : 'Tampilkan selengkapnya'}
            </Text>
          )}
        </TouchableOpacity>

        {/* Entry Footer */}
        <View style={styles.entryFooter}>
          <Text style={styles.entryMeta}>
            {item.answer.length} karakter
          </Text>
          {item.updated_at !== item.created_at && (
            <Text style={styles.entryMeta}>
              Diupdate {new Date(item.updated_at).toLocaleDateString('id-ID')}
            </Text>
          )}
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-text-outline" size={64} color="#CCCCCC" />
      <Text style={styles.emptyStateTitle}>
        {searchQuery ? 'Tidak Ada Hasil' : 'Belum Ada Entri'}
      </Text>
      <Text style={styles.emptyStateDescription}>
        {searchQuery 
          ? `Tidak ditemukan entri yang cocok dengan "${searchQuery}"`
          : 'Mulai menulis jurnal untuk melihat riwayat di sini'
        }
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666666" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cari dalam jurnal..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {filterOptions.map(renderFilterButton)}
      </View>

      {/* Results Summary */}
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryText}>
          {filteredEntries.length} entri
          {searchQuery && ` untuk "${searchQuery}"`}
          {selectedFilter !== 'all' && ` (${filterOptions.find(f => f.key === selectedFilter)?.label})`}
        </Text>
      </View>

      {/* Entries List */}
      <FlatList
        data={filteredEntries}
        renderItem={renderHistoryEntry}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          filteredEntries.length === 0 ? { flex: 1 } : { paddingBottom: 20 }
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    margin: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  filterButtonSelected: {
    backgroundColor: '#DD76B9',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  filterButtonTextSelected: {
    color: '#FFFFFF',
  },
  summaryContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 14,
    color: '#666666',
  },
  entryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  entryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  entryQuestion: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    flex: 1,
  },
  entryDate: {
    fontSize: 12,
    color: '#666666',
  },
  entryAnswer: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 22,
    marginBottom: 8,
  },
  expandButton: {
    fontSize: 14,
    color: '#DD76B9',
    fontWeight: '500',
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  entryMeta: {
    fontSize: 12,
    color: '#999999',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default EnhancedJournalHistory;
