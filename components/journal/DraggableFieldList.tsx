import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  PanResponder,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { customFieldStyles } from '@/styles/CustomFieldStyles';
import type { JournalCustomField } from '@/types/journal';

interface DraggableFieldListProps {
  fields: JournalCustomField[];
  onReorder: (newOrder: { id: string; order: number }[]) => void;
  onEdit: (field: JournalCustomField) => void;
  onDelete: (field: JournalCustomField) => void;
  onAIGenerate: (field: JournalCustomField) => void;
  showAIGenerator?: boolean;
}

interface DraggableFieldItemProps {
  field: JournalCustomField;
  index: number;
  onEdit: (field: JournalCustomField) => void;
  onDelete: (field: JournalCustomField) => void;
  onAIGenerate: (field: JournalCustomField) => void;
  onDragStart: (index: number) => void;
  onDragEnd: () => void;
  isDragging: boolean;
  showAIGenerator: boolean;
}

function DraggableFieldItem({
  field,
  index,
  onEdit,
  onDelete,
  onAIGenerate,
  onDragStart,
  onDragEnd,
  isDragging,
  showAIGenerator,
}: DraggableFieldItemProps) {
  const [dragY] = useState(new Animated.Value(0));
  const [isDragActive, setIsDragActive] = useState(false);

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to vertical drags on the drag handle area
      return Math.abs(gestureState.dy) > 5;
    },
    onPanResponderGrant: () => {
      setIsDragActive(true);
      onDragStart(index);
    },
    onPanResponderMove: (evt, gestureState) => {
      dragY.setValue(gestureState.dy);
    },
    onPanResponderRelease: () => {
      setIsDragActive(false);
      onDragEnd();
      Animated.spring(dragY, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    },
  });

  const getFieldTypeLabel = (fieldType: string) => {
    switch (fieldType) {
      case 'custom_question':
        return 'Pertanyaan';
      case 'custom_title':
        return 'Judul';
      default:
        return 'Unknown';
    }
  };

  return (
    <Animated.View
      style={[
        customFieldStyles.fieldItem,
        isDragActive && customFieldStyles.fieldItemDragging,
        {
          transform: [{ translateY: dragY }],
          zIndex: isDragActive ? 1000 : 1,
        },
      ]}
    >
      <View style={customFieldStyles.fieldHeader}>
        <View style={customFieldStyles.fieldContent}>
          <Text style={customFieldStyles.fieldText}>
            {field.question_text}
          </Text>
          <Text style={[
            customFieldStyles.fieldType,
            field.field_type === 'custom_question' && customFieldStyles.fieldTypeQuestion,
            field.field_type === 'custom_title' && customFieldStyles.fieldTypeTitle,
          ]}>
            {getFieldTypeLabel(field.field_type)}
          </Text>
        </View>

        <View style={customFieldStyles.fieldActions}>
          {/* AI Generate button (only for questions) */}
          {showAIGenerator && field.field_type === 'custom_question' && (
            <TouchableOpacity
              style={customFieldStyles.actionButton}
              onPress={() => onAIGenerate(field)}
            >
              <Ionicons name="sparkles" size={20} color="#DD76B9" />
            </TouchableOpacity>
          )}

          {/* Edit button */}
          <TouchableOpacity
            style={customFieldStyles.actionButton}
            onPress={() => onEdit(field)}
          >
            <Ionicons name="pencil" size={20} color="#666666" />
          </TouchableOpacity>

          {/* Delete button */}
          <TouchableOpacity
            style={customFieldStyles.actionButton}
            onPress={() => onDelete(field)}
          >
            <Ionicons name="trash" size={20} color="#FF6B6B" />
          </TouchableOpacity>

          {/* Drag handle */}
          <View
            style={[
              customFieldStyles.dragHandle,
              isDragActive && { backgroundColor: '#DD76B9' },
            ]}
            {...panResponder.panHandlers}
          >
            <Ionicons 
              name="reorder-two" 
              size={20} 
              color={isDragActive ? '#FFFFFF' : '#CCCCCC'} 
            />
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

export function DraggableFieldList({
  fields,
  onReorder,
  onEdit,
  onDelete,
  onAIGenerate,
  showAIGenerator = true,
}: DraggableFieldListProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [reorderedFields, setReorderedFields] = useState(fields);

  // Update reordered fields when props change
  React.useEffect(() => {
    setReorderedFields(fields);
  }, [fields]);

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragEnd = () => {
    if (draggedIndex !== null) {
      // For now, we'll implement a simple reorder
      // In a full implementation, you'd calculate the new position based on drag distance
      const newOrder = reorderedFields.map((field, index) => ({
        id: field.id,
        order: index + 1,
      }));
      
      onReorder(newOrder);
    }
    setDraggedIndex(null);
  };

  const renderFieldItem = ({ item, index }: { item: JournalCustomField; index: number }) => (
    <DraggableFieldItem
      field={item}
      index={index}
      onEdit={onEdit}
      onDelete={onDelete}
      onAIGenerate={onAIGenerate}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      isDragging={draggedIndex === index}
      showAIGenerator={showAIGenerator}
    />
  );

  const handleSimpleReorder = (fromIndex: number, toIndex: number) => {
    const newFields = [...reorderedFields];
    const [movedField] = newFields.splice(fromIndex, 1);
    newFields.splice(toIndex, 0, movedField);
    
    setReorderedFields(newFields);
    
    const newOrder = newFields.map((field, index) => ({
      id: field.id,
      order: index + 1,
    }));
    
    onReorder(newOrder);
  };

  // Simple reorder buttons for demonstration
  const renderReorderButtons = (item: JournalCustomField, index: number) => (
    <View style={styles.reorderButtons}>
      {index > 0 && (
        <TouchableOpacity
          style={styles.reorderButton}
          onPress={() => handleSimpleReorder(index, index - 1)}
        >
          <Ionicons name="chevron-up" size={16} color="#666666" />
        </TouchableOpacity>
      )}
      {index < reorderedFields.length - 1 && (
        <TouchableOpacity
          style={styles.reorderButton}
          onPress={() => handleSimpleReorder(index, index + 1)}
        >
          <Ionicons name="chevron-down" size={16} color="#666666" />
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={reorderedFields}
        renderItem={({ item, index }) => (
          <View style={styles.itemContainer}>
            {renderFieldItem({ item, index })}
            {renderReorderButtons(item, index)}
          </View>
        )}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={draggedIndex === null}
      />
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
  },
  itemContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  reorderButtons: {
    marginLeft: 8,
    justifyContent: 'center' as const,
  },
  reorderButton: {
    padding: 4,
    backgroundColor: '#F8F9FA',
    borderRadius: 4,
    marginVertical: 2,
  },
};

export default DraggableFieldList;
