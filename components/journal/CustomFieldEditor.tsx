import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { customFieldStyles } from '@/styles/CustomFieldStyles';

import { useUpdateCustomField } from '@/hooks/useCustomFields';
import type { JournalCustomField, FieldType, CustomFieldConfig } from '@/types/journal';
import { JOURNAL_CONSTANTS } from '@/types/journal';

interface CustomFieldEditorProps {
  visible: boolean;
  field: JournalCustomField;
  onClose: () => void;
  onSuccess?: () => void;
}

interface FormData {
  questionText: string;
  fieldType: Exclude<FieldType, 'system'>;
  placeholder: string;
  maxLength: string;
  isRequired: boolean;
}

interface FormErrors {
  questionText?: string;
  placeholder?: string;
  maxLength?: string;
}

export function CustomFieldEditor({ 
  visible, 
  field,
  onClose, 
  onSuccess 
}: CustomFieldEditorProps) {
  const [formData, setFormData] = useState<FormData>({
    questionText: '',
    fieldType: 'custom_question',
    placeholder: '',
    maxLength: '',
    isRequired: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const updateMutation = useUpdateCustomField();

  // Initialize form data when field changes
  useEffect(() => {
    if (field && visible) {
      const config = field.custom_config || {};
      setFormData({
        questionText: field.question_text || '',
        fieldType: field.field_type as Exclude<FieldType, 'system'>,
        placeholder: config.placeholder || '',
        maxLength: config.maxLength ? config.maxLength.toString() : '',
        isRequired: config.isRequired || false,
      });
      setErrors({});
      setFocusedField(null);
    }
  }, [field, visible]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate question text
    if (!formData.questionText.trim()) {
      newErrors.questionText = 'Teks pertanyaan wajib diisi';
    } else if (formData.questionText.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH) {
      newErrors.questionText = `Maksimal ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH} karakter`;
    }

    // Validate placeholder
    if (formData.placeholder && formData.placeholder.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH) {
      newErrors.placeholder = `Maksimal ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH} karakter`;
    }

    // Validate max length
    if (formData.maxLength) {
      const maxLengthNum = parseInt(formData.maxLength);
      if (isNaN(maxLengthNum) || maxLengthNum < 1) {
        newErrors.maxLength = 'Panjang maksimal harus berupa angka positif';
      } else if (maxLengthNum > JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH) {
        newErrors.maxLength = `Maksimal ${JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} karakter`;
      } else if (formData.fieldType === 'custom_title' && maxLengthNum > 100) {
        newErrors.maxLength = 'Judul maksimal 100 karakter';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const customConfig: CustomFieldConfig = {};
      
      if (formData.placeholder) {
        customConfig.placeholder = formData.placeholder;
      }
      
      if (formData.maxLength) {
        customConfig.maxLength = parseInt(formData.maxLength);
      }
      
      customConfig.isRequired = formData.isRequired;

      await updateMutation.mutateAsync({
        fieldId: field.id,
        update: {
          question_text: formData.questionText.trim(),
          custom_config: customConfig,
        }
      });

      onSuccess?.();
      onClose();
    } catch (error: any) {
      Alert.alert(
        'Gagal Mengupdate Field',
        error.message || 'Terjadi kesalahan saat mengupdate field'
      );
    }
  };

  const getFieldStyle = (fieldName: string) => [
    customFieldStyles.formInput,
    focusedField === fieldName && customFieldStyles.formInputFocused,
    errors[fieldName as keyof FormErrors] && customFieldStyles.formInputError,
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={customFieldStyles.modalOverlay}>
        <View style={customFieldStyles.modalContainer}>
          {/* Header */}
          <View style={customFieldStyles.modalHeader}>
            <Text style={customFieldStyles.modalTitle}>
              Edit Field
            </Text>
            <TouchableOpacity
              style={customFieldStyles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color="#666666" />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Field Type Display (read-only for now) */}
            <View style={customFieldStyles.formSection}>
              <Text style={customFieldStyles.formLabel}>Tipe Field</Text>
              <View style={[
                customFieldStyles.formInput,
                { backgroundColor: '#F8F9FA' }
              ]}>
                <Text style={{ color: '#666666' }}>
                  {formData.fieldType === 'custom_title' ? 'Judul' : 'Pertanyaan'}
                </Text>
              </View>
              <Text style={customFieldStyles.formHint}>
                Tipe field tidak dapat diubah setelah dibuat
              </Text>
            </View>

            {/* Question Text */}
            <View style={customFieldStyles.formSection}>
              <Text style={customFieldStyles.formLabel}>
                {formData.fieldType === 'custom_title' ? 'Teks Judul' : 'Teks Pertanyaan'} *
              </Text>
              <TextInput
                style={[
                  getFieldStyle('questionText'),
                  formData.fieldType === 'custom_title' ? {} : customFieldStyles.formTextArea,
                ]}
                value={formData.questionText}
                onChangeText={(text) => setFormData(prev => ({ ...prev, questionText: text }))}
                onFocus={() => setFocusedField('questionText')}
                onBlur={() => setFocusedField(null)}
                placeholder={
                  formData.fieldType === 'custom_title' 
                    ? 'Contoh: Refleksi Harian' 
                    : 'Contoh: Apa yang membuatmu bahagia hari ini?'
                }
                multiline={formData.fieldType !== 'custom_title'}
                maxLength={JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH}
                editable={!updateMutation.isPending}
              />
              {errors.questionText && (
                <Text style={customFieldStyles.formError}>{errors.questionText}</Text>
              )}
              <Text style={customFieldStyles.formHint}>
                {formData.questionText.length}/{JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH} karakter
              </Text>
            </View>

            {/* Placeholder (only for questions) */}
            {formData.fieldType === 'custom_question' && (
              <View style={customFieldStyles.formSection}>
                <Text style={customFieldStyles.formLabel}>Placeholder (Opsional)</Text>
                <TextInput
                  style={getFieldStyle('placeholder')}
                  value={formData.placeholder}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, placeholder: text }))}
                  onFocus={() => setFocusedField('placeholder')}
                  onBlur={() => setFocusedField(null)}
                  placeholder="Tulis jawabanmu di sini..."
                  maxLength={JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH}
                  editable={!updateMutation.isPending}
                />
                {errors.placeholder && (
                  <Text style={customFieldStyles.formError}>{errors.placeholder}</Text>
                )}
                <Text style={customFieldStyles.formHint}>
                  Teks yang akan muncul sebagai petunjuk di field input
                </Text>
              </View>
            )}

            {/* Max Length */}
            <View style={customFieldStyles.formSection}>
              <Text style={customFieldStyles.formLabel}>
                Panjang Maksimal {formData.fieldType === 'custom_question' ? 'Jawaban' : 'Judul'} (Opsional)
              </Text>
              <TextInput
                style={getFieldStyle('maxLength')}
                value={formData.maxLength}
                onChangeText={(text) => setFormData(prev => ({ ...prev, maxLength: text }))}
                onFocus={() => setFocusedField('maxLength')}
                onBlur={() => setFocusedField(null)}
                placeholder={formData.fieldType === 'custom_title' ? '50' : '500'}
                keyboardType="numeric"
                editable={!updateMutation.isPending}
              />
              {errors.maxLength && (
                <Text style={customFieldStyles.formError}>{errors.maxLength}</Text>
              )}
              <Text style={customFieldStyles.formHint}>
                Kosongkan untuk menggunakan batas default (
                {formData.fieldType === 'custom_title' ? '100' : JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} karakter)
              </Text>
            </View>
          </ScrollView>

          {/* Actions */}
          <View style={customFieldStyles.modalActions}>
            <TouchableOpacity
              style={[customFieldStyles.modalButton, customFieldStyles.modalButtonSecondary]}
              onPress={onClose}
              disabled={updateMutation.isPending}
            >
              <Text style={[customFieldStyles.modalButtonText, customFieldStyles.modalButtonTextSecondary]}>
                Batal
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                customFieldStyles.modalButton,
                customFieldStyles.modalButtonPrimary,
                updateMutation.isPending && customFieldStyles.modalButtonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={customFieldStyles.modalButtonText}>
                  Update Field
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

export default CustomFieldEditor;
