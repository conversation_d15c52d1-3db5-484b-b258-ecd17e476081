import React from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CustomFieldManager } from './CustomFieldManager';
import type { JournalCustomField } from '@/types/journal';

interface CustomFieldManagerModalProps {
  visible: boolean;
  onClose: () => void;
  onFieldSelect?: (field: JournalCustomField) => void;
  showAIGenerator?: boolean;
}

export function CustomFieldManagerModal({
  visible,
  onClose,
  onFieldSelect,
  showAIGenerator = true,
}: CustomFieldManagerModalProps) {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color="#666666" />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <CustomFieldManager
          onFieldSelect={(field) => {
            onFieldSelect?.(field);
            onClose();
          }}
          showAIGenerator={showAIGenerator}
        />
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
  },
});

export default CustomFieldManagerModal;
