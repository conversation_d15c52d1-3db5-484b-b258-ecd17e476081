import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { customFieldService } from '@/lib/customFieldService';
import { 
  useAIGenerationWithRateLimit, 
  useCanGenerateQuestions 
} from '@/hooks/useAIQuestionGenerator';
import { useCanCreateCustomField } from '@/hooks/useCustomFields';
import type { CustomFieldConfig } from '@/types/journal';

interface SimpleAIQuestionHelperProps {
  baseQuestion: string;
  baseQuestionId?: string;
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export function SimpleAIQuestionHelper({
  baseQuestion,
  baseQuestionId,
  visible,
  onClose,
  onSuccess,
}: SimpleAIQuestionHelperProps) {
  const { user } = useUniversalAuth();
  const [questions, setQuestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [creatingField, setCreatingField] = useState<string | null>(null);

  const { generateWithCheck, isPending } = useAIGenerationWithRateLimit();
  const { canGenerate, remaining } = useCanGenerateQuestions();
  const { canCreate: canCreateField, remaining: fieldsRemaining } = useCanCreateCustomField();

  // Auto-generate questions when modal opens
  useEffect(() => {
    if (visible && questions.length === 0) {
      generateMixedQuestions();
    }
  }, [visible]);

  const generateMixedQuestions = async () => {
    if (!canGenerate) {
      Alert.alert(
        'Daily Limit Reached',
        `You've used all your writing idea generations today. Try again tomorrow!`
      );
      return;
    }

    setLoading(true);
    setQuestions([]);

    try {
      // Generate mixed questions using the new 'mixed' type
      const result = await generateWithCheck({
        baseQuestion,
        baseQuestionId,
        questionType: 'mixed',
        count: 3,
        userPreferences: {
          tone: 'empathetic',
          length: 'medium',
        },
      });

      if (result.success && result.questions.length > 0) {
        setQuestions(result.questions);
      } else {
        Alert.alert(
          'No Ideas Found',
          'Unable to generate writing ideas right now. Please try again.'
        );
      }
    } catch (error: any) {
      Alert.alert(
        'Something Went Wrong',
        error.message || 'Unable to generate writing ideas. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleUseQuestion = async (question: string) => {
    if (!user?.id) {
      Alert.alert('Error', 'Please log in to save questions');
      return;
    }

    if (!canCreateField) {
      Alert.alert(
        'Question Limit Reached',
        `You've reached the maximum of 20 custom questions. Remove some questions to add new ones.`
      );
      return;
    }

    setCreatingField(question);

    try {
      const customConfig: CustomFieldConfig = {
        aiGenerated: true,
        parentFieldId: baseQuestionId,
        placeholder: 'Share your thoughts...',
        isRequired: false,
      };

      await customFieldService.createCustomField(user.id, {
        question_text: question,
        field_type: 'custom_question',
        custom_config: customConfig,
      });

      Alert.alert(
        '✨ Question Added!',
        'Your new writing prompt has been added to your journal.',
        [
          {
            text: 'Great!',
            onPress: () => {
              onSuccess?.();
              onClose();
            }
          }
        ]
      );
    } catch (error: any) {
      Alert.alert(
        'Unable to Save',
        error.message || 'Something went wrong while saving your question.'
      );
    } finally {
      setCreatingField(null);
    }
  };

  const handleRegenerate = () => {
    setQuestions([]);
    generateMixedQuestions();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.iconContainer}>
              <Ionicons name="lightbulb" size={24} color="#FFD700" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.title}>Writing Ideas</Text>
              <Text style={styles.subtitle}>Questions to help you reflect deeper</Text>
            </View>
          </View>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {loading || isPending ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#DD76B9" />
              <Text style={styles.loadingText}>Finding great questions...</Text>
            </View>
          ) : questions.length > 0 ? (
            <View style={styles.questionsContainer}>
              {questions.map((question, index) => (
                <View key={index} style={styles.questionCard}>
                  <Text style={styles.questionText}>{question}</Text>
                  <TouchableOpacity
                    style={[
                      styles.useButton,
                      creatingField === question && styles.useButtonDisabled
                    ]}
                    onPress={() => handleUseQuestion(question)}
                    disabled={creatingField === question}
                  >
                    {creatingField === question ? (
                      <ActivityIndicator size="small" color="#FFFFFF" />
                    ) : (
                      <Ionicons name="add-circle" size={20} color="#FFFFFF" />
                    )}
                    <Text style={styles.useButtonText}>
                      {creatingField === question ? 'Adding...' : 'Use this question'}
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="bulb-outline" size={48} color="#CCC" />
              <Text style={styles.emptyText}>No ideas generated yet</Text>
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        {questions.length > 0 && (
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.regenerateButton}
              onPress={handleRegenerate}
              disabled={loading || isPending || !canGenerate}
            >
              <Ionicons name="refresh" size={20} color="#DD76B9" />
              <Text style={styles.regenerateButtonText}>Get different ideas</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Usage info */}
        <View style={styles.usageInfo}>
          <Text style={styles.usageText}>
            {remaining} idea generations remaining today • {fieldsRemaining} question slots available
          </Text>
        </View>
      </View>
    </Modal>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  headerContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFF9E6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600' as const,
    color: '#1A1A1A',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  questionsContainer: {
    gap: 16,
  },
  questionCard: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  questionText: {
    fontSize: 16,
    color: '#1A1A1A',
    lineHeight: 24,
    marginBottom: 12,
  },
  useButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: '#DD76B9',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
  },
  useButtonDisabled: {
    backgroundColor: '#CCC',
  },
  useButtonText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#FFFFFF',
  },
  emptyContainer: {
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 12,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#F1F3F4',
  },
  regenerateButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  regenerateButtonText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#DD76B9',
  },
  usageInfo: {
    padding: 20,
    paddingTop: 0,
    alignItems: 'center' as const,
  },
  usageText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center' as const,
  },
};
