import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { aiGeneratorStyles } from '@/styles/AIGeneratorStyles';
import { 
  useAIGenerationWithRateLimit, 
  useCanGenerateQuestions,
  useAIGenerationAnalytics 
} from '@/hooks/useAIQuestionGenerator';
import type { AIGenerationType } from '@/types/journal';

interface AIQuestionGeneratorProps {
  baseQuestion: string;
  baseQuestionId?: string;
  onQuestionsGenerated?: (questions: string[]) => void;
  onQuestionSelect?: (question: string) => void;
  onClose?: () => void;
  compact?: boolean;
}

const generationTypes = [
  {
    type: 'follow_up' as const,
    icon: 'arrow-down-circle-outline' as const,
    title: 'Lanjutan',
    description: 'Pertanyaan yang menggali lebih dalam',
  },
  {
    type: 'alternative' as const,
    icon: 'swap-horizontal-outline' as const,
    title: 'Alternatif',
    description: 'Variasi dengan kata berbeda',
  },
  {
    type: 'related' as const,
    icon: 'git-branch-outline' as const,
    title: 'Terkait',
    description: 'Pertanyaan dengan tema serupa',
  },
];

export function AIQuestionGenerator({
  baseQuestion,
  baseQuestionId,
  onQuestionsGenerated,
  onQuestionSelect,
  onClose,
  compact = false,
}: AIQuestionGeneratorProps) {
  const [selectedType, setSelectedType] = useState<AIGenerationType>('follow_up');
  const [context, setContext] = useState('');
  const [contextFocused, setContextFocused] = useState(false);
  const [generatedQuestions, setGeneratedQuestions] = useState<string[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<Set<string>>(new Set());

  const { generateWithCheck, isPending, error } = useAIGenerationWithRateLimit();
  const { canGenerate, remaining, dailyLimit, currentCount } = useCanGenerateQuestions();
  const analytics = useAIGenerationAnalytics();

  // Reset state when base question changes
  useEffect(() => {
    setGeneratedQuestions([]);
    setSelectedQuestions(new Set());
    setContext('');
  }, [baseQuestion]);

  const handleGenerate = async () => {
    if (!canGenerate) {
      Alert.alert(
        'Batas Harian Tercapai',
        `Anda sudah menggunakan ${dailyLimit} generasi hari ini. Coba lagi besok.`
      );
      return;
    }

    try {
      const result = await generateWithCheck({
        baseQuestion,
        baseQuestionId,
        questionType: selectedType,
        count: 3,
        context: context.trim() || undefined,
        userPreferences: {
          tone: 'empathetic',
          length: 'medium',
        },
      });

      if (result.success && result.questions.length > 0) {
        setGeneratedQuestions(result.questions);
        setSelectedQuestions(new Set());
        onQuestionsGenerated?.(result.questions);
      } else {
        Alert.alert(
          'Gagal Generate',
          result.error || 'Tidak ada pertanyaan yang berhasil dibuat'
        );
      }
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Terjadi kesalahan saat generate pertanyaan'
      );
    }
  };

  const toggleQuestionSelection = (question: string) => {
    const newSelected = new Set(selectedQuestions);
    if (newSelected.has(question)) {
      newSelected.delete(question);
    } else {
      newSelected.add(question);
    }
    setSelectedQuestions(newSelected);
  };

  const handleUseSelected = () => {
    if (selectedQuestions.size === 0) {
      Alert.alert('Pilih Pertanyaan', 'Silakan pilih minimal satu pertanyaan');
      return;
    }

    const questionsArray = Array.from(selectedQuestions);
    questionsArray.forEach(question => {
      onQuestionSelect?.(question);
    });

    // Reset selections
    setSelectedQuestions(new Set());
    
    if (compact) {
      onClose?.();
    }
  };

  const getRateLimitColor = () => {
    const usagePercentage = (currentCount / dailyLimit) * 100;
    if (usagePercentage >= 90) return 'error';
    if (usagePercentage >= 70) return 'warning';
    return 'normal';
  };

  const rateLimitColor = getRateLimitColor();

  return (
    <View style={aiGeneratorStyles.container}>
      {/* Header */}
      <View style={aiGeneratorStyles.header}>
        <View style={aiGeneratorStyles.headerIcon}>
          <Ionicons name="sparkles" size={24} color="#DD76B9" />
        </View>
        <View style={aiGeneratorStyles.headerText}>
          <Text style={aiGeneratorStyles.title}>AI Question Generator</Text>
          <Text style={aiGeneratorStyles.subtitle}>
            Buat pertanyaan reflektif dengan bantuan AI
          </Text>
        </View>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={aiGeneratorStyles.modalCloseButton}>
            <Ionicons name="close" size={24} color="#666666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Base Question Display */}
      <View style={aiGeneratorStyles.baseQuestionContainer}>
        <Text style={aiGeneratorStyles.baseQuestionLabel}>Pertanyaan Dasar</Text>
        <Text style={aiGeneratorStyles.baseQuestionText}>{baseQuestion}</Text>
      </View>

      {/* Rate Limit Indicator */}
      <View style={[
        aiGeneratorStyles.rateLimitContainer,
        rateLimitColor === 'warning' && aiGeneratorStyles.rateLimitWarning,
        rateLimitColor === 'error' && aiGeneratorStyles.rateLimitError,
      ]}>
        <Ionicons 
          name={rateLimitColor === 'error' ? 'warning' : 'information-circle'} 
          size={16} 
          color={
            rateLimitColor === 'error' ? '#721C24' : 
            rateLimitColor === 'warning' ? '#856404' : '#666666'
          } 
        />
        <Text style={[
          aiGeneratorStyles.rateLimitText,
          rateLimitColor === 'warning' && aiGeneratorStyles.rateLimitWarningText,
          rateLimitColor === 'error' && aiGeneratorStyles.rateLimitErrorText,
        ]}>
          {canGenerate 
            ? `${remaining} generasi tersisa hari ini (${currentCount}/${dailyLimit})`
            : `Batas harian tercapai (${currentCount}/${dailyLimit}). Reset besok.`
          }
        </Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Generation Type Selector */}
        <View style={aiGeneratorStyles.typeSelector}>
          <Text style={aiGeneratorStyles.typeSelectorLabel}>Tipe Generasi</Text>
          <View style={aiGeneratorStyles.typeOptions}>
            {generationTypes.map((type) => {
              const isSelected = selectedType === type.type;
              return (
                <TouchableOpacity
                  key={type.type}
                  style={[
                    aiGeneratorStyles.typeOption,
                    isSelected && aiGeneratorStyles.typeOptionSelected,
                  ]}
                  onPress={() => setSelectedType(type.type)}
                  disabled={isPending}
                >
                  <View style={aiGeneratorStyles.typeOptionIcon}>
                    <Ionicons
                      name={type.icon}
                      size={20}
                      color={isSelected ? '#DD76B9' : '#666666'}
                    />
                  </View>
                  <Text style={[
                    aiGeneratorStyles.typeOptionText,
                    isSelected && aiGeneratorStyles.typeOptionTextSelected,
                  ]}>
                    {type.title}
                  </Text>
                  <Text style={aiGeneratorStyles.typeOptionDescription}>
                    {type.description}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {/* Context Input */}
        <View style={aiGeneratorStyles.contextSection}>
          <Text style={aiGeneratorStyles.contextLabel}>
            Konteks Tambahan (Opsional)
          </Text>
          <TextInput
            style={[
              aiGeneratorStyles.contextInput,
              contextFocused && aiGeneratorStyles.contextInputFocused,
            ]}
            value={context}
            onChangeText={setContext}
            onFocus={() => setContextFocused(true)}
            onBlur={() => setContextFocused(false)}
            placeholder="Berikan konteks untuk pertanyaan yang lebih personal..."
            multiline
            maxLength={500}
            editable={!isPending}
          />
          <Text style={aiGeneratorStyles.contextHint}>
            Contoh: &quot;Hari ini saya merasa stress karena ujian&quot;
          </Text>
        </View>

        {/* Generate Button */}
        <TouchableOpacity
          style={[
            aiGeneratorStyles.generateButton,
            (!canGenerate || isPending) && aiGeneratorStyles.generateButtonDisabled,
          ]}
          onPress={handleGenerate}
          disabled={!canGenerate || isPending}
        >
          {isPending ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Ionicons name="sparkles" size={20} color="#FFFFFF" />
          )}
          <Text style={[
            aiGeneratorStyles.generateButtonText,
            (!canGenerate || isPending) && aiGeneratorStyles.generateButtonTextDisabled,
          ]}>
            {isPending ? 'Generating...' : 'Generate Pertanyaan'}
          </Text>
        </TouchableOpacity>

        {/* Error Display */}
        {error && (
          <View style={aiGeneratorStyles.errorContainer}>
            <Ionicons name="alert-circle" size={16} color="#721C24" />
            <Text style={aiGeneratorStyles.errorText}>
              {error.message || 'Terjadi kesalahan saat generate pertanyaan'}
            </Text>
          </View>
        )}

        {/* Generated Questions */}
        {generatedQuestions.length > 0 && (
          <View style={aiGeneratorStyles.questionsContainer}>
            <View style={aiGeneratorStyles.questionsHeader}>
              <Text style={aiGeneratorStyles.questionsTitle}>
                Pertanyaan yang Dihasilkan
              </Text>
              <TouchableOpacity
                style={aiGeneratorStyles.regenerateButton}
                onPress={handleGenerate}
                disabled={!canGenerate || isPending}
              >
                <Ionicons name="refresh" size={16} color="#666666" />
                <Text style={aiGeneratorStyles.regenerateButtonText}>
                  Generate Ulang
                </Text>
              </TouchableOpacity>
            </View>

            {generatedQuestions.map((question, index) => {
              const isSelected = selectedQuestions.has(question);
              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    aiGeneratorStyles.questionItem,
                    isSelected && aiGeneratorStyles.questionItemSelected,
                  ]}
                  onPress={() => toggleQuestionSelection(question)}
                >
                  <View style={aiGeneratorStyles.questionHeader}>
                    <Text style={aiGeneratorStyles.questionText}>
                      {question}
                    </Text>
                    <View style={aiGeneratorStyles.questionActions}>
                      <TouchableOpacity
                        style={aiGeneratorStyles.questionActionButton}
                        onPress={() => toggleQuestionSelection(question)}
                      >
                        <Ionicons
                          name={isSelected ? 'checkmark-circle' : 'ellipse-outline'}
                          size={20}
                          color={isSelected ? '#DD76B9' : '#666666'}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}

            {/* Action Buttons */}
            {selectedQuestions.size > 0 && (
              <View style={aiGeneratorStyles.actionButtons}>
                <TouchableOpacity
                  style={[aiGeneratorStyles.actionButton, aiGeneratorStyles.actionButtonSecondary]}
                  onPress={() => setSelectedQuestions(new Set())}
                >
                  <Ionicons name="close" size={16} color="#666666" />
                  <Text style={[aiGeneratorStyles.actionButtonText, aiGeneratorStyles.actionButtonTextSecondary]}>
                    Batal
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[aiGeneratorStyles.actionButton, aiGeneratorStyles.actionButtonPrimary]}
                  onPress={handleUseSelected}
                >
                  <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                  <Text style={[aiGeneratorStyles.actionButtonText, aiGeneratorStyles.actionButtonTextPrimary]}>
                    Gunakan ({selectedQuestions.size})
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {/* Empty State */}
        {generatedQuestions.length === 0 && !isPending && !error && (
          <View style={aiGeneratorStyles.emptyState}>
            <View style={aiGeneratorStyles.emptyStateIcon}>
              <Ionicons name="bulb-outline" size={48} color="#DD76B9" />
            </View>
            <Text style={aiGeneratorStyles.emptyStateText}>
              Klik &quot;Generate Pertanyaan&quot; untuk membuat pertanyaan reflektif baru dengan bantuan AI
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

export default AIQuestionGenerator;
