import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { customFieldStyles } from '@/styles/CustomFieldStyles';
import { FieldTypeSelector } from './FieldTypeSelector';
import { useCreateCustomField, useCanCreateCustomField } from '@/hooks/useCustomFields';
import type { FieldType, CustomFieldConfig } from '@/types/journal';
import { JOURNAL_CONSTANTS } from '@/types/journal';

interface CustomFieldCreatorProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface FormData {
  questionText: string;
  fieldType: Exclude<FieldType, 'system'>;
  placeholder: string;
  maxLength: string;
  isRequired: boolean;
}

interface FormErrors {
  questionText?: string;
  placeholder?: string;
  maxLength?: string;
}

export function CustomFieldCreator({ 
  visible, 
  onClose, 
  onSuccess 
}: CustomFieldCreatorProps) {
  const [formData, setFormData] = useState<FormData>({
    questionText: '',
    fieldType: 'custom_question',
    placeholder: '',
    maxLength: '',
    isRequired: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const createMutation = useCreateCustomField();
  const { canCreate, remaining, maxAllowed } = useCanCreateCustomField();

  // Reset form when modal opens/closes
  useEffect(() => {
    if (visible) {
      setFormData({
        questionText: '',
        fieldType: 'custom_question',
        placeholder: '',
        maxLength: '',
        isRequired: false,
      });
      setErrors({});
      setFocusedField(null);
    }
  }, [visible]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate question text
    if (!formData.questionText.trim()) {
      newErrors.questionText = 'Teks pertanyaan wajib diisi';
    } else if (formData.questionText.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH) {
      newErrors.questionText = `Maksimal ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH} karakter`;
    }

    // Validate placeholder
    if (formData.placeholder && formData.placeholder.length > JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH) {
      newErrors.placeholder = `Maksimal ${JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH} karakter`;
    }

    // Validate max length
    if (formData.maxLength) {
      const maxLengthNum = parseInt(formData.maxLength);
      if (isNaN(maxLengthNum) || maxLengthNum < 1) {
        newErrors.maxLength = 'Panjang maksimal harus berupa angka positif';
      } else if (maxLengthNum > JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH) {
        newErrors.maxLength = `Maksimal ${JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} karakter`;
      } else if (formData.fieldType === 'custom_title' && maxLengthNum > 100) {
        newErrors.maxLength = 'Judul maksimal 100 karakter';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    if (!canCreate) {
      Alert.alert(
        'Batas Tercapai',
        `Anda sudah mencapai batas maksimal ${maxAllowed} field kustom.`
      );
      return;
    }

    try {
      const customConfig: CustomFieldConfig = {};
      
      if (formData.placeholder) {
        customConfig.placeholder = formData.placeholder;
      }
      
      if (formData.maxLength) {
        customConfig.maxLength = parseInt(formData.maxLength);
      }
      
      customConfig.isRequired = formData.isRequired;

      await createMutation.mutateAsync({
        question_text: formData.questionText.trim(),
        field_type: formData.fieldType,
        custom_config: customConfig,
      });

      onSuccess?.();
      onClose();
    } catch (error: any) {
      Alert.alert(
        'Gagal Membuat Field',
        error.message || 'Terjadi kesalahan saat membuat field kustom'
      );
    }
  };

  const getFieldStyle = (fieldName: string) => [
    customFieldStyles.formInput,
    focusedField === fieldName && customFieldStyles.formInputFocused,
    errors[fieldName as keyof FormErrors] && customFieldStyles.formInputError,
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={customFieldStyles.modalOverlay}>
        <View style={customFieldStyles.modalContainer}>
          {/* Header */}
          <View style={customFieldStyles.modalHeader}>
            <Text style={customFieldStyles.modalTitle}>
              Buat Field Baru
            </Text>
            <TouchableOpacity
              style={customFieldStyles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color="#666666" />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Limit indicator */}
            {!canCreate && (
              <View style={[customFieldStyles.limitIndicator, customFieldStyles.limitWarning]}>
                <Ionicons name="warning" size={16} color="#856404" />
                <Text style={[customFieldStyles.limitText, customFieldStyles.limitWarningText]}>
                  Batas maksimal {maxAllowed} field tercapai
                </Text>
              </View>
            )}

            {canCreate && remaining <= 3 && (
              <View style={customFieldStyles.limitIndicator}>
                <Ionicons name="information-circle" size={16} color="#666666" />
                <Text style={customFieldStyles.limitText}>
                  {remaining} field tersisa dari {maxAllowed}
                </Text>
              </View>
            )}

            {/* Field Type Selector */}
            <View style={customFieldStyles.formSection}>
              <Text style={customFieldStyles.formLabel}>Tipe Field</Text>
              <FieldTypeSelector
                selectedType={formData.fieldType}
                onTypeSelect={(type) => setFormData(prev => ({ ...prev, fieldType: type }))}
                disabled={createMutation.isPending}
              />
            </View>

            {/* Question Text */}
            <View style={customFieldStyles.formSection}>
              <Text style={customFieldStyles.formLabel}>
                {formData.fieldType === 'custom_title' ? 'Teks Judul' : 'Teks Pertanyaan'} *
              </Text>
              <TextInput
                style={[
                  getFieldStyle('questionText'),
                  formData.fieldType === 'custom_title' ? {} : customFieldStyles.formTextArea,
                ]}
                value={formData.questionText}
                onChangeText={(text) => setFormData(prev => ({ ...prev, questionText: text }))}
                onFocus={() => setFocusedField('questionText')}
                onBlur={() => setFocusedField(null)}
                placeholder={
                  formData.fieldType === 'custom_title' 
                    ? 'Contoh: Refleksi Harian' 
                    : 'Contoh: Apa yang membuatmu bahagia hari ini?'
                }
                multiline={formData.fieldType !== 'custom_title'}
                maxLength={JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH}
                editable={!createMutation.isPending}
              />
              {errors.questionText && (
                <Text style={customFieldStyles.formError}>{errors.questionText}</Text>
              )}
              <Text style={customFieldStyles.formHint}>
                {formData.questionText.length}/{JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_TEXT_LENGTH} karakter
              </Text>
            </View>

            {/* Placeholder (only for questions) */}
            {formData.fieldType === 'custom_question' && (
              <View style={customFieldStyles.formSection}>
                <Text style={customFieldStyles.formLabel}>Placeholder (Opsional)</Text>
                <TextInput
                  style={getFieldStyle('placeholder')}
                  value={formData.placeholder}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, placeholder: text }))}
                  onFocus={() => setFocusedField('placeholder')}
                  onBlur={() => setFocusedField(null)}
                  placeholder="Tulis jawabanmu di sini..."
                  maxLength={JOURNAL_CONSTANTS.MAX_CUSTOM_FIELD_PLACEHOLDER_LENGTH}
                  editable={!createMutation.isPending}
                />
                {errors.placeholder && (
                  <Text style={customFieldStyles.formError}>{errors.placeholder}</Text>
                )}
                <Text style={customFieldStyles.formHint}>
                  Teks yang akan muncul sebagai petunjuk di field input
                </Text>
              </View>
            )}

            {/* Max Length */}
            <View style={customFieldStyles.formSection}>
              <Text style={customFieldStyles.formLabel}>
                Panjang Maksimal {formData.fieldType === 'custom_question' ? 'Jawaban' : 'Judul'} (Opsional)
              </Text>
              <TextInput
                style={getFieldStyle('maxLength')}
                value={formData.maxLength}
                onChangeText={(text) => setFormData(prev => ({ ...prev, maxLength: text }))}
                onFocus={() => setFocusedField('maxLength')}
                onBlur={() => setFocusedField(null)}
                placeholder={formData.fieldType === 'custom_title' ? '50' : '500'}
                keyboardType="numeric"
                editable={!createMutation.isPending}
              />
              {errors.maxLength && (
                <Text style={customFieldStyles.formError}>{errors.maxLength}</Text>
              )}
              <Text style={customFieldStyles.formHint}>
                Kosongkan untuk menggunakan batas default (
                {formData.fieldType === 'custom_title' ? '100' : JOURNAL_CONSTANTS.MAX_ANSWER_LENGTH} karakter)
              </Text>
            </View>
          </ScrollView>

          {/* Actions */}
          <View style={customFieldStyles.modalActions}>
            <TouchableOpacity
              style={[customFieldStyles.modalButton, customFieldStyles.modalButtonSecondary]}
              onPress={onClose}
              disabled={createMutation.isPending}
            >
              <Text style={[customFieldStyles.modalButtonText, customFieldStyles.modalButtonTextSecondary]}>
                Batal
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                customFieldStyles.modalButton,
                customFieldStyles.modalButtonPrimary,
                (!canCreate || createMutation.isPending) && customFieldStyles.modalButtonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={!canCreate || createMutation.isPending}
            >
              {createMutation.isPending ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={customFieldStyles.modalButtonText}>
                  Buat Field
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

export default CustomFieldCreator;
