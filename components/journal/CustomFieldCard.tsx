import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { customFieldStyles } from '@/styles/CustomFieldStyles';
import { SimpleAIQuestionHelper } from './SimpleAIQuestionHelper';
import type { JournalQuestion, JournalEntry, CustomFieldConfig } from '@/types/journal';

interface CustomFieldCardProps {
  field: JournalQuestion;
  entry?: JournalEntry;
  onSave: (fieldId: string, answer: string) => Promise<void>;
  onAIGenerate?: (field: JournalQuestion) => void;
  autoSave?: boolean;
  disabled?: boolean;
  showAIButton?: boolean;
}

export function CustomFieldCard({
  field,
  entry,
  onSave,
  onAIGenerate,
  autoSave = true,
  disabled = false,
  showAIButton = true,
}: CustomFieldCardProps) {
  const [answer, setAnswer] = useState(entry?.answer || '');
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [showAI, setShowAI] = useState(false);
  const [focused, setFocused] = useState(false);
  
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Get custom configuration
  const config: CustomFieldConfig = field.custom_config || {};
  const maxLength = config.maxLength || (field.field_type === 'custom_title' ? 100 : 2000);
  const placeholder = config.placeholder || 
    (field.field_type === 'custom_title' ? 'Masukkan judul...' : 'Tulis jawabanmu di sini...');

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || disabled) return;
    
    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Don't save if answer hasn't changed from entry
    if (answer === (entry?.answer || '')) return;

    // Don't save empty answers for title fields
    if (field.field_type === 'custom_title' && answer.trim().length === 0) return;

    // Set timeout for auto-save
    saveTimeoutRef.current = setTimeout(async () => {
      if (answer.trim().length > 0) {
        await handleSave();
      }
    }, 1000); // 1 second delay

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [answer, autoSave, disabled]);

  const handleSave = async () => {
    if (disabled || isSaving) return;

    try {
      setIsSaving(true);
      await onSave(field.id, answer.trim());
      setLastSaved(new Date().toLocaleTimeString('id-ID', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
      
      // Show save animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(2000),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } catch (error) {
      console.error('Error saving field:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAIGenerate = () => {
    if (onAIGenerate) {
      onAIGenerate(field);
    } else {
      setShowAI(true);
    }
  };

  const handleAIQuestionSelect = (question: string) => {
    // For now, just close the AI generator
    // In a full implementation, this might create a new field
    setShowAI(false);
  };

  const getFieldIcon = () => {
    switch (field.field_type) {
      case 'custom_title':
        return 'text-outline';
      case 'custom_question':
        return 'help-circle-outline';
      default:
        return 'document-text-outline';
    }
  };

  const getFieldColor = () => {
    switch (field.field_type) {
      case 'custom_title':
        return '#4EAF64';
      case 'custom_question':
        return '#DD76B9';
      default:
        return '#666666';
    }
  };

  const isTitle = field.field_type === 'custom_title';
  const fieldColor = getFieldColor();

  return (
    <View style={customFieldStyles.fieldItem}>
      {/* Field Header */}
      <View style={customFieldStyles.fieldHeader}>
        <View style={customFieldStyles.fieldContent}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <Ionicons 
              name={getFieldIcon()} 
              size={20} 
              color={fieldColor} 
              style={{ marginRight: 8 }} 
            />
            <Text style={[customFieldStyles.fieldText, { color: fieldColor }]}>
              {field.question_text}
            </Text>
          </View>
          
          {field.ai_generated && (
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <Ionicons name="sparkles" size={12} color="#DD76B9" />
              <Text style={[customFieldStyles.fieldType, { color: '#DD76B9', marginLeft: 4 }]}>
                AI Generated
              </Text>
            </View>
          )}
        </View>

        <View style={customFieldStyles.fieldActions}>
          {/* AI Generate button (only for questions) */}
          {showAIButton && field.field_type === 'custom_question' && (
            <TouchableOpacity
              style={customFieldStyles.actionButton}
              onPress={handleAIGenerate}
              disabled={disabled}
            >
              <Ionicons name="sparkles" size={20} color="#DD76B9" />
            </TouchableOpacity>
          )}

          {/* Save status indicator */}
          {isSaving && (
            <ActivityIndicator size="small" color={fieldColor} style={{ marginLeft: 8 }} />
          )}
          
          {lastSaved && (
            <Animated.View style={{ opacity: fadeAnim, marginLeft: 8 }}>
              <Text style={[customFieldStyles.fieldType, { color: '#4EAF64' }]}>
                Tersimpan {lastSaved}
              </Text>
            </Animated.View>
          )}
        </View>
      </View>

      {/* Input Field */}
      <TextInput
        style={[
          customFieldStyles.formInput,
          focused && customFieldStyles.formInputFocused,
          isTitle ? {} : customFieldStyles.formTextArea,
          {
            borderColor: focused ? fieldColor : '#E0E0E0',
            backgroundColor: focused ? `${fieldColor}08` : '#FFFFFF',
          }
        ]}
        value={answer}
        onChangeText={setAnswer}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        placeholder={placeholder}
        multiline={!isTitle}
        numberOfLines={isTitle ? 1 : 4}
        maxLength={maxLength}
        editable={!disabled}
        textAlignVertical={isTitle ? 'center' : 'top'}
      />

      {/* Character count */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 4 }}>
        <Text style={customFieldStyles.formHint}>
          {answer.length}/{maxLength} karakter
        </Text>
        
        {config.isRequired && answer.trim().length === 0 && (
          <Text style={[customFieldStyles.formHint, { color: '#FF6B6B' }]}>
            Wajib diisi
          </Text>
        )}
      </View>

      {/* Simple AI Question Helper Modal */}
      {showAI && (
        <SimpleAIQuestionHelper
          baseQuestion={field.question_text}
          baseQuestionId={field.id}
          visible={showAI}
          onClose={() => setShowAI(false)}
          onSuccess={() => setShowAI(false)}
        />
      )}
    </View>
  );
}

export default CustomFieldCard;
