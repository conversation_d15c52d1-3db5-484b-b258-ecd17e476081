import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { customFieldStyles } from '@/styles/CustomFieldStyles';
import { CustomFieldCreator } from './CustomFieldCreator';
import { CustomFieldEditor } from './CustomFieldEditor';
import { SimpleAIQuestionHelper } from './SimpleAIQuestionHelper';
import { DraggableFieldList } from './DraggableFieldList';
import {
  useCustomFields,
  useCanCreateCustomField,
  useArchiveCustomField,
  useReorderCustomFields,

} from '@/hooks/useCustomFields';
import type { JournalCustomField } from '@/types/journal';

interface CustomFieldManagerProps {
  onFieldSelect?: (field: JournalCustomField) => void;
  showAIGenerator?: boolean;
}



export function CustomFieldManager({
  onFieldSelect,
  showAIGenerator = true
}: CustomFieldManagerProps) {
  const [showCreator, setShowCreator] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [showAI, setShowAI] = useState(false);
  const [selectedFieldForEdit, setSelectedFieldForEdit] = useState<JournalCustomField | null>(null);
  const [selectedFieldForAI, setSelectedFieldForAI] = useState<JournalCustomField | null>(null);

  const { data: customFields, isLoading, error } = useCustomFields();
  const { canCreate, remaining, maxAllowed } = useCanCreateCustomField();
  const archiveMutation = useArchiveCustomField();
  const reorderMutation = useReorderCustomFields();


  const handleCreateField = () => {
    if (!canCreate) {
      Alert.alert(
        'Batas Tercapai',
        `Anda sudah mencapai batas maksimal ${maxAllowed} field kustom.`
      );
      return;
    }
    setShowCreator(true);
  };

  const handleEditField = (field: JournalCustomField) => {
    setSelectedFieldForEdit(field);
    setShowEditor(true);
  };

  const handleDeleteField = (field: JournalCustomField) => {
    Alert.alert(
      'Hapus Field',
      `Apakah Anda yakin ingin menghapus "${field.question_text}"?`,
      [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Hapus',
          style: 'destructive',
          onPress: async () => {
            try {
              await archiveMutation.mutateAsync(field.id);
            } catch (error: any) {
              Alert.alert(
                'Gagal Menghapus',
                error.message || 'Terjadi kesalahan saat menghapus field'
              );
            }
          },
        },
      ]
    );
  };

  const handleAIGenerate = (field: JournalCustomField) => {
    setSelectedFieldForAI(field);
    setShowAI(true);
  };

  const handleAISuccess = () => {
    // Close AI helper - new field will appear automatically
    setShowAI(false);
    setSelectedFieldForAI(null);
    // TanStack Query will automatically refresh the field list
  };



  const renderEmptyState = () => (
    <View style={customFieldStyles.emptyState}>
      <View style={customFieldStyles.emptyStateIcon}>
        <Ionicons name="add-circle-outline" size={64} color="#DD76B9" />
      </View>
      <Text style={customFieldStyles.emptyStateTitle}>
        Belum Ada Field Kustom
      </Text>
      <Text style={customFieldStyles.emptyStateDescription}>
        Buat field kustom untuk mempersonalisasi pengalaman journaling Anda. 
        Anda bisa membuat pertanyaan reflektif atau judul untuk mengorganisir jurnal.
      </Text>
      <TouchableOpacity
        style={customFieldStyles.emptyStateButton}
        onPress={handleCreateField}
        disabled={!canCreate}
      >
        <Text style={customFieldStyles.emptyStateButtonText}>
          Buat Field Pertama
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading) {
    return (
      <View style={customFieldStyles.loadingContainer}>
        <ActivityIndicator size="large" color="#DD76B9" />
        <Text style={customFieldStyles.loadingText}>
          Memuat field kustom...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={customFieldStyles.emptyState}>
        <View style={customFieldStyles.emptyStateIcon}>
          <Ionicons name="alert-circle-outline" size={64} color="#FF6B6B" />
        </View>
        <Text style={customFieldStyles.emptyStateTitle}>
          Gagal Memuat Field
        </Text>
        <Text style={customFieldStyles.emptyStateDescription}>
          Terjadi kesalahan saat memuat field kustom. Silakan coba lagi.
        </Text>
      </View>
    );
  }

  return (
    <View style={customFieldStyles.container}>
      <View style={customFieldStyles.managerContainer}>
        {/* Header */}
        <View style={customFieldStyles.managerHeader}>
          <Text style={customFieldStyles.managerTitle}>
            Field Kustom
          </Text>
          <TouchableOpacity
            style={[
              customFieldStyles.addButton,
              !canCreate && customFieldStyles.addButtonDisabled,
            ]}
            onPress={handleCreateField}
            disabled={!canCreate}
          >
            <Ionicons 
              name="add" 
              size={20} 
              color={canCreate ? "#FFFFFF" : "#999999"} 
            />
            <Text style={[
              customFieldStyles.addButtonText,
              !canCreate && { color: '#999999' },
            ]}>
              Tambah
            </Text>
          </TouchableOpacity>
        </View>

        {/* Limit indicator */}
        {canCreate && remaining <= 3 && (
          <View style={customFieldStyles.limitIndicator}>
            <Ionicons name="information-circle" size={16} color="#666666" />
            <Text style={customFieldStyles.limitText}>
              {remaining} field tersisa dari {maxAllowed}
            </Text>
          </View>
        )}

        {!canCreate && (
          <View style={[customFieldStyles.limitIndicator, customFieldStyles.limitWarning]}>
            <Ionicons name="warning" size={16} color="#856404" />
            <Text style={[customFieldStyles.limitText, customFieldStyles.limitWarningText]}>
              Batas maksimal {maxAllowed} field tercapai
            </Text>
          </View>
        )}

        {/* Fields List */}
        {customFields && customFields.length > 0 ? (
          <DraggableFieldList
            fields={customFields}
            onReorder={reorderMutation.mutate}
            onEdit={handleEditField}
            onDelete={handleDeleteField}
            onAIGenerate={handleAIGenerate}
            showAIGenerator={showAIGenerator}
          />
        ) : (
          renderEmptyState()
        )}
      </View>

      {/* Field Creator Modal */}
      <CustomFieldCreator
        visible={showCreator}
        onClose={() => setShowCreator(false)}
        onSuccess={() => {
          setShowCreator(false);
          // Fields will be automatically refreshed via TanStack Query
        }}
      />

      {/* Field Editor Modal */}
      {showEditor && selectedFieldForEdit && (
        <CustomFieldEditor
          visible={showEditor}
          field={selectedFieldForEdit}
          onClose={() => {
            setShowEditor(false);
            setSelectedFieldForEdit(null);
          }}
          onSuccess={() => {
            setShowEditor(false);
            setSelectedFieldForEdit(null);
            // Fields will be automatically refreshed via TanStack Query
          }}
        />
      )}

      {/* Simple AI Question Helper Modal */}
      {showAI && selectedFieldForAI && (
        <SimpleAIQuestionHelper
          baseQuestion={selectedFieldForAI.question_text}
          baseQuestionId={selectedFieldForAI.id}
          visible={showAI}
          onClose={() => {
            setShowAI(false);
            setSelectedFieldForAI(null);
          }}
          onSuccess={handleAISuccess}
        />
      )}
    </View>
  );
}

export default CustomFieldManager;
