import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  StyleSheet,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SimpleAIQuestionHelper } from './SimpleAIQuestionHelper';
import type { JournalQuestion, JournalEntry, CustomFieldConfig } from '@/types/journal';

interface UnifiedJournalCardProps {
  field: JournalQuestion;
  existingEntry?: JournalEntry;
  onSave: (fieldId: string, answer: string) => Promise<void>;
  onAIGenerate?: (field: JournalQuestion) => void;
  showAIButton?: boolean;
  autoSave?: boolean;
  showSaveButton?: boolean;
  disabled?: boolean;
}

export function UnifiedJournalCard({
  field,
  existingEntry,
  onSave,
  onAIGenerate,
  showAIButton = true,
  autoSave = true,
  showSaveButton = false,
  disabled = false,
}: UnifiedJournalCardProps) {
  const [answer, setAnswer] = useState(existingEntry?.answer || '');
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [showAI, setShowAI] = useState(false);
  const [focused, setFocused] = useState(false);
  const [hasEdited, setHasEdited] = useState(false);
  
  const saveTimeoutRef = useRef<number | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Get field configuration
  const config: CustomFieldConfig = field.custom_config || {};
  const isCustomField = field.field_type && field.field_type !== 'system';
  const isTitle = field.field_type === 'custom_title';
  const maxLength = config.maxLength || (isTitle ? 100 : 2000);
  const placeholder = config.placeholder || 
    (isTitle ? 'Masukkan judul...' : 'Tulis jawabanmu di sini...');

  // Reset state when entry changes
  useEffect(() => {
    setAnswer(existingEntry?.answer || '');
    setHasEdited(false);
    setLastSaved(null);
  }, [existingEntry]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || disabled || !hasEdited) return;
    
    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Don't save if answer hasn't changed from entry
    if (answer === (existingEntry?.answer || '')) return;

    // Don't save empty answers for title fields unless required
    if (isTitle && answer.trim().length === 0 && !config.isRequired) return;

    // Set timeout for auto-save
    saveTimeoutRef.current = setTimeout(async () => {
      if (answer.trim().length > 0 || config.isRequired) {
        await handleSave();
      }
    }, 1000); // 1 second delay

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [answer, autoSave, disabled, hasEdited]);

  const handleSave = async () => {
    if (disabled || isSaving) return;

    try {
      setIsSaving(true);
      await onSave(field.id, answer.trim());
      setLastSaved(new Date().toLocaleTimeString('id-ID', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
      setHasEdited(false);
      
      // Show save animation
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(2000),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } catch (error) {
      console.error('Error saving field:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTextChange = (newText: string) => {
    setAnswer(newText);
    setHasEdited(true);
  };

  const handleAIGenerate = () => {
    if (onAIGenerate) {
      onAIGenerate(field);
    } else {
      setShowAI(true);
    }
  };

  const handleAISuccess = () => {
    // Close the AI helper and refresh the page to show new fields
    setShowAI(false);
    // The new field will appear automatically due to TanStack Query cache invalidation
  };

  const getFieldIcon = () => {
    switch (field.field_type) {
      case 'custom_title':
        return 'text-outline';
      case 'custom_question':
        return 'help-circle-outline';
      default:
        return 'document-text-outline';
    }
  };

  const getFieldColor = () => {
    switch (field.field_type) {
      case 'custom_title':
        return '#4EAF64';
      case 'custom_question':
        return '#DD76B9';
      default:
        return '#DD76B9'; // Default journal theme color
    }
  };

  const fieldColor = getFieldColor();
  const showCharacterCount = isCustomField || answer.length > maxLength * 0.8;

  return (
    <View style={styles.card}>
      {/* Field Header */}
      <View style={styles.cardHeader}>
        <View style={styles.questionContainer}>
          {isCustomField && (
            <View style={styles.fieldTypeIndicator}>
              <Ionicons 
                name={getFieldIcon()} 
                size={16} 
                color={fieldColor} 
                style={styles.fieldIcon}
              />
              {field.ai_generated && (
                <Ionicons name="sparkles" size={12} color="#DD76B9" style={styles.aiIcon} />
              )}
            </View>
          )}
          <Text style={[styles.cardQuestion, isCustomField && { color: fieldColor }]}>
            {field.question_text}
          </Text>
        </View>

        <View style={styles.cardActions}>
          {/* Get writing ideas button (only for questions) */}
          {showAIButton && field.field_type !== 'custom_title' && (
            <TouchableOpacity
              style={styles.aiButton}
              onPress={handleAIGenerate}
              disabled={disabled}
              accessibilityLabel="Get writing ideas"
              accessibilityHint="Tap to get suggestions for new journal questions"
            >
              <Ionicons name="sparkles" size={20} color="#DD76B9" />
            </TouchableOpacity>
          )}

          {/* Save status indicator */}
          {isSaving && (
            <ActivityIndicator size="small" color={fieldColor} style={styles.savingIndicator} />
          )}
          
          {lastSaved && (
            <Animated.View style={[styles.savedIndicator, { opacity: fadeAnim }]}>
              <Text style={styles.savedText}>
                Tersimpan {lastSaved}
              </Text>
            </Animated.View>
          )}
        </View>
      </View>

      {/* Input Container */}
      <View style={styles.inputContainer}>
        <TextInput
          style={[
            styles.input,
            focused && { borderColor: fieldColor, backgroundColor: `${fieldColor}08` },
            isTitle && styles.titleInput,
            config.isRequired && answer.trim().length === 0 && styles.requiredInput,
          ]}
          value={answer}
          onChangeText={handleTextChange}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          placeholder={placeholder}
          multiline={!isTitle}
          numberOfLines={isTitle ? 1 : 4}
          maxLength={maxLength}
          editable={!disabled}
          textAlignVertical={isTitle ? 'center' : 'top'}
        />

        {/* Manual save button */}
        {showSaveButton && hasEdited && (
          <TouchableOpacity
            style={[
              styles.saveButton,
              isSaving && styles.saveButtonDisabled
            ]}
            onPress={handleSave}
            disabled={isSaving}
            activeOpacity={0.8}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Ionicons name="checkmark" size={18} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Footer */}
      {(showCharacterCount || config.isRequired) && (
        <View style={styles.cardFooter}>
          {showCharacterCount && (
            <Text style={[
              styles.characterCount,
              answer.length > maxLength * 0.9 && styles.characterCountWarning,
              answer.length >= maxLength && styles.characterCountError,
            ]}>
              {answer.length}/{maxLength} karakter
            </Text>
          )}
          
          {config.isRequired && answer.trim().length === 0 && (
            <Text style={styles.requiredText}>
              Wajib diisi
            </Text>
          )}
        </View>
      )}

      {/* Simple AI Question Helper Modal */}
      {showAI && (
        <SimpleAIQuestionHelper
          baseQuestion={field.question_text}
          baseQuestionId={field.id}
          visible={showAI}
          onClose={() => setShowAI(false)}
          onSuccess={handleAISuccess}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  questionContainer: {
    flex: 1,
    marginRight: 12,
  },
  fieldTypeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  fieldIcon: {
    marginRight: 4,
  },
  aiIcon: {
    marginLeft: 4,
  },
  cardQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    lineHeight: 22,
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FFF8FC',
    marginLeft: 8,
  },
  savingIndicator: {
    marginLeft: 8,
  },
  savedIndicator: {
    marginLeft: 8,
  },
  savedText: {
    fontSize: 12,
    color: '#4EAF64',
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#FFFFFF',
    minHeight: 80,
    textAlignVertical: 'top',
  },
  titleInput: {
    minHeight: 44,
    textAlignVertical: 'center',
  },
  requiredInput: {
    borderColor: '#FF6B6B',
    backgroundColor: '#FFF5F5',
  },
  saveButton: {
    backgroundColor: '#4B3425',
    borderRadius: 18,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 4px rgba(75, 52, 37, 0.3)',
      },
      default: {
        shadowColor: '#4B3425',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.3,
        shadowRadius: 4,
      },
    }),
    elevation: 3,
  },
  saveButtonDisabled: {
    backgroundColor: '#CBD5E0',
    ...Platform.select({
      web: {
        boxShadow: 'none',
      },
      default: {
        shadowOpacity: 0,
      },
    }),
    elevation: 0,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
  },
  characterCountWarning: {
    color: '#FF8C00',
  },
  characterCountError: {
    color: '#FF6B6B',
  },
  requiredText: {
    fontSize: 12,
    color: '#FF6B6B',
    fontWeight: '500',
  },
});

export default UnifiedJournalCard;
