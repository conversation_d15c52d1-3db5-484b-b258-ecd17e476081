import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface AppointmentCalendarProps {
  currentMonth: Date;
  selectedDate: string | null;
  availableDates: string[];
  bookedDates: string[];
  loading?: boolean;
  onDateSelect: (date: string) => void;
  onNavigateMonth: (direction: 'prev' | 'next') => void;
  onGoToToday: () => void;
}

const AppointmentCalendar: React.FC<AppointmentCalendarProps> = ({
  currentMonth,
  selectedDate,
  availableDates,
  bookedDates,
  loading = false,
  onDateSelect,
  onNavigateMonth,
  onGoToToday,
}) => {
  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

  const today = new Date();
  const todayString = today.toISOString().split('T')[0];

  // Get first day of month and number of days
  const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
  const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startingDayOfWeek = firstDay.getDay();

  // Generate calendar days
  const calendarDays: (number | null)[] = [];
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null);
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  const formatDateString = (day: number): string => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth() + 1;
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  };

  const isAvailable = (day: number): boolean => {
    const dateString = formatDateString(day);
    return availableDates.includes(dateString);
  };

  const isBooked = (day: number): boolean => {
    const dateString = formatDateString(day);
    return bookedDates.includes(dateString);
  };

  const isToday = (day: number): boolean => {
    const dateString = formatDateString(day);
    return dateString === todayString;
  };

  const isSelected = (day: number): boolean => {
    const dateString = formatDateString(day);
    return dateString === selectedDate;
  };

  const isPastDate = (day: number): boolean => {
    const dateString = formatDateString(day);
    return dateString < todayString;
  };

  const renderDay = (day: number | null, index: number) => {
    if (day === null) {
      return <View key={index} style={styles.emptyDay} />;
    }

    const dateString = formatDateString(day);
    const dayIsAvailable = isAvailable(day);
    const dayIsBooked = isBooked(day);
    const dayIsToday = isToday(day);
    const dayIsSelected = isSelected(day);
    const dayIsPast = isPastDate(day);

    // Determine if the day is selectable
    const isSelectable = !dayIsPast && (dayIsAvailable || dayIsBooked);

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.dayCell,
          dayIsToday && styles.todayCell,
          dayIsSelected && styles.selectedCell,
          dayIsBooked && styles.bookedCell,
          dayIsPast && styles.pastCell,
        ]}
        onPress={() => isSelectable ? onDateSelect(dateString) : null}
        activeOpacity={isSelectable ? 0.7 : 1}
        disabled={!isSelectable}
      >
        <Text
          style={[
            styles.dayText,
            dayIsToday && styles.todayText,
            dayIsSelected && styles.selectedText,
            dayIsBooked && styles.bookedText,
            dayIsPast && styles.pastText,
          ]}
        >
          {day}
        </Text>
        {dayIsAvailable && !dayIsBooked && (
          <View style={styles.availableIndicator} />
        )}
        {dayIsBooked && (
          <View style={styles.bookedIndicator} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Calendar Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => onNavigateMonth('prev')}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={20} color="#B5A136" />
        </TouchableOpacity>

        <View style={styles.monthYearContainer}>
          <Text style={styles.monthYearText}>
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.navButton}
          onPress={() => onNavigateMonth('next')}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-forward" size={20} color="#B5A136" />
        </TouchableOpacity>
      </View>

      {/* Today Button */}
      <TouchableOpacity
        style={styles.todayButton}
        onPress={onGoToToday}
        activeOpacity={0.7}
      >
        <Text style={styles.todayButtonText}>Hari Ini</Text>
      </TouchableOpacity>

      {/* Calendar Grid */}
      <View style={styles.calendar}>
        {/* Day Headers */}
        <View style={styles.dayHeaderRow}>
          {dayNames.map((dayName, index) => (
            <View key={index} style={styles.dayHeader}>
              <Text style={styles.dayHeaderText}>{dayName}</Text>
            </View>
          ))}
        </View>

        {/* Calendar Days */}
        <View style={styles.daysGrid}>
          {calendarDays.map((day, index) => renderDay(day, index))}
        </View>
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendIndicator, styles.availableIndicator]} />
          <Text style={styles.legendText}>Tersedia</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendIndicator, styles.bookedIndicator]} />
          <Text style={styles.legendText}>Sudah Dipesan</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  navButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#F6EBB1',
  },
  monthYearContainer: {
    flex: 1,
    alignItems: 'center',
  },
  monthYearText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#454459',
  },
  todayButton: {
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F6EBB1',
    borderRadius: 20,
    marginBottom: 16,
  },
  todayButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B5A136',
  },
  calendar: {
    marginBottom: 16,
  },
  dayHeaderRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  dayHeader: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  dayHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  emptyDay: {
    width: '14.28%',
    aspectRatio: 1,
  },
  dayCell: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginBottom: 4,
  },
  dayText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#454459',
  },
  todayCell: {
    backgroundColor: '#E3F2FD',
    borderRadius: 6,
    borderWidth: 1.5,
    borderColor: '#4285F4',
  },
  todayText: {
    color: '#4285F4',
    fontWeight: '700',
  },
  selectedCell: {
    backgroundColor: '#B5A136',
    borderRadius: 6,
  },
  selectedText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  bookedCell: {
    backgroundColor: '#FEE2E2',
    borderRadius: 6,
  },
  bookedText: {
    color: '#DC2626',
    fontWeight: '600',
  },
  pastCell: {
    opacity: 0.3,
  },
  pastText: {
    color: '#9CA3AF',
  },
  availableIndicator: {
    position: 'absolute',
    bottom: 2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#10B981',
  },
  bookedIndicator: {
    position: 'absolute',
    bottom: 2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#DC2626',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 24,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
  },
});

export default AppointmentCalendar;
