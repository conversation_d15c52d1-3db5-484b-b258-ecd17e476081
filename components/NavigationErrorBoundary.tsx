import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { reportError, addBreadcrumb } from '@/lib/sentryConfig';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
  onRetry?: () => void;
  onGoHome?: () => void;
  onResetNavigation?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  retryCount: number;
}

export class NavigationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      retryCount: 0
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('[NavigationErrorBoundary] Error caught:', error);
    console.error('[NavigationErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Add breadcrumb for navigation error context
    addBreadcrumb(
      'Navigation component error occurred',
      'error',
      {
        component: 'NavigationErrorBoundary',
        errorName: error.name,
        errorMessage: error.message,
        retryCount: this.state.retryCount,
        componentStack: errorInfo.componentStack?.split('\n').slice(0, 3).join('\n')
      }
    );

    // Report error to Sentry with navigation-specific context
    reportError(error, {
      component: 'NavigationErrorBoundary',
      action: 'navigation_component_render',
      metadata: {
        retryCount: this.state.retryCount,
        errorInfo: {
          componentStack: errorInfo.componentStack,
          errorBoundary: 'NavigationErrorBoundary'
        }
      }
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;
    
    addBreadcrumb(
      'Navigation error boundary retry attempted',
      'user_action',
      { 
        component: 'NavigationErrorBoundary',
        retryCount: newRetryCount
      }
    );

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: newRetryCount
    });

    this.props.onRetry?.();
  };

  handleGoHome = () => {
    addBreadcrumb(
      'User navigated to home from navigation error boundary',
      'user_action',
      { component: 'NavigationErrorBoundary' }
    );

    this.props.onGoHome?.();
  };

  handleResetNavigation = () => {
    addBreadcrumb(
      'User reset navigation from error boundary',
      'user_action',
      { component: 'NavigationErrorBoundary' }
    );

    this.props.onResetNavigation?.();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI for navigation components
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="compass-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Navigasi Bermasalah</Text>
          <Text style={styles.errorMessage}>
            Terjadi kesalahan pada sistem navigasi. Anda bisa mencoba lagi, kembali ke beranda, atau reset navigasi.
          </Text>
          
          {this.state.retryCount < 3 && (
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={this.handleRetry}
            >
              <Text style={styles.retryButtonText}>
                Coba Lagi {this.state.retryCount > 0 && `(${this.state.retryCount + 1}/3)`}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity 
            style={styles.homeButton}
            onPress={this.handleGoHome}
          >
            <Ionicons name="home-outline" size={20} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.homeButtonText}>Kembali ke Beranda</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.resetButton}
            onPress={this.handleResetNavigation}
          >
            <Ionicons name="refresh-outline" size={20} color="#333" style={styles.buttonIcon} />
            <Text style={styles.resetButtonText}>Reset Navigasi</Text>
          </TouchableOpacity>

          <View style={styles.quickAccessContainer}>
            <Text style={styles.quickAccessTitle}>Akses Cepat:</Text>
            <View style={styles.quickAccessButtons}>
              <TouchableOpacity style={styles.quickButton}>
                <Ionicons name="book-outline" size={24} color="#A08CFB" />
                <Text style={styles.quickButtonText}>Journal</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.quickButton}>
                <Ionicons name="happy-outline" size={24} color="#A08CFB" />
                <Text style={styles.quickButtonText}>Mood</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.quickButton}>
                <Ionicons name="chatbubble-outline" size={24} color="#A08CFB" />
                <Text style={styles.quickButtonText}>Chat</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {__DEV__ && this.state.error && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugTitle}>Debug Info:</Text>
              <Text style={styles.debugText}>
                {this.state.error.name}: {this.state.error.message}
              </Text>
              <Text style={styles.debugText}>
                Retry Count: {this.state.retryCount}
              </Text>
              {this.state.error.stack && (
                <Text style={styles.debugStack}>
                  {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                </Text>
              )}
            </View>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FEFEFE',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#A08CFB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  homeButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  homeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resetButton: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  resetButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonIcon: {
    marginRight: 8,
  },
  quickAccessContainer: {
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 10,
    marginBottom: 16,
    width: '100%',
  },
  quickAccessTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  quickAccessButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickButton: {
    alignItems: 'center',
    padding: 12,
  },
  quickButtonText: {
    fontSize: 12,
    color: '#A08CFB',
    marginTop: 4,
    fontWeight: '600',
  },
  debugContainer: {
    backgroundColor: '#F5F5F5',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  debugStack: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
});
