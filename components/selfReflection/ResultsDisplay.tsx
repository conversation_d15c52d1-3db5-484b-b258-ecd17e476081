/**
 * Results Display Component
 * Shows the self-reflection results with risk level and recommendations
 */

import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { selfReflectionStyles } from '@/styles/SelfReflectionStyles';
import type { ResultsDisplayProps, RiskLevel } from '@/types/selfReflection';
import { getRiskLevelColor, getRiskLevelBackground } from '@/types/selfReflection';

// Risk level display configuration
const RISK_LEVEL_CONFIG: Record<RiskLevel, {
  icon: string;
  title: string;
  description: string;
  recommendations: string[];
}> = {
  green: {
    icon: '😊',
    title: 'Kondisi Emosional Stabil',
    description: 'Kamu tampak dalam kondisi yang cukup baik. Temani siap menemani percakapan ringan dan menyenangkan.',
    recommendations: [
      'Lanjutkan aktivitas positif yang sudah kamu lakukan',
      'Jaga pola tidur dan makan yang teratur',
      'Tetap terhubung dengan orang-orang terdekat'
    ]
  },
  yellow: {
    icon: '😔',
    title: '<PERSON> Tanda Kelelahan Emosional',
    description: 'Kamu mungkin sedang mengalami sedikit tekanan. Temani akan lebih hati-hati dan memberikan validasi emosional.',
    recommendations: [
      'Luangkan waktu untuk istirahat dan self-care',
      'Bicarakan perasaan dengan orang yang dipercaya',
      'Pertimbangkan aktivitas yang menenangkan seperti meditasi'
    ]
  },
  red: {
    icon: '😰',
    title: 'Distress Cukup Tinggi',
    description: 'Kamu tampak sedang mengalami tekanan yang cukup berat. Temani akan memberikan dukungan dengan empati tinggi.',
    recommendations: [
      'Pertimbangkan untuk berbicara dengan konselor profesional',
      'Jangan ragu untuk meminta bantuan dari keluarga atau teman',
      'Fokus pada kebutuhan dasar: makan, tidur, dan keamanan'
    ]
  },
  emergency: {
    icon: '🚨',
    title: 'Perlu Perhatian Khusus',
    description: 'Kondisi kamu memerlukan dukungan profesional segera. Temani akan mengaktifkan protokol keselamatan.',
    recommendations: [
      'Hubungi hotline kesehatan mental: 119',
      'Jangan sendirian - hubungi keluarga atau teman terdekat',
      'Pertimbangkan untuk pergi ke fasilitas kesehatan mental'
    ]
  }
};

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  session,
  riskAssessmentRule,
  onContinue,
}) => {
  const config = RISK_LEVEL_CONFIG[session.riskLevel];
  const riskColor = getRiskLevelColor(session.riskLevel);
  const riskBackground = getRiskLevelBackground(session.riskLevel);

  const getStyleForRiskLevel = (level: RiskLevel) => {
    switch (level) {
      case 'green':
        return {
          card: selfReflectionStyles.riskGreen,
          text: selfReflectionStyles.riskGreenText,
        };
      case 'yellow':
        return {
          card: selfReflectionStyles.riskYellow,
          text: selfReflectionStyles.riskYellowText,
        };
      case 'red':
        return {
          card: selfReflectionStyles.riskRed,
          text: selfReflectionStyles.riskRedText,
        };
      case 'emergency':
        return {
          card: selfReflectionStyles.riskEmergency,
          text: selfReflectionStyles.riskEmergencyText,
        };
      default:
        return {
          card: selfReflectionStyles.riskGreen,
          text: selfReflectionStyles.riskGreenText,
        };
    }
  };

  const styles = getStyleForRiskLevel(session.riskLevel);

  return (
    <View style={selfReflectionStyles.container}>
      <View style={selfReflectionStyles.scrollContainer}>
        {/* Header */}
        <View style={selfReflectionStyles.headerContainer}>
          <Text style={selfReflectionStyles.headerTitle}>
            Hasil Self-Reflection Check-In
          </Text>
          <Text style={selfReflectionStyles.headerSubtitle}>
            Terima kasih sudah berbagi. Berikut adalah ringkasan kondisi emosional kamu hari ini.
          </Text>
        </View>

        {/* Risk Level Card */}
        <View style={selfReflectionStyles.resultsContainer}>
          <View style={[selfReflectionStyles.riskLevelCard, styles.card]}>
            <View style={[
              selfReflectionStyles.riskLevelIcon,
              { backgroundColor: riskBackground }
            ]}>
              <Text style={selfReflectionStyles.riskLevelIconText}>
                {config.icon}
              </Text>
            </View>
            
            <Text style={[selfReflectionStyles.riskLevelTitle, styles.text]}>
              {config.title}
            </Text>
            
            <Text style={selfReflectionStyles.riskLevelMessage}>
              {config.description}
            </Text>
            
            <Text style={selfReflectionStyles.riskLevelScore}>
              Skor: {session.totalScore} dari {session.metadata?.maxPossibleScore || 'N/A'}
            </Text>
          </View>
        </View>

        {/* Recommendations */}
        <View style={selfReflectionStyles.questionCard}>
          <Text style={[selfReflectionStyles.questionText, { marginBottom: 16 }]}>
            Rekomendasi untuk Kamu
          </Text>
          
          {config.recommendations.map((recommendation, index) => (
            <View key={index} style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 12 }}>
              <Text style={{ fontSize: 16, marginRight: 12, color: riskColor }}>
                •
              </Text>
              <Text style={{ flex: 1, fontSize: 14, color: '#454459', lineHeight: 20 }}>
                {recommendation}
              </Text>
            </View>
          ))}
        </View>

        {/* AI Tone Preview */}
        <View style={selfReflectionStyles.questionCard}>
          <Text style={[selfReflectionStyles.questionText, { marginBottom: 16 }]}>
            Bagaimana Temani Akan Membantu
          </Text>
          
          <View style={{
            backgroundColor: riskBackground,
            borderRadius: 10,
            padding: 16,
            borderLeftWidth: 4,
            borderLeftColor: riskColor,
          }}>
            <Text style={{ fontSize: 14, color: '#454459', lineHeight: 20, fontStyle: 'italic' }}>
              &ldquo;{riskAssessmentRule.actions[session.riskLevel]?.message || 'Temani akan menyesuaikan gaya percakapan dengan kondisi kamu.'}&rdquo;
            </Text>
          </View>
          
          {session.supportPreference && session.supportPreference !== 'unsure' && (
            <View style={{ marginTop: 12 }}>
              <Text style={{ fontSize: 14, color: '#6B7280', textAlign: 'center' }}>
                Berdasarkan preferensi kamu: {getSupportPreferenceText(session.supportPreference)}
              </Text>
            </View>
          )}
        </View>

        {/* Privacy Notice */}
        <View style={selfReflectionStyles.questionCard}>
          <Text style={[selfReflectionStyles.questionText, { fontSize: 16, marginBottom: 12 }]}>
            Privasi Terjaga 🔒
          </Text>
          
          <Text style={{ fontSize: 14, color: '#6B7280', textAlign: 'center', lineHeight: 20 }}>
            Jawaban kamu tidak disimpan secara permanen. Informasi ini hanya digunakan untuk 
            menyesuaikan cara Temani berinteraksi dengan kamu hari ini.
          </Text>
        </View>

        {/* Continue Button */}
        <View style={{ paddingVertical: 20 }}>
          <Pressable
            style={[selfReflectionStyles.navButton, selfReflectionStyles.navButtonPrimary, { width: '100%' }]}
            onPress={onContinue}
          >
            <Text style={[selfReflectionStyles.navButtonText, selfReflectionStyles.navButtonTextPrimary]}>
              Mulai Ngobrol dengan Temani
            </Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

// Helper function to get support preference text
const getSupportPreferenceText = (preference: string): string => {
  const preferenceMap: Record<string, string> = {
    'light_chat': 'Ngobrol ringan',
    'validation': 'Validasi emosional',
    'insight': 'Insight dan refleksi',
    'coping_skills': 'Belajar coping skills',
    'unsure': 'Belum yakin'
  };
  
  return preferenceMap[preference] || preference;
};

export default ResultsDisplay;
