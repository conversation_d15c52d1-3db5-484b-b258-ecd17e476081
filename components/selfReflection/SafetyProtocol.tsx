/**
 * Safety Protocol Component
 * Displays emergency support resources and crisis intervention
 */

import React from 'react';
import { View, Text, Pressable, Linking, Alert } from 'react-native';
import { selfReflectionStyles } from '@/styles/SelfReflectionStyles';
import type { SafetyProtocolProps } from '@/types/selfReflection';

// Crisis support resources in Indonesia
const CRISIS_RESOURCES = [
  {
    name: 'Hotline Kesehatan Mental',
    phone: '119',
    description: 'Layanan darurat kesehatan mental 24/7',
    type: 'emergency',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    phone: '021-78842580',
    description: 'Konseling dan dukungan trauma',
    type: 'counseling',
  },
  {
    name: 'Into The Light Indonesia',
    phone: '021-78842580',
    description: 'Pencegahan bunuh diri dan dukungan krisis',
    type: 'suicide_prevention',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    phone: '119',
    description: 'Layanan konseling dan dukungan psikologis',
    type: 'counseling',
  },
];

export const SafetyProtocol: React.FC<SafetyProtocolProps> = ({
  session,
  onContinue,
  onSeekHelp,
}) => {
  console.log('SafetyProtocol rendered for session:', session.id, 'risk level:', session.riskLevel);
  const handleCallHotline = async (phone: string, name: string) => {
    try {
      const phoneUrl = `tel:${phone}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);
      
      if (canOpen) {
        Alert.alert(
          'Hubungi Bantuan Profesional',
          `Apakah kamu ingin menghubungi ${name} di ${phone}?`,
          [
            {
              text: 'Batal',
              style: 'cancel',
            },
            {
              text: 'Hubungi',
              onPress: () => {
                Linking.openURL(phoneUrl);
                onSeekHelp();
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Nomor Telepon',
          `${name}: ${phone}\n\nSalin nomor ini dan hubungi menggunakan aplikasi telepon kamu.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error opening phone dialer:', error);
      Alert.alert(
        'Nomor Telepon',
        `${name}: ${phone}\n\nSalin nomor ini dan hubungi menggunakan aplikasi telepon kamu.`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleEmergencyContact = () => {
    Alert.alert(
      'Darurat',
      'Jika kamu dalam bahaya langsung atau memiliki pikiran untuk menyakiti diri sendiri, segera hubungi:\n\n• 119 (Hotline Kesehatan Mental)\n• 112 (Nomor Darurat Nasional)\n• Pergi ke UGD rumah sakit terdekat',
      [
        {
          text: 'Hubungi 119',
          onPress: () => handleCallHotline('119', 'Hotline Kesehatan Mental'),
        },
        {
          text: 'Hubungi 112',
          onPress: () => handleCallHotline('112', 'Nomor Darurat Nasional'),
        },
        {
          text: 'Nanti',
          style: 'cancel',
        },
      ]
    );
  };

  const isEmergencyLevel = session.riskLevel === 'emergency';
  const hasHighRisk = session.riskLevel === 'red' || session.riskLevel === 'emergency';

  return (
    <View style={selfReflectionStyles.container}>
      <View style={selfReflectionStyles.scrollContainer}>
        {/* Emergency Alert */}
        {isEmergencyLevel && (
          <View style={selfReflectionStyles.safetyContainer}>
            <Text style={selfReflectionStyles.safetyIcon}>🚨</Text>
            <Text style={selfReflectionStyles.safetyTitle}>
              Dukungan Segera Tersedia
            </Text>
            <Text style={selfReflectionStyles.safetyMessage}>
              Terima kasih sudah berbagi. Temani peduli dengan kondisi kamu saat ini. 
              Kamu tidak sendirian, dan bantuan profesional tersedia 24/7.
            </Text>
            
            <View style={selfReflectionStyles.safetyButtonsContainer}>
              <Pressable
                style={selfReflectionStyles.safetyButton}
                onPress={handleEmergencyContact}
              >
                <Text style={selfReflectionStyles.safetyButtonText}>
                  Hubungi Bantuan Darurat
                </Text>
              </Pressable>
            </View>
          </View>
        )}

        {/* High Risk Support */}
        {hasHighRisk && !isEmergencyLevel && (
          <View style={[selfReflectionStyles.safetyContainer, { backgroundColor: '#FFFBEB', borderColor: '#FDE68A' }]}>
            <Text style={selfReflectionStyles.safetyIcon}>⚠️</Text>
            <Text style={[selfReflectionStyles.safetyTitle, { color: '#D97706' }]}>
              Dukungan Profesional Direkomendasikan
            </Text>
            <Text style={[selfReflectionStyles.safetyMessage, { color: '#92400E' }]}>
              Berdasarkan jawaban kamu, mungkin akan membantu untuk berbicara dengan 
              profesional kesehatan mental. Mereka dapat memberikan dukungan yang lebih tepat.
            </Text>
          </View>
        )}

        {/* Professional Resources */}
        <View style={selfReflectionStyles.questionCard}>
          <Text style={[selfReflectionStyles.questionText, { marginBottom: 20 }]}>
            Sumber Bantuan Profesional
          </Text>
          
          {CRISIS_RESOURCES.map((resource, index) => (
            <Pressable
              key={index}
              style={[
                selfReflectionStyles.optionButton,
                { marginBottom: 12, backgroundColor: '#F8F9FA' }
              ]}
              onPress={() => handleCallHotline(resource.phone, resource.name)}
            >
              <View style={{ flex: 1 }}>
                <Text style={[selfReflectionStyles.optionText, { fontWeight: '600', marginBottom: 4 }]}>
                  {resource.name}
                </Text>
                <Text style={[selfReflectionStyles.optionText, { fontSize: 14, color: '#6B7280' }]}>
                  {resource.description}
                </Text>
                <Text style={[selfReflectionStyles.optionText, { fontSize: 14, fontWeight: '600', color: '#A08CFB' }]}>
                  📞 {resource.phone}
                </Text>
              </View>
            </Pressable>
          ))}
        </View>

        {/* Immediate Safety Tips */}
        <View style={selfReflectionStyles.questionCard}>
          <Text style={[selfReflectionStyles.questionText, { marginBottom: 16 }]}>
            Tips Keamanan Segera
          </Text>
          
          <View style={{ gap: 12 }}>
            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Text style={{ fontSize: 16, marginRight: 8 }}>🏠</Text>
              <Text style={{ flex: 1, fontSize: 14, color: '#454459', lineHeight: 20 }}>
                Pastikan kamu berada di tempat yang aman
              </Text>
            </View>
            
            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Text style={{ fontSize: 16, marginRight: 8 }}>👥</Text>
              <Text style={{ flex: 1, fontSize: 14, color: '#454459', lineHeight: 20 }}>
                Hubungi teman atau keluarga yang bisa dipercaya
              </Text>
            </View>
            
            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Text style={{ fontSize: 16, marginRight: 8 }}>🧘</Text>
              <Text style={{ flex: 1, fontSize: 14, color: '#454459', lineHeight: 20 }}>
                Coba teknik pernapasan: tarik napas 4 detik, tahan 4 detik, buang 4 detik
              </Text>
            </View>
            
            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Text style={{ fontSize: 16, marginRight: 8 }}>🚫</Text>
              <Text style={{ flex: 1, fontSize: 14, color: '#454459', lineHeight: 20 }}>
                Hindari alkohol atau zat yang dapat memperburuk kondisi
              </Text>
            </View>
          </View>
        </View>

        {/* Continue Options */}
        <View style={selfReflectionStyles.questionCard}>
          <Text style={[selfReflectionStyles.questionText, { marginBottom: 16 }]}>
            Langkah Selanjutnya
          </Text>
          
          <Text style={{ fontSize: 14, color: '#6B7280', textAlign: 'center', marginBottom: 20, lineHeight: 20 }}>
            Kamu bisa melanjutkan percakapan dengan Temani, tapi ingat bahwa Temani bukan 
            pengganti bantuan profesional untuk kondisi yang serius.
          </Text>
          
          <View style={selfReflectionStyles.safetyButtonsContainer}>
            <Pressable
              style={[selfReflectionStyles.safetyButton, selfReflectionStyles.safetyButtonSecondary]}
              onPress={() => {
                console.log('Lanjutkan dengan Temani button clicked');
                onContinue();
              }}
            >
              <Text style={selfReflectionStyles.safetyButtonTextSecondary}>
                Lanjutkan dengan Temani
              </Text>
            </Pressable>
            
            <Pressable
              style={selfReflectionStyles.safetyButton}
              onPress={() => handleCallHotline('119', 'Hotline Kesehatan Mental')}
            >
              <Text style={selfReflectionStyles.safetyButtonText}>
                Hubungi Bantuan Profesional
              </Text>
            </Pressable>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SafetyProtocol;
