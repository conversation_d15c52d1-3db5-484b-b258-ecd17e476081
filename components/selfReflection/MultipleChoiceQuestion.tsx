/**
 * Multiple Choice Question Component
 * Renders multiple choice questions with emoji and text options
 */

import React from 'react';
import { View, Text, Pressable, Animated } from 'react-native';
import { selfReflectionStyles } from '@/styles/SelfReflectionStyles';
import type { MultipleChoiceQuestionProps } from '@/types/selfReflection';
import { getQuestionText, getOptionText } from '@/types/selfReflection';

export const MultipleChoiceQuestion: React.FC<MultipleChoiceQuestionProps> = ({
  question,
  selectedOption,
  onOptionSelect,
}) => {
  const [animatedValues] = React.useState(
    () => new Map(question.options?.map(option => [option.id, new Animated.Value(1)]) || [])
  );

  const handleOptionPress = (optionId: string) => {
    // Animate button press
    const animatedValue = animatedValues.get(optionId);
    if (animatedValue) {
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }

    onOptionSelect(optionId);
  };

  if (!question.options || question.options.length === 0) {
    return (
      <View style={selfReflectionStyles.errorContainer}>
        <Text style={selfReflectionStyles.errorText}>
          No options available for this question
        </Text>
      </View>
    );
  }

  return (
    <View style={selfReflectionStyles.questionCard}>
      <Text style={selfReflectionStyles.questionText}>
        {getQuestionText(question)}
      </Text>
      
      <View style={selfReflectionStyles.optionsContainer}>
        {question.options.map((option) => {
          const isSelected = selectedOption === option.id;
          const animatedValue = animatedValues.get(option.id);
          
          return (
            <Animated.View
              key={option.id}
              style={{
                transform: [{ scale: animatedValue || 1 }],
              }}
            >
              <Pressable
                style={[
                  selfReflectionStyles.optionButton,
                  isSelected && selfReflectionStyles.optionButtonSelected,
                  option.color && isSelected && {
                    borderColor: option.color,
                    shadowColor: option.color,
                  },
                ]}
                onPress={() => handleOptionPress(option.id)}
                accessibilityRole="button"
                accessibilityLabel={`${option.emoji || ''} ${getOptionText(option)}`.trim()}
                accessibilityState={{ selected: isSelected }}
              >
                {option.emoji && (
                  <Text style={selfReflectionStyles.optionEmoji}>
                    {option.emoji}
                  </Text>
                )}
                
                <Text
                  style={[
                    selfReflectionStyles.optionText,
                    isSelected && selfReflectionStyles.optionTextSelected,
                    isSelected && option.color && { color: option.color },
                  ]}
                >
                  {getOptionText(option)}
                </Text>
                
                {/* Selection indicator */}
                {isSelected && (
                  <View
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      backgroundColor: option.color || '#A08CFB',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Text style={{ color: '#FFFFFF', fontSize: 12, fontWeight: 'bold' }}>
                      ✓
                    </Text>
                  </View>
                )}
              </Pressable>
            </Animated.View>
          );
        })}
      </View>
      
      {/* Helper text for safety questions */}
      {question.metadata?.isSafetyQuestion && (
        <View
          style={{
            marginTop: 16,
            padding: 12,
            backgroundColor: '#FEF2F2',
            borderRadius: 8,
            borderLeftWidth: 4,
            borderLeftColor: '#EF4444',
          }}
        >
          <Text
            style={{
              fontSize: 14,
              color: '#7F1D1D',
              fontStyle: 'italic',
              textAlign: 'center',
            }}
          >
            Pertanyaan ini membantu kami memahami kondisi kamu dengan lebih baik. 
            Jawaban kamu akan membantu Temani memberikan dukungan yang tepat.
          </Text>
        </View>
      )}
    </View>
  );
};

export default MultipleChoiceQuestion;
