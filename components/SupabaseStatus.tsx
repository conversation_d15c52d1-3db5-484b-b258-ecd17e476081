import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { validateSupabaseConnection } from '@/lib/supabase';
import { Feather } from '@expo/vector-icons';

interface ConnectionStatus {
  isValid: boolean;
  error?: string;
  loading: boolean;
}

export const SupabaseStatus: React.FC = () => {
  const [status, setStatus] = useState<ConnectionStatus>({
    isValid: false,
    loading: true,
  });

  useEffect(() => {
    const checkConnection = async () => {
      try {
        const result = await validateSupabaseConnection();
        setStatus({
          isValid: result.isValid,
          error: result.error,
          loading: false,
        });
      } catch (error) {
        setStatus({
          isValid: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          loading: false,
        });
      }
    };

    checkConnection();
  }, []);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  if (status.loading) {
    return (
      <View style={styles.container}>
        <View style={[styles.statusCard, styles.loadingCard]}>
          <Feather name="alert-circle" size={16} color="#f59e0b" />
          <Text style={styles.loadingText}>Checking Supabase connection...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[
        styles.statusCard,
        status.isValid ? styles.successCard : styles.errorCard
      ]}>
        {status.isValid ? (
          <>
            <Feather name="check-circle" size={16} color="#10b981" />
            <Text style={styles.successText}>Supabase connected successfully</Text>
          </>
        ) : (
          <>
            <Feather name="x-circle" size={16} color="#ef4444" />
            <View style={styles.errorContent}>
              <Text style={styles.errorText}>Supabase connection failed</Text>
              {status.error && (
                <Text style={styles.errorDetail}>{status.error}</Text>
              )}
            </View>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  statusCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  successCard: {
    backgroundColor: '#ecfdf5',
    borderLeftColor: '#10b981',
  },
  errorCard: {
    backgroundColor: '#fef2f2',
    borderLeftColor: '#ef4444',
  },
  loadingCard: {
    backgroundColor: '#fffbeb',
    borderLeftColor: '#f59e0b',
  },
  successText: {
    color: '#065f46',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 8,
  },
  errorContent: {
    flex: 1,
    marginLeft: 8,
  },
  errorText: {
    color: '#dc2626',
    fontSize: 12,
    fontWeight: '500',
  },
  errorDetail: {
    color: '#dc2626',
    fontSize: 11,
    marginTop: 2,
    opacity: 0.8,
  },
  loadingText: {
    color: '#d97706',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 8,
  },
});