import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import type { VoiceTranscriptMessage } from '@/hooks/useVoiceTranscript';

interface TranscriptViewProps {
  messages: VoiceTranscriptMessage[];
  isVisible: boolean;
  loading: boolean;
  error: string | null;
  onClose: () => void;
  onSaveToChat: () => void;
  onClear: () => void;
}

const { height: screenHeight } = Dimensions.get('window');

export const TranscriptView: React.FC<TranscriptViewProps> = ({
  messages,
  isVisible,
  loading,
  error,
  onClose,
  onSaveToChat,
  onClear,
}) => {
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (isVisible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.spring(slideAnim, {
        toValue: screenHeight,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [isVisible, slideAnim]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0 && scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const renderMessage = (message: VoiceTranscriptMessage, index: number) => {
    const isUser = message.sender === 'user';
    const isLastMessage = index === messages.length - 1;
    
    return (
      <View
        key={`${message.id}-${index}`}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.aiMessageContainer,
        ]}
      >
        <View
          style={[
            styles.messageBubble,
            isUser ? styles.userMessageBubble : styles.aiMessageBubble,
            !message.isFinal && styles.tentativeMessage,
          ]}
        >
          <Text
            style={[
              styles.messageText,
              isUser ? styles.userMessageText : styles.aiMessageText,
              !message.isFinal && styles.tentativeText,
            ]}
          >
            {message.content}
          </Text>
          
          {!message.isFinal && (
            <View style={styles.tentativeIndicator}>
              <Text style={styles.tentativeLabel}>Mengetik...</Text>
            </View>
          )}
        </View>
        
        <Text style={styles.timestamp}>
          {formatTime(message.timestamp)}
          {!message.isFinal && ' (sementara)'}
        </Text>
      </View>
    );
  };

  if (!isVisible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      {/* Header */}
      <LinearGradient
        colors={['#FFD572', '#FEBD38']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <MaterialCommunityIcons name="text" size={24} color="#454459" />
            <Text style={styles.headerTitle}>Transkrip Percakapan</Text>
          </View>
          
          <View style={styles.headerActions}>
            {messages.length > 0 && (
              <>
                {/* Save button removed - voice conversations are no longer saved to chat history */}
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={onClear}
                  disabled={loading}
                >
                  <Feather name="trash-2" size={20} color="#454459" />
                </TouchableOpacity>
              </>
            )}

            <TouchableOpacity
              style={styles.headerButton}
              onPress={onClose}
            >
              <Feather name="x" size={24} color="#454459" />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>

      {/* Content */}
      <View style={styles.content}>
        {/* Info note about not saving to chat history */}
        <View style={styles.infoContainer}>
          <Feather name="info" size={16} color="#6B7280" />
          <Text style={styles.infoText}>
            Percakapan suara tidak disimpan ke riwayat chat
          </Text>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Feather name="alert-circle" size={20} color="#EF4444" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {messages.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="microphone-outline" size={48} color="#A9A9A9" />
            <Text style={styles.emptyStateTitle}>Belum Ada Percakapan</Text>
            <Text style={styles.emptyStateText}>
              Mulai berbicara dengan Temani untuk melihat transkrip percakapan di sini
            </Text>
          </View>
        ) : (
          <ScrollView
            ref={scrollViewRef}
            style={styles.messagesContainer}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={false}
          >
            {messages.map(renderMessage)}
          </ScrollView>
        )}

        {loading && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Menyimpan percakapan...</Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FFFFFF',
    zIndex: 1000,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#454459',
    marginLeft: 12,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  content: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F9FF',
    padding: 12,
    margin: 16,
    marginBottom: 8,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#BAE6FD',
  },
  infoText: {
    color: '#6B7280',
    fontSize: 13,
    marginLeft: 8,
    flex: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    padding: 12,
    margin: 16,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#454459',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 32,
  },
  messageContainer: {
    marginBottom: 16,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  aiMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '85%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 10,
  },
  userMessageBubble: {
    backgroundColor: '#FFF1D2',
    borderBottomRightRadius: 4,
  },
  aiMessageBubble: {
    backgroundColor: '#F5F5F5',
    borderBottomLeftRadius: 4,
  },
  tentativeMessage: {
    opacity: 0.7,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: '#5B3E31',
  },
  aiMessageText: {
    color: '#374151',
  },
  tentativeText: {
    fontStyle: 'italic',
  },
  tentativeIndicator: {
    marginTop: 4,
  },
  tentativeLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  timestamp: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    marginHorizontal: 4,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
  },
});
