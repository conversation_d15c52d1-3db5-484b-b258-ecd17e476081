/**
 * Debug component to display auth status for troubleshooting
 * Only shows in development mode
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

export const DebugAuthStatus: React.FC = () => {
  const { 
    user, 
    loading, 
    userStatusLoading, 
    onboardingCompleted, 
    selfReflectionCompleted 
  } = useUniversalAuth();

  // Only show in development
  if (__DEV__ === false) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🐛 Debug Auth Status</Text>
      <Text style={styles.item}>User: {user ? `✅ ${user.id.slice(0, 8)}...` : '❌ None'}</Text>
      <Text style={styles.item}>Loading: {loading ? '⏳ True' : '✅ False'}</Text>
      <Text style={styles.item}>User Status Loading: {userStatusLoading ? '⏳ True' : '✅ False'}</Text>
      <Text style={styles.item}>Onboarding: {onboardingCompleted ? '✅ Completed' : '❌ Not completed'}</Text>
      <Text style={styles.item}>Self-Reflection: {selfReflectionCompleted ? '✅ Completed' : '❌ Not completed'}</Text>
      <Text style={styles.timestamp}>Updated: {new Date().toLocaleTimeString()}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 8,
    zIndex: 1000,
    minWidth: 250,
  },
  title: {
    color: '#FFD572',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  item: {
    color: '#FFFFFF',
    fontSize: 10,
    marginBottom: 2,
    fontFamily: 'monospace',
  },
  timestamp: {
    color: '#A0A0A0',
    fontSize: 9,
    marginTop: 5,
    fontStyle: 'italic',
  },
});

export default DebugAuthStatus;
