import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export interface StandardHeaderProps {
  title: string;
  iconName: keyof typeof Ionicons.glyphMap;
  iconColor: string;
  leftElement?: React.ReactNode;
  rightElement?: React.ReactNode;
  backgroundColor?: string;
}

export default function StandardHeader({
  title,
  iconName,
  iconColor,
  leftElement,
  rightElement,
  backgroundColor = 'transparent',
}: StandardHeaderProps) {
  return (
    <View style={[styles.header, { backgroundColor }]}>
      {/* Left element or empty placeholder */}
      <View style={styles.sideElement}>
        {leftElement}
      </View>
      
      {/* Center content with icon and title */}
      <View style={styles.centerContent}>
        <Ionicons 
          name={iconName} 
          size={20} 
          color={iconColor} 
          style={styles.icon} 
        />
        <Text style={styles.title}>{title}</Text>
      </View>
      
      {/* Right element or empty placeholder */}
      <View style={styles.sideElement}>
        {rightElement}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sideElement: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#5B3E31',
  },
});
