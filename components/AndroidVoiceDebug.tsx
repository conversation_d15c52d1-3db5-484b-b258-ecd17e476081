import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import { androidPermissions } from '@/utils/androidPermissions';
import { runAndroidVoiceTests, runAndroidVoiceTestsWithAlert } from '@/scripts/testAndroidVoiceFixes';

interface DebugInfo {
  platform: string;
  hasPermission: boolean | null;
  lastError: string | null;
  audioSessionActive: boolean;
  testResults: any[];
}

export const AndroidVoiceDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    platform: Platform.OS,
    hasPermission: null,
    lastError: null,
    audioSessionActive: false,
    testResults: [],
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    checkInitialStatus();
  }, []);

  const checkInitialStatus = async () => {
    if (Platform.OS === 'android') {
      try {
        const hasPermission = await androidPermissions.checkMicrophonePermission();
        setDebugInfo(prev => ({ ...prev, hasPermission }));
      } catch (error: any) {
        setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      }
    }
  };

  const handleCheckPermission = async () => {
    setIsLoading(true);
    try {
      const hasPermission = await androidPermissions.checkMicrophonePermission();
      setDebugInfo(prev => ({ 
        ...prev, 
        hasPermission, 
        lastError: null 
      }));
      
      Alert.alert(
        'Permission Status',
        `Microphone permission: ${hasPermission ? 'Granted' : 'Not granted'}`,
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      Alert.alert('Error', error.message, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestPermission = async () => {
    setIsLoading(true);
    try {
      const granted = await androidPermissions.requestPermissionWithDialog();
      setDebugInfo(prev => ({ 
        ...prev, 
        hasPermission: granted, 
        lastError: null 
      }));
      
      Alert.alert(
        'Permission Request Result',
        `Permission ${granted ? 'granted' : 'denied'}`,
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      Alert.alert('Error', error.message, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInitializeAudio = async () => {
    setIsLoading(true);
    try {
      const initialized = await androidPermissions.initializeAudioSession();
      setDebugInfo(prev => ({ 
        ...prev, 
        audioSessionActive: initialized, 
        lastError: null 
      }));
      
      Alert.alert(
        'Audio Session',
        `Audio session ${initialized ? 'initialized successfully' : 'failed to initialize'}`,
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      Alert.alert('Error', error.message, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanupAudio = async () => {
    setIsLoading(true);
    try {
      await androidPermissions.cleanupAudioSession();
      setDebugInfo(prev => ({ 
        ...prev, 
        audioSessionActive: false, 
        lastError: null 
      }));
      
      Alert.alert('Audio Session', 'Audio session cleaned up', [{ text: 'OK' }]);
    } catch (error: any) {
      setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      Alert.alert('Error', error.message, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRunTests = async () => {
    setIsLoading(true);
    try {
      const results = await runAndroidVoiceTests();
      setDebugInfo(prev => ({ 
        ...prev, 
        testResults: results, 
        lastError: null 
      }));
      
      const passedCount = results.filter(r => r.passed).length;
      Alert.alert(
        'Test Results',
        `${passedCount}/${results.length} tests passed`,
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      Alert.alert('Error', error.message, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRunTestsWithAlert = async () => {
    setIsLoading(true);
    try {
      await runAndroidVoiceTestsWithAlert();
      setDebugInfo(prev => ({ ...prev, lastError: null }));
    } catch (error: any) {
      setDebugInfo(prev => ({ ...prev, lastError: error.message }));
      Alert.alert('Error', error.message, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  if (Platform.OS !== 'android') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Android Voice Debug</Text>
        <Text style={styles.warning}>
          This debug component is only available on Android platform.
          Current platform: {Platform.OS}
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Android Voice Debug</Text>
      
      {/* Status Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status Information</Text>
        <Text style={styles.info}>Platform: {debugInfo.platform}</Text>
        <Text style={styles.info}>
          Has Permission: {debugInfo.hasPermission === null ? 'Unknown' : debugInfo.hasPermission ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.info}>
          Audio Session Active: {debugInfo.audioSessionActive ? 'Yes' : 'No'}
        </Text>
        {debugInfo.lastError && (
          <Text style={styles.error}>Last Error: {debugInfo.lastError}</Text>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handleCheckPermission}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Check Permission</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handleRequestPermission}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Request Permission</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handleInitializeAudio}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Initialize Audio Session</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handleCleanupAudio}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Cleanup Audio Session</Text>
        </TouchableOpacity>
      </View>

      {/* Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Tests</Text>
        
        <TouchableOpacity 
          style={[styles.button, styles.testButton, isLoading && styles.buttonDisabled]} 
          onPress={handleRunTests}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Run All Tests</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.testButton, isLoading && styles.buttonDisabled]} 
          onPress={handleRunTestsWithAlert}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Run Tests with Alert</Text>
        </TouchableOpacity>
      </View>

      {/* Test Results */}
      {debugInfo.testResults.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          {debugInfo.testResults.map((result, index) => (
            <View key={index} style={styles.testResult}>
              <Text style={[styles.testName, result.passed ? styles.testPassed : styles.testFailed]}>
                {result.passed ? '✅' : '❌'} {result.testName}
              </Text>
              {result.error && (
                <Text style={styles.testError}>Error: {result.error}</Text>
              )}
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  warning: {
    fontSize: 16,
    color: '#ff6b6b',
    textAlign: 'center',
    marginTop: 50,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  info: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
  error: {
    fontSize: 14,
    color: '#ff6b6b',
    marginTop: 8,
  },
  button: {
    backgroundColor: '#2B7EFF',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  testButton: {
    backgroundColor: '#10B981',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  testResult: {
    marginBottom: 8,
    padding: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 4,
  },
  testName: {
    fontSize: 14,
    fontWeight: '600',
  },
  testPassed: {
    color: '#10B981',
  },
  testFailed: {
    color: '#EF4444',
  },
  testError: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
});
