import React from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { Feather } from '@expo/vector-icons';
import type { JournalEntryWithQuestion } from '@/types/journal';

interface JournalDateViewProps {
  selectedDate: string | null;
  entries: JournalEntryWithQuestion[];
  loading: boolean;
  error: string | null;
}

const JournalHistoryCard: React.FC<{ entry: JournalEntryWithQuestion }> = ({ entry }) => (
  <View style={styles.historyCard}>
    <Text style={styles.historyCardQuestion}>{entry.question.question_text}</Text>
    <Text style={styles.historyCardAnswer}>{entry.answer}</Text>
  </View>
);

const JournalDateView: React.FC<JournalDateViewProps> = ({
  selectedDate,
  entries,
  loading,
  error,
}) => {
  const formatDateIndonesian = (dateString: string): string => {
    const date = new Date(dateString);
    const days = ['<PERSON><PERSON>', 'Senin', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'];
    const months = [
      '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>',
      '<PERSON>i', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    const dayName = days[date.getDay()];
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${dayName}, ${day} ${month} ${year}`;
  };

  if (!selectedDate) {
    return (
      <View style={styles.emptyContainer}>
        <Feather name="calendar" size={48} color="#C5C5C5" />
        <Text style={styles.emptyTitle}>Pilih Tanggal</Text>
        <Text style={styles.emptySubtitle}>
          Pilih tanggal di kalender untuk melihat jurnal Anda
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#DD76B9" />
        <Text style={styles.loadingText}>Memuat jurnal...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Feather name="alert-circle" size={48} color="#EF4444" />
        <Text style={styles.errorTitle}>Terjadi Kesalahan</Text>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (entries.length === 0) {
    const isToday = selectedDate === new Date().toISOString().split('T')[0];
    
    return (
      <View style={styles.emptyContainer}>
        <Feather name="book-open" size={48} color="#C5C5C5" />
        <Text style={styles.emptyTitle}>
          {isToday ? 'Belum Ada Jurnal Hari Ini' : 'Tidak Ada Jurnal'}
        </Text>
        <Text style={styles.emptySubtitle}>
          {isToday 
            ? 'Mulai tulis jurnal Anda hari ini'
            : `Tidak ada jurnal untuk ${formatDateIndonesian(selectedDate)}`
          }
        </Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.scrollView} 
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      {/* Date Header */}
      <View style={styles.dateHeaderContainer}>
        <Text style={styles.dateHeader}>
          {formatDateIndonesian(selectedDate)}
        </Text>
        <View style={styles.entryCountContainer}>
          <Text style={styles.entryCountText}>
            {entries.length} {entries.length === 1 ? 'jurnal' : 'jurnal'}
          </Text>
        </View>
      </View>

      {/* Journal Entries */}
      {entries.map((entry) => (
        <JournalHistoryCard
          key={entry.id}
          entry={entry}
        />
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingTop: 8,
  },
  dateHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  dateHeader: {
    fontSize: 18,
    fontWeight: '700',
    color: '#5B3E31',
  },
  entryCountContainer: {
    backgroundColor: '#DD76B9',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  entryCountText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  historyCard: {
    backgroundColor: '#FFF',
    borderRadius: 24,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  historyCardQuestion: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0056A8',
    marginBottom: 8,
  },
  historyCardAnswer: {
    fontSize: 14,
    color: '#4B3425',
    lineHeight: 20,
  },
  // Loading states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  // Error states
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#EF4444',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  // Empty states
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default JournalDateView;
