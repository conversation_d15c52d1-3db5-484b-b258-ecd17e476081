import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { usePageThemeFromState } from '../hooks/usePageTheme';
import { NEUTRAL_COLORS } from '../constants/themes';

interface TabButtonProps {
  focused: boolean;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  primaryColor: string;
}

const TabButton = ({ focused, icon, onPress, primaryColor }: TabButtonProps) => {
  return (
    <TouchableOpacity
      style={styles.tabButton}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Ionicons
        name={icon as any}
        size={24}
        color={focused ? primaryColor : NEUTRAL_COLORS.inactive}
      />
    </TouchableOpacity>
  );
};

export default function TabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  // Get the current page theme based on the active route
  const currentTheme = usePageThemeFromState(state);

  // Create dynamic styles based on the current theme
  const dynamicStyles = StyleSheet.create({
    container: {
      ...styles.container,
      backgroundColor: currentTheme.background,
    },
  });

  // Filter out routes that have href explicitly set to null (hidden routes)
  const visibleRoutes = state.routes.filter(route => {
    const { options } = descriptors[route.key];
    // Only exclude routes where href is explicitly set to null
    return (options as any).href !== null;
  });

  return (
    <View style={dynamicStyles.container}>
      {visibleRoutes.map((route) => {
        // Find the original index in the full routes array for focus calculation
        const originalIndex = state.routes.findIndex(r => r.key === route.key);
        const focused = state.index === originalIndex;

        // Map route names to Ionicons based on the screenshot (5 icons)
        const getIconName = () => {
          switch (route.name) {
            case 'chat':
              return 'chatbubble-ellipses-outline';
            case 'appointment':
              return 'calendar-outline';
            case 'index':
              return focused ? 'home' : 'home-outline'; // Blue home icon when active
            case 'mood':
              return 'happy-outline';
            case 'journal':
              return 'document-text-outline';
            default:
              return 'apps-outline';
          }
        };

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!focused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        return (
          <TabButton
            key={route.key}
            focused={focused}
            icon={getIconName()}
            onPress={onPress}
            primaryColor={currentTheme.primary}
          />
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF', // This will be overridden by dynamic styles
    height: 50,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)', // Subtle border for definition
  },
  tabButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
});
