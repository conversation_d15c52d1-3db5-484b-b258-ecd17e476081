import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface AppointmentTimeSlotsProps {
  selectedDate: string | null;
  selectedTime: string | null;
  availableTimeSlots: string[];
  loading?: boolean;
  onTimeSelect: (time: string) => void;
}

const AppointmentTimeSlots: React.FC<AppointmentTimeSlotsProps> = ({
  selectedDate,
  selectedTime,
  availableTimeSlots,
  loading = false,
  onTimeSelect,
}) => {
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return date.toLocaleDateString('id-ID', options);
  };

  if (!selectedDate) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Ionicons name="calendar-outline" size={48} color="#B5A136" />
          <Text style={styles.emptyStateTitle}>Pilih <PERSON></Text>
          <Text style={styles.emptyStateSubtitle}>
            Silakan pilih tanggal terlebih dahulu untuk melihat waktu yang tersedia
          </Text>
        </View>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.sectionTitle}>Waktu Tersedia</Text>
          <Text style={styles.selectedDateText}>{formatDate(selectedDate)}</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#B5A136" />
          <Text style={styles.loadingText}>Memuat waktu tersedia...</Text>
        </View>
      </View>
    );
  }

  if (availableTimeSlots.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.sectionTitle}>Waktu Tersedia</Text>
          <Text style={styles.selectedDateText}>{formatDate(selectedDate)}</Text>
        </View>
        <View style={styles.emptyState}>
          <Ionicons name="time-outline" size={48} color="#DC2626" />
          <Text style={styles.emptyStateTitle}>Tidak Ada Waktu Tersedia</Text>
          <Text style={styles.emptyStateSubtitle}>
            Maaf, tidak ada waktu konsultasi yang tersedia pada tanggal ini. 
            Silakan pilih tanggal lain.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>Waktu Tersedia</Text>
        <Text style={styles.selectedDateText}>{formatDate(selectedDate)}</Text>
      </View>

      <View style={styles.timeSlotsGrid}>
        {availableTimeSlots.map((time) => (
          <TouchableOpacity
            key={time}
            style={[
              styles.timeSlot,
              selectedTime === time && styles.selectedTimeSlot
            ]}
            onPress={() => onTimeSelect(time)}
            activeOpacity={0.7}
          >
            <View style={styles.timeSlotContent}>
              <Ionicons 
                name="time-outline" 
                size={16} 
                color={selectedTime === time ? '#FFFFFF' : '#B5A136'} 
                style={styles.timeIcon}
              />
              <Text style={[
                styles.timeSlotText,
                selectedTime === time && styles.selectedTimeSlotText
              ]}>
                {time}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {selectedTime && (
        <View style={styles.selectedTimeInfo}>
          <View style={styles.selectedTimeCard}>
            <Ionicons name="checkmark-circle" size={20} color="#10B981" />
            <Text style={styles.selectedTimeInfoText}>
              Waktu dipilih: {selectedTime} pada {formatDate(selectedDate)}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#454459',
    marginBottom: 4,
  },
  selectedDateText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  timeSlotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  timeSlot: {
    minWidth: '30%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 10,
    backgroundColor: '#F6EBB1',
    borderWidth: 1,
    borderColor: '#F6EBB1',
  },
  selectedTimeSlot: {
    backgroundColor: '#B5A136',
    borderColor: '#B5A136',
  },
  timeSlotContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeIcon: {
    marginRight: 6,
  },
  timeSlotText: {
    fontSize: 16,
    color: '#454459',
    fontWeight: '500',
  },
  selectedTimeSlotText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  selectedTimeInfo: {
    marginTop: 16,
  },
  selectedTimeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    padding: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#BBF7D0',
  },
  selectedTimeInfoText: {
    fontSize: 14,
    color: '#059669',
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#454459',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 12,
  },
});

export default AppointmentTimeSlots;
