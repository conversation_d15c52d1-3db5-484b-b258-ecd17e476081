import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { isGoogleSigninEnabled, isSignupEnabled, AUTH_FEATURES } from '@/lib/authConfig';

export default function DebugEnvVars() {
  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔧 Debug: Environment Variables</Text>
      
      <View style={styles.row}>
        <Text style={styles.label}>Google Signin Enabled:</Text>
        <Text style={[styles.value, isGoogleSigninEnabled() ? styles.enabled : styles.disabled]}>
          {isGoogleSigninEnabled() ? 'TRUE' : 'FALSE'}
        </Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Signup Enabled:</Text>
        <Text style={[styles.value, isSignupEnabled() ? styles.enabled : styles.disabled]}>
          {isSignupEnabled() ? 'TRUE' : 'FALSE'}
        </Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Raw ENV Values:</Text>
      </View>
      
      <View style={styles.envRow}>
        <Text style={styles.envLabel}>EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED:</Text>
        <Text style={styles.envValue}>
          {process.env.EXPO_PUBLIC_GOOGLE_SIGNIN_ENABLED || 'undefined'}
        </Text>
      </View>
      
      <View style={styles.envRow}>
        <Text style={styles.envLabel}>EXPO_PUBLIC_SIGNUP_ENABLED:</Text>
        <Text style={styles.envValue}>
          {process.env.EXPO_PUBLIC_SIGNUP_ENABLED || 'undefined'}
        </Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>AUTH_FEATURES Object:</Text>
        <Text style={styles.envValue}>
          {JSON.stringify(AUTH_FEATURES, null, 2)}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  envRow: {
    marginBottom: 4,
    marginLeft: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  envLabel: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  value: {
    fontSize: 14,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  envValue: {
    fontSize: 12,
    color: '#333',
    fontFamily: 'monospace',
    backgroundColor: '#fff',
    padding: 4,
    borderRadius: 4,
    marginTop: 2,
  },
  enabled: {
    backgroundColor: '#d4edda',
    color: '#155724',
  },
  disabled: {
    backgroundColor: '#f8d7da',
    color: '#721c24',
  },
});
