import React, { useEffect } from 'react';
import { View, Text } from 'react-native';
import Animated, {
  useSharedValue,
  withTiming,
  withSequence,
  withRepeat,
  useAnimatedStyle,
  interpolate,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { chatStyles } from '@/styles/ChatStyles';

export const TypingIndicator: React.FC = () => {
  const dot1 = useSharedValue(0);
  const dot2 = useSharedValue(0);
  const dot3 = useSharedValue(0);

  useEffect(() => {
    const animateDot = (dot: Animated.SharedValue<number>, delay: number) => {
      dot.value = withRepeat(
        withSequence(
          withTiming(0, { duration: delay }),
          withTiming(1, { duration: 400 }),
          withTiming(0, { duration: 400 })
        ),
        -1,
        false
      );
    };

    animateDot(dot1, 0);
    animateDot(dot2, 200);
    animateDot(dot3, 400);

    // Cleanup function
    return () => {
      dot1.value = 0;
      dot2.value = 0;
      dot3.value = 0;
    };
  }, [dot1, dot2, dot3]);

  const dot1Style = useAnimatedStyle(() => ({
    opacity: dot1.value,
    transform: [
      {
        scale: interpolate(dot1.value, [0, 1], [1, 1.2]),
      },
    ],
  }));

  const dot2Style = useAnimatedStyle(() => ({
    opacity: dot2.value,
    transform: [
      {
        scale: interpolate(dot2.value, [0, 1], [1, 1.2]),
      },
    ],
  }));

  const dot3Style = useAnimatedStyle(() => ({
    opacity: dot3.value,
    transform: [
      {
        scale: interpolate(dot3.value, [0, 1], [1, 1.2]),
      },
    ],
  }));

  return (
    <View style={chatStyles.typingIndicator}>
      <Feather name="message-circle" size={16} color="#64748B" />
      <Text style={chatStyles.typingText}>Temani sedang mengetik</Text>
      <View style={chatStyles.typingDots}>
        <Animated.View style={[chatStyles.typingDot, dot1Style]} />
        <Animated.View style={[chatStyles.typingDot, dot2Style]} />
        <Animated.View style={[chatStyles.typingDot, dot3Style]} />
      </View>
    </View>
  );
};