import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { reportError, reportMessage, addBreadcrumb, startTransaction } from '@/lib/sentryConfig';

interface Props {
  style?: any;
}

export const SentryTestButton: React.FC<Props> = ({ style }) => {
  const testSentryIntegration = () => {
    Alert.alert(
      'Test Sentry Integration',
      'Choose a test to run:',
      [
        {
          text: 'Test Error',
          onPress: () => {
            addBreadcrumb('User triggered test error', 'test', { action: 'error_test' });
            const testError = new Error('This is a test error for Sentry integration');
            reportError(testError, {
              component: 'SentryTestButton',
              action: 'test_error',
              metadata: { testType: 'manual_error_test' }
            });
            Alert.alert('Success', 'Test error sent to Sentry!');
          }
        },
        {
          text: 'Test Message',
          onPress: () => {
            addBreadcrumb('User triggered test message', 'test', { action: 'message_test' });
            reportMessage('Test message from Temani app', 'info', {
              component: 'SentryTestButton',
              action: 'test_message',
              metadata: { testType: 'manual_message_test' }
            });
            Alert.alert('Success', 'Test message sent to Sentry!');
          }
        },
        {
          text: 'Test Transaction',
          onPress: () => {
            addBreadcrumb('User triggered test transaction', 'test', { action: 'transaction_test' });
            const transaction = startTransaction('test.manual_transaction', 'test');
            
            // Simulate some work
            setTimeout(() => {
              transaction.setStatus('ok');
              transaction.setTag('test_type', 'manual');
              transaction.setMeasurement('test_duration', 1000, 'millisecond');
              transaction.finish();
              Alert.alert('Success', 'Test transaction sent to Sentry!');
            }, 1000);
          }
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };

  // Only show in development
  if (!__DEV__) {
    return null;
  }

  return (
    <TouchableOpacity 
      style={[styles.button, style]} 
      onPress={testSentryIntegration}
    >
      <Text style={styles.buttonText}>Test Sentry</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginVertical: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});
