/**
 * Insufficient Credits Modal
 * Shown when user tries to start a voice call without sufficient credits
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useVoiceCredits } from '@/hooks/useVoiceCredits';
import { VOICE_CREDITS_CONFIG } from '@/types/voiceCredits';

interface InsufficientCreditsModalProps {
  visible: boolean;
  onClose: () => void;
  onNavigateToTextChat?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const InsufficientCreditsModal: React.FC<InsufficientCreditsModalProps> = ({
  visible,
  onClose,
  onNavigateToTextChat,
}) => {
  const { credits, formatCreditsDisplay } = useVoiceCredits();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.iconContainer}>
                <Ionicons name="call-outline" size={32} color="#FF6B6B" />
                <View style={styles.blockIcon}>
                  <Ionicons name="close-circle" size={20} color="#FF6B6B" />
                </View>
              </View>
              <Text style={styles.title}>Insufficient Credits</Text>
              <Text style={styles.subtitle}>
                You don&apos;t have enough credits to start a voice call
              </Text>
            </View>

            {/* Current Credits */}
            <View style={styles.creditsSection}>
              <Text style={styles.sectionTitle}>Current Balance</Text>
              <View style={styles.creditsCard}>
                <View style={styles.creditsRow}>
                  <Ionicons name="wallet-outline" size={20} color="#666" />
                  <Text style={styles.creditsText}>
                    {credits ? formatCreditsDisplay() : '0 credits'}
                  </Text>
                </View>
                {credits && credits.creditsUsed > 0 && (
                  <Text style={styles.usedCreditsText}>
                    {credits.creditsUsed} credits used
                  </Text>
                )}
              </View>
            </View>

            {/* How Credits Work */}
            <View style={styles.infoSection}>
              <Text style={styles.sectionTitle}>How Voice Credits Work</Text>
              <View style={styles.infoCard}>
                <View style={styles.infoRow}>
                  <Ionicons name="time-outline" size={16} color="#4CAF50" />
                  <Text style={styles.infoText}>
                    1 credit = {VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT} seconds of call time
                  </Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="person-add-outline" size={16} color="#4CAF50" />
                  <Text style={styles.infoText}>
                    New accounts get {VOICE_CREDITS_CONFIG.INITIAL_CREDITS} free credits
                  </Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="calculator-outline" size={16} color="#4CAF50" />
                  <Text style={styles.infoText}>
                    Credits are deducted after each call
                  </Text>
                </View>
              </View>
            </View>

            {/* Alternative Options */}
            <View style={styles.alternativesSection}>
              <Text style={styles.sectionTitle}>Alternative Options</Text>
              <View style={styles.alternativeCard}>
                <Ionicons name="chatbubble-outline" size={20} color="#A08CFB" />
                <View style={styles.alternativeContent}>
                  <Text style={styles.alternativeTitle}>Text Chat</Text>
                  <Text style={styles.alternativeDescription}>
                    Continue your conversation with unlimited text messaging
                  </Text>
                </View>
              </View>
              
              <View style={styles.alternativeCard}>
                <Ionicons name="journal-outline" size={20} color="#A08CFB" />
                <View style={styles.alternativeContent}>
                  <Text style={styles.alternativeTitle}>Journal Writing</Text>
                  <Text style={styles.alternativeDescription}>
                    Express your thoughts through guided journaling
                  </Text>
                </View>
              </View>
            </View>

            {/* Future Features Note */}
            <View style={styles.futureSection}>
              <View style={styles.futureCard}>
                <Ionicons name="gift-outline" size={20} color="#FFA500" />
                <Text style={styles.futureText}>
                  Credit recharge options coming soon! For now, enjoy unlimited text chat and journaling.
                </Text>
              </View>
            </View>
          </ScrollView>

          {/* Action Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.primaryButton, styles.singleButton]}
              onPress={() => {
                onNavigateToTextChat?.();
                onClose();
              }}
              activeOpacity={0.7}
            >
              <Text style={styles.primaryButtonText}>Continue with Text Chat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    width: Math.min(screenWidth - 40, 400),
    maxHeight: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  header: {
    alignItems: 'center',
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  blockIcon: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    backgroundColor: 'white',
    borderRadius: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D3748',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  creditsSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 12,
  },
  creditsCard: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  creditsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  creditsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
  },
  usedCreditsText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  infoSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  infoCard: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#4A5568',
    flex: 1,
  },
  alternativesSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  alternativeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    marginBottom: 12,
  },
  alternativeContent: {
    flex: 1,
  },
  alternativeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 4,
  },
  alternativeDescription: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  futureSection: {
    padding: 20,
  },
  futureCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    padding: 16,
    backgroundColor: '#FFFBF0',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FED7AA',
  },
  futureText: {
    fontSize: 12,
    color: '#D69E2E',
    lineHeight: 16,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#F1F3F4',
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#A08CFB',
    backgroundColor: 'white',
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#A08CFB',
  },
  primaryButton: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#A08CFB',
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  singleButton: {
    flex: 1,
    maxWidth: 200,
    alignSelf: 'center',
  },
});

export default InsufficientCreditsModal;
