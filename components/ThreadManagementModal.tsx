import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  TextInput,
  ScrollView,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { ConfirmationModal } from '@/components/ConfirmationModal';
import type { Thread } from '@/types/chat';

interface ThreadManagementModalProps {
  isVisible: boolean;
  threads: Thread[];
  currentThreadId?: string;
  onSelectThread: (thread: Thread) => void;
  onCreateNewThread: () => void;
  onDeleteThread: (threadId: string) => void;
  onRenameThread: (threadId: string, newTitle: string) => void;
  onClose: () => void;
}

export const ThreadManagementModal: React.FC<ThreadManagementModalProps> = ({
  isVisible,
  threads,
  currentThreadId,
  onSelectThread,
  onCreateNewThread,
  onDeleteThread,
  onRenameThread,
  onClose,
}) => {
  const [editingThreadId, setEditingThreadId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [threadToDelete, setThreadToDelete] = useState<string | null>(null);

  const handleStartRename = (thread: Thread) => {
    setEditingThreadId(thread.id);
    setEditingTitle(thread.title || 'Percakapan Baru');
  };

  const handleSaveRename = () => {
    if (editingThreadId && editingTitle.trim()) {
      onRenameThread(editingThreadId, editingTitle.trim());
      setEditingThreadId(null);
      setEditingTitle('');
    }
  };

  const handleCancelRename = () => {
    setEditingThreadId(null);
    setEditingTitle('');
  };

  const handleDeleteThread = (threadId: string) => {
    setThreadToDelete(threadId);
    setShowDeleteConfirmation(true);
  };

  const handleConfirmDelete = () => {
    if (threadToDelete) {
      onDeleteThread(threadToDelete);
      setShowDeleteConfirmation(false);
      setThreadToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false);
    setThreadToDelete(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString('id-ID', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: '2-digit',
      });
    }
  };

  const renderThreadItem = ({ item }: { item: Thread }) => {
    const isCurrentThread = item.id === currentThreadId;
    const isEditing = editingThreadId === item.id;

    return (
      <View style={[
        {
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: 12,
          marginVertical: 6,
          borderWidth: 1,
          borderColor: isCurrentThread ? '#4B3425' : 'rgba(255, 255, 255, 0.3)',
          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.05,
              shadowRadius: 8,
            },
            android: {
              elevation: 2,
            }
          }),
        },
        isCurrentThread && { backgroundColor: 'rgba(75, 52, 37, 0.1)' }
      ]}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 16,
            paddingVertical: 14,
          }}
          onPress={() => {
            if (!isEditing) {
              onSelectThread(item);
              onClose(); // Close modal when selecting a thread
            }
          }}
          disabled={isEditing}
        >
          <View style={{
            flex: 1,
          }}>
            {isEditing ? (
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
              }}>
                <TextInput
                  style={{
                    flex: 1,
                    fontSize: 16,
                    fontWeight: '600',
                    color: '#4B3425',
                    backgroundColor: 'rgba(255, 255, 255, 0.5)',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 8,
                    borderWidth: 1,
                    borderColor: '#4B3425',
                  }}
                  value={editingTitle}
                  onChangeText={setEditingTitle}
                  placeholder="Nama percakapan"
                  placeholderTextColor="#5B3E31"
                  autoFocus
                  maxLength={50}
                />
                <View style={{
                  flexDirection: 'row',
                  marginLeft: 12,
                }}>
                  <TouchableOpacity
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.5)',
                      borderRadius: 8,
                      width: 32,
                      height: 32,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginLeft: 4,
                    }}
                    onPress={handleSaveRename}
                  >
                    <Feather name="check" size={16} color="#4B3425" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.5)',
                      borderRadius: 8,
                      width: 32,
                      height: 32,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginLeft: 4,
                    }}
                    onPress={handleCancelRename}
                  >
                    <Feather name="x" size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <>
                <Text style={[
                  {
                    fontSize: 16,
                    fontWeight: '600',
                    color: '#4B3425',
                    marginBottom: 4,
                  },
                  isCurrentThread && { fontWeight: '700' }
                ]}>
                  {item.title || 'Percakapan Baru'}
                </Text>
                <Text style={{
                  fontSize: 13,
                  color: '#5B3E31',
                  fontWeight: '500',
                }}>
                  {formatDate(item.updated_at)}
                </Text>
              </>
            )}
          </View>

          {!isEditing && (
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <TouchableOpacity
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.5)',
                  borderRadius: 8,
                  width: 32,
                  height: 32,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: 8,
                }}
                onPress={() => handleStartRename(item)}
              >
                <Feather name="edit-2" size={16} color="#4B3425" />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.5)',
                  borderRadius: 8,
                  width: 32,
                  height: 32,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginLeft: 8,
                }}
                onPress={() => handleDeleteThread(item.id)}
              >
                <Feather name="trash-2" size={16} color="#EF4444" />
              </TouchableOpacity>
            </View>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  // Animation values
  const screenWidth = Dimensions.get('window').width;
  const slideAnim = useRef(new Animated.Value(screenWidth)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;
  
  // Animation for panel open/close
  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenWidth,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [isVisible, slideAnim, backdropOpacity, screenWidth]);

  // Return null when not visible to avoid unnecessary rendering
  if (!isVisible) return null;

  return (
    <>
      {/* Backdrop for dimming the background */}
      <Animated.View 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          opacity: backdropOpacity,
        }}
      >
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={{flex: 1}} />
        </TouchableWithoutFeedback>
      </Animated.View>

      {/* Sliding panel from right */}
      <Animated.View 
        style={[
          {
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            width: screenWidth * 0.85, // 85% of screen width
            backgroundColor: '#E5D787', // Gold background color
            zIndex: 1001,
            transform: [{ translateX: slideAnim }],
            paddingTop: 50, // Safe area for notch
            paddingBottom: 20,
            borderTopLeftRadius: 20,
            borderBottomLeftRadius: 20,
            ...Platform.select({
              ios: {
                shadowColor: '#000',
                shadowOffset: { width: -4, height: 0 },
                shadowOpacity: 0.2,
                shadowRadius: 8,
              },
              android: {
                elevation: 16,
              }
            })
          },
        ]}
      >
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingBottom: 16,
          borderBottomWidth: 1,
          borderBottomColor: 'rgba(0,0,0,0.1)',
        }}>
          <Text style={{
            fontSize: 20,
            fontWeight: '700',
            color: '#4B3425',
          }}>Riwayat Percakapan</Text>
          <TouchableOpacity
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={onClose}
          >
            <Feather name="x" size={24} color="#4B3425" />
          </TouchableOpacity>
        </View>

        {/* New conversation button */}
        <View style={{
          paddingHorizontal: 20,
          paddingVertical: 16,
        }}>
          <TouchableOpacity
            style={{
              backgroundColor: '#4B3425',
              borderRadius: 12,
              paddingVertical: 12,
              paddingHorizontal: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => {
              onCreateNewThread();
              onClose();
            }}
          >
            <Feather name="plus" size={20} color="#fff" />
            <Text style={{
              color: '#fff',
              fontSize: 16,
              fontWeight: '600',
              marginLeft: 8,
            }}>Percakapan Baru</Text>
          </TouchableOpacity>
        </View>

        {/* Thread list */}
        <ScrollView 
          style={{
            flex: 1,
            paddingHorizontal: 20,
          }}
          showsVerticalScrollIndicator={false}
        >
          {threads.length > 0 ? (
            <FlatList
              data={threads}
              renderItem={renderThreadItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />
          ) : (
            <View style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              paddingVertical: 60,
            }}>
              <Feather name="message-circle" size={48} color="rgba(75, 52, 37, 0.3)" />
              <Text style={{
                fontSize: 18,
                color: '#4B3425',
                fontWeight: '600',
                marginTop: 16,
                marginBottom: 8,
              }}>
                Belum ada percakapan
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#5B3E31',
                textAlign: 'center',
              }}>
                Mulai percakapan baru dengan Temani
              </Text>
            </View>
          )}
        </ScrollView>
      </Animated.View>

      {/* Confirmation Modal for Delete */}
      <ConfirmationModal
        isVisible={showDeleteConfirmation}
        title="Hapus Percakapan"
        message="Apakah Anda yakin ingin menghapus percakapan ini? Tindakan ini tidak dapat dibatalkan."
        confirmText="Hapus"
        cancelText="Batal"
        confirmButtonColor="#EF4444"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </>
  );
};