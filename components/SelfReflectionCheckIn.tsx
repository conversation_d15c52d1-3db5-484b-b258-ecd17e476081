/**
 * Main Self-Reflection Check-In Component
 * Orchestrates the entire self-reflection flow with dynamic questions
 */

import React, { useState, useEffect } from 'react';
import { View, Text, Pressable, ScrollView, ActivityIndicator, Alert, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { selfReflectionStyles, getResponsiveStyles } from '@/styles/SelfReflectionStyles';
import { useConvexSelfReflection } from '@/lib/convexSelfReflectionService';
import { AUTH_CONFIG } from '@/lib/env';

import { MultipleChoiceQuestion } from './selfReflection/MultipleChoiceQuestion';
import type {
  SelfReflectionCheckInProps,
  QuestionSet,
  SelfReflectionSession
} from '@/types/selfReflection';

type FlowState = 'loading' | 'introduction' | 'questions' | 'completed' | 'error';

export const SelfReflectionCheckIn: React.FC<SelfReflectionCheckInProps> = ({
  onComplete,
  onSkip,
  language = 'id',
}) => {
  const { user } = useUniversalAuth();

  // Use Convex or Supabase based on feature flag
  const convexSelfReflection = useConvexSelfReflection();
  const useConvex = AUTH_CONFIG.useConvexAuth;

  const [flowState, setFlowState] = useState<FlowState>('loading');
  const [questionSet, setQuestionSet] = useState<QuestionSet | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [session, setSession] = useState<SelfReflectionSession | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { containerStyle } = getResponsiveStyles();

  // Load question set when user is authenticated
  useEffect(() => {
    console.log('SelfReflectionCheckIn useEffect:', { user: !!user, userId: user?.id, language, useConvex });

    if (user) {
      console.log('User authenticated, loading question set...');
      if (useConvex) {
        loadConvexQuestionSet();
      } else {
        loadQuestionSet();
      }
    } else {
      console.log('No user, waiting for authentication...');
      setFlowState('loading');
      setError('Waiting for authentication...');
    }
  }, [user, language, useConvex]);

  // Convex data loading
  useEffect(() => {
    if (useConvex && convexSelfReflection.questionSet) {
      console.log('Convex question set loaded:', {
        questionsCount: convexSelfReflection.questionSet.questions?.length || 0,
        riskRulesCount: convexSelfReflection.questionSet.riskAssessmentRules?.length || 0
      });

      setQuestionSet(convexSelfReflection.questionSet);
      setFlowState('introduction');
      setError(null);
    } else if (useConvex && convexSelfReflection.isLoading) {
      setFlowState('loading');
      setError(null);
    }
  }, [useConvex, convexSelfReflection.questionSet, convexSelfReflection.isLoading]);

  const loadConvexQuestionSet = async () => {
    // Data loading is handled by the useConvexSelfReflection hook
    console.log('Using Convex for self-reflection data');
  };

  const loadQuestionSet = async () => {
    try {
      console.log('loadQuestionSet called (Supabase):', { user: !!user, userId: user?.id, language });

      if (!user) {
        console.log('No user in loadQuestionSet, setting error state');
        setError('Please wait for authentication...');
        setFlowState('loading');
        return;
      }

      console.log('Setting loading state and fetching questions...');
      setFlowState('loading');
      setError(null);

      const { QuestionConfigService } = await import('@/lib/questionConfigService');
      const loadedQuestionSet = await QuestionConfigService.getActiveQuestionSet(language);
      console.log('Question set loaded successfully:', {
        questionsCount: loadedQuestionSet.questions.length,
        riskRulesCount: loadedQuestionSet.riskAssessmentRules.length
      });

      setQuestionSet(loadedQuestionSet);
      setFlowState('introduction'); // Go to introduction first, not questions
    } catch (err) {
      console.error('Failed to load question set:', err);

      // Handle specific authentication errors
      if (err instanceof Error && err.message.includes('PGRST116')) {
        setError('Authentication required. Please try refreshing the page.');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to load questions');
      }
      setFlowState('error');
    }
  };

  const handleQuestionResponse = (questionKey: string, response: any) => {
    setResponses(prev => ({
      ...prev,
      [questionKey]: response
    }));
  };

  const handleNext = () => {
    if (!questionSet) return;

    const currentQuestion = questionSet.questions[currentQuestionIndex];
    const currentResponse = responses[currentQuestion.key || currentQuestion.questionKey];

    // Validate required questions
    if (currentQuestion.isRequired && (currentResponse === null || currentResponse === undefined)) {
      Alert.alert(
        'Pertanyaan Wajib',
        'Mohon jawab pertanyaan ini untuk melanjutkan, atau pilih "Skip" jika tersedia.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Move to next question or complete
    if (currentQuestionIndex < questionSet.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      completeCheckIn();
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const completeCheckIn = async () => {
    if (!questionSet || !user) return;

    try {
      setIsSubmitting(true);
      console.log('Completing self-reflection check-in...', { useConvex });

      let completedSession: SelfReflectionSession;

      if (useConvex) {
        // Use Convex service
        completedSession = await convexSelfReflection.saveSession(
          responses,
          language
        );
      } else {
        // Use Supabase service
        const { SelfReflectionService } = await import('@/lib/selfReflectionService');
        completedSession = await SelfReflectionService.saveSession(
          user.id,
          responses,
          language
        );
      }

      console.log('Session saved successfully:', completedSession.id, 'Risk level:', completedSession.riskLevel);
      setSession(completedSession);

      // 2. Update completion status immediately
      try {
        if (useConvex) {
          await convexSelfReflection.updateCompletionStatus({
            selfReflectionCompleted: true
          });
        } else {
          const { UserProfileService } = await import('@/lib/userProfileService');
          await UserProfileService.updateCompletionStatus(user.id, {
            selfReflectionCompleted: true
          });
        }
        console.log('Completion status updated successfully');
      } catch (statusError) {
        console.warn('Failed to update completion status:', statusError);
        // Continue anyway - session is saved
      }

      // 3. Go directly to simple completion screen (no safety protocol or results)
      setFlowState('completed');

    } catch (err) {
      console.error('Failed to complete check-in:', err);
      setError(err instanceof Error ? err.message : 'Failed to save responses');
      setFlowState('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCompletionContinue = () => {
    console.log('Completion continue clicked, calling onComplete with session:', session?.id);
    if (session) {
      onComplete(session);
    }
  };

  // Introduction flow handlers
  const startQuestions = () => {
    console.log('Starting questions from introduction');
    setFlowState('questions');
  };

  const handleSkipFromIntroduction = () => {
    console.log('🔄 Skipping self-reflection from introduction - NOT marking as completed');

    // DON'T update completion status when skipping
    // User should see self-reflection again when they log back in

    // Call the skip handler directly
    if (onSkip) {
      console.log('🔄 Calling onSkip handler...');
      onSkip();
    }
  };

  const handleSkip = () => {
    Alert.alert(
      'Skip Self-Reflection?',
      'Self-reflection check-in membantu Temani memahami kondisi kamu dengan lebih baik. Yakin ingin skip?',
      [
        {
          text: 'Tetap Lanjutkan',
          style: 'cancel',
        },
        {
          text: 'Skip',
          onPress: () => onSkip?.(),
        },
      ]
    );
  };

  const renderProgressBar = () => {
    if (!questionSet || flowState !== 'questions') return null;

    const progress = ((currentQuestionIndex + 1) / questionSet.questions.length) * 100;

    console.log('🟢 Progress Bar Debug:', {
      currentIndex: currentQuestionIndex,
      totalQuestions: questionSet.questions.length,
      progressPercentage: Math.round(progress),
      flowState,
      width: `${Math.round(progress)}%`
    });

    return (
      <View style={selfReflectionStyles.progressContainer}>
        <View style={selfReflectionStyles.progressBar}>
          <View
            style={[
              selfReflectionStyles.progressFill,
              { width: `${Math.round(progress)}%` }
            ]}
          />
        </View>
        <Text style={selfReflectionStyles.progressText}>
          {currentQuestionIndex + 1} dari {questionSet.questions.length}
        </Text>
      </View>
    );
  };

  const renderCurrentQuestion = () => {
    if (!questionSet) return null;

    const currentQuestion = questionSet.questions[currentQuestionIndex];
    const questionKey = currentQuestion.key || currentQuestion.questionKey;
    const currentResponse = responses[questionKey];

    switch (currentQuestion.type || currentQuestion.questionType) {
      case 'multiple_choice':
        return (
          <MultipleChoiceQuestion
            question={currentQuestion}
            selectedOption={currentResponse}
            onOptionSelect={(optionId) => handleQuestionResponse(questionKey, optionId)}
          />
        );
      
      // Add other question types here as needed
      default:
        return (
          <View style={selfReflectionStyles.errorContainer}>
            <Text style={selfReflectionStyles.errorText}>
              Unsupported question type: {currentQuestion.questionType}
            </Text>
          </View>
        );
    }
  };

  const renderNavigationButtons = () => {
    if (!questionSet || flowState !== 'questions') return null;

    const currentQuestion = questionSet.questions[currentQuestionIndex];
    const currentResponse = responses[currentQuestion.questionKey];
    const isLastQuestion = currentQuestionIndex === questionSet.questions.length - 1;
    const canProceed = !currentQuestion.isRequired || currentResponse !== null && currentResponse !== undefined;

    return (
      <View style={selfReflectionStyles.navigationContainer}>
        <Pressable
          style={[
            selfReflectionStyles.navButton,
            currentQuestionIndex === 0 
              ? selfReflectionStyles.navButtonDisabled 
              : selfReflectionStyles.navButtonSecondary
          ]}
          onPress={handlePrevious}
          disabled={currentQuestionIndex === 0}
        >
          <Text style={[
            selfReflectionStyles.navButtonText,
            currentQuestionIndex === 0 
              ? selfReflectionStyles.navButtonTextDisabled 
              : selfReflectionStyles.navButtonTextSecondary
          ]}>
            Sebelumnya
          </Text>
        </Pressable>

        <Pressable
          style={[
            selfReflectionStyles.navButton,
            canProceed 
              ? selfReflectionStyles.navButtonPrimary 
              : selfReflectionStyles.navButtonDisabled
          ]}
          onPress={handleNext}
          disabled={!canProceed || isSubmitting}
        >
          <Text style={[
            selfReflectionStyles.navButtonText,
            canProceed 
              ? selfReflectionStyles.navButtonTextPrimary 
              : selfReflectionStyles.navButtonTextDisabled
          ]}>
            {isSubmitting ? 'Menyimpan...' : isLastQuestion ? 'Selesai' : 'Selanjutnya'}
          </Text>
        </Pressable>
      </View>
    );
  };

  // Render different states
  console.log('SelfReflectionCheckIn render:', {
    flowState,
    user: !!user,
    userId: user?.id,
    questionSetLoaded: !!questionSet,
    error
  });

  if (flowState === 'loading') {
    const loadingText = !user
      ? 'Menunggu autentikasi...'
      : 'Memuat pertanyaan...';

    console.log('Rendering loading state:', { loadingText, user: !!user });

    return (
      <SafeAreaView style={selfReflectionStyles.safeArea}>
        <View style={selfReflectionStyles.loadingContainer}>
          <ActivityIndicator size="large" color="#A08CFB" />
          <Text style={selfReflectionStyles.loadingText}>
            {loadingText}
          </Text>
          {!user && (
            <Text style={[selfReflectionStyles.loadingText, { fontSize: 14, marginTop: 8, fontStyle: 'italic' }]}>
              Pastikan kamu sudah login
            </Text>
          )}
        </View>
      </SafeAreaView>
    );
  }

  if (flowState === 'introduction' && questionSet) {
    console.log('Rendering introduction screen');
    return (
      <SafeAreaView style={selfReflectionStyles.safeArea}>
        {/* Background image - same as onboarding */}
        <View style={selfReflectionStyles.backgroundContainer}>
          <Image
            source={require('../assets/images/background-decoration.png')}
            style={selfReflectionStyles.backgroundImage}
            resizeMode="cover"
          />
        </View>

        <View style={selfReflectionStyles.container}>
          {/* Header */}
          <View style={selfReflectionStyles.headerContainer}>
            <Text style={selfReflectionStyles.headerTitle}>
              Self-Reflection
            </Text>
          </View>

          {/* Introduction Content */}
          <View style={selfReflectionStyles.introContainer}>
            {/* Hero Section */}
            <View style={selfReflectionStyles.introHeroSection}>
              <Text style={selfReflectionStyles.introEmoji}>🤗</Text>
            </View>

            {/* Message */}
            <View style={selfReflectionStyles.introTextContainer}>
              <Text style={selfReflectionStyles.introMessage}>
                Hai, sebelum kita ngobrol, aku bisa bantu kamu cek keadaan emosimu
                lewat beberapa pertanyaan ringan.
              </Text>
            </View>

            {/* Buttons */}
            <View style={selfReflectionStyles.introButtonsContainer}>
              <Pressable
                style={[selfReflectionStyles.introButton, selfReflectionStyles.introPrimaryButton]}
                onPress={startQuestions}
              >
                <Text style={selfReflectionStyles.introPrimaryButtonText}>Boleh</Text>
              </Pressable>

              <Pressable
                style={[selfReflectionStyles.introButton, selfReflectionStyles.introSecondaryButton]}
                onPress={handleSkipFromIntroduction}
              >
                <Text style={selfReflectionStyles.introSecondaryButtonText}>Skip Dulu Aja</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  if (flowState === 'error') {
    return (
      <SafeAreaView style={selfReflectionStyles.safeArea}>
        <View style={selfReflectionStyles.container}>
          <View style={selfReflectionStyles.errorContainer}>
            <Text style={selfReflectionStyles.errorText}>
              {error || 'Terjadi kesalahan saat memuat pertanyaan'}
            </Text>

            {/* Show different actions based on error type */}
            {error?.includes('authentication') || error?.includes('Authentication') ? (
              <View style={{ marginTop: 16, gap: 12 }}>
                <Text style={[selfReflectionStyles.errorText, { fontSize: 14, fontStyle: 'italic' }]}>
                  Pastikan kamu sudah login dengan benar
                </Text>
                <Pressable
                  style={[selfReflectionStyles.navButton, selfReflectionStyles.navButtonPrimary]}
                  onPress={loadQuestionSet}
                >
                  <Text style={[selfReflectionStyles.navButtonText, selfReflectionStyles.navButtonTextPrimary]}>
                    Coba Lagi
                  </Text>
                </Pressable>
              </View>
            ) : (
              <Pressable
                style={[selfReflectionStyles.navButton, selfReflectionStyles.navButtonPrimary, { marginTop: 16 }]}
                onPress={loadQuestionSet}
              >
                <Text style={[selfReflectionStyles.navButtonText, selfReflectionStyles.navButtonTextPrimary]}>
                  Coba Lagi
                </Text>
              </Pressable>
            )}
          </View>
        </View>
      </SafeAreaView>
    );
  }

  if (flowState === 'completed' && session) {
    console.log('Rendering completion screen for session:', session.id);
    return (
      <SafeAreaView style={selfReflectionStyles.safeArea}>
        {/* Background image - same as onboarding */}
        <View style={selfReflectionStyles.backgroundContainer}>
          <Image
            source={require('../assets/images/background-decoration.png')}
            style={selfReflectionStyles.backgroundImage}
            resizeMode="cover"
          />
        </View>

        <View style={selfReflectionStyles.container}>
          <View style={selfReflectionStyles.scrollContainer}>
            {/* Appreciation Message */}
            <View style={[selfReflectionStyles.questionCard, { alignItems: 'center', paddingVertical: 40 }]}>
              <Text style={{ fontSize: 48, marginBottom: 20 }}>🙏</Text>

              <Text style={[selfReflectionStyles.questionText, {
                textAlign: 'center',
                fontSize: 24,
                fontWeight: '600',
                marginBottom: 16,
                color: '#A08CFB'
              }]}>
                Makasih udah berbagi!
              </Text>

              <Text style={[selfReflectionStyles.questionDescription, {
                textAlign: 'center',
                fontSize: 16,
                lineHeight: 24,
                color: '#6B7280',
                marginBottom: 32
              }]}>
                Jawaban kamu membantu Temani memahami kondisi kamu dengan lebih baik.
                Sekarang kita bisa ngobrol dengan lebih personal! 💜
              </Text>

              {/* Continue Button */}
              <Pressable
                style={[selfReflectionStyles.navButton, selfReflectionStyles.navButtonPrimary, {
                  paddingHorizontal: 32,
                  paddingVertical: 16,
                  minWidth: 200
                }]}
                onPress={handleCompletionContinue}
              >
                <Text style={[selfReflectionStyles.navButtonText, selfReflectionStyles.navButtonTextPrimary, {
                  fontSize: 16,
                  fontWeight: '600'
                }]}>
                  Lanjut ke Beranda
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // Main questions flow
  return (
    <SafeAreaView style={selfReflectionStyles.safeArea}>
      {/* Background image - same as onboarding */}
      <View style={selfReflectionStyles.backgroundContainer}>
        <Image
          source={require('../assets/images/background-decoration.png')}
          style={selfReflectionStyles.backgroundImage}
          resizeMode="cover"
        />
      </View>

      <View style={[selfReflectionStyles.container, containerStyle]}>
        {/* Header */}
        <View style={selfReflectionStyles.headerContainer}>
          <Text style={selfReflectionStyles.headerTitle}>
            Self-Reflection
          </Text>
        </View>

        {/* Progress Bar */}
        {renderProgressBar()}

        {/* Question Content */}
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={selfReflectionStyles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {renderCurrentQuestion()}
        </ScrollView>

        {/* Navigation */}
        {renderNavigationButtons()}
      </View>
    </SafeAreaView>
  );
};

export default SelfReflectionCheckIn;
