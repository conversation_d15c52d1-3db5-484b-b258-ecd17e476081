import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

export default function AuthFlowTester() {
  const {
    user,
    loading,
    userStatusLoading,
    onboardingCompleted,
    selfReflectionCompleted,
    signInWithEmail,
    signOut,
    completeOnboarding,
    error,
    clearError
  } = useUniversalAuth();

  // Only show in development mode
  if (__DEV__ === false) {
    return null;
  }

  const testFailedLogin = async () => {
    clearError();
    try {
      await signInWithEmail('<EMAIL>', 'wrongpassword');
    } catch (error) {
      console.log('Expected failed login error:', error);
    }
  };

  const clearLocalStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.clear();
      Alert.alert('Local Storage Cleared', 'Please refresh the page to test first-time user flow');
    }
  };

  const testCompleteOnboarding = async () => {
    await completeOnboarding();
    Alert.alert('Onboarding Completed', 'Onboarding status has been saved');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Auth Flow Tester</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>User: {user ? user.email : 'Not authenticated'}</Text>
        <Text style={styles.statusText}>Loading: {loading ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>User Status Loading: {userStatusLoading ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>Onboarding Completed: {onboardingCompleted ? 'Yes' : 'No'}</Text>
        <Text style={styles.statusText}>Self Reflection Completed: {selfReflectionCompleted ? 'Yes' : 'No'}</Text>
        {error && <Text style={styles.errorText}>Error: {error}</Text>}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={clearLocalStorage}>
          <Text style={styles.buttonText}>Clear Local Storage</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testCompleteOnboarding}>
          <Text style={styles.buttonText}>Complete Onboarding</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testFailedLogin}>
          <Text style={styles.buttonText}>Test Failed Login</Text>
        </TouchableOpacity>

        {user && (
          <TouchableOpacity style={[styles.button, styles.signOutButton]} onPress={signOut}>
            <Text style={styles.buttonText}>Sign Out</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    margin: 20,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statusContainer: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#333',
  },
  errorText: {
    fontSize: 14,
    color: '#ff0000',
    marginTop: 10,
  },
  buttonContainer: {
    gap: 10,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  signOutButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
