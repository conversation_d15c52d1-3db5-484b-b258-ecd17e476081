/**
 * Voice Credits Verification Component
 * Tests the voice credits system against the existing database implementation
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { useVoiceCredits } from '@/hooks/useVoiceCredits';
import { voiceCreditsService } from '@/lib/voiceCreditsService';
import { VOICE_CREDITS_CONFIG } from '@/types/voiceCredits';

interface VerificationResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'PENDING';
  message: string;
  details?: any;
}

export const VoiceCreditsVerification: React.FC = () => {
  const { user } = useUniversalAuth();
  const voiceCredits = useVoiceCredits();
  const [results, setResults] = useState<VerificationResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (test: string, status: 'PASS' | 'FAIL' | 'PENDING', message: string, details?: any) => {
    setResults(prev => [...prev, { test, status, message, details }]);
  };

  const runVerification = async () => {
    if (!user) {
      Alert.alert('Error', 'Please log in to run verification tests');
      return;
    }

    setIsRunning(true);
    setResults([]);

    try {
      // Test 1: Check if user has credits
      addResult('User Credits', 'PENDING', 'Checking user credits...');
      
      try {
        const userCredits = await voiceCreditsService.getUserCredits(user.id);
        if (typeof userCredits.creditsRemaining === 'number') {
          addResult('User Credits', 'PASS', `User has ${userCredits.creditsRemaining} credits`, userCredits);
        } else {
          addResult('User Credits', 'FAIL', 'Invalid credits data structure', userCredits);
        }
      } catch (error) {
        addResult('User Credits', 'FAIL', 'Error getting user credits', error);
      }

      // Test 2: Check credit calculation
      addResult('Credit Calculation', 'PENDING', 'Testing credit calculation...');
      
      const testDurations = [5, 10, 15, 25, 60];
      let calculationPassed = true;
      
      for (const duration of testDurations) {
        const expectedCredits = Math.max(1, Math.ceil(duration / VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT));
        // We can't directly test the calculation function, but we can verify the constants
        if (VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT !== 10) {
          calculationPassed = false;
          break;
        }
      }
      
      if (calculationPassed) {
        addResult('Credit Calculation', 'PASS', 'Credit calculation constants are correct');
      } else {
        addResult('Credit Calculation', 'FAIL', 'Credit calculation constants are incorrect');
      }

      // Test 3: Check credits checking functionality
      addResult('Credit Checking', 'PENDING', 'Testing credit checking...');
      
      try {
        const creditCheck = await voiceCreditsService.checkCredits(user.id);
        if (typeof creditCheck.hasCredits === 'boolean' && typeof creditCheck.creditsRemaining === 'number') {
          addResult('Credit Checking', 'PASS', `Credit check works: ${creditCheck.hasCredits ? 'Has credits' : 'No credits'}`, creditCheck);
        } else {
          addResult('Credit Checking', 'FAIL', 'Invalid credit check response', creditCheck);
        }
      } catch (error) {
        addResult('Credit Checking', 'FAIL', 'Error checking credits', error);
      }

      // Test 4: Test hook functionality
      addResult('React Hook', 'PENDING', 'Testing useVoiceCredits hook...');
      
      if (voiceCredits.credits && typeof voiceCredits.canStartCall === 'boolean') {
        addResult('React Hook', 'PASS', 'useVoiceCredits hook working correctly', {
          hasCredits: voiceCredits.credits,
          canStartCall: voiceCredits.canStartCall,
          isSessionActive: voiceCredits.isSessionActive
        });
      } else {
        addResult('React Hook', 'FAIL', 'useVoiceCredits hook not working properly', {
          credits: voiceCredits.credits,
          canStartCall: voiceCredits.canStartCall
        });
      }

      // Test 5: Test constants
      addResult('Configuration', 'PENDING', 'Checking configuration constants...');
      
      const configTests = [
        { name: 'INITIAL_CREDITS', value: VOICE_CREDITS_CONFIG.INITIAL_CREDITS, expected: 10 },
        { name: 'SECONDS_PER_CREDIT', value: VOICE_CREDITS_CONFIG.SECONDS_PER_CREDIT, expected: 10 },
        { name: 'MINIMUM_CREDIT_DEDUCTION', value: VOICE_CREDITS_CONFIG.MINIMUM_CREDIT_DEDUCTION, expected: 1 },
      ];
      
      const configPassed = configTests.every(test => test.value === test.expected);
      
      if (configPassed) {
        addResult('Configuration', 'PASS', 'All configuration constants are correct', configTests);
      } else {
        addResult('Configuration', 'FAIL', 'Some configuration constants are incorrect', configTests);
      }

    } catch (error) {
      addResult('General Error', 'FAIL', 'Unexpected error during verification', error);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASS': return '#4CAF50';
      case 'FAIL': return '#F44336';
      case 'PENDING': return '#FF9800';
      default: return '#666';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PASS': return '✅';
      case 'FAIL': return '❌';
      case 'PENDING': return '⏳';
      default: return '❓';
    }
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Please log in to run voice credits verification</Text>
      </View>
    );
  }

  const passedTests = results.filter(r => r.status === 'PASS').length;
  const failedTests = results.filter(r => r.status === 'FAIL').length;
  const pendingTests = results.filter(r => r.status === 'PENDING').length;

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Voice Credits Verification</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>System Status</Text>
        <Text style={styles.statusText}>User: {user.email}</Text>
        <Text style={styles.statusText}>
          Current Credits: {voiceCredits.credits?.creditsRemaining ?? 'Loading...'}
        </Text>
        <Text style={styles.statusText}>
          Can Start Call: {voiceCredits.canStartCall ? 'Yes' : 'No'}
        </Text>
      </View>

      <View style={styles.section}>
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, isRunning && styles.buttonDisabled]} 
            onPress={runVerification}
            disabled={isRunning}
          >
            <Text style={styles.buttonText}>
              {isRunning ? 'Running Tests...' : 'Run Verification'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.clearButton} onPress={clearResults}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
      </View>

      {results.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryText, { color: '#4CAF50' }]}>
              Passed: {passedTests}
            </Text>
            <Text style={[styles.summaryText, { color: '#F44336' }]}>
              Failed: {failedTests}
            </Text>
            <Text style={[styles.summaryText, { color: '#FF9800' }]}>
              Pending: {pendingTests}
            </Text>
          </View>
          
          {results.map((result, index) => (
            <View key={index} style={styles.resultItem}>
              <View style={styles.resultHeader}>
                <Text style={styles.resultIcon}>{getStatusIcon(result.status)}</Text>
                <Text style={styles.resultTest}>{result.test}</Text>
                <Text style={[styles.resultStatus, { color: getStatusColor(result.status) }]}>
                  {result.status}
                </Text>
              </View>
              <Text style={styles.resultMessage}>{result.message}</Text>
              {result.details && (
                <Text style={styles.resultDetails}>
                  {JSON.stringify(result.details, null, 2)}
                </Text>
              )}
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F8F9FA',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2D3748',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#4A5568',
    marginBottom: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    backgroundColor: '#A08CFB',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#CBD5E0',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  clearButton: {
    backgroundColor: '#E53E3E',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  clearButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 8,
    backgroundColor: '#F7FAFC',
    borderRadius: 4,
  },
  summaryText: {
    fontSize: 14,
    fontWeight: '600',
  },
  resultItem: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#F7FAFC',
    borderRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#E2E8F0',
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  resultIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  resultTest: {
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
  },
  resultStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  resultMessage: {
    fontSize: 12,
    color: '#4A5568',
    marginBottom: 4,
  },
  resultDetails: {
    fontSize: 10,
    color: '#718096',
    fontFamily: 'monospace',
    backgroundColor: '#EDF2F7',
    padding: 8,
    borderRadius: 4,
  },
  errorText: {
    fontSize: 16,
    color: '#E53E3E',
    textAlign: 'center',
    marginTop: 50,
  },
});

export default VoiceCreditsVerification;
