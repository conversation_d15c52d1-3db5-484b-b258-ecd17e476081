import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface ComingSoonOverlayProps {
  title?: string;
  message?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  visible?: boolean;
  style?: any;
}

export const ComingSoonOverlay: React.FC<ComingSoonOverlayProps> = ({
  title = "Segera Hadir!",
  message = "Kami sedang bekerja keras untuk menghadirkan fitur keren ini untukmu. Tunggu ya!",
  icon = "construct-outline",
  visible = true,
  style
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim]);

  if (!visible) return null;

  return (
    <Animated.View 
      style={[
        styles.overlay,
        { opacity: fadeAnim },
        style
      ]}
    >
      <View style={styles.contentContainer}>
        {/* Decorative gradient bar */}
        <LinearGradient
          colors={['#FFD572', '#FEBD38']}
          style={styles.decorativeBar}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        />
        
        {/* Icon */}
        <View style={styles.iconContainer}>
          <Ionicons 
            name={icon} 
            size={48} 
            color="#A08CFB" 
          />
        </View>
        
        {/* Title */}
        <Text style={styles.title}>{title}</Text>
        
        {/* Message */}
        <Text style={styles.message}>{message}</Text>
        
        {/* Fun emoji */}
        <Text style={styles.emoji}>🚀</Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
    paddingHorizontal: 20,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 32,
    alignItems: 'center',
    maxWidth: 320,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  decorativeBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  iconContainer: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#F8F9FF',
    borderRadius: 50,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#454459',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emoji: {
    fontSize: 24,
    marginTop: 8,
  },
});
