import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SleepQualityLevel, SLEEP_QUALITY_OPTIONS } from '../../types/mood';

interface TimelineSleepTrackerProps {
  selectedQuality: SleepQualityLevel | null;
  onQualitySelect: (quality: SleepQualityLevel) => void;
}

export const TimelineSleepTracker: React.FC<TimelineSleepTrackerProps> = ({
  selectedQuality,
  onQualitySelect,
}) => {
  const renderTimelineItem = (option: typeof SLEEP_QUALITY_OPTIONS[0], index: number) => {
    const isSelected = selectedQuality === option.id;
    const isLast = index === SLEEP_QUALITY_OPTIONS.length - 1;

    return (
      <View key={option.id} style={styles.timelineItem}>
        <View style={styles.leftSection}>
          <TouchableOpacity
            style={styles.textContainer}
            onPress={() => onQualitySelect(option.id)}
          >
            <Text style={[
              styles.optionLabel,
              isSelected && styles.selectedLabel
            ]}>
              {option.label}
            </Text>
            <Text style={[
              styles.optionDuration,
              isSelected && styles.selectedDuration
            ]}>
              🕐 {option.duration}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.timelineSection}>
          <View style={[
            styles.timelineNode,
            isSelected && { backgroundColor: option.color }
          ]}>
            {isSelected && (
              <View style={styles.selectedIndicator}>
                <Text style={styles.selectedIcon}>↻</Text>
              </View>
            )}
          </View>
          {!isLast && <View style={styles.timelineLine} />}
        </View>

        <View style={styles.rightSection}>
          <TouchableOpacity
            style={[
              styles.emojiContainer,
              { backgroundColor: option.color }
            ]}
            onPress={() => onQualitySelect(option.id)}
          >
            <Text style={styles.emoji}>{option.emoji}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {SLEEP_QUALITY_OPTIONS.map((option, index) => renderTimelineItem(option, index))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
    height: 60,
  },
  leftSection: {
    flex: 1,
    paddingRight: 16,
  },
  textContainer: {
    alignItems: 'flex-start',
  },
  optionLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#BDBDBD',
    marginBottom: 4,
  },
  selectedLabel: {
    color: '#5D4037',
  },
  optionDuration: {
    fontSize: 14,
    color: '#BDBDBD',
  },
  selectedDuration: {
    color: '#757575',
  },
  timelineSection: {
    alignItems: 'center',
    width: 24,
    marginHorizontal: 16,
  },
  timelineNode: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  selectedIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'inherit',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  selectedIcon: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  timelineLine: {
    width: 4,
    flex: 1,
    backgroundColor: '#E0E0E0',
    position: 'absolute',
    top: 20,
    bottom: -32,
  },
  rightSection: {
    width: 60,
    alignItems: 'center',
  },
  emojiContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emoji: {
    fontSize: 24,
  },
});
