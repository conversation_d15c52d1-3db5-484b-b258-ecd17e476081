import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { MoodAnalytics } from './MoodAnalytics';
import { MoodTrendChart } from './MoodTrendChart';
import { MoodInsights } from './MoodInsights';
import { useMoodTrends } from '../../hooks/useMoodQuery';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';
import { ComingSoonOverlay } from '../ComingSoonOverlay';

type AnalyticsTab = 'overview' | 'trends' | 'insights';
type TimePeriod = 'week' | 'month';

export const MoodAnalyticsView: React.FC = () => {
  const { user } = useUniversalAuth();
  const [activeTab, setActiveTab] = useState<AnalyticsTab>('overview');
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('week');
  const [periodCount, setPeriodCount] = useState(4);

  // Fetch mood trends data
  const { data: trends, isLoading, error } = useMoodTrends(timePeriod, periodCount);

  // Calculate date range for analytics
  const getDateRange = () => {
    const endDate = new Date();
    const startDate = new Date();
    
    if (timePeriod === 'week') {
      startDate.setDate(endDate.getDate() - (periodCount * 7));
    } else {
      startDate.setMonth(endDate.getMonth() - periodCount);
    }

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    };
  };

  const { startDate, endDate } = getDateRange();

  const renderTabButton = (tab: AnalyticsTab, label: string, icon: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tab && styles.tabButtonActive
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <Ionicons
        name={icon as any}
        size={20}
        color={activeTab === tab ? '#FFFFFF' : '#6B7280'}
      />
      <Text style={[
        styles.tabButtonText,
        activeTab === tab && styles.tabButtonTextActive
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderPeriodButton = (period: TimePeriod, label: string) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        timePeriod === period && styles.periodButtonActive
      ]}
      onPress={() => setTimePeriod(period)}
    >
      <Text style={[
        styles.periodButtonText,
        timePeriod === period && styles.periodButtonTextActive
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderContent = () => {
    if (!user?.id) {
      return (
        <View style={styles.authContainer}>
          <Ionicons name="person-circle-outline" size={64} color="#6B7280" />
          <Text style={styles.authTitle}>Login Required</Text>
          <Text style={styles.authMessage}>
            Please log in to view your mood analytics
          </Text>
        </View>
      );
    }

    switch (activeTab) {
      case 'overview':
        return (
          <MoodAnalytics
            startDate={startDate}
            endDate={endDate}
            period={timePeriod}
            count={periodCount}
          />
        );
      
      case 'trends':
        return (
          <ScrollView style={styles.trendsContainer} showsVerticalScrollIndicator={false}>
            <View style={styles.trendsContent}>
              <Text style={styles.sectionTitle}>Mood Trends</Text>
              {trends && trends.length > 0 ? (
                <>
                  <MoodTrendChart
                    trends={trends}
                    metric="mood"
                    title="Mood Level Over Time"
                  />
                  <MoodTrendChart
                    trends={trends}
                    metric="sleep"
                    title="Sleep Hours Over Time"
                  />
                  <MoodTrendChart
                    trends={trends}
                    metric="stress"
                    title="Stress Level Over Time"
                  />
                </>
              ) : (
                <View style={styles.noDataContainer}>
                  <Ionicons name="bar-chart-outline" size={48} color="#6B7280" />
                  <Text style={styles.noDataText}>
                    No trend data available yet. Keep tracking your mood to see patterns!
                  </Text>
                </View>
              )}
            </View>
          </ScrollView>
        );
      
      case 'insights':
        return (
          <MoodInsights
            trends={trends || []}
            period={timePeriod}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {renderTabButton('overview', 'Overview', 'analytics')}
        {renderTabButton('trends', 'Trends', 'trending-up')}
        {renderTabButton('insights', 'Insights', 'bulb')}
      </View>

      {/* Time Period Selector */}
      {user?.id && (
        <View style={styles.periodContainer}>
          <Text style={styles.periodLabel}>Time Period:</Text>
          <View style={styles.periodButtons}>
            {renderPeriodButton('week', 'Weekly')}
            {renderPeriodButton('month', 'Monthly')}
          </View>
        </View>
      )}

      {/* Content */}
      <View style={styles.contentContainer}>
        {renderContent()}

        {/* Coming Soon Overlay */}
        <ComingSoonOverlay
          title="Analitik Segera Hadir! 📊"
          message="Halo! Kami lagi nyiapin insight mood yang keren banget buat kamu. Bakal ada grafik, tren, dan tips personal - pasti seru deh!"
          icon="analytics-outline"
          visible={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
  },
  tabButtonActive: {
    backgroundColor: '#4EAF64',
  },
  tabButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  tabButtonTextActive: {
    color: '#FFFFFF',
  },
  periodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  periodLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginRight: 12,
  },
  periodButtons: {
    flexDirection: 'row',
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
  },
  periodButtonActive: {
    backgroundColor: '#4EAF64',
  },
  periodButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  contentContainer: {
    flex: 1,
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  authTitle: {
    marginTop: 16,
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
  },
  authMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  trendsContainer: {
    flex: 1,
  },
  trendsContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 16,
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  noDataText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
