import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import {
  DayOfWeek,
  DailyFeelingLevel,
  WeeklyMoodData,
  DAYS_OF_WEEK,
  DAILY_FEELING_COLORS,
  DAILY_FEELING_BACKGROUND,
  DAILY_FEELING_TEXTS,
  DAILY_FEELING_LEVELS
} from '../../types/mood';

interface StressLevelTrackerProps {
  weeklyData: WeeklyMoodData;
  onDailyFeelingSelect: (day: DayOfWeek, feeling: DailyFeelingLevel) => void;
  readOnly?: boolean;
}

export const StressLevelTracker: React.FC<StressLevelTrackerProps> = ({
  weeklyData,
  onDailyFeelingSelect,
  readOnly = false
}) => {
  return (
    <View style={styles.container}>
      {/* Daily feeling indicators row */}
      <View style={styles.stressRow}>
        {DAYS_OF_WEEK.map((day) => {
          const dailyFeeling = weeklyData[day].dailyFeeling;
          return (
            <View key={day} style={styles.stressColumn}>
              {readOnly ? (
                <View style={styles.stressIndicator}>
                  {/* Outer circle background */}
                  <View style={styles.outerCircle}>
                    {/* Inner circle with daily feeling color */}
                    <View style={[
                      styles.innerCircle,
                      dailyFeeling && { backgroundColor: DAILY_FEELING_COLORS[dailyFeeling] },
                      !dailyFeeling && styles.emptyInnerCircle,
                    ]}>
                      <Text style={[
                        styles.stressText,
                        !dailyFeeling && styles.emptyStressText,
                      ]}>
                        {dailyFeeling || '?'}
                      </Text>
                    </View>
                  </View>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.stressIndicator}
                  onPress={() => {
                    // Cycle through daily feeling levels 1-4, then back to null
                    if (dailyFeeling === null) {
                      onDailyFeelingSelect(day, 1);
                    } else if (dailyFeeling < 4) {
                      onDailyFeelingSelect(day, (dailyFeeling + 1) as DailyFeelingLevel);
                    } else {
                      // Reset to null or cycle back to 1
                      onDailyFeelingSelect(day, 1);
                    }
                  }}
                  activeOpacity={0.7}
                >
                  {/* Outer circle background */}
                  <View style={styles.outerCircle}>
                    {/* Inner circle with daily feeling color */}
                    <View style={[
                      styles.innerCircle,
                      dailyFeeling && { backgroundColor: DAILY_FEELING_COLORS[dailyFeeling] },
                      !dailyFeeling && styles.emptyInnerCircle,
                    ]}>
                      <Text style={[
                        styles.stressText,
                        !dailyFeeling && styles.emptyStressText,
                      ]}>
                        {dailyFeeling || '?'}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              )}
            </View>
          );
        })}
      </View>

      {/* Days row */}
      <View style={styles.daysRow}>
        {DAYS_OF_WEEK.map((day) => (
          <View key={day} style={styles.dayColumn}>
            <Text style={styles.dayLabel}>{day}</Text>
          </View>
        ))}
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        {DAILY_FEELING_LEVELS.map((level) => (
          <View key={level} style={styles.legendItem}>
            <View style={[styles.legendCircle, { backgroundColor: DAILY_FEELING_COLORS[level] }]}>
              <Text style={styles.legendNumber}>{level}</Text>
            </View>
            <Text style={styles.legendText}>{DAILY_FEELING_TEXTS[level]}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  stressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  stressColumn: {
    flex: 1,
    alignItems: 'center',
  },
  stressIndicator: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  outerCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: DAILY_FEELING_BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyInnerCircle: {
    backgroundColor: '#E5E7EB',
  },
  stressText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  emptyStressText: {
    color: '#9CA3AF',
    fontSize: 18,
  },
  daysRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayColumn: {
    flex: 1,
    alignItems: 'center',
  },
  dayLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#5B3E31',
  },
  legend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    width: '48%',
  },
  legendCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  legendNumber: {
    fontSize: 12,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
    flex: 1,
  },
});
