import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import Svg, { Path, ForeignObject } from 'react-native-svg';
import { MoodLevel, CIRCULAR_MOOD_SECTIONS } from '../../types/mood';
import { MoodSvgIcon } from './MoodSvgIcon';
import { useMoodTrackerResponsive } from '../../hooks/useResponsiveDimensions';

const { width: screenWidth } = Dimensions.get('window');

// Click-to-select configuration - no arrow needed

interface CircularMoodSelectorProps {
  selectedMood: MoodLevel | null;
  onMoodSelect: (mood: MoodLevel) => void;
}

// No arrow needed - clean circular design

export const CircularMoodSelector: React.FC<CircularMoodSelectorProps> = ({
  selectedMood,
  onMoodSelect,
}) => {
  const ringRotation = useSharedValue(0);

  // Get responsive configuration
  const responsiveConfig = useMoodTrackerResponsive();

  // Use responsive container size
  const CONTAINER_SIZE = responsiveConfig.circularSelectorSize;
  const RING_RADIUS = CONTAINER_SIZE / 2.3;
  const STROKE_WIDTH = Math.max(45 * (CONTAINER_SIZE / 320), 35); // Scale stroke width
  const CENTER_X = CONTAINER_SIZE / 2;
  const CENTER_Y = CONTAINER_SIZE / 2;
  const SECTION_ANGLE = 72; // 360° / 5 sections

  // Convert mood level to rotation offset (align to top)
  const moodToRotation = (mood: MoodLevel | null): number => {
    if (!mood) return 0;
    const section = CIRCULAR_MOOD_SECTIONS.find(s => s.id === mood);
    if (!section) return 0;

    const sectionCenter = (section.angle.start + section.angle.end) / 2;
    return -sectionCenter; // Negative to move section to top (0°)
  };

  // No need for rotation-to-mood conversion with click-to-select

  // Handle section click - rotate clicked section to top (0°/360°)
  const handleSectionClick = (moodId: MoodLevel) => {
    const section = CIRCULAR_MOOD_SECTIONS.find(s => s.id === moodId);
    if (!section) return;

    const sectionCenter = (section.angle.start + section.angle.end) / 2;
    // Rotate so the clicked section moves to top (0° position)
    const targetRotation = -sectionCenter;

    // Animate to target position
    ringRotation.value = withSpring(targetRotation, {
      damping: 15,
      stiffness: 150
    });

    // Update mood selection
    onMoodSelect(moodId);
  };

  // Update rotation when selectedMood changes
  useEffect(() => {
    const targetRotation = moodToRotation(selectedMood);
    ringRotation.value = withSpring(targetRotation, {
      damping: 15,
      stiffness: 150,
    });
  }, [selectedMood, ringRotation]);

  // Create SVG path for circular section
  const createCircularSectionPath = (startAngle: number, endAngle: number) => {
    // Convert to radians and adjust for SVG coordinate system (0° = top, clockwise)
    const startRadian = ((startAngle - 90) * Math.PI) / 180;
    const endRadian = ((endAngle - 90) * Math.PI) / 180;

    const innerRadius = RING_RADIUS - STROKE_WIDTH;
    const outerRadius = RING_RADIUS;

    const x1 = CENTER_X + outerRadius * Math.cos(startRadian);
    const y1 = CENTER_Y + outerRadius * Math.sin(startRadian);
    const x2 = CENTER_X + outerRadius * Math.cos(endRadian);
    const y2 = CENTER_Y + outerRadius * Math.sin(endRadian);

    const x3 = CENTER_X + innerRadius * Math.cos(endRadian);
    const y3 = CENTER_Y + innerRadius * Math.sin(endRadian);
    const x4 = CENTER_X + innerRadius * Math.cos(startRadian);
    const y4 = CENTER_Y + innerRadius * Math.sin(startRadian);

    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4} Z`;
  };

  // No gesture handling needed - using click-to-select instead

  // Animated style for rotating ring
  const rotatableStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${ringRotation.value}deg` },
    ],
  }));

  return (
    <View style={styles.container}>
      <View style={[styles.selectorContainer, { width: CONTAINER_SIZE, height: CONTAINER_SIZE }]}>
        {/* Clickable circular ring */}
        <Animated.View style={[styles.ringContent, rotatableStyle]}>
          <Svg width={CONTAINER_SIZE} height={CONTAINER_SIZE}>
            {/* Render clickable circular mood sections */}
            {CIRCULAR_MOOD_SECTIONS.map((section) => (
              <Path
                key={section.id}
                d={createCircularSectionPath(section.angle.start, section.angle.end)}
                fill={section.color}
                opacity={selectedMood === section.id ? 1 : 0.85}
                onPress={() => handleSectionClick(section.id)}
              />
            ))}

          </Svg>
        </Animated.View>

        {/* Render SVG icons with animated positioning that follows ring rotation */}
        {CIRCULAR_MOOD_SECTIONS.map((section) => {
          const IconComponent = () => {
            const iconStyle = useAnimatedStyle(() => {
              const currentRotation = ringRotation.value;
              const angle = (section.angle.start + section.angle.end) / 2;
              // Calculate position based on current ring rotation
              const adjustedAngle = angle + currentRotation;
              const radian = ((adjustedAngle - 90) * Math.PI) / 180;
              const labelRadius = RING_RADIUS - STROKE_WIDTH / 2;
              const x = CENTER_X + labelRadius * Math.cos(radian);
              const y = CENTER_Y + labelRadius * Math.sin(radian);

              return {
                position: 'absolute',
                left: x - 15.5,
                top: y - 15.5,
                width: 31,
                height: 31,
              };
            });

            return (
              <Animated.View style={iconStyle}>
                <MoodSvgIcon
                  svgString={section.svgIcon!}
                  size={31}
                  onPress={() => handleSectionClick(section.id)}
                />
              </Animated.View>
            );
          };

          if (section.svgIcon) {
            return <IconComponent key={`icon-${section.id}`} />;
          }

          return null;
        })}

        {/* Clean design without arrow */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 10,
  },
  selectorContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ringContent: {
    position: 'relative',
  },

});
