import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import Svg, { Path, Circle, Text as SvgText } from 'react-native-svg';
import { MoodLevel, SemicircularMoodPickerProps } from '../../types/mood';

const { width: screenWidth } = Dimensions.get('window');

// Configuration constants
const PICKER_WIDTH = Math.min(screenWidth - 40, 320);
const PICKER_HEIGHT = PICKER_WIDTH / 2;
const RADIUS = PICKER_WIDTH / 2 - 20;
const CENTER_X = PICKER_WIDTH / 2;
const CENTER_Y = PICKER_HEIGHT;
const STROKE_WIDTH = 30;

// Mood sections configuration
const MOOD_SECTIONS = [
  { mood: 1 as MoodLevel, color: '#926247', startAngle: 0, endAngle: 36, emoji: '😢' },
  { mood: 2 as MoodLevel, color: '#ED7E1C', startAngle: 36, endAngle: 72, emoji: '😔' },
  { mood: 3 as MoodLevel, color: '#FFCE5C', startAngle: 72, endAngle: 108, emoji: '😐' },
  { mood: 4 as MoodLevel, color: '#9BB168', startAngle: 108, endAngle: 144, emoji: '🙂' },
  { mood: 5 as MoodLevel, color: '#A694F5', startAngle: 144, endAngle: 180, emoji: '😊' },
];

export const SemicircularMoodPicker: React.FC<SemicircularMoodPickerProps> = ({
  selectedMood,
  onMoodSelect,
  colors,
}) => {
  const indicatorAngle = useSharedValue(90); // Start at center (neutral)

  // Convert mood level to angle
  const moodToAngle = (mood: MoodLevel | null): number => {
    if (!mood) return 90; // Default to center
    const section = MOOD_SECTIONS.find(s => s.mood === mood);
    return section ? (section.startAngle + section.endAngle) / 2 : 90;
  };

  // Convert angle to mood level
  const angleToMood = (angle: number): MoodLevel => {
    const normalizedAngle = Math.max(0, Math.min(180, angle));
    const section = MOOD_SECTIONS.find(s => 
      normalizedAngle >= s.startAngle && normalizedAngle <= s.endAngle
    );
    return section ? section.mood : 3;
  };

  // Update indicator position when selectedMood changes
  useEffect(() => {
    const targetAngle = moodToAngle(selectedMood);
    indicatorAngle.value = withSpring(targetAngle, {
      damping: 15,
      stiffness: 150,
    });
  }, [selectedMood]);

  // Calculate indicator position
  const getIndicatorPosition = (angle: number) => {
    const radian = (angle * Math.PI) / 180;
    const x = CENTER_X + (RADIUS - STROKE_WIDTH / 2) * Math.cos(radian);
    const y = CENTER_Y - (RADIUS - STROKE_WIDTH / 2) * Math.sin(radian);
    return { x, y };
  };

  // Create SVG path for semicircle section
  const createSectionPath = (startAngle: number, endAngle: number) => {
    const startRadian = (startAngle * Math.PI) / 180;
    const endRadian = (endAngle * Math.PI) / 180;
    
    const innerRadius = RADIUS - STROKE_WIDTH;
    const outerRadius = RADIUS;
    
    const x1 = CENTER_X + outerRadius * Math.cos(startRadian);
    const y1 = CENTER_Y - outerRadius * Math.sin(startRadian);
    const x2 = CENTER_X + outerRadius * Math.cos(endRadian);
    const y2 = CENTER_Y - outerRadius * Math.sin(endRadian);
    
    const x3 = CENTER_X + innerRadius * Math.cos(endRadian);
    const y3 = CENTER_Y - innerRadius * Math.sin(endRadian);
    const x4 = CENTER_X + innerRadius * Math.cos(startRadian);
    const y4 = CENTER_Y - innerRadius * Math.sin(startRadian);
    
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
    
    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x4} ${y4} Z`;
  };

  // Modern gesture handler using new API
  const panGesture = Gesture.Pan()
    .onStart(() => {
      // Store initial angle if needed
    })
    .onUpdate((event) => {
      const { x, y } = event;
      const deltaX = x - CENTER_X;
      const deltaY = CENTER_Y - y;

      let angle = (Math.atan2(deltaY, deltaX) * 180) / Math.PI;
      if (angle < 0) angle = 0;
      if (angle > 180) angle = 180;

      indicatorAngle.value = angle;

      // Update mood selection
      const newMood = angleToMood(angle);
      runOnJS(onMoodSelect)(newMood);
    })
    .onEnd(() => {
      // Snap to nearest mood if needed
    });

  // Animated style for indicator
  const indicatorStyle = useAnimatedStyle(() => {
    const { x, y } = getIndicatorPosition(indicatorAngle.value);
    return {
      transform: [
        { translateX: x - 12 },
        { translateY: y - 12 },
      ],
    };
  });

  return (
    <GestureHandlerRootView style={styles.container}>
      <View style={[styles.pickerContainer, { width: PICKER_WIDTH, height: PICKER_HEIGHT }]}>
        <GestureDetector gesture={panGesture}>
          <Animated.View style={styles.svgContainer}>
            <Svg width={PICKER_WIDTH} height={PICKER_HEIGHT}>
              {/* Render mood sections */}
              {MOOD_SECTIONS.map((section) => (
                <Path
                  key={section.mood}
                  d={createSectionPath(section.startAngle, section.endAngle)}
                  fill={colors[section.mood]}
                  opacity={selectedMood === section.mood ? 1 : 0.8}
                />
              ))}
              
              {/* Render emoji labels */}
              {MOOD_SECTIONS.map((section) => {
                const angle = (section.startAngle + section.endAngle) / 2;
                const radian = (angle * Math.PI) / 180;
                const labelRadius = RADIUS - STROKE_WIDTH / 2;
                const x = CENTER_X + labelRadius * Math.cos(radian);
                const y = CENTER_Y - labelRadius * Math.sin(radian);
                
                return (
                  <SvgText
                    key={`emoji-${section.mood}`}
                    x={x}
                    y={y}
                    fontSize="24"
                    textAnchor="middle"
                    alignmentBaseline="middle"
                  >
                    {section.emoji}
                  </SvgText>
                );
              })}
            </Svg>
            
            {/* Animated indicator */}
            <Animated.View style={[styles.indicator, indicatorStyle]} />
          </Animated.View>
        </GestureDetector>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pickerContainer: {
    position: 'relative',
  },
  svgContainer: {
    position: 'relative',
  },
  indicator: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#5B3E31',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },
});
