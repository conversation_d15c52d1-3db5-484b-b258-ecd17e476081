import React from 'react';
import { View, Text, StyleSheet, ScrollView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  WeeklyMoodData,
  DayOfWeek,
  MoodLevel,
  SleepQualityLevel,
  DailyFeelingLevel
} from '../../types/mood';
import { MoodSelector } from './MoodSelector';
import { PhysicalHealthTracker } from './PhysicalHealthTracker';
import { SleepTracker } from './SleepTracker';
import { StressLevelTracker } from './StressLevelTracker';
import { EnhancedCTAButton } from './EnhancedCTAButton';

interface MoodTrackingViewProps {
  displayData: WeeklyMoodData;
  isLoadingData: boolean;
  hasError: any;
  refetch: () => void;
  onOpenDailyTracker: () => void;
  hasDataToday: boolean;
  isLoadingTodayData: boolean;
}

export const MoodTrackingView: React.FC<MoodTrackingViewProps> = ({
  displayData,
  isLoadingData,
  hasError,
  refetch,
  onOpenDailyTracker,
  hasDataToday,
  isLoadingTodayData,
}) => {
  // Dummy functions for read-only mode (components still expect these props)
  const dummyUpdateMood = () => {};
  const dummyUpdatePhysicalHealth = () => {};
  const dummyUpdateSleepQuality = () => {};
  const dummyUpdateDailyFeeling = () => {};

  if (isLoadingData) {
    return null; // Loading is handled by parent
  }

  if (hasError) {
    return null; // Error is handled by parent
  }

  return (
    <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.contentContainer}>


        {/* Enhanced Call to Action */}
        <EnhancedCTAButton
          onPress={onOpenDailyTracker}
          hasDataToday={hasDataToday}
          isLoading={isLoadingTodayData}
          disabled={isLoadingData}
        />

        {/* Mood Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Mood</Text>
          <MoodSelector
            weeklyData={displayData}
            onMoodSelect={dummyUpdateMood}
            readOnly={true}
          />
        </View>

        {/* Physical Health Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Kesehatan Fisik</Text>
          <PhysicalHealthTracker
            weeklyData={displayData}
            onHealthToggle={dummyUpdatePhysicalHealth}
            readOnly={true}
          />
        </View>

        {/* Sleep Quality Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Kualitas Tidur</Text>
          <SleepTracker
            weeklyData={displayData}
            onSleepQualityChange={dummyUpdateSleepQuality}
            readOnly={true}
          />
        </View>

        {/* Daily Feeling Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Perasaan Harian</Text>
          <StressLevelTracker
            weeklyData={displayData}
            onDailyFeelingSelect={dummyUpdateDailyFeeling}
            readOnly={true}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  

  
  // Section Containers
  sectionContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginHorizontal: 20,
    marginVertical: 8,
    padding: 20,
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      },
    }),
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },

  // Development Panel
  devPanel: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    margin: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  devPanelTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  devButtonsContainer: {
    flexDirection: 'row',
  },
  devButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  devButtonActive: {
    backgroundColor: '#4EAF64',
    borderColor: '#4EAF64',
  },
  devButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  devButtonTextActive: {
    color: '#FFFFFF',
  },
});
