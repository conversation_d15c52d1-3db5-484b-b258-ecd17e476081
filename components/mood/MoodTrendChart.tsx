import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import type { MoodTrend } from '../../types/mood';

interface MoodTrendChartProps {
  trends: MoodTrend[];
  metric: 'mood' | 'sleep' | 'stress';
  title?: string;
}

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 40; // 20px padding on each side
const chartHeight = 200;

export const MoodTrendChart: React.FC<MoodTrendChartProps> = ({
  trends,
  metric,
  title
}) => {
  if (!trends || trends.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No data available</Text>
      </View>
    );
  }

  // Get the values for the selected metric
  const getMetricValue = (trend: MoodTrend): number => {
    switch (metric) {
      case 'mood':
        return trend.avgMood;
      case 'sleep':
        return trend.avgSleepHours;
      case 'stress':
        return trend.avgStressLevel;
      default:
        return 0;
    }
  };

  // Get the max value for scaling
  const getMaxValue = (): number => {
    switch (metric) {
      case 'mood':
        return 5;
      case 'sleep':
        return Math.max(12, Math.max(...trends.map(getMetricValue)));
      case 'stress':
        return 5;
      default:
        return 5;
    }
  };

  // Get color for the metric
  const getMetricColor = (): string => {
    switch (metric) {
      case 'mood':
        return '#4EAF64';
      case 'sleep':
        return '#3B82F6';
      case 'stress':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  // Get unit for the metric
  const getMetricUnit = (): string => {
    switch (metric) {
      case 'mood':
        return '/5';
      case 'sleep':
        return 'h';
      case 'stress':
        return '/5';
      default:
        return '';
    }
  };

  const values = trends.map(getMetricValue);
  const maxValue = getMaxValue();
  const minValue = Math.min(0, Math.min(...values));
  const range = maxValue - minValue;
  
  // Calculate positions for the line chart
  const pointSpacing = chartWidth / (trends.length - 1);
  const points = values.map((value, index) => {
    const x = index * pointSpacing;
    const y = chartHeight - ((value - minValue) / range) * chartHeight;
    return { x, y, value };
  });

  // Create SVG path for the line
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x} ${point.y}`;
  }, '');

  return (
    <View style={styles.container}>
      {title && <Text style={styles.title}>{title}</Text>}
      
      <View style={styles.chartContainer}>
        {/* Y-axis labels */}
        <View style={styles.yAxisContainer}>
          <Text style={styles.axisLabel}>{maxValue.toFixed(1)}</Text>
          <Text style={styles.axisLabel}>{(maxValue * 0.5).toFixed(1)}</Text>
          <Text style={styles.axisLabel}>{minValue.toFixed(1)}</Text>
        </View>

        {/* Chart area */}
        <View style={[styles.chartArea, { width: chartWidth, height: chartHeight }]}>
          {/* Grid lines */}
          <View style={styles.gridContainer}>
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
              <View
                key={index}
                style={[
                  styles.gridLine,
                  { top: ratio * chartHeight }
                ]}
              />
            ))}
          </View>

          {/* Data points and line simulation using Views */}
          {points.map((point, index) => (
            <View key={index}>
              {/* Line segment to next point */}
              {index < points.length - 1 && (
                <View
                  style={[
                    styles.lineSegment,
                    {
                      left: point.x,
                      top: point.y,
                      width: Math.sqrt(
                        Math.pow(points[index + 1].x - point.x, 2) +
                        Math.pow(points[index + 1].y - point.y, 2)
                      ),
                      transform: [
                        {
                          rotate: `${Math.atan2(
                            points[index + 1].y - point.y,
                            points[index + 1].x - point.x
                          )}rad`
                        }
                      ],
                      backgroundColor: getMetricColor(),
                    }
                  ]}
                />
              )}
              
              {/* Data point */}
              <View
                style={[
                  styles.dataPoint,
                  {
                    left: point.x - 4,
                    top: point.y - 4,
                    backgroundColor: getMetricColor(),
                  }
                ]}
              />
              
              {/* Value label */}
              <Text
                style={[
                  styles.valueLabel,
                  {
                    left: point.x - 15,
                    top: point.y - 25,
                  }
                ]}
              >
                {point.value.toFixed(1)}{getMetricUnit()}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* X-axis labels */}
      <View style={styles.xAxisContainer}>
        {trends.map((trend, index) => (
          <Text key={index} style={styles.xAxisLabel}>
            {metric === 'mood' ? 'Week' : 'Period'} {index + 1}
          </Text>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#6B7280',
  },
  chartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  yAxisContainer: {
    width: 40,
    height: chartHeight,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 8,
  },
  axisLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  chartArea: {
    position: 'relative',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
  },
  gridContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  gridLine: {
    position: 'absolute',
    width: '100%',
    height: 1,
    backgroundColor: '#E5E7EB',
  },
  lineSegment: {
    position: 'absolute',
    height: 2,
    borderRadius: 1,
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  valueLabel: {
    position: 'absolute',
    fontSize: 10,
    color: '#374151',
    fontWeight: '500',
    width: 30,
    textAlign: 'center',
  },
  xAxisContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    paddingLeft: 40,
  },
  xAxisLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    flex: 1,
  },
});
