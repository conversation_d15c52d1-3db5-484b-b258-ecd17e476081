import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolate,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface EnhancedCTAButtonProps {
  onPress: () => void;
  hasDataToday: boolean;
  isLoading: boolean;
  disabled?: boolean;
}

export const EnhancedCTAButton: React.FC<EnhancedCTAButtonProps> = ({
  onPress,
  hasDataToday,
  isLoading,
  disabled = false,
}) => {
  // Animation values
  const pulseScale = useSharedValue(1);
  const glowOpacity = useSharedValue(0.1);
  const pressScale = useSharedValue(1);

  // Determine button state and content
  const getButtonContent = () => {
    if (isLoading) {
      return {
        title: "Memuat...",
        subtitle: "",
        icon: null,
        colors: {
          background: '#6B7280',
          backgroundHover: '#6B7280',
          text: '#FFFFFF',
        },
      };
    }

    if (hasDataToday) {
      return {
        title: "Ubah Mood Tracker Hari Ini",
        subtitle: "Perbarui data mood Anda",
        icon: "create" as const,
        colors: {
          background: '#2563EB',
          backgroundHover: '#1D4ED8',
          text: '#FFFFFF',
        },
      };
    }

    return {
      title: "Isi Mood Tracker Hari Ini",
      subtitle: "Pantau kesehatan mental Anda setiap hari",
      icon: "add-circle" as const,
      colors: {
        background: '#4EAF64',
        backgroundHover: '#45A05A',
        text: '#FFFFFF',
      },
    };
  };

  const buttonContent = getButtonContent();

  // Start appropriate animation based on state
  useEffect(() => {
    if (isLoading) {
      // No special animation for loading
      pulseScale.value = 1;
      glowOpacity.value = 0.1;
    } else if (hasDataToday) {
      // Subtle glow animation for modify state
      pulseScale.value = 1;
      glowOpacity.value = withRepeat(
        withSequence(
          withTiming(0.2, { duration: 1500 }),
          withTiming(0.1, { duration: 1500 })
        ),
        -1,
        false
      );
    } else {
      // Gentle pulse animation for new data state
      glowOpacity.value = 0.1;
      pulseScale.value = withRepeat(
        withSequence(
          withTiming(1.02, { duration: 1000 }),
          withTiming(1, { duration: 1000 })
        ),
        -1,
        false
      );
    }
  }, [hasDataToday, isLoading]);

  // Handle press with haptic feedback and animation
  const handlePress = async () => {
    if (disabled || isLoading) return;

    // Haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Press animation
    pressScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );

    // Call the onPress handler
    onPress();
  };

  // Animated styles
  const animatedButtonStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: pulseScale.value * pressScale.value },
      ],
    };
  });

  const animatedShadowStyle = useAnimatedStyle(() => {
    const shadowOpacity = interpolate(
      glowOpacity.value,
      [0.1, 0.2],
      [0.1, 0.25]
    );

    return {
      shadowOpacity,
      elevation: interpolate(glowOpacity.value, [0.1, 0.2], [4, 8]),
    };
  });

  return (
    <View style={styles.container}>
      <Animated.View style={[animatedButtonStyle, animatedShadowStyle]}>
        <TouchableOpacity
          style={[
            styles.button,
            {
              backgroundColor: buttonContent.colors.background,
              opacity: disabled ? 0.6 : 1,
            },
          ]}
          onPress={handlePress}
          disabled={disabled || isLoading}
          activeOpacity={0.8}
        >
          <View style={styles.contentContainer}>
            {/* Icon and Loading Spinner */}
            <View style={styles.iconContainer}>
              {isLoading ? (
                <ActivityIndicator size="small" color={buttonContent.colors.text} />
              ) : (
                buttonContent.icon && (
                  <Ionicons
                    name={buttonContent.icon}
                    size={24}
                    color={buttonContent.colors.text}
                    style={styles.icon}
                  />
                )
              )}
            </View>

            {/* Text Content */}
            <View style={styles.textContainer}>
              <Text style={[styles.title, { color: buttonContent.colors.text }]}>
                {buttonContent.title}
              </Text>
              {buttonContent.subtitle && (
                <Text style={[styles.subtitle, { color: buttonContent.colors.text }]}>
                  {buttonContent.subtitle}
                </Text>
              )}
            </View>

            {/* Arrow Indicator */}
            {!isLoading && (
              <View style={styles.arrowContainer}>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={buttonContent.colors.text}
                  style={styles.arrow}
                />
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginVertical: 16,
  },
  button: {
    borderRadius: 16,
    paddingVertical: 18,
    paddingHorizontal: 20,
    ...Platform.select({
      web: {
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
      },
      default: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowRadius: 12,
        elevation: 4,
      },
    }),
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    marginRight: 4,
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
    lineHeight: 20,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    opacity: 0.8,
    marginTop: 2,
    lineHeight: 18,
  },
  arrowContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrow: {
    opacity: 0.8,
  },
});
