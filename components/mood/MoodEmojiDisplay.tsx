import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { MoodLevel, CIRCULAR_MOOD_SECTIONS } from '../../types/mood';
import { MoodSvgIcon } from './MoodSvgIcon';

interface MoodEmojiDisplayProps {
  selectedMood: MoodLevel | null;
  responseText?: string;
}

export const MoodEmojiDisplay: React.FC<MoodEmojiDisplayProps> = ({
  selectedMood,
  responseText
}) => {
  const currentMoodSection = selectedMood
    ? CIRCULAR_MOOD_SECTIONS.find(section => section.id === selectedMood)
    : CIRCULAR_MOOD_SECTIONS[2]; // Default to neutral

  const displayText = responseText || currentMoodSection?.responseText || '<PERSON>a biasa aja.';
  const displayEmoji = currentMoodSection?.emoji || '😐';

  // Animated styles for emoji
  const emojiAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { 
          scale: selectedMood 
            ? withSequence(
                withTiming(1.1, { duration: 150 }),
                withSpring(1, { damping: 8, stiffness: 100 })
              )
            : withSpring(1)
        }
      ]
    };
  });

  // Animated styles for text
  const textAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(1, { duration: 300 }),
      transform: [
        { translateY: withSpring(0, { damping: 10, stiffness: 100 }) }
      ]
    };
  });

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.iconContainer, emojiAnimatedStyle]}>
        {currentMoodSection?.svgIcon ? (
          <MoodSvgIcon
            svgString={currentMoodSection.svgIcon}
            size={80}
          />
        ) : (
          <Text style={styles.largeEmoji}>
            {displayEmoji}
          </Text>
        )}
      </Animated.View>
      <Animated.View style={textAnimatedStyle}>
        <Text style={styles.responseText}>
          {displayText}
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 30,
    minHeight: 140,
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 18,
  },
  largeEmoji: {
    fontSize: 80,
    lineHeight: 88,
  },
  responseText: {
    fontSize: 22,
    fontWeight: '400',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 28,
    paddingHorizontal: 20,
  },
});
