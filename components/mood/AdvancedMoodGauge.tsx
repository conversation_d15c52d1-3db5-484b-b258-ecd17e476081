import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import Svg, { Path, Text as SvgText, Defs, Filter, FeDropShadow } from 'react-native-svg';
import { MoodLevel, ENHANCED_MOOD_SECTIONS } from '../../types/mood';

const { width: screenWidth } = Dimensions.get('window');

// LARGE-SCALE Configuration constants
const GAUGE_WIDTH = Math.min(screenWidth - 20, 400);
const GAUGE_HEIGHT = GAUGE_WIDTH / 1.8;
const RADIUS = GAUGE_WIDTH / 2.2;
const CENTER_X = GAUGE_WIDTH / 2;
const CENTER_Y = GAUGE_HEIGHT;
const STROKE_WIDTH = 60; // Much thicker sections
const ARROW_SIZE = { width: 60, height: 45 };

// Rotation configuration
const ROTATION_SENSITIVITY = 0.6;
const ARROW_POSITION_ANGLE = 90;

interface AdvancedMoodGaugeProps {
  selectedMood: MoodLevel | null;
  onMoodSelect: (mood: MoodLevel) => void;
}

// Enhanced Fixed Arrow Component with shadow
const FixedArrow: React.FC = () => (
  <View style={styles.fixedArrow}>
    <Svg width={ARROW_SIZE.width} height={ARROW_SIZE.height}>
      <Defs>
        <Filter id="arrowShadow">
          <FeDropShadow dx="0" dy="3" stdDeviation="3" floodOpacity="0.3" />
        </Filter>
      </Defs>
      <Path
        d="M30 8 L45 30 L38 30 L38 37 L22 37 L22 30 L15 30 Z"
        fill="#5B3E31"
        stroke="#FFFFFF"
        strokeWidth="3"
        filter="url(#arrowShadow)"
      />
    </Svg>
  </View>
);

export const AdvancedMoodGauge: React.FC<AdvancedMoodGaugeProps> = ({
  selectedMood,
  onMoodSelect,
}) => {
  const gaugeRotation = useSharedValue(0);

  // Convert mood level to rotation offset
  const moodToRotation = (mood: MoodLevel | null): number => {
    if (!mood) return 0;
    const section = ENHANCED_MOOD_SECTIONS.find(s => s.id === mood);
    if (!section) return 0;
    
    const sectionCenter = (section.angle.start + section.angle.end) / 2;
    return ARROW_POSITION_ANGLE - sectionCenter;
  };

  // Convert rotation to mood level
  const getSelectedMoodFromRotation = (rotation: number): MoodLevel => {
    const effectiveAngle = (ARROW_POSITION_ANGLE - rotation) % 360;
    const normalizedAngle = effectiveAngle < 0 ? effectiveAngle + 360 : effectiveAngle;
    const sectionAngle = normalizedAngle % 180;
    
    const section = ENHANCED_MOOD_SECTIONS.find(s => 
      sectionAngle >= s.angle.start && sectionAngle <= s.angle.end
    );
    return section ? section.id : 3;
  };

  // Snap to nearest mood section
  const snapToNearestMood = (rotation: number): number => {
    const mood = getSelectedMoodFromRotation(rotation);
    return moodToRotation(mood);
  };

  // Handle direct tap on mood section
  const handleSectionTap = (sectionId: MoodLevel) => {
    const targetRotation = moodToRotation(sectionId);
    gaugeRotation.value = withSpring(targetRotation, {
      damping: 15,
      stiffness: 150
    });
    onMoodSelect(sectionId);
  };

  // Update rotation when selectedMood changes
  useEffect(() => {
    const targetRotation = moodToRotation(selectedMood);
    gaugeRotation.value = withSpring(targetRotation, {
      damping: 15,
      stiffness: 150,
    });
  }, [selectedMood, gaugeRotation]);

  // Create SVG path for mood section
  const createSectionPath = (startAngle: number, endAngle: number) => {
    const startRadian = (startAngle * Math.PI) / 180;
    const endRadian = (endAngle * Math.PI) / 180;
    
    const innerRadius = RADIUS - STROKE_WIDTH;
    const outerRadius = RADIUS;
    
    const x1 = CENTER_X + outerRadius * Math.cos(startRadian);
    const y1 = CENTER_Y - outerRadius * Math.sin(startRadian);
    const x2 = CENTER_X + outerRadius * Math.cos(endRadian);
    const y2 = CENTER_Y - outerRadius * Math.sin(endRadian);
    
    const x3 = CENTER_X + innerRadius * Math.cos(endRadian);
    const y3 = CENTER_Y - innerRadius * Math.sin(endRadian);
    const x4 = CENTER_X + innerRadius * Math.cos(startRadian);
    const y4 = CENTER_Y - innerRadius * Math.sin(startRadian);
    
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
    
    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x4} ${y4} Z`;
  };

  // Enhanced gesture handler using new API
  const panGesture = Gesture.Pan()
    .onStart(() => {
      // Store initial rotation
    })
    .onUpdate((event) => {
      const deltaX = event.translationX;
      const deltaY = event.translationY;

      const rotationDelta = (deltaX - deltaY) * ROTATION_SENSITIVITY;
      gaugeRotation.value = rotationDelta;

      const selectedMood = getSelectedMoodFromRotation(gaugeRotation.value);
      runOnJS(onMoodSelect)(selectedMood);
    })
    .onEnd(() => {
      // Snap to nearest mood section
      const targetRotation = snapToNearestMood(gaugeRotation.value);
      gaugeRotation.value = withSpring(targetRotation, {
        damping: 15,
        stiffness: 150
      });
    });

  // Animated style for rotating gauge
  const rotatableStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${gaugeRotation.value}deg` },
    ],
  }));

  return (
    <GestureHandlerRootView style={styles.container}>
      <View style={[styles.gaugeContainer, { width: GAUGE_WIDTH, height: GAUGE_HEIGHT + 40 }]}>
        {/* Rotatable gauge sections */}
        <GestureDetector gesture={panGesture}>
          <Animated.View style={[styles.gaugeContent, rotatableStyle]}>
            <Svg width={GAUGE_WIDTH} height={GAUGE_HEIGHT}>
              {/* Render mood sections */}
              {ENHANCED_MOOD_SECTIONS.map((section) => (
                <Path
                  key={section.id}
                  d={createSectionPath(section.angle.start, section.angle.end)}
                  fill={section.color}
                  opacity={selectedMood === section.id ? 1 : 0.85}
                />
              ))}

              {/* Render emojis in sections */}
              {ENHANCED_MOOD_SECTIONS.map((section) => {
                const angle = (section.angle.start + section.angle.end) / 2;
                const radian = (angle * Math.PI) / 180;
                const labelRadius = RADIUS - STROKE_WIDTH / 2;
                const x = CENTER_X + labelRadius * Math.cos(radian);
                const y = CENTER_Y - labelRadius * Math.sin(radian);

                return (
                  <SvgText
                    key={`emoji-${section.id}`}
                    x={x}
                    y={y}
                    fontSize="32"
                    textAnchor="middle"
                    alignmentBaseline="middle"
                  >
                    {section.emoji}
                  </SvgText>
                );
              })}
            </Svg>
          </Animated.View>
        </GestureDetector>

        {/* Fixed arrow at bottom center */}
        <FixedArrow />
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  gaugeContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gaugeContent: {
    position: 'relative',
  },
  fixedArrow: {
    position: 'absolute',
    bottom: -15,
    left: '50%',
    marginLeft: -30,
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
});
