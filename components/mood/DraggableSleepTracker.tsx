import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { SleepQualityLevel, SLEEP_QUALITY_OPTIONS_0_10, SleepQualityOptionExtended } from '../../types/mood';
import { useMoodTrackerResponsive } from '../../hooks/useResponsiveDimensions';

interface DraggableSleepTrackerProps {
  selectedQuality: SleepQualityLevel | null;
  onQualitySelect: (quality: SleepQualityLevel) => void;
}

const { height: screenHeight } = Dimensions.get('window');

// Base slider configuration - will be made responsive
const SLIDER_WIDTH = 6;
const HANDLE_SIZE = 40;
const TRACK_COLOR = '#E0E0E0';
const ACTIVE_TRACK_COLOR = '#FF9800';

export const DraggableSleepTracker: React.FC<DraggableSleepTrackerProps> = ({
  selectedQuality,
  onQualitySelect,
}) => {
  // Get responsive configuration
  const responsiveConfig = useMoodTrackerResponsive();

  // Use responsive slider height
  const SLIDER_HEIGHT = responsiveConfig.sliderHeight;

  // Shared values for animation
  const sliderPosition = useSharedValue(0);
  const isDragging = useSharedValue(false);

  // State for stable text display during dragging
  const [displayQuality, setDisplayQuality] = useState<SleepQualityLevel>(selectedQuality || 5);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Convert quality level to slider position (0-10 maps to 0-SLIDER_HEIGHT)
  const qualityToPosition = (quality: SleepQualityLevel): number => {
    return ((10 - quality) / 10) * SLIDER_HEIGHT;
  };

  // Convert slider position to quality level
  const positionToQuality = (position: number): SleepQualityLevel => {
    const normalizedPosition = Math.max(0, Math.min(position, SLIDER_HEIGHT));
    const quality = Math.round(10 - (normalizedPosition / SLIDER_HEIGHT) * 10);
    return Math.max(0, Math.min(10, quality)) as SleepQualityLevel;
  };

  // Get current option based on position
  const getCurrentOption = (quality: SleepQualityLevel): SleepQualityOptionExtended => {
    return SLEEP_QUALITY_OPTIONS_0_10.find(option => option.id === quality) || SLEEP_QUALITY_OPTIONS_0_10[5];
  };

  // Initialize position based on selected quality
  useEffect(() => {
    if (selectedQuality !== null) {
      const targetPosition = qualityToPosition(selectedQuality);
      sliderPosition.value = withSpring(targetPosition, {
        damping: 15,
        stiffness: 150,
      });
      setDisplayQuality(selectedQuality);
    } else {
      // Default to middle position (quality 5)
      sliderPosition.value = withSpring(qualityToPosition(5), {
        damping: 15,
        stiffness: 150,
      });
      setDisplayQuality(5);
    }
  }, [selectedQuality]);

  // Debounced update for display quality to prevent layout shifts
  const updateDisplayQuality = (newQuality: SleepQualityLevel) => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    updateTimeoutRef.current = setTimeout(() => {
      setDisplayQuality(newQuality);
    }, 100); // 100ms debounce
  };

  // Haptic feedback function with debouncing
  let lastHapticTime = 0;
  const triggerHaptic = () => {
    const now = Date.now();
    if (now - lastHapticTime > 100) { // Debounce haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      lastHapticTime = now;
    }
  };

  // Store initial position for gesture
  const initialPosition = useSharedValue(0);

  // Tap gesture for direct positioning with constraints
  const tapGesture = Gesture.Tap()
    .maxDistance(20) // Prevent accidental taps during drag
    .onStart((event) => {
      const tapY = event.y;
      const newPosition = Math.max(0, Math.min(tapY, SLIDER_HEIGHT));
      const newQuality = positionToQuality(newPosition);

      sliderPosition.value = withSpring(qualityToPosition(newQuality), {
        damping: 15,
        stiffness: 150,
      });

      runOnJS(onQualitySelect)(newQuality);
      runOnJS(triggerHaptic)();
    });

  // Pan gesture handler with vertical-only constraints
  const panGesture = Gesture.Pan()
    .activeOffsetY([-10, 10])      // Only activate on vertical movement ≥10px
    .failOffsetX([-15, 15])        // Fail gesture if horizontal movement >15px
    .minPointers(1)                // Require exactly 1 finger
    .maxPointers(1)                // Prevent multi-touch interference
    .onStart(() => {
      isDragging.value = true;
      initialPosition.value = sliderPosition.value;
    })
    .onUpdate((event) => {
      // Calculate new position based on ONLY vertical translation
      const newPosition = Math.max(0, Math.min(initialPosition.value + event.translationY, SLIDER_HEIGHT));
      sliderPosition.value = newPosition;

      const newQuality = positionToQuality(newPosition);
      runOnJS(onQualitySelect)(newQuality);
      runOnJS(updateDisplayQuality)(newQuality);
      runOnJS(triggerHaptic)();
    })
    .onEnd(() => {
      isDragging.value = false;

      // Snap to nearest quality level
      const currentQuality = positionToQuality(sliderPosition.value);
      const targetPosition = qualityToPosition(currentQuality);

      sliderPosition.value = withSpring(targetPosition, {
        damping: 15,
        stiffness: 150,
      });
    });

  // Combine gestures with exclusive handling to prevent conflicts
  const combinedGesture = Gesture.Exclusive(panGesture, tapGesture);

  // Animated styles with horizontal movement prevention
  const handleStyle = useAnimatedStyle(() => {
    const currentQuality = positionToQuality(sliderPosition.value);
    const currentOption = getCurrentOption(currentQuality);

    return {
      transform: [
        { translateX: 0 }, // Force X position to 0 - prevent horizontal movement
        { translateY: sliderPosition.value - HANDLE_SIZE / 2 }
      ],
      backgroundColor: currentOption.color,
      shadowOpacity: isDragging.value ? 0.3 : 0.2,
      elevation: isDragging.value ? 8 : 4,
    };
  });

  const activeTrackStyle = useAnimatedStyle(() => {
    const currentQuality = positionToQuality(sliderPosition.value);
    const currentOption = getCurrentOption(currentQuality);
    
    return {
      height: sliderPosition.value,
      backgroundColor: currentOption.color,
    };
  });

  // Get current quality for display
  const currentQuality = selectedQuality !== null ? selectedQuality : 5;
  const currentOption = getCurrentOption(currentQuality);

  // Use stable display quality to prevent layout shifts during dragging
  const displayOption = getCurrentOption(displayQuality);
  const isOptimalRange = displayQuality >= 7;

  return (
    <GestureHandlerRootView style={[styles.container, { paddingHorizontal: responsiveConfig.containerPadding }]}>
      <View style={[styles.trackerContainer, { height: SLIDER_HEIGHT + 60 }]}>
        {/* Left Section - Labels and Duration with responsive width */}
        <View style={[styles.leftSection, { width: responsiveConfig.leftSectionWidth }]}>
          <Text
            style={[
              styles.optionLabel,
              styles.selectedLabel,
              { fontSize: responsiveConfig.optionFontSize }
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
            adjustsFontSizeToFit
            minimumFontScale={0.8}
          >
            {displayOption.shortLabel}
          </Text>
          <Text
            style={[
              styles.optionDuration,
              styles.selectedDuration,
              { fontSize: responsiveConfig.labelFontSize }
            ]}
            numberOfLines={1}
          >
            🕐 {displayOption.duration}
          </Text>
          <View style={styles.qualityNumberContainer}>
            <Text style={[styles.qualityNumber, { fontSize: responsiveConfig.optionFontSize }]}>
              {displayQuality}/10
            </Text>
            {isOptimalRange && (
              <Text style={[styles.optimalIndicator, { fontSize: responsiveConfig.labelFontSize }]}>✓</Text>
            )}
          </View>
          <Text
            style={[
              styles.healthImpact,
              isOptimalRange && styles.healthImpactOptimal,
              { fontSize: responsiveConfig.labelFontSize }
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {displayOption.healthImpact}
          </Text>
        </View>

        {/* Center Section - Draggable Slider (Absolutely positioned) */}
        <View style={styles.sliderSection}>
          {/* Single gesture detector for the entire slider area */}
          <GestureDetector gesture={combinedGesture}>
            <View style={[styles.sliderTrack, { height: SLIDER_HEIGHT }]}>
              {/* Background track */}
              <View style={styles.backgroundTrack} />

              {/* Active track (colored portion) */}
              <Animated.View style={[styles.activeTrack, activeTrackStyle]} />

              {/* Draggable handle */}
              <Animated.View style={[styles.sliderHandle, handleStyle]}>
                <View style={styles.handleInner}>
                  <Text style={styles.handleText}>{displayQuality}</Text>
                </View>
              </Animated.View>
            </View>
          </GestureDetector>
        </View>

        {/* Right Section - Emoji Container */}
        <View style={styles.rightSection}>
          <View style={[styles.emojiContainer, { backgroundColor: displayOption.color }]}>
            <Text style={styles.emoji}>{displayOption.emoji}</Text>
          </View>
        </View>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingHorizontal will be set dynamically via responsiveConfig
    paddingVertical: 20,
  },
  trackerContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    // height will be set dynamically via responsiveConfig
    paddingVertical: 30,
    paddingHorizontal: 20,
    overflow: 'hidden', // Prevent any overflow
  },
  leftSection: {
    // width will be set dynamically via responsiveConfig
    paddingRight: 16,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  optionLabel: {
    // fontSize will be set dynamically via responsiveConfig
    fontWeight: '600',
    color: '#5D4037',
    marginBottom: 2,
    width: '100%', // Take full width of container
    textAlign: 'left',
  },
  selectedLabel: {
    color: '#5D4037',
  },
  optionDuration: {
    // fontSize will be set dynamically via responsiveConfig
    color: '#757575',
    marginBottom: 4,
    width: '100%', // Take full width of container
    textAlign: 'left',
  },
  selectedDuration: {
    color: '#757575',
  },
  qualityNumberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  qualityNumber: {
    // fontSize will be set dynamically via responsiveConfig
    fontWeight: 'bold',
    color: '#5D4037',
    marginRight: 4,
  },
  optimalIndicator: {
    // fontSize will be set dynamically via responsiveConfig
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  healthImpact: {
    // fontSize will be set dynamically via responsiveConfig
    color: '#8D6E63',
    fontStyle: 'italic',
    width: '100%', // Take full width of container
    textAlign: 'left',
  },
  healthImpactOptimal: {
    color: '#4CAF50',
    fontWeight: '600',
  },
  sliderSection: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    minWidth: 60, // Fixed width to prevent shifts
    maxWidth: 60,
    marginHorizontal: 20, // Fixed margins
  },
  sliderTrack: {
    width: SLIDER_WIDTH,
    position: 'relative',
    justifyContent: 'flex-start',
    alignSelf: 'center', // Ensure track stays centered
    left: 0, // Prevent horizontal drift
  },
  backgroundTrack: {
    position: 'absolute',
    width: SLIDER_WIDTH,
    height: '100%',
    backgroundColor: TRACK_COLOR,
    borderRadius: SLIDER_WIDTH / 2,
  },
  activeTrack: {
    position: 'absolute',
    width: SLIDER_WIDTH,
    borderRadius: SLIDER_WIDTH / 2,
    top: 0,
  },
  sliderHandle: {
    position: 'absolute',
    width: HANDLE_SIZE,
    height: HANDLE_SIZE,
    borderRadius: HANDLE_SIZE / 2,
    left: -((HANDLE_SIZE - SLIDER_WIDTH) / 2),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  handleInner: {
    width: HANDLE_SIZE - 4,
    height: HANDLE_SIZE - 4,
    borderRadius: (HANDLE_SIZE - 4) / 2,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  handleText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#5D4037',
  },
  rightSection: {
    width: 60,
    minWidth: 60, // Fixed width to prevent shifts
    maxWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emojiContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  emoji: {
    fontSize: 24,
  },
});
