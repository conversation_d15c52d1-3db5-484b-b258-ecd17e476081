import React from 'react';
import { View } from 'react-native';
import { SvgXml } from 'react-native-svg';

interface MoodSvgIconProps {
  svgString: string;
  size?: number;
  onPress?: () => void;
}

export const MoodSvgIcon: React.FC<MoodSvgIconProps> = ({ 
  svgString, 
  size = 31,
  onPress 
}) => {
  return (
    <View style={{ width: size, height: size }}>
      <SvgXml 
        xml={svgString} 
        width={size} 
        height={size}
        onPress={onPress}
      />
    </View>
  );
};
