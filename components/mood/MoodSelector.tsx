import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import {
  DayOfWeek,
  MoodLevel,
  WeeklyMoodData,
  DAYS_OF_WEEK,
  MOOD_ASSET_MAP
} from '../../types/mood';

interface MoodSelectorProps {
  weeklyData: WeeklyMoodData;
  onMoodSelect: (day: DayOfWeek, mood: MoodLevel) => void;
  readOnly?: boolean;
}

export const MoodSelector: React.FC<MoodSelectorProps> = ({
  weeklyData,
  onMoodSelect,
  readOnly = false
}) => {
  return (
    <View style={styles.container}>
      {/* Days row */}
      <View style={styles.daysRow}>
        {DAYS_OF_WEEK.map((day) => (
          <View key={day} style={styles.dayColumn}>
            <Text style={styles.dayLabel}>{day}</Text>
          </View>
        ))}
      </View>

      {/* Mood icons row */}
      <View style={styles.moodRow}>
        {DAYS_OF_WEEK.map((day) => {
          const selectedMood = weeklyData[day].mood;
          return (
            <View key={day} style={styles.moodColumn}>
              <View style={styles.moodIconContainer}>
                {selectedMood ? (
                  readOnly ? (
                    <View style={[styles.moodIcon, styles.selectedMoodIcon]}>
                      <Image
                        source={MOOD_ASSET_MAP[selectedMood]}
                        style={styles.moodImage}
                        resizeMode="contain"
                      />
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={[styles.moodIcon, styles.selectedMoodIcon]}
                      onPress={() => {
                        // Cycle through mood levels
                        const nextMood = selectedMood < 5 ? (selectedMood + 1) as MoodLevel : 1;
                        onMoodSelect(day, nextMood);
                      }}
                      activeOpacity={0.7}
                    >
                      <Image
                        source={MOOD_ASSET_MAP[selectedMood]}
                        style={styles.moodImage}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  )
                ) : (
                  readOnly ? (
                    <View style={[styles.moodIcon, styles.emptyMoodIcon]}>
                      <Text style={styles.emptyMoodText}>?</Text>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={[styles.moodIcon, styles.emptyMoodIcon]}
                      onPress={() => onMoodSelect(day, 3)} // Default to neutral mood
                      activeOpacity={0.7}
                    >
                      <Text style={styles.emptyMoodText}>?</Text>
                    </TouchableOpacity>
                  )
                )}
              </View>
            </View>
          );
        })}
      </View>

      {/* Instructions */}
      {!readOnly && (
        <View style={styles.instructions}>
          <Text style={styles.instructionText}>
            Ketuk emoji untuk mengubah mood atau ketuk &quot;?&quot; untuk memilih mood
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  daysRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  dayColumn: {
    flex: 1,
    alignItems: 'center',
  },
  dayLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#5B3E31',
  },
  moodRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  moodColumn: {
    flex: 1,
    alignItems: 'center',
  },
  moodIconContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moodIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedMoodIcon: {
    backgroundColor: '#F0F9FF',
    borderWidth: 2,
    borderColor: '#2B7EFF',
  },
  emptyMoodIcon: {
    backgroundColor: '#F3F4F6',
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderStyle: 'dashed',
  },
  moodImage: {
    width: 40,
    height: 40,
  },
  emptyMoodText: {
    fontSize: 20,
    color: '#9CA3AF',
    fontWeight: '600',
  },
  instructions: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  instructionText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 16,
  },
});
