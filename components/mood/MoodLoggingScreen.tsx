import React, { useState } from 'react';
import { View, StyleSheet, SafeAreaView, TouchableOpacity, Text } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { MoodLevel, CIRCULAR_MOOD_SECTIONS } from '../../types/mood';
import { MoodQuestionHeader } from './MoodQuestionHeader';
import { MoodEmojiDisplay } from './MoodEmojiDisplay';
import { CircularMoodSelector } from './CircularMoodSelector';
import StandardHeader from '../StandardHeader';
import { HEADER_CONFIGS } from '../../styles/HeaderStyles';
import { StandardContinueButton } from '../StandardContinueButton';
import { useMoodTrackerResponsive } from '../../hooks/useResponsiveDimensions';

interface MoodLoggingScreenProps {
  initialMood?: MoodLevel | null;
  onComplete?: (mood: MoodLevel) => void;
  onCancel?: () => void;
  showProgressHeader?: boolean;
  currentStep?: number;
  totalSteps?: number;
}

export const MoodLoggingScreen: React.FC<MoodLoggingScreenProps> = ({
  initialMood = null,
  onComplete,
  onCancel,
  showProgressHeader = false,
  currentStep = 1,
  totalSteps = 4
}) => {
  const [selectedMood, setSelectedMood] = useState<MoodLevel | null>(initialMood);

  // Get responsive configuration
  const responsiveConfig = useMoodTrackerResponsive();

  const handleMoodSelect = (mood: MoodLevel) => {
    setSelectedMood(mood);
  };

  const handleComplete = () => {
    if (selectedMood && onComplete) {
      onComplete(selectedMood);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const currentMoodSection = selectedMood
    ? CIRCULAR_MOOD_SECTIONS.find(section => section.id === selectedMood)
    : null;

  // Progress header component
  const renderProgressHeader = () => (
    <View style={styles.progressWrapper}>
      <View style={styles.progressContainer}>
        <View style={styles.backButton}>
          <TouchableOpacity onPress={handleCancel}>
            <Ionicons name="chevron-back" size={24} color="#5B3E31" />
          </TouchableOpacity>
        </View>

        <View style={styles.progressTracker}>
          <Text style={styles.progressText}>{currentStep}/{totalSteps}</Text>
        </View>

        <View style={styles.spacer} />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Background gradient */}
      <LinearGradient
        colors={['#C6FFDD', '#FFFFFF']}
        locations={[0, 0.3]}
        style={styles.backgroundGradient}
      />
      
      {/* Conditional header rendering */}
      {showProgressHeader ? renderProgressHeader() : (
        <StandardHeader
          title={HEADER_CONFIGS.moodLogging.title}
          iconName={HEADER_CONFIGS.moodLogging.iconName}
          iconColor={HEADER_CONFIGS.moodLogging.iconColor}
          leftElement={
            <TouchableOpacity onPress={handleCancel}>
              <Ionicons name="chevron-back" size={24} color="#5B3E31" />
            </TouchableOpacity>
          }
        />
      )}

      {/* Main content with responsive padding */}
      <View style={[styles.content, { paddingHorizontal: responsiveConfig.containerPadding }]}>
        {/* Question section */}
        <MoodQuestionHeader />

        {/* Emoji feedback section */}
        <MoodEmojiDisplay
          selectedMood={selectedMood}
          responseText={currentMoodSection?.responseText}
        />

        {/* Circular mood selector section */}
        <View style={[styles.selectorSection, { paddingVertical: responsiveConfig.sectionMargin }]}>
          <CircularMoodSelector
            selectedMood={selectedMood}
            onMoodSelect={handleMoodSelect}
          />
        </View>

        {/* Action button */}
        <View style={[styles.actionSection, {
          paddingHorizontal: responsiveConfig.containerPadding,
          paddingBottom: responsiveConfig.buttonMargin
        }]}>
          <StandardContinueButton
            onPress={handleComplete}
            disabled={!selectedMood}
            text="Lanjut"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  // Progress header styles (matching DailyMoodTracker)
  progressWrapper: {
    paddingHorizontal: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingVertical: 10,
  },
  backButton: {
    width: 40,
  },
  progressTracker: {
    backgroundColor: '#FFEFCF',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  progressText: {
    color: '#5B3E31',
    fontWeight: '600',
    fontSize: 14,
  },
  spacer: {
    width: 40,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },

  content: {
    flex: 1,
    // paddingHorizontal will be set dynamically via responsiveConfig
  },
  selectorSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // paddingVertical will be set dynamically via responsiveConfig
  },
  actionSection: {
    paddingTop: 20,
    // paddingHorizontal and paddingBottom will be set dynamically via responsiveConfig
  },

});
