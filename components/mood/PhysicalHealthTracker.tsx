import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { 
  DayOfWeek, 
  WeeklyMoodData, 
  DAYS_OF_WEEK 
} from '../../types/mood';

interface PhysicalHealthTrackerProps {
  weeklyData: WeeklyMoodData;
  onHealthToggle: (day: DayOfWeek, isHealthy: boolean) => void;
  readOnly?: boolean;
}

export const PhysicalHealthTracker: React.FC<PhysicalHealthTrackerProps> = ({
  weeklyData,
  onHealthToggle,
  readOnly = false
}) => {
  return (
    <View style={styles.container}>
      {/* Days row */}
      <View style={styles.daysRow}>
        {DAYS_OF_WEEK.map((day) => (
          <View key={day} style={styles.dayColumn}>
            <Text style={styles.dayLabel}>{day}</Text>
          </View>
        ))}
      </View>

      {/* Health status row */}
      <View style={styles.healthRow}>
        {DAYS_OF_WEEK.map((day) => {
          // Boolean logic: true = healthy (sehat), false = sick (sakit)
          const isHealthy = weeklyData[day].physicalHealth;
          return (
            <View key={day} style={styles.healthColumn}>
              {readOnly ? (
                <View
                  style={[
                    styles.healthIndicator,
                    isHealthy === true && styles.healthyIndicator,
                    isHealthy === false && styles.sickIndicator,
                    isHealthy === null && styles.emptyIndicator,
                  ]}
                >
                  {isHealthy === true && (
                    <Ionicons
                      name="checkmark"
                      size={24}
                      color="#10B981"
                    />
                  )}
                  {isHealthy === false && (
                    <Ionicons
                      name="close"
                      size={24}
                      color="#EF4444"
                    />
                  )}
                  {isHealthy === null && (
                    <View style={styles.emptyDot} />
                  )}
                </View>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.healthIndicator,
                    isHealthy === true && styles.healthyIndicator,
                    isHealthy === false && styles.sickIndicator,
                    isHealthy === null && styles.emptyIndicator,
                  ]}
                  onPress={() => {
                    // Cycle through: null -> true -> false -> null
                    if (isHealthy === null) {
                      onHealthToggle(day, true);
                    } else if (isHealthy === true) {
                      onHealthToggle(day, false);
                    } else {
                      onHealthToggle(day, true); // Reset to healthy
                    }
                  }}
                  activeOpacity={0.7}
                >
                  {isHealthy === true && (
                    <Ionicons
                      name="checkmark"
                      size={24}
                      color="#10B981"
                    />
                  )}
                  {isHealthy === false && (
                    <Ionicons
                      name="close"
                      size={24}
                      color="#EF4444"
                    />
                  )}
                  {isHealthy === null && (
                    <View style={styles.emptyDot} />
                  )}
                </TouchableOpacity>
              )}
            </View>
          );
        })}
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendIndicator, styles.healthyIndicator]}>
            <Ionicons name="checkmark" size={16} color="#10B981" />
          </View>
          <Text style={styles.legendText}>Sehat</Text>
        </View>
        
        <View style={styles.legendItem}>
          <View style={[styles.legendIndicator, styles.sickIndicator]}>
            <Ionicons name="close" size={16} color="#EF4444" />
          </View>
          <Text style={styles.legendText}>Sakit</Text>
        </View>
        
        <View style={styles.legendItem}>
          <View style={[styles.legendIndicator, styles.emptyIndicator]}>
            <View style={styles.emptyDot} />
          </View>
          <Text style={styles.legendText}>Belum diisi</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  daysRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  dayColumn: {
    flex: 1,
    alignItems: 'center',
  },
  dayLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#5B3E31',
  },
  healthRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  healthColumn: {
    flex: 1,
    alignItems: 'center',
  },
  healthIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  healthyIndicator: {
    backgroundColor: '#ECFDF5',
    borderColor: '#10B981',
  },
  sickIndicator: {
    backgroundColor: '#FEF2F2',
    borderColor: '#EF4444',
  },
  emptyIndicator: {
    backgroundColor: '#F9FAFB',
    borderColor: '#D1D5DB',
    borderStyle: 'dashed',
  },
  emptyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#9CA3AF',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
});
