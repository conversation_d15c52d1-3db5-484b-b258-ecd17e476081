import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform, SafeAreaView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { StandardContinueButton } from '../StandardContinueButton';
import { DraggableSleepTracker } from './DraggableSleepTracker';
import {
  DayOfWeek,
  DailyMoodData,
  MOOD_ASSET_MAP,
  STRESS_LEVELS,
  MOOD_LEVEL_COLORS,
  getMoodResponseText,
  SLEEP_QUALITY_LEVELS,
  SLEEP_QUALITY_LABELS,
  SLEEP_QUALITY_TIME_LABELS,
  getSleepQualityMoodAsset,
  DAILY_FEELING_LEVELS,
  DAILY_FEELING_COLORS,
  DAILY_FEELING_TEXTS,
  getDailyFeelingText,
  DailyFeelingLevel
} from '../../types/mood';
import { SemicircularMoodPicker } from './SemicircularMoodPicker';
import { MoodLoggingScreen } from './MoodLoggingScreen';
import { useMoodTrackerResponsive } from '../../hooks/useResponsiveDimensions';

interface DailyMoodTrackerProps {
  initialData: DailyMoodData;
  onComplete: (data: DailyMoodData) => void;
  onCancel: () => void;
  currentDay: DayOfWeek;
}

export const DailyMoodTracker: React.FC<DailyMoodTrackerProps> = ({
  initialData,
  onComplete,
  onCancel,
  currentDay
}) => {
  // Current step state (1-4)
  const [currentStep, setCurrentStep] = useState(1);

  // Temporary data state to collect responses
  const [tempData, setTempData] = useState<DailyMoodData>({...initialData});

  // Get responsive configuration
  const responsiveConfig = useMoodTrackerResponsive();



  // Helper function to update specific field
  const updateField = (field: keyof DailyMoodData, value: any) => {
    setTempData(prev => ({
      ...prev,
      [field]: value
    }));
  };



  // Validation logic for each step
  const canProceedStep2 = tempData.physicalHealth !== null;
  const canProceedStep3 = tempData.sleepQuality !== null;
  const canProceedStep4 = tempData.dailyFeeling !== null;

  // Check if current step can proceed
  const canProceed = () => {
    switch (currentStep) {
      case 2:
        return canProceedStep2;
      case 3:
        return canProceedStep3;
      case 4:
        return canProceedStep4;
      default:
        return true;
    }
  };

  // Handle next step navigation
  const handleNext = () => {
    if (!canProceed()) return;

    if (currentStep < 4) {
      setCurrentStep(prev => prev + 1);
    } else {
      // We're at the last step, complete the process
      onComplete(tempData);
    }
  };

  // Handle previous step navigation
  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    } else {
      // We're at the first step, cancel the process
      onCancel();
    }
  };

  // Enhanced Mood selection step (Step 1) - Using new MoodLoggingScreen with progress header
  const renderMoodStep = () => {
    return (
      <MoodLoggingScreen
        initialMood={tempData.mood}
        onComplete={(mood) => {
          updateField('mood', mood);
          handleNext();
        }}
        onCancel={handleBack}
        showProgressHeader={true}
        currentStep={1}
        totalSteps={4}
      />
    );
  };

  // Physical health step (Step 2)
  const renderPhysicalHealthStep = () => {
    // IMPORTANT: Boolean logic follows official schema:
    // true = healthy (sehat), false = sick (sakit)
    // "Ya, sakit" → false, "Tidak, sehat" → true
    console.log('Current physicalHealth value:', tempData.physicalHealth);

    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.questionText, { fontSize: responsiveConfig.questionFontSize }]}>
          Apakah Anda merasakan sakit fisik?
        </Text>

        {/* Responsive Options */}
        <View style={[styles.optionsContainer, { gap: responsiveConfig.sectionMargin / 2 }]}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              {
                minHeight: responsiveConfig.buttonHeight,
                padding: responsiveConfig.containerPadding / 2
              },
              tempData.physicalHealth === false && styles.selectedButton
            ]}
            onPress={() => updateField('physicalHealth', false)}
          >
            <View style={styles.optionContent}>
              <Ionicons
                name="checkmark-circle"
                size={Math.max(24, responsiveConfig.optionFontSize + 8)}
                color={tempData.physicalHealth === false ? "#FFFFFF" : "#D1D5DB"}
              />
              <View style={styles.optionTextContainer}>
                <Text style={[
                  styles.optionText,
                  { fontSize: responsiveConfig.optionFontSize },
                  tempData.physicalHealth === false && styles.selectedButtonText
                ]}>
                  Ya, di satu tempat atau lebih
                </Text>
                <Text style={[
                  styles.optionSubtext,
                  { fontSize: responsiveConfig.labelFontSize },
                  tempData.physicalHealth === false && styles.selectedButtonText
                ]}>
                  Saya merasakan sakit fisik di berbagai tempat di tubuh saya
                </Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.optionButton,
              {
                minHeight: responsiveConfig.buttonHeight,
                padding: responsiveConfig.containerPadding / 2
              },
              tempData.physicalHealth === true && styles.selectedButton
            ]}
            onPress={() => updateField('physicalHealth', true)}
          >
            <View style={styles.optionContent}>
              <Ionicons
                name="close-circle"
                size={Math.max(24, responsiveConfig.optionFontSize + 8)}
                color={tempData.physicalHealth === true ? "#FFFFFF" : "#D1D5DB"}
              />
              <View style={styles.optionTextContainer}>
                <Text style={[
                  styles.optionText,
                  { fontSize: responsiveConfig.optionFontSize },
                  tempData.physicalHealth === true && styles.selectedButtonText
                ]}>
                  Tidak Ada Rasa Sakit Fisik
                </Text>
                <Text style={[
                  styles.optionSubtext,
                  { fontSize: responsiveConfig.labelFontSize },
                  tempData.physicalHealth === true && styles.selectedButtonText
                ]}>
                  Saya sama sekali tidak merasakan sakit fisik di tubuh saya :)
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Sleep quality step (Step 3) - Responsive draggable slider design
  const renderSleepQualityStep = () => {
    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.questionText, { fontSize: responsiveConfig.questionFontSize }]}>
          Bagaimana kualitas tidur Anda?
        </Text>

        <DraggableSleepTracker
          selectedQuality={tempData.sleepQuality}
          onQualitySelect={(quality) => updateField('sleepQuality', quality)}
        />
      </View>
    );
  };

  // Daily feeling step (Step 4) - Responsive design with adaptive sizing
  const renderDailyFeelingStep = () => {
    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.questionText, { fontSize: responsiveConfig.questionFontSize }]}>
          Gimana perasaan kamu hari ini?
        </Text>

        {/* Responsive large number display in center */}
        <View style={[
          styles.dailyFeelingCentralDisplay,
          {
            width: responsiveConfig.centralDisplaySize,
            height: responsiveConfig.centralDisplaySize,
            borderRadius: responsiveConfig.centralDisplaySize / 2,
            marginVertical: responsiveConfig.sectionMargin,
          }
        ]}>
          <Text style={[
            styles.dailyFeelingCentralNumber,
            {
              fontSize: responsiveConfig.centralNumberFontSize,
              color: tempData.dailyFeeling ? DAILY_FEELING_COLORS[tempData.dailyFeeling] : '#5B3E31'
            }
          ]}>
            {tempData.dailyFeeling || '?'}
          </Text>
        </View>

        {/* Responsive horizontal selector row */}
        <View style={[
          styles.dailyFeelingSelector,
          {
            width: responsiveConfig.selectorWidth,
            marginVertical: responsiveConfig.sectionMargin,
          }
        ]}>
          {DAILY_FEELING_LEVELS.map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.dailyFeelingCircle,
                {
                  width: Math.max(responsiveConfig.minTouchTarget, 50),
                  height: Math.max(responsiveConfig.minTouchTarget, 50),
                  borderRadius: Math.max(responsiveConfig.minTouchTarget, 50) / 2,
                },
                tempData.dailyFeeling === level && [
                  styles.selectedDailyFeelingCircle,
                  { backgroundColor: DAILY_FEELING_COLORS[level] }
                ]
              ]}
              onPress={() => updateField('dailyFeeling', level)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.dailyFeelingCircleText,
                { fontSize: responsiveConfig.optionFontSize },
                tempData.dailyFeeling === level && styles.selectedDailyFeelingCircleText
              ]}>
                {level}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Response text at bottom */}
        {tempData.dailyFeeling && (
          <Text style={[
            styles.dailyFeelingResponseText,
            { fontSize: responsiveConfig.optionFontSize }
          ]}>
            {getDailyFeelingText(tempData.dailyFeeling)}
          </Text>
        )}
      </View>
    );
  };

  // Render the current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderMoodStep();
      case 2:
        return renderPhysicalHealthStep();
      case 3:
        return renderSleepQualityStep();
      case 4:
        return renderDailyFeelingStep();
      default:
        return null;
    }
  };

  // For step 1 (mood selection), render the full-screen MoodLoggingScreen
  if (currentStep === 1) {
    return renderMoodStep();
  }

  // For other steps, use responsive layout with ScrollView
  return (
    <SafeAreaView style={styles.container}>
      {/* Background gradient - matching step 1 exactly */}
      <LinearGradient
        colors={['#C6FFDD', '#FFFFFF']}
        locations={[0, 0.3]}
        style={styles.backgroundGradient}
      />

      {/* Progress indicator - direct child of SafeAreaView to match step 1 exactly */}
      <View style={styles.progressWrapper}>
        <View style={styles.progressContainer}>
          <View style={styles.backButton}>
            <TouchableOpacity onPress={handleBack}>
              <Ionicons name="chevron-back" size={24} color="#5B3E31" />
            </TouchableOpacity>
          </View>

          <View style={styles.progressTracker}>
            <Text style={styles.progressText}>{currentStep}/4</Text>
          </View>

          <View style={styles.spacer} />
        </View>
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >

      {/* Scrollable content area */}
      <ScrollView
        style={styles.scrollContent}
        contentContainerStyle={[styles.scrollContentContainer, { paddingHorizontal: responsiveConfig.containerPadding }]}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {/* Current step content */}
        {renderCurrentStep()}
      </ScrollView>

      {/* Fixed continue button at bottom */}
      <View style={[styles.buttonContainer, {
        paddingHorizontal: responsiveConfig.containerPadding,
        paddingBottom: responsiveConfig.buttonMargin
      }]}>
        <StandardContinueButton
          onPress={handleNext}
          disabled={!canProceed()}
          text={currentStep === 4 ? "Selesai" : "Lanjut"}
        />
      </View>
    </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardContainer: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  // Progress header styles - matching step 1 exactly
  progressWrapper: {
    paddingHorizontal: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    marginBottom: 20, // Changed from 10 to 20 to match step 1
  },
  backButton: {
    width: 40,
  },
  progressTracker: {
    backgroundColor: '#FFEFCF',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  progressText: {
    color: '#5B3E31',
    fontWeight: '600',
    fontSize: 14, // Fixed font size to match step 1 exactly
  },
  spacer: {
    width: 40,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  stepContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    minHeight: 400, // Ensure minimum content height
  },
  questionText: {
    // fontSize will be set dynamically via responsiveConfig
    fontWeight: '700',
    color: '#5B3E31',
    textAlign: 'center',
    marginBottom: 20,
  },
  responseText: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
  moodSelectorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  centralMoodDisplay: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  selectedMoodContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F0F9FF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#2B7EFF',
  },
  moodImage: {
    width: 80,
    height: 80,
  },

  optionsContainer: {
    width: '100%',
    marginVertical: 20,
    // gap will be set dynamically via responsiveConfig
  },
  optionButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10, // User's preferred corner radius
    borderWidth: 1,
    borderColor: '#E5E7EB',
    // padding, minHeight will be set dynamically via responsiveConfig
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  selectedButton: {
    backgroundColor: '#9BB168', // Solid green background when selected
    borderColor: '#9BB168',
  },
  selectedButtonText: {
    color: '#FFFFFF', // White text when selected
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  optionText: {
    // fontSize will be set dynamically via responsiveConfig
    fontWeight: '600',
    color: '#454459', // User's preferred text color
    marginVertical: 8,
  },
  optionSubtext: {
    // fontSize will be set dynamically via responsiveConfig
    color: '#6B7280',
  },


  selectedText: {
    color: '#5B3E31',
    fontWeight: '600',
  },
  selectedHoursText: {
    color: '#5B3E31',
  },
  stressLevelText: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
  stressLevelContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#F59E0B',
    marginVertical: 30,
  },
  stressLevelNumber: {
    fontSize: 72,
    fontWeight: '700',
    color: '#F59E0B',
  },
  stressLevelSlider: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 20,
  },
  stressLevelOption: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  selectedStressLevelOption: {
    backgroundColor: '#F59E0B',
    borderColor: '#F59E0B',
  },
  stressLevelOptionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
  },
  // Daily feeling step styles - Responsive design
  dailyFeelingCentralDisplay: {
    // width, height, borderRadius, marginVertical will be set dynamically via responsiveConfig
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dailyFeelingCentralNumber: {
    // fontSize will be set dynamically via responsiveConfig
    fontWeight: '700',
    // color will be set dynamically based on selection
  },
  dailyFeelingSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // width and marginVertical will be set dynamically via responsiveConfig
  },
  dailyFeelingCircle: {
    // width, height, borderRadius will be set dynamically via responsiveConfig
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedDailyFeelingCircle: {
    borderColor: 'transparent',
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  dailyFeelingCircleText: {
    // fontSize will be set dynamically via responsiveConfig
    fontWeight: '600',
    color: '#5B3E31',
  },
  selectedDailyFeelingCircleText: {
    color: '#FFFFFF',
  },
  dailyFeelingResponseText: {
    // fontSize will be set dynamically via responsiveConfig
    color: '#6B7280',
    marginTop: 20,
    textAlign: 'center',
    fontWeight: '500',
  },
  nextButton: {
    backgroundColor: '#8B4513', // Solid brown to match design
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignSelf: 'center',
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
    marginRight: 8,
  },
  buttonContainer: {
    paddingTop: 20,
    // paddingHorizontal and paddingBottom will be set dynamically via responsiveConfig
  },
});
