import React from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMoodTrends, useMoodHistory } from '../../hooks/useMoodQuery';
import type { MoodTrend } from '../../types/mood';

interface MoodAnalyticsProps {
  startDate: string;
  endDate: string;
  period?: 'week' | 'month';
  count?: number;
}

export const MoodAnalytics: React.FC<MoodAnalyticsProps> = ({
  startDate,
  endDate,
  period = 'week',
  count = 4
}) => {
  // Fetch mood trends
  const { 
    data: trends, 
    isLoading: trendsLoading, 
    error: trendsError 
  } = useMoodTrends(period, count);

  // Fetch mood history for detailed analysis
  const { 
    data: history, 
    isLoading: historyLoading, 
    error: historyError 
  } = useMoodHistory(startDate, endDate);

  const isLoading = trendsLoading || historyLoading;
  const hasError = trendsError || historyError;

  // Calculate insights from the data
  const calculateInsights = (trends: MoodTrend[]) => {
    if (!trends || trends.length === 0) return [];

    const insights: string[] = [];
    const latestTrend = trends[trends.length - 1];
    const previousTrend = trends[trends.length - 2];

    if (latestTrend && previousTrend) {
      // Mood trend analysis
      const moodChange = latestTrend.avgMood - previousTrend.avgMood;
      if (moodChange > 0.5) {
        insights.push('🌟 Mood Anda menunjukkan peningkatan yang baik!');
      } else if (moodChange < -0.5) {
        insights.push('💙 Mood Anda sedikit menurun, jaga kesehatan mental ya');
      }

      // Sleep analysis
      const sleepChange = latestTrend.avgSleepHours - previousTrend.avgSleepHours;
      if (sleepChange > 1) {
        insights.push('😴 Jam tidur Anda meningkat, pertahankan!');
      } else if (sleepChange < -1) {
        insights.push('⏰ Coba tidur lebih awal untuk kesehatan yang lebih baik');
      }

      // Stress analysis
      const stressChange = latestTrend.avgStressLevel - previousTrend.avgStressLevel;
      if (stressChange < -0.5) {
        insights.push('🧘 Level stress Anda menurun, bagus sekali!');
      } else if (stressChange > 0.5) {
        insights.push('🌱 Coba teknik relaksasi untuk mengurangi stress');
      }

      // Health analysis
      const healthyDaysRatio = latestTrend.healthyDays / latestTrend.totalEntries;
      if (healthyDaysRatio > 0.8) {
        insights.push('💪 Kesehatan fisik Anda sangat baik!');
      } else if (healthyDaysRatio < 0.5) {
        insights.push('🏥 Jaga kesehatan fisik dengan istirahat yang cukup');
      }
    }

    return insights;
  };

  const insights = trends ? calculateInsights(trends) : [];

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4EAF64" />
        <Text style={styles.loadingText}>Analyzing your mood data...</Text>
      </View>
    );
  }

  if (hasError) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="analytics-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>Unable to load analytics</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Ionicons name="analytics" size={24} color="#4EAF64" />
        <Text style={styles.title}>Mood Analytics</Text>
      </View>

      {/* Summary Cards */}
      {trends && trends.length > 0 && (
        <View style={styles.summaryContainer}>
          <Text style={styles.sectionTitle}>Summary</Text>
          <View style={styles.cardsRow}>
            <View style={styles.summaryCard}>
              <Text style={styles.cardValue}>
                {trends[trends.length - 1]?.avgMood.toFixed(1) || '0.0'}
              </Text>
              <Text style={styles.cardLabel}>Avg Mood</Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={styles.cardValue}>
                {trends[trends.length - 1]?.avgSleepHours.toFixed(1) || '0.0'}h
              </Text>
              <Text style={styles.cardLabel}>Avg Sleep</Text>
            </View>
            <View style={styles.summaryCard}>
              <Text style={styles.cardValue}>
                {trends[trends.length - 1]?.avgStressLevel.toFixed(1) || '0.0'}
              </Text>
              <Text style={styles.cardLabel}>Avg Stress</Text>
            </View>
          </View>
        </View>
      )}

      {/* Insights */}
      {insights.length > 0 && (
        <View style={styles.insightsContainer}>
          <Text style={styles.sectionTitle}>Insights</Text>
          {insights.map((insight, index) => (
            <View key={index} style={styles.insightItem}>
              <Text style={styles.insightText}>{insight}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Trends Overview */}
      {trends && trends.length > 0 && (
        <View style={styles.trendsContainer}>
          <Text style={styles.sectionTitle}>Trends Overview</Text>
          {trends.map((trend, index) => (
            <View key={index} style={styles.trendItem}>
              <Text style={styles.trendPeriod}>
                {period === 'week' ? 'Week' : 'Month'} {index + 1}
              </Text>
              <View style={styles.trendMetrics}>
                <Text style={styles.trendMetric}>
                  Mood: {trend.avgMood.toFixed(1)}/5
                </Text>
                <Text style={styles.trendMetric}>
                  Sleep: {trend.avgSleepHours.toFixed(1)}h
                </Text>
                <Text style={styles.trendMetric}>
                  Stress: {trend.avgStressLevel.toFixed(1)}/5
                </Text>
                <Text style={styles.trendMetric}>
                  Healthy Days: {trend.healthyDays}/{trend.totalEntries}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}

      {/* Data Summary */}
      {history && history.length > 0 && (
        <View style={styles.dataContainer}>
          <Text style={styles.sectionTitle}>Data Summary</Text>
          <Text style={styles.dataText}>
            Total entries: {history.length}
          </Text>
          <Text style={styles.dataText}>
            Date range: {startDate} to {endDate}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    marginLeft: 8,
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  summaryContainer: {
    padding: 20,
    paddingTop: 10,
  },
  cardsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  cardValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#4EAF64',
  },
  cardLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  insightsContainer: {
    padding: 20,
    paddingTop: 10,
  },
  insightItem: {
    backgroundColor: '#F0F9FF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  insightText: {
    fontSize: 14,
    color: '#1F2937',
    lineHeight: 20,
  },
  trendsContainer: {
    padding: 20,
    paddingTop: 10,
  },
  trendItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  trendPeriod: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  trendMetrics: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  trendMetric: {
    fontSize: 12,
    color: '#6B7280',
    marginRight: 12,
    marginBottom: 4,
  },
  dataContainer: {
    padding: 20,
    paddingTop: 10,
  },
  dataText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
});
