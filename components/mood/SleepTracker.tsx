import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, TextInput, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import {
  DayOfWeek,
  WeeklyMoodData,
  SleepQualityLevel,
  DAYS_OF_WEEK
} from '../../types/mood';

interface SleepTrackerProps {
  weeklyData: WeeklyMoodData;
  onSleepQualityChange: (day: DayOfWeek, quality: SleepQualityLevel) => void;
  readOnly?: boolean;
}

export const SleepTracker: React.FC<SleepTrackerProps> = ({
  weeklyData,
  onSleepQualityChange,
  readOnly = false
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDay, setSelectedDay] = useState<DayOfWeek | null>(null);
  const [inputQuality, setInputQuality] = useState('');

  // Fixed bar height and max quality for consistent scaling
  const maxQuality = 10; // Fixed scale to 10 quality levels
  const barHeight = 160; // Fixed bar height - increased for better visibility

  const handleBarPress = (day: DayOfWeek) => {
    if (readOnly) return; // Don't open modal in read-only mode
    setSelectedDay(day);
    setInputQuality(weeklyData[day].sleepQuality?.toString() || '');
    setModalVisible(true);
  };

  const handleSaveQuality = () => {
    if (!selectedDay) return;

    const quality = parseInt(inputQuality);
    if (isNaN(quality) || quality < 0 || quality > 10) {
      Alert.alert('Error', 'Masukkan kualitas tidur yang valid (0-10)');
      return;
    }

    onSleepQualityChange(selectedDay, quality as SleepQualityLevel);
    setModalVisible(false);
    setSelectedDay(null);
    setInputQuality('');
  };

  const getFilledHeight = (quality: number | null): number => {
    if (!quality) return 0;
    const calculatedHeight = (quality / maxQuality) * barHeight;
    // Ensure minimum height of 32px to accommodate the number text
    const minHeight = 32;
    return Math.min(Math.max(calculatedHeight, minHeight), barHeight);
  };

  return (
    <View style={styles.container}>
      {/* Chart */}
      <View style={styles.chartContainer}>
        <View style={styles.chartArea}>
          {DAYS_OF_WEEK.map((day) => {
            const quality = weeklyData[day].sleepQuality;
            const filledHeight = getFilledHeight(quality);

            return (
              <View key={day} style={styles.dayBarContainer}>
                {/* Bar with base and filled portion */}
                {readOnly ? (
                  <View
                    style={[styles.barWrapper, { height: barHeight }]}
                  >
                    {/* Base bar (always full height) */}
                    <View style={styles.baseBar}>
                      {quality === null && (
                        <Ionicons name="add" size={16} color="#454459" />
                      )}
                    </View>

                    {/* Filled portion (gradient overlay) */}
                    {quality !== null && quality > 0 && (
                      <LinearGradient
                        colors={['#FFD572', '#FEBD38']}
                        locations={[0, 1]}
                        style={[styles.filledBar, {
                          height: filledHeight,
                          bottom: 0,
                          position: 'absolute'
                        }]}
                      >
                        {/* Quality label positioned inside the filled portion */}
                        <Text style={styles.hoursLabel}>{quality}</Text>
                      </LinearGradient>
                    )}
                  </View>
                ) : (
                  <TouchableOpacity
                    style={[styles.barWrapper, { height: barHeight }]}
                    onPress={() => handleBarPress(day)}
                    activeOpacity={0.7}
                  >
                    {/* Base bar (always full height) */}
                    <View style={styles.baseBar}>
                      {quality === null && (
                        <Ionicons name="add" size={16} color="#454459" />
                      )}
                    </View>

                    {/* Filled portion (gradient overlay) */}
                    {quality !== null && quality > 0 && (
                      <LinearGradient
                        colors={['#FFD572', '#FEBD38']}
                        locations={[0, 1]}
                        style={[styles.filledBar, {
                          height: filledHeight,
                          bottom: 0,
                          position: 'absolute'
                        }]}
                      >
                        {/* Quality label positioned inside the filled portion */}
                        <Text style={styles.hoursLabel}>{quality}</Text>
                      </LinearGradient>
                    )}
                  </TouchableOpacity>
                )}
              </View>
            );
          })}
        </View>
        
        {/* Y-axis labels */}
        <View style={styles.yAxisLabels}>
          {[10, 8, 6, 4, 2, 0].map(hour => (
            <Text key={hour} style={styles.yAxisLabel}>{hour}</Text>
          ))}
        </View>
      </View>

      {/* Days row */}
      <View style={styles.daysRow}>
        {DAYS_OF_WEEK.map((day) => (
          <View key={day} style={styles.dayColumn}>
            <Text style={styles.dayLabel}>{day}</Text>
          </View>
        ))}
      </View>

      {/* Input Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Kualitas Tidur - {selectedDay}
            </Text>

            <Text style={styles.modalSubtitle}>
              Bagaimana kualitas tidur kamu? (0-10)
            </Text>

            <TextInput
              style={styles.input}
              value={inputQuality}
              onChangeText={setInputQuality}
              placeholder="Contoh: 7"
              keyboardType="numeric"
              autoFocus
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Batal</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleSaveQuality}
              >
                <Text style={styles.saveButtonText}>Simpan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  chartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 16,
    paddingLeft: 30,
  },
  chartArea: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 160,
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    height: 160,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 8,
  },
  yAxisLabel: {
    fontSize: 12,
    color: '#454459',
    fontWeight: '500',
  },
  dayBarContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 35,
    position: 'relative',
  },
  barWrapper: {
    width: 28,
    position: 'relative',
    justifyContent: 'flex-end',
  },
  baseBar: {
    width: 28,
    height: '100%',
    backgroundColor: '#A08CFB',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filledBar: {
    width: 28,
    borderRadius: 10,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: 4,
  },
  hoursLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#454459',
  },
  daysRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 30,
  },
  dayColumn: {
    width: 35,
    alignItems: 'center',
  },
  dayLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#5B3E31',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '80%',
    maxWidth: 300,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
  },
  saveButton: {
    backgroundColor: '#2B7EFF',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
