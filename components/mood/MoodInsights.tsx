import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import type { MoodTrend } from '../../types/mood';

interface MoodInsightsProps {
  trends: MoodTrend[];
  period?: 'week' | 'month';
}

interface Insight {
  type: 'positive' | 'neutral' | 'warning';
  icon: string;
  title: string;
  description: string;
}

export const MoodInsights: React.FC<MoodInsightsProps> = ({
  trends,
  period = 'week'
}) => {
  // Generate insights from trend data
  const generateInsights = (trends: MoodTrend[]): Insight[] => {
    if (!trends || trends.length < 2) {
      return [{
        type: 'neutral',
        icon: 'information-circle',
        title: 'Butuh Lebih Banyak Data',
        description: 'Terus isi mood tracker untuk mendapatkan insights yang lebih akurat.'
      }];
    }

    const insights: Insight[] = [];
    const latest = trends[trends.length - 1];
    const previous = trends[trends.length - 2];

    // Mood trend analysis
    const moodChange = latest.avgMood - previous.avgMood;
    if (moodChange > 0.5) {
      insights.push({
        type: 'positive',
        icon: 'happy',
        title: 'Mood Membaik! 🌟',
        description: `Mood Anda meningkat ${moodChange.toFixed(1)} poin. Pertahankan aktivitas positif yang sedang Anda lakukan!`
      });
    } else if (moodChange < -0.5) {
      insights.push({
        type: 'warning',
        icon: 'heart',
        title: 'Mood Menurun 💙',
        description: `Mood Anda turun ${Math.abs(moodChange).toFixed(1)} poin. Coba lakukan aktivitas yang Anda sukai atau berbicara dengan orang terdekat.`
      });
    } else {
      insights.push({
        type: 'neutral',
        icon: 'trending-up',
        title: 'Mood Stabil',
        description: 'Mood Anda cukup stabil. Terus jaga keseimbangan hidup Anda.'
      });
    }

    // Sleep analysis
    const sleepChange = latest.avgSleepHours - previous.avgSleepHours;
    if (latest.avgSleepHours >= 7 && latest.avgSleepHours <= 9) {
      insights.push({
        type: 'positive',
        icon: 'moon',
        title: 'Tidur Berkualitas 😴',
        description: `Rata-rata tidur ${latest.avgSleepHours.toFixed(1)} jam per hari sangat ideal untuk kesehatan.`
      });
    } else if (latest.avgSleepHours < 6) {
      insights.push({
        type: 'warning',
        icon: 'time',
        title: 'Kurang Tidur ⏰',
        description: `Rata-rata tidur ${latest.avgSleepHours.toFixed(1)} jam kurang dari kebutuhan. Coba tidur lebih awal.`
      });
    } else if (latest.avgSleepHours > 10) {
      insights.push({
        type: 'neutral',
        icon: 'bed',
        title: 'Tidur Berlebihan',
        description: `Rata-rata tidur ${latest.avgSleepHours.toFixed(1)} jam mungkin terlalu banyak. Coba atur jadwal tidur yang konsisten.`
      });
    }

    // Stress analysis
    const stressChange = latest.avgStressLevel - previous.avgStressLevel;
    if (latest.avgStressLevel <= 2) {
      insights.push({
        type: 'positive',
        icon: 'leaf',
        title: 'Stress Rendah 🧘',
        description: `Level stress ${latest.avgStressLevel.toFixed(1)}/5 sangat baik. Anda berhasil mengelola stress dengan baik!`
      });
    } else if (latest.avgStressLevel >= 4) {
      insights.push({
        type: 'warning',
        icon: 'warning',
        title: 'Stress Tinggi 🌱',
        description: `Level stress ${latest.avgStressLevel.toFixed(1)}/5 cukup tinggi. Coba teknik relaksasi seperti meditasi atau olahraga ringan.`
      });
    } else if (stressChange < -0.5) {
      insights.push({
        type: 'positive',
        icon: 'checkmark-circle',
        title: 'Stress Menurun',
        description: `Level stress turun ${Math.abs(stressChange).toFixed(1)} poin. Strategi pengelolaan stress Anda berhasil!`
      });
    }

    // Physical health analysis
    const healthyRatio = latest.healthyDays / latest.totalEntries;
    if (healthyRatio >= 0.8) {
      insights.push({
        type: 'positive',
        icon: 'fitness',
        title: 'Kesehatan Prima 💪',
        description: `${Math.round(healthyRatio * 100)}% hari Anda dalam kondisi sehat. Pertahankan gaya hidup sehat!`
      });
    } else if (healthyRatio < 0.5) {
      insights.push({
        type: 'warning',
        icon: 'medical',
        title: 'Perhatikan Kesehatan 🏥',
        description: `Hanya ${Math.round(healthyRatio * 100)}% hari Anda merasa sehat. Jaga pola makan dan istirahat yang cukup.`
      });
    }

    // Consistency analysis
    if (latest.totalEntries >= 5) {
      insights.push({
        type: 'positive',
        icon: 'calendar',
        title: 'Konsisten Tracking! 📊',
        description: `Anda mengisi mood tracker ${latest.totalEntries} hari. Konsistensi ini membantu memahami pola mood Anda.`
      });
    } else {
      insights.push({
        type: 'neutral',
        icon: 'calendar-outline',
        title: 'Tingkatkan Konsistensi',
        description: 'Coba isi mood tracker setiap hari untuk insights yang lebih akurat.'
      });
    }

    return insights;
  };

  const insights = generateInsights(trends);

  const getInsightStyle = (type: Insight['type']) => {
    switch (type) {
      case 'positive':
        return {
          backgroundColor: '#ECFDF5',
          borderColor: '#10B981',
          iconColor: '#10B981',
        };
      case 'warning':
        return {
          backgroundColor: '#FEF2F2',
          borderColor: '#EF4444',
          iconColor: '#EF4444',
        };
      case 'neutral':
      default:
        return {
          backgroundColor: '#F0F9FF',
          borderColor: '#3B82F6',
          iconColor: '#3B82F6',
        };
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="bulb" size={24} color="#F59E0B" />
        <Text style={styles.title}>Insights & Recommendations</Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {insights.map((insight, index) => {
          const style = getInsightStyle(insight.type);
          return (
            <View
              key={index}
              style={[
                styles.insightCard,
                {
                  backgroundColor: style.backgroundColor,
                  borderColor: style.borderColor,
                }
              ]}
            >
              <View style={styles.insightHeader}>
                <Ionicons
                  name={insight.icon as any}
                  size={20}
                  color={style.iconColor}
                />
                <Text style={[styles.insightTitle, { color: style.iconColor }]}>
                  {insight.title}
                </Text>
              </View>
              <Text style={styles.insightDescription}>
                {insight.description}
              </Text>
            </View>
          );
        })}

        {/* Tips section */}
        <View style={styles.tipsContainer}>
          <Text style={styles.tipsTitle}>💡 Tips untuk Mood yang Lebih Baik</Text>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>• Tidur 7-9 jam setiap malam</Text>
            <Text style={styles.tipItem}>• Olahraga ringan 30 menit sehari</Text>
            <Text style={styles.tipItem}>• Praktik mindfulness atau meditasi</Text>
            <Text style={styles.tipItem}>• Habiskan waktu di alam terbuka</Text>
            <Text style={styles.tipItem}>• Terhubung dengan orang-orang terdekat</Text>
            <Text style={styles.tipItem}>• Batasi konsumsi media sosial</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    marginLeft: 8,
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
  },
  insightCard: {
    marginHorizontal: 20,
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  insightDescription: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  tipsContainer: {
    margin: 20,
    padding: 16,
    backgroundColor: '#FFFBEB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 12,
  },
  tipsList: {
    marginLeft: 8,
  },
  tipItem: {
    fontSize: 14,
    color: '#78350F',
    lineHeight: 22,
    marginBottom: 4,
  },
});
