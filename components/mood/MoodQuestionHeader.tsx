import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface MoodQuestionHeaderProps {
  question?: string;
}

export const MoodQuestionHeader: React.FC<MoodQuestionHeaderProps> = ({
  question = "How would you describe your mood?"
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.questionText}>{question}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 30,
    alignItems: 'center',
  },
  questionText: {
    fontSize: 28,
    fontWeight: '600',
    color: '#5B3E31',
    textAlign: 'center',
    lineHeight: 36,
    letterSpacing: -0.5,
  },
});
