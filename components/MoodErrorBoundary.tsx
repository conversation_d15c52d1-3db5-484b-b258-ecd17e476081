import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { reportError, addBreadcrumb } from '@/lib/sentryConfig';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
  onRetry?: () => void;
  onSavePartial?: () => void;
  onSkip?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  retryCount: number;
}

export class MoodErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      retryCount: 0
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('[MoodErrorBoundary] Error caught:', error);
    console.error('[MoodErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Add breadcrumb for mood error context
    addBreadcrumb(
      'Mood component error occurred',
      'error',
      {
        component: 'MoodErrorBoundary',
        errorName: error.name,
        errorMessage: error.message,
        retryCount: this.state.retryCount,
        componentStack: errorInfo.componentStack?.split('\n').slice(0, 3).join('\n')
      }
    );

    // Report error to Sentry with mood-specific context
    reportError(error, {
      component: 'MoodErrorBoundary',
      action: 'mood_component_render',
      metadata: {
        retryCount: this.state.retryCount,
        errorInfo: {
          componentStack: errorInfo.componentStack,
          errorBoundary: 'MoodErrorBoundary'
        }
      }
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;
    
    addBreadcrumb(
      'Mood error boundary retry attempted',
      'user_action',
      { 
        component: 'MoodErrorBoundary',
        retryCount: newRetryCount
      }
    );

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: newRetryCount
    });

    this.props.onRetry?.();
  };

  handleSavePartial = () => {
    addBreadcrumb(
      'User saved partial mood data from error boundary',
      'user_action',
      { component: 'MoodErrorBoundary' }
    );

    this.props.onSavePartial?.();
  };

  handleSkip = () => {
    addBreadcrumb(
      'User skipped mood entry from error boundary',
      'user_action',
      { component: 'MoodErrorBoundary' }
    );

    this.props.onSkip?.();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI for mood components
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="happy-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Mood Tracker Bermasalah</Text>
          <Text style={styles.errorMessage}>
            Terjadi kesalahan pada mood tracker. Anda bisa mencoba lagi, menyimpan data yang sudah diisi, atau melewati untuk sekarang.
          </Text>
          
          {this.state.retryCount < 3 && (
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={this.handleRetry}
            >
              <Text style={styles.retryButtonText}>
                Coba Lagi {this.state.retryCount > 0 && `(${this.state.retryCount + 1}/3)`}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity 
            style={styles.saveButton}
            onPress={this.handleSavePartial}
          >
            <Text style={styles.saveButtonText}>Simpan Data yang Ada</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.skipButton}
            onPress={this.handleSkip}
          >
            <Text style={styles.skipButtonText}>Lewati untuk Sekarang</Text>
          </TouchableOpacity>

          <View style={styles.helpContainer}>
            <Text style={styles.helpText}>
              💡 Tips: Mood tracker membantu memahami pola emosi Anda. Jika bermasalah, Anda bisa mengisinya nanti.
            </Text>
          </View>
          
          {__DEV__ && this.state.error && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugTitle}>Debug Info:</Text>
              <Text style={styles.debugText}>
                {this.state.error.name}: {this.state.error.message}
              </Text>
              <Text style={styles.debugText}>
                Retry Count: {this.state.retryCount}
              </Text>
              {this.state.error.stack && (
                <Text style={styles.debugStack}>
                  {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                </Text>
              )}
            </View>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FEFEFE',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#A08CFB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 24,
  },
  skipButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  helpContainer: {
    backgroundColor: '#E8F5E8',
    padding: 16,
    borderRadius: 10,
    marginBottom: 16,
  },
  helpText: {
    fontSize: 14,
    color: '#2E7D32',
    textAlign: 'center',
    lineHeight: 20,
  },
  debugContainer: {
    backgroundColor: '#F5F5F5',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  debugStack: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
});
