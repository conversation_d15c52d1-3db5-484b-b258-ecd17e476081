import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';

interface VoiceMascotProps {
  conversationStatus: 'disconnected' | 'connecting' | 'connected';
  isSpeaking: boolean;
  micMuted: boolean;
}

export default function VoiceMascot({ conversationStatus, isSpeaking, micMuted }: VoiceMascotProps) {
  const getStatusText = () => {
    if (conversationStatus === 'connecting') return 'Menghubungkan...';
    if (conversationStatus === 'connected') {
      if (micMuted) return 'Mikrofon dimatikan';
      if (isSpeaking) return 'Temani sedang berbicara...';
      return '<PERSON><PERSON> berbicara dengan <PERSON>';
    }
    return 'Tekan untuk memulai panggilan suara';
  };

  return (
    <>
      {/* Mascot - larger size without decorative elements */}
      <View style={styles.mascotContainer}>
        <Image
          source={require('../../assets/images/call-mascot.png')}
          style={styles.mascotImage}
          resizeMode="contain"
        />
      </View>
      
      {/* Text content */}
      <View style={styles.textContent}>
        <Text style={styles.subtitle}>
          {conversationStatus === 'connected' ? 'Temani mendengarkan Anda,' : 'Temani siap mendengarkan,'}
        </Text>
        <Text style={styles.callToAction}>
          {getStatusText()}
        </Text>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  mascotContainer: {
    width: 280,
    height: 280,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginTop: 10,
  },
  mascotImage: {
    width: 220,
    height: 220,
  },
  textContent: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 10,
  },
  callToAction: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
  },
});
