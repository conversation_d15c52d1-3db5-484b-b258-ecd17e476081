import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Platform,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Conversation } from '@elevenlabs/client';
import Animated, {
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';
import { useVoiceTranscript } from '@/hooks/useVoiceTranscript';
import { useVoiceCredits } from '@/hooks/useVoiceCredits';
import { TranscriptView } from '@/components/TranscriptView';
import { useVoiceAmplitude } from '@/hooks/useVoiceAmplitude';
import { androidPermissions } from '@/utils/androidPermissions';
import { initializeMediaDevicesPolyfill, isPolyfillNeeded } from '@/utils/MediaDevicesPolyfill';
import VoiceHeader from './VoiceHeader';
import VoiceTabSelector from './VoiceTabSelector';
import VoiceMascot from './VoiceMascot';
import VoiceControls from './VoiceControls';
import VoiceCreditsDisplay from '@/components/VoiceCreditsDisplay';
import InsufficientCreditsModal from '@/components/InsufficientCreditsModal';
import { VOICE_CALL_CONFIG, VoiceErrorType, VOICE_ERROR_MESSAGES, VoiceConfigHelpers } from '@/constants/voiceConfig';

interface VoiceContainerProps {
  onBackToChat?: () => void;
}

export default function VoiceContainer({ onBackToChat }: VoiceContainerProps) {
  // Voice conversation states
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [micMuted, setMicMuted] = useState(false);
  const [conversationStatus, setConversationStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [isSpeaking, setIsSpeaking] = useState(false);



  // Animation values
  const pulseAnimation = useSharedValue(0);

  // Store a reference to the conversation instance for direct method calls
  const conversationInstanceRef = useRef<Conversation | null>(null);

  // Store timeout reference for call duration limit enforcement
  const conversationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Voice transcript functionality
  const transcript = useVoiceTranscript();

  // Voice credits functionality
  const voiceCredits = useVoiceCredits();
  const [showInsufficientCreditsModal, setShowInsufficientCreditsModal] = useState(false);

  // Enhanced voice amplitude tracking with new hook
  const { voiceIntensity, peakValue } = useVoiceAmplitude(
    conversationInstanceRef.current,
    micMuted,
    {
      smoothingFactor: 0.15, // More responsive
      responseDuration: 30,   // Faster response
      peakThreshold: 0.7,     // Peak detection threshold
      fallbackAnimationIntensity: 0.6 // Enhanced fallback
    }
  );

  // Conversation event handlers
  const handleConnect = useCallback(async () => {
    console.log('[VoiceContainer] Voice conversation connected');
    setConversationStatus('connected');
    setError(null);

    // Voice transcription will be logged to console only (no chat thread creation)
    console.log('[VoiceContainer] Voice session connected - transcriptions will be logged to console');

    // Start voice credits session
    try {
      const agentId = process.env.EXPO_PUBLIC_ELEVENLABS_AGENT_ID;
      console.log('[VoiceContainer] Starting credits session with agent:', agentId);
      const sessionResult = await voiceCredits.startSession(agentId);
      console.log('[VoiceContainer] Voice credits session started successfully:', {
        sessionId: sessionResult.sessionId,
        maxDuration: sessionResult.maxCallDuration
      });

      // Start timeout monitoring to enforce call duration limit
      const timeoutId = setTimeout(async () => {
        console.log('[VoiceContainer] Call duration limit reached (2 minutes), terminating conversation');
        try {
          // First end the actual voice conversation
          if (conversationInstanceRef.current) {
            await conversationInstanceRef.current.endSession();
            conversationInstanceRef.current = null;
          }
          setConversationStatus('disconnected');
          setMicMuted(false);

          // Then end the credits session
          if (voiceCredits.currentSessionId) {
            await voiceCredits.endSession(VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS);
          }

          // Show user notification using centralized error message
          const timeoutError = VOICE_ERROR_MESSAGES[VoiceErrorType.TIMEOUT_EXCEEDED];
          Alert.alert(
            timeoutError.title,
            timeoutError.message,
            [{ text: timeoutError.action }]
          );
        } catch (error) {
          console.error('[VoiceContainer] Error during timeout termination:', error);
        }
      }, VoiceConfigHelpers.getTimeoutMs(sessionResult.maxCallDuration));

      // Store timeout ID for cleanup
      conversationTimeoutRef.current = timeoutId;

    } catch (creditsError) {
      console.error('[VoiceContainer] Failed to start credits session:', creditsError);
      // Don't block the conversation, just log the error
    }

    // Start enhanced pulse animation when connected
    pulseAnimation.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 1000 }),
        withTiming(0, { duration: 1000 })
      ),
      -1,
      false
    );
  }, [transcript, pulseAnimation, voiceCredits]);

  const handleDisconnect = useCallback(async () => {
    console.log('[VoiceContainer] Voice conversation disconnected');

    // Clear the timeout if conversation ends normally
    if (conversationTimeoutRef.current) {
      clearTimeout(conversationTimeoutRef.current);
      conversationTimeoutRef.current = null;
      console.log('[VoiceContainer] Cleared conversation timeout');
    }

    // Log session state at disconnect time for debugging
    console.log('[VoiceContainer] Session state at disconnect:', {
      currentSessionId: voiceCredits.currentSessionId,
      isSessionActive: voiceCredits.isSessionActive,
      timestamp: new Date().toISOString(),
      trigger: 'handleDisconnect'
    });

    setConversationStatus('disconnected');
    setIsSpeaking(false);

    // End voice credits session - FIXED: Check currentSessionId instead of isSessionActive
    try {
      if (voiceCredits.currentSessionId) {
        console.log('[VoiceContainer] Attempting to end credits session:', voiceCredits.currentSessionId);
        await voiceCredits.endSession();
        console.log('[VoiceContainer] Voice credits session ended successfully');
      } else {
        console.log('[VoiceContainer] No active session to end - attempting emergency recovery');

        // Try emergency session recovery
        const recoveredSessionId = await voiceCredits.emergencySessionRecovery();
        if (recoveredSessionId) {
          console.log('[VoiceContainer] Emergency recovery successful, ending recovered session:', recoveredSessionId);
          await voiceCredits.endSession();
          console.log('[VoiceContainer] Recovered session ended successfully');
        } else {
          console.log('[VoiceContainer] Emergency recovery failed - no session to end');
        }
      }
    } catch (creditsError) {
      console.error('[VoiceContainer] Failed to end credits session:', creditsError);
      // Even if endSession fails, try to handle session interruption
      try {
        if (voiceCredits.currentSessionId) {
          console.log('[VoiceContainer] Attempting session interruption cleanup');
          await voiceCredits.handleSessionInterruption('interrupted');
        }
      } catch (interruptError) {
        console.error('[VoiceContainer] Failed to handle session interruption:', interruptError);
      }
    }

    // Stop all animations when disconnected
    pulseAnimation.value = withTiming(0, { duration: 300 });
  }, [pulseAnimation, voiceCredits]);

  const handleMessage = useCallback((message: any) => {
    // Enhanced console logging for voice transcriptions
    const userTranscript = message?.user_transcription_event?.user_transcript;
    const agentResponse = message?.agent_response_event?.agent_response;
    const isFinal = message?.user_transcription_event?.is_final;

    console.log('🎤 [TRANSCRIPTION] Voice message received:', {
      timestamp: new Date().toISOString(),
      type: message?.type,
      hasUserTranscript: !!userTranscript,
      hasAgentResponse: !!agentResponse,
      isFinal,
      note: 'Chat thread creation disabled'
    });

    // Log actual transcription content for debugging (no chat thread creation)
    if (userTranscript) {
      console.log('🎤 [TRANSCRIPTION] 👤 USER:', {
        text: userTranscript,
        isFinal,
        timestamp: new Date().toLocaleString('id-ID')
      });
    }

    if (agentResponse) {
      console.log('🎤 [TRANSCRIPTION] 🤖 AI:', {
        text: agentResponse,
        timestamp: new Date().toLocaleString('id-ID')
      });
    }

    // NOTE: transcript.handleVoiceMessage(message) removed to prevent chat thread creation
  }, [transcript]);

  const handleError = useCallback((error: any) => {
    console.error('[VoiceContainer] Voice conversation error:', error);

    // Check for extension-related errors that might interfere with session state
    const errorMessage = error?.message || error?.toString() || '';
    const isExtensionError = errorMessage.includes('ExtensionMessagingService') ||
                           errorMessage.includes('onMessage listener went out of scope') ||
                           errorMessage.includes('Extension context invalidated');

    if (isExtensionError) {
      console.warn('[VoiceContainer] Browser extension interference detected:', {
        error: errorMessage,
        sessionId: voiceCredits.currentSessionId,
        timestamp: new Date().toISOString(),
        note: 'Extension error ignored - session should continue normally'
      });

      // Don't set error state for extension-related issues, just log them
      // The session should continue normally
      return;
    }

    // Determine error type and get appropriate message
    let errorType = VoiceErrorType.UNKNOWN_ERROR;

    if (errorMessage.includes('WebSocket') || errorMessage.includes('network') || errorMessage.includes('connection')) {
      errorType = VoiceErrorType.NETWORK_ERROR;
    } else if (errorMessage.includes('permission') || errorMessage.includes('microphone') || errorMessage.includes('getUserMedia')) {
      errorType = VoiceErrorType.MICROPHONE_PERMISSION;
    } else if (errorMessage.includes('agent') || errorMessage.includes('unavailable')) {
      errorType = VoiceErrorType.AGENT_UNAVAILABLE;
    } else if (errorMessage.includes('session') || errorMessage.includes('failed')) {
      errorType = VoiceErrorType.SESSION_FAILED;
    }

    const errorConfig = VOICE_ERROR_MESSAGES[errorType];
    setError(errorConfig.message);
    setConversationStatus('disconnected');

    Alert.alert(
      errorConfig.title,
      errorConfig.message,
      [{ text: errorConfig.action }]
    );
  }, [voiceCredits.currentSessionId]);

  // Global error handler for extension interference
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      const errorMessage = event.error?.message || event.message || '';
      const isExtensionError = errorMessage.includes('ExtensionMessagingService') ||
                             errorMessage.includes('onMessage listener went out of scope') ||
                             errorMessage.includes('Extension context invalidated');

      if (isExtensionError) {
        console.warn('[VoiceContainer] Global extension error caught:', {
          error: errorMessage,
          sessionId: voiceCredits.currentSessionId,
          timestamp: new Date().toISOString(),
          note: 'Extension error prevented from affecting session'
        });

        // Prevent the error from propagating
        event.preventDefault();
        return false;
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const errorMessage = event.reason?.message || event.reason?.toString() || '';
      const isExtensionError = errorMessage.includes('ExtensionMessagingService') ||
                             errorMessage.includes('onMessage listener went out of scope') ||
                             errorMessage.includes('Extension context invalidated');

      if (isExtensionError) {
        console.warn('[VoiceContainer] Unhandled extension promise rejection caught:', {
          error: errorMessage,
          sessionId: voiceCredits.currentSessionId,
          timestamp: new Date().toISOString(),
          note: 'Extension promise rejection prevented from affecting session'
        });

        // Prevent the error from propagating
        event.preventDefault();
        return false;
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('error', handleGlobalError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      return () => {
        window.removeEventListener('error', handleGlobalError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }
  }, [voiceCredits.currentSessionId]);

  const handleModeChange = useCallback((mode: any) => {
    console.log('[VoiceContainer] Mode changed:', mode);
    setIsSpeaking(mode === 'speaking');
  }, []);



  // Initialize polyfill and check microphone permission on mount
  useEffect(() => {
    console.log('[VoiceContainer] Component mounted, initializing...');
    console.log('[VoiceContainer] navigator:', typeof navigator);
    console.log('[VoiceContainer] navigator.mediaDevices:', typeof navigator !== 'undefined' ? navigator.mediaDevices : 'navigator undefined');
    console.log('[VoiceContainer] navigator.mediaDevices.getUserMedia:', typeof navigator !== 'undefined' && navigator.mediaDevices ? navigator.mediaDevices.getUserMedia : 'mediaDevices undefined');

    // Initialize MediaDevices polyfill if needed
    const polyfillNeeded = isPolyfillNeeded();
    console.log('[VoiceContainer] isPolyfillNeeded result:', polyfillNeeded);

    if (polyfillNeeded) {
      console.log('[VoiceContainer] Polyfill needed, initializing...');
      initializeMediaDevicesPolyfill();

      // Verify polyfill was installed
      console.log('[VoiceContainer] After polyfill - navigator.mediaDevices.getUserMedia:', typeof navigator !== 'undefined' && navigator.mediaDevices ? navigator.mediaDevices.getUserMedia : 'still undefined');
    } else {
      console.log('[VoiceContainer] Native mediaDevices available, no polyfill needed');
    }

    // Check microphone permission after polyfill is ready
    setTimeout(() => {
      checkMicrophonePermission();
    }, 100);
  }, []);

  // Cleanup timeout on component unmount
  useEffect(() => {
    return () => {
      if (conversationTimeoutRef.current) {
        clearTimeout(conversationTimeoutRef.current);
        conversationTimeoutRef.current = null;
        console.log('[VoiceContainer] Cleared conversation timeout on unmount');
      }
    };
  }, []);

  const checkMicrophonePermission = async () => {
    if (Platform.OS === 'web') {
      // Web platform - check for native getUserMedia support
      if (typeof navigator !== 'undefined' && navigator?.mediaDevices?.getUserMedia) {
        try {
          console.log('[VoiceContainer] Web platform - testing native getUserMedia...');
          // Check if we already have permission by attempting to get user media
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          stream.getTracks().forEach(track => track.stop());
          console.log('[VoiceContainer] Web platform - microphone permission already granted');
          setPermissionGranted(true);
        } catch (error) {
          console.log('[VoiceContainer] Web platform - microphone permission not yet granted:', error);
          // Permission not granted yet, will request when needed
          setPermissionGranted(false);
        }
      } else {
        console.log('[VoiceContainer] Web platform - navigator.mediaDevices.getUserMedia not available');
        setPermissionGranted(false);
      }
    } else if (Platform.OS === 'android') {
      // For Android, check permission using expo-audio
      try {
        const hasPermission = await androidPermissions.checkMicrophonePermission();
        setPermissionGranted(hasPermission);
        console.log('[VoiceContainer] Android microphone permission status:', hasPermission);
      } catch (error) {
        console.error('[VoiceContainer] Error checking Android microphone permission:', error);
        setPermissionGranted(false);
      }
    } else {
      // For iOS, assume permission will be handled by the system
      setPermissionGranted(true);
    }
  };

  const requestMicrophonePermission = async (): Promise<boolean> => {
    console.log('[VoiceContainer] requestMicrophonePermission called');
    console.log('[VoiceContainer] Platform.OS:', Platform.OS);
    console.log('[VoiceContainer] navigator available:', typeof navigator !== 'undefined');
    console.log('[VoiceContainer] getUserMedia available:', typeof navigator !== 'undefined' && navigator?.mediaDevices?.getUserMedia);

    if (Platform.OS === 'web' && typeof navigator !== 'undefined' && navigator?.mediaDevices?.getUserMedia) {
      console.log('[VoiceContainer] Using web getUserMedia approach');
      try {
        setIsRequestingPermission(true);

        // Request microphone permission
        console.log('[VoiceContainer] Calling getUserMedia...');
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log('[VoiceContainer] getUserMedia successful, stream:', stream);
        stream.getTracks().forEach(track => track.stop());

        setPermissionGranted(true);
        console.log('[VoiceContainer] Permission granted successfully');
        return true;
      } catch (error: any) {
        console.error('[VoiceContainer] Microphone permission error:', error);
        console.error('[VoiceContainer] Error name:', error.name);
        console.error('[VoiceContainer] Error message:', error.message);

        let errorMessage = 'Temani memerlukan akses mikrofon untuk fitur panggilan suara.';

        if (error.name === 'NotAllowedError') {
          errorMessage = 'Akses mikrofon ditolak. Silakan izinkan akses mikrofon di pengaturan browser Anda.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'Mikrofon tidak ditemukan. Pastikan perangkat mikrofon terhubung.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = 'Browser Anda tidak mendukung akses mikrofon.';
        }

        setError(errorMessage);
        Alert.alert(
          'Akses Mikrofon Diperlukan',
          errorMessage,
          [{ text: 'OK' }]
        );

        return false;
      } finally {
        setIsRequestingPermission(false);
      }
    } else if (Platform.OS === 'web') {
      // Web platform without navigator.mediaDevices
      console.log('[VoiceContainer] Web platform detected but navigator.mediaDevices not available');
      setError('Microphone not supported in this browser environment');
      setPermissionGranted(false);
      return false;
    } else if (Platform.OS === 'android') {
      // For Android, use our custom permission manager
      try {
        setIsRequestingPermission(true);
        setError(null);

        console.log('[VoiceContainer] Requesting Android microphone permission...');
        const granted = await androidPermissions.requestPermissionWithDialog();

        if (granted) {
          setPermissionGranted(true);
          console.log('[VoiceContainer] Android microphone permission granted');

          // Initialize audio session for Android
          const audioInitialized = await androidPermissions.initializeAudioSession();
          if (!audioInitialized) {
            setError('Gagal menginisialisasi sesi audio');
            return false;
          }

          return true;
        } else {
          setPermissionGranted(false);
          setError('Izin mikrofon diperlukan untuk fitur panggilan suara');
          console.log('[VoiceContainer] Android microphone permission denied');
          return false;
        }
      } catch (error: any) {
        console.error('[VoiceContainer] Error requesting Android microphone permission:', error);
        setError(`Gagal meminta izin mikrofon: ${error.message}`);
        return false;
      } finally {
        setIsRequestingPermission(false);
      }
    } else {
      // For iOS, permission is handled by the system
      setPermissionGranted(true);
      return true;
    }
  };

  // Get a signed URL for connecting to the ElevenLabs API
  const getSignedUrl = async (): Promise<string | null> => {
    try {
      console.log('[VoiceContainer] Getting signed URL from Supabase function...');

      const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/elevenlabs-signed-url`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.warn('[VoiceContainer] Failed to get signed URL, falling back to public agent');
        // Fallback to using agent ID directly for public agents
        const agentId = process.env.EXPO_PUBLIC_ELEVENLABS_AGENT_ID;
        return agentId || null;
      }

      const data = await response.json();
      console.log('[VoiceContainer] Got signed URL successfully');
      return data.signedUrl;
    } catch (err) {
      console.error('[VoiceContainer] Error getting signed URL, falling back to agent ID:', err);
      // Fallback to using agent ID directly
      const agentId = process.env.EXPO_PUBLIC_ELEVENLABS_AGENT_ID;
      return agentId || null;
    }
  };

  // Start voice conversation with agent using Client SDK
  const startVoiceConversation = useCallback(async () => {
    console.log('[VoiceContainer] startVoiceConversation called');

    // Check voice credits before starting conversation
    try {
      const creditCheck = await voiceCredits.checkCredits();
      if (!creditCheck.hasCredits) {
        console.log('[VoiceContainer] Insufficient credits:', creditCheck.message);
        setShowInsufficientCreditsModal(true);
        return;
      }
      console.log('[VoiceContainer] Credits check passed:', creditCheck.creditsRemaining, 'credits remaining');
    } catch (creditsError) {
      console.error('[VoiceContainer] Credits check failed:', creditsError);
      setError('Unable to check voice credits. Please try again.');
      return;
    }

    console.log('[VoiceContainer] Platform.OS:', Platform.OS);
    console.log('[VoiceContainer] permissionGranted:', permissionGranted);
    console.log('[VoiceContainer] navigator available:', typeof navigator !== 'undefined');
    console.log('[VoiceContainer] getUserMedia available:', typeof navigator !== 'undefined' && navigator?.mediaDevices?.getUserMedia);

    // Ensure polyfill is initialized before starting conversation
    console.log('[VoiceContainer] Checking polyfill before conversation...');
    console.log('[VoiceContainer] navigator.mediaDevices.getUserMedia before check:', typeof navigator !== 'undefined' && navigator.mediaDevices ? navigator.mediaDevices.getUserMedia : 'undefined');

    const needsPolyfill = isPolyfillNeeded();
    console.log('[VoiceContainer] Polyfill needed before conversation:', needsPolyfill);

    if (needsPolyfill) {
      console.log('[VoiceContainer] Re-initializing polyfill before conversation...');
      initializeMediaDevicesPolyfill();
      console.log('[VoiceContainer] After re-init - getUserMedia:', typeof navigator !== 'undefined' && navigator.mediaDevices ? navigator.mediaDevices.getUserMedia : 'still undefined');
    } else {
      console.log('[VoiceContainer] Polyfill not needed, getUserMedia should be available');
    }

    try {
      // Request microphone permission if not granted
      if (!permissionGranted) {
        console.log('[VoiceContainer] Requesting microphone permission...');
        const granted = await requestMicrophonePermission();
        console.log('[VoiceContainer] Permission request result:', granted);
        if (!granted) return;
      }

      setConversationStatus('connecting');

      const agentId = process.env.EXPO_PUBLIC_ELEVENLABS_AGENT_ID;
      if (!agentId) {
        setError('Agent ID belum dikonfigurasi. Silakan hubungi administrator.');
        Alert.alert(
          'Konfigurasi Belum Lengkap',
          'Fitur panggilan suara belum dikonfigurasi dengan benar.',
          [{ text: 'OK' }]
        );
        return;
      }

      console.log('[VoiceContainer] Starting voice conversation with agent:', agentId);

      // For web platform, ensure microphone access is granted before starting session
      if (Platform.OS === 'web' && typeof navigator !== 'undefined' && navigator?.mediaDevices?.getUserMedia) {
        try {
          // Request microphone access explicitly for web
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          // Stop the stream immediately as we just needed to check permission
          stream.getTracks().forEach(track => track.stop());
          console.log('[VoiceContainer] Web microphone access confirmed');
        } catch (micError: any) {
          console.error('[VoiceContainer] Web microphone access failed:', micError);
          setError('Tidak dapat mengakses mikrofon. Pastikan izin mikrofon telah diberikan.');
          Alert.alert(
            'Izin Mikrofon Diperlukan',
            'Temani memerlukan akses mikrofon untuk fitur panggilan suara. Silakan izinkan akses mikrofon di pengaturan browser Anda.',
            [{ text: 'OK' }]
          );
          setConversationStatus('disconnected');
          return;
        }
      }

      // Request microphone access first (as recommended by ElevenLabs docs)
      // Only for web platform - mobile platforms handle permissions differently
      if (Platform.OS === 'web' && typeof navigator !== 'undefined' && navigator?.mediaDevices?.getUserMedia) {
        try {
          console.log('🎤 [TRANSCRIPTION] Requesting microphone access...');
          await navigator.mediaDevices.getUserMedia({ audio: true });
          console.log('🎤 [TRANSCRIPTION] ✅ Microphone access granted');
        } catch (micError) {
          console.error('🎤 [TRANSCRIPTION] ❌ Microphone access denied:', micError);
          throw new Error(`Microphone access required for voice conversation: ${micError.message}`);
        }
      } else if (Platform.OS === 'web') {
        // Web platform without navigator.mediaDevices (like Expo Go)
        console.log('🎤 [TRANSCRIPTION] Web platform detected but navigator.mediaDevices not available');
        throw new Error('Voice conversation not supported in this environment. Please use a web browser or native app.');
      } else {
        // For mobile platforms, log that we're skipping web-specific permission check
        console.log('🎤 [TRANSCRIPTION] Mobile platform detected - skipping web getUserMedia check');
      }

      // Try direct agent ID approach first (recommended for public agents)
      try {
        const sessionOptions = {
          agentId: agentId,
          onConnect: handleConnect,
          onDisconnect: handleDisconnect,
          onMessage: handleMessage,
          onError: handleError,
          onModeChange: handleModeChange
        };

        console.log('=== [VoiceContainer] STARTING SESSION WITH DIRECT AGENT ID ===');
        console.log('[VoiceContainer] Session options:', {
          agentId,
          hasOnConnect: typeof handleConnect === 'function',
          hasOnDisconnect: typeof handleDisconnect === 'function',
          hasOnMessage: typeof handleMessage === 'function',
          hasOnError: typeof handleError === 'function',
          hasOnModeChange: typeof handleModeChange === 'function'
        });

        const conversation = await Conversation.startSession(sessionOptions);
        conversationInstanceRef.current = conversation;
        setMicMuted(false);
        console.log('[VoiceContainer] Direct agent ID connection successful!');
        console.log('=== [VoiceContainer] SESSION STARTED SUCCESSFULLY ===');
        return;

      } catch (directError: any) {
        console.warn('[VoiceContainer] Direct agent ID failed, trying signed URL approach:', directError.message);

        // Fallback to signed URL approach (for private agents)
        try {
          const signedUrl = await getSignedUrl();

          if (!signedUrl) {
            throw new Error('Failed to get signed URL for private agent');
          }

          const sessionOptions = {
            url: signedUrl,
            onConnect: handleConnect,
            onDisconnect: handleDisconnect,
            onMessage: handleMessage,
            onError: handleError,
            onModeChange: handleModeChange
          };

          console.log('=== [VoiceContainer] STARTING SESSION WITH SIGNED URL ===');

          const conversation = await Conversation.startSession(sessionOptions);
          conversationInstanceRef.current = conversation;
          setMicMuted(false);
          console.log('[VoiceContainer] Signed URL connection successful!');
          console.log('=== [VoiceContainer] SIGNED URL SESSION STARTED SUCCESSFULLY ===');

        } catch (signedUrlError: any) {
          console.error('[VoiceContainer] Both connection methods failed:', {
            directError: directError.message,
            signedUrlError: signedUrlError.message
          });
          throw new Error(`Connection failed: ${directError.message}`);
        }
      }

    } catch (error: any) {
      console.error('[VoiceContainer] Failed to start voice conversation:', error);
      setConversationStatus('disconnected');

      // Determine error type and get appropriate message
      let errorType = VoiceErrorType.SESSION_FAILED;

      if (error.message?.includes('getUserMedia') || error.message?.includes('microphone')) {
        errorType = VoiceErrorType.MICROPHONE_PERMISSION;
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorType = VoiceErrorType.NETWORK_ERROR;
      } else if (error.message?.includes('agent') || error.message?.includes('unavailable')) {
        errorType = VoiceErrorType.AGENT_UNAVAILABLE;
      } else if (error.message?.includes('credits') || error.message?.includes('insufficient')) {
        errorType = VoiceErrorType.CREDITS_INSUFFICIENT;
      }

      const errorConfig = VOICE_ERROR_MESSAGES[errorType];
      setError(errorConfig.message);

      // Show detailed error in development
      const alertMessage = VOICE_CALL_CONFIG.ENABLE_VERBOSE_LOGGING
        ? `${errorConfig.message}\n\nDetail: ${error.message}`
        : errorConfig.message;

      Alert.alert(errorConfig.title, alertMessage, [{ text: errorConfig.action }]);
    }
  }, [permissionGranted, handleConnect, handleDisconnect, handleMessage, handleError, handleModeChange, voiceCredits]);

  // End voice conversation using Client SDK
  const endVoiceConversation = useCallback(async () => {
    try {
      // Clear the timeout when manually ending conversation
      if (conversationTimeoutRef.current) {
        clearTimeout(conversationTimeoutRef.current);
        conversationTimeoutRef.current = null;
        console.log('[VoiceContainer] Cleared conversation timeout on manual end');
      }

      if (conversationInstanceRef.current) {
        await conversationInstanceRef.current.endSession();
        conversationInstanceRef.current = null;
      }
      setError(null);
      setMicMuted(false);
      setConversationStatus('disconnected');

      // Cleanup Android audio session
      if (Platform.OS === 'android') {
        await androidPermissions.cleanupAudioSession();
        console.log('[VoiceContainer] Android audio session cleaned up');
      }
    } catch (err) {
      console.error('Failed to end voice conversation:', err);
      // Don't show error for ending conversation, just log it
    }
  }, []);

  // Toggle mute state using Client SDK
  const handleToggleMute = useCallback(() => {
    if (conversationStatus !== 'connected') return;

    try {
      console.log('[DEBUG] Current mute state before toggle:', micMuted);
      const newMuteState = !micMuted;

      // Update React state
      setMicMuted(newMuteState);

      // Use Client SDK mute functionality
      const conversation = conversationInstanceRef.current;
      if (conversation && typeof conversation.setMicMuted === 'function') {
        console.log('[DEBUG] Calling setMicMuted on Client SDK conversation:', newMuteState);
        conversation.setMicMuted(newMuteState);
      }

      console.log('[DEBUG] Microphone mute state changed to:', newMuteState);
    } catch (err) {
      console.error('[DEBUG] Failed to toggle mute state:', err);
    }
  }, [conversationStatus, micMuted]);

  // Handle transcript button press
  const handleTranscript = () => {
    transcript.toggleTranscript();
  };

  // Note: Save transcript functionality removed - voice conversations are no longer saved to chat history

  // Handle clearing transcript
  const handleClearTranscript = () => {
    Alert.alert(
      'Hapus Transkrip',
      'Apakah Anda yakin ingin menghapus transkrip percakapan ini?',
      [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Hapus',
          style: 'destructive',
          onPress: transcript.clearTranscript
        }
      ]
    );
  };



  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Background gradient - exact colors from original */}
      <LinearGradient
        colors={['#E8EFFD', '#FFFFFF']}
        locations={[0.17, 0.58]}
        style={styles.backgroundGradient}
      />

      {/* Header */}
      <VoiceHeader />

      {/* Tab selector */}
      <VoiceTabSelector
        onChatPress={onBackToChat}
        onCallPress={() => {}} // No action needed, already on call
      />

      {/* Voice Credits Display - Moved below tab selector */}
      <View style={styles.creditsContainer}>
        <VoiceCreditsDisplay />
      </View>

      {/* Main content */}
      <View style={styles.contentContainer}>
        {/* Mascot and text */}
        <VoiceMascot
          conversationStatus={conversationStatus}
          isSpeaking={isSpeaking}
          micMuted={micMuted}
        />

        {/* Action buttons */}
        <VoiceControls
          conversationStatus={conversationStatus}
          isRequestingPermission={isRequestingPermission}
          micMuted={micMuted}
          voiceIntensity={voiceIntensity}
          peakValue={peakValue}
          pulseAnimation={pulseAnimation}
          onTranscript={handleTranscript}
          onMicPress={conversationStatus === 'connected' ? endVoiceConversation : startVoiceConversation}
          onToggleMute={handleToggleMute}
        />
      </View>

      {/* Transcript View */}
      <TranscriptView
        messages={transcript.messages}
        isVisible={transcript.isVisible}
        loading={transcript.loading}
        error={transcript.error}
        onClose={transcript.hideTranscript}
        onSaveToChat={() => {}} // No-op function since saving is disabled
        onClear={handleClearTranscript}
      />

      {/* Insufficient Credits Modal */}
      <InsufficientCreditsModal
        visible={showInsufficientCreditsModal}
        onClose={() => setShowInsufficientCreditsModal(false)}
        onNavigateToTextChat={onBackToChat}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  creditsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 20,
    paddingBottom: 30,
  },

});
