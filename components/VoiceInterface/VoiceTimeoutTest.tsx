/**
 * Voice Timeout Test Component
 * 
 * Development-only component for testing voice call timeout mechanisms
 * This component should only be used in development builds
 */

import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { VOICE_CALL_CONFIG, VoiceConfigHelpers } from '@/constants/voiceConfig';
import { runVoiceTests } from '@/utils/voiceTestUtils';

interface TimeoutTestState {
  isRunning: boolean;
  elapsedTime: number;
  testType: 'primary' | 'backup' | 'both';
  results: string[];
}

export default function VoiceTimeoutTest() {
  const [testState, setTestState] = useState<TimeoutTestState>({
    isRunning: false,
    elapsedTime: 0,
    testType: 'primary',
    results: []
  });

  const [primaryTimeout, setPrimaryTimeout] = useState<NodeJS.Timeout | null>(null);
  const [backupTimeout, setBackupTimeout] = useState<NodeJS.Timeout | null>(null);
  const [intervalRef, setIntervalRef] = useState<NodeJS.Timeout | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (primaryTimeout) clearTimeout(primaryTimeout);
      if (backupTimeout) clearTimeout(backupTimeout);
      if (intervalRef) clearInterval(intervalRef);
    };
  }, [primaryTimeout, backupTimeout, intervalRef]);

  // Don't render in production
  if (!VOICE_CALL_CONFIG.ENABLE_VERBOSE_LOGGING) {
    return null;
  }

  const addResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestState(prev => ({
      ...prev,
      results: [...prev.results, `[${timestamp}] ${message}`]
    }));
  };

  const startTimeoutTest = (type: 'primary' | 'backup' | 'both') => {
    if (testState.isRunning) {
      Alert.alert('Test Already Running', 'Please stop the current test first.');
      return;
    }

    setTestState(prev => ({
      ...prev,
      isRunning: true,
      elapsedTime: 0,
      testType: type,
      results: []
    }));

    addResult(`Starting ${type} timeout test`);
    addResult(`Max duration: ${VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS} seconds`);

    // Start elapsed time counter
    const interval = setInterval(() => {
      setTestState(prev => ({
        ...prev,
        elapsedTime: prev.elapsedTime + 1
      }));
    }, 1000);
    setIntervalRef(interval);

    // Set up primary timeout (simulating VoiceContainer timeout)
    if (type === 'primary' || type === 'both') {
      const primaryTimeoutMs = VoiceConfigHelpers.getTimeoutMs(VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS);
      const timeout = setTimeout(() => {
        addResult(`🎯 PRIMARY TIMEOUT TRIGGERED at ${primaryTimeoutMs}ms`);
        if (type === 'primary') {
          stopTest('Primary timeout completed successfully');
        }
      }, primaryTimeoutMs);
      setPrimaryTimeout(timeout);
    }

    // Set up backup timeout (simulating useVoiceCredits backup)
    if (type === 'backup' || type === 'both') {
      const backupTimeoutMs = VoiceConfigHelpers.getBackupTimeoutMs(VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS);
      const timeout = setTimeout(() => {
        addResult(`🚨 BACKUP TIMEOUT TRIGGERED at ${backupTimeoutMs}ms`);
        if (type === 'backup') {
          stopTest('Backup timeout completed successfully');
        } else if (type === 'both') {
          stopTest('Both timeouts completed - backup should trigger after primary');
        }
      }, backupTimeoutMs);
      setBackupTimeout(timeout);
    }
  };

  const stopTest = (reason: string) => {
    // Clear all timeouts and intervals
    if (primaryTimeout) {
      clearTimeout(primaryTimeout);
      setPrimaryTimeout(null);
    }
    if (backupTimeout) {
      clearTimeout(backupTimeout);
      setBackupTimeout(null);
    }
    if (intervalRef) {
      clearInterval(intervalRef);
      setIntervalRef(null);
    }

    addResult(`Test stopped: ${reason}`);
    setTestState(prev => ({
      ...prev,
      isRunning: false
    }));
  };

  const runAutomatedTests = async () => {
    addResult('Starting automated test suite...');
    try {
      const suite = await runVoiceTests();
      addResult(`Automated tests completed: ${suite.passedTests}/${suite.totalTests} passed`);
      
      if (suite.failedTests > 0) {
        addResult(`❌ ${suite.failedTests} tests failed - check console for details`);
      } else {
        addResult('✅ All automated tests passed!');
      }
    } catch (error) {
      addResult(`❌ Automated tests failed: ${error}`);
    }
  };

  const clearResults = () => {
    setTestState(prev => ({
      ...prev,
      results: []
    }));
  };



  return (
    <View style={styles.container}>
      <Text style={styles.title}>Voice Timeout Test (Dev Only)</Text>
      
      <View style={styles.configInfo}>
        <Text style={styles.configText}>Max Duration: {VOICE_CALL_CONFIG.MAX_CALL_DURATION_SECONDS}s</Text>
        <Text style={styles.configText}>Primary Buffer: {VOICE_CALL_CONFIG.TIMEOUT_BUFFER_MS}ms</Text>
        <Text style={styles.configText}>Backup Buffer: {VOICE_CALL_CONFIG.BACKUP_TIMEOUT_BUFFER_MS}ms</Text>
      </View>

      {testState.isRunning && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>
            Test Running: {testState.testType} | Elapsed: {testState.elapsedTime}s
          </Text>
        </View>
      )}

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, testState.isRunning && styles.buttonDisabled]}
          onPress={() => startTimeoutTest('primary')}
          disabled={testState.isRunning}
        >
          <Text style={styles.buttonText}>Test Primary Timeout</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, testState.isRunning && styles.buttonDisabled]}
          onPress={() => startTimeoutTest('backup')}
          disabled={testState.isRunning}
        >
          <Text style={styles.buttonText}>Test Backup Timeout</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, testState.isRunning && styles.buttonDisabled]}
          onPress={() => startTimeoutTest('both')}
          disabled={testState.isRunning}
        >
          <Text style={styles.buttonText}>Test Both Timeouts</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.stopButton, !testState.isRunning && styles.buttonDisabled]}
          onPress={() => stopTest('Manual stop')}
          disabled={!testState.isRunning}
        >
          <Text style={styles.buttonText}>Stop Test</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.automatedButton]}
          onPress={runAutomatedTests}
        >
          <Text style={styles.buttonText}>Run Automated Tests</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testState.results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f0f0f0',
    margin: 10,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ff6b6b',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#ff6b6b',
  },
  configInfo: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  configText: {
    fontSize: 12,
    color: '#666',
  },
  statusContainer: {
    backgroundColor: '#4ecdc4',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#4ecdc4',
    padding: 10,
    borderRadius: 5,
    flex: 1,
    marginHorizontal: 2,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  stopButton: {
    backgroundColor: '#ff6b6b',
  },
  automatedButton: {
    backgroundColor: '#45b7d1',
  },
  clearButton: {
    backgroundColor: '#96ceb4',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
  },
  resultsContainer: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
    maxHeight: 200,
  },
  resultsTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  resultText: {
    fontSize: 11,
    color: '#333',
    marginBottom: 2,
  },
});
