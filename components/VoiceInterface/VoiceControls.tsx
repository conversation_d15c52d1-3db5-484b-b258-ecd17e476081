import React from 'react';
import { View, TouchableOpacity, Text, ActivityIndicator, StyleSheet, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons, FontAwesome5 } from '@expo/vector-icons';
import VoiceAnimationRings from '@/components/VoiceAnimationRings';

interface VoiceControlsProps {
  conversationStatus: 'disconnected' | 'connecting' | 'connected';
  isRequestingPermission: boolean;
  micMuted: boolean;
  voiceIntensity: number;
  peakValue: number;
  pulseAnimation: any;
  onTranscript: () => void;
  onMicPress: () => void;
  onToggleMute: () => void;
}

export default function VoiceControls({
  conversationStatus,
  isRequestingPermission,
  micMuted,
  voiceIntensity,
  peakValue,
  pulseAnimation,
  onTranscript,
  onMicPress,
  onToggleMute
}: VoiceControlsProps) {
  return (
    <View style={styles.actionContainer}>
      {/* Left button - Transcript */}
      <TouchableOpacity 
        style={styles.secondaryButton}
        onPress={onTranscript}
        disabled={conversationStatus !== 'connected'}
      >
        <View style={[styles.secondaryButtonCircle, conversationStatus !== 'connected' && styles.buttonDisabled]}>
          <MaterialCommunityIcons
            name="text"
            size={24}
            color={conversationStatus === 'connected' ? "#4A55A2" : "#A9A9A9"}
          />
        </View>
        <Text style={[styles.secondaryButtonText, conversationStatus !== 'connected' && styles.buttonTextDisabled]}>Transcript</Text>
      </TouchableOpacity>
      
      {/* Middle button - Enhanced Mic with Multi-layer Animation */}
      <VoiceAnimationRings
        isConnected={conversationStatus === 'connected'}
        voiceIntensity={voiceIntensity}
        peakValue={peakValue}
        pulseAnimation={pulseAnimation}
        size={140}
      >
        <TouchableOpacity
          style={styles.micButtonContainer}
          onPress={() => {
            console.log('[VoiceControls] Mic button pressed');
            onMicPress();
          }}
          disabled={isRequestingPermission}
        >
          <LinearGradient
            colors={['#87C4E5', '#4AACF4']}
            style={[styles.micButton, isRequestingPermission && styles.micButtonDisabled]}
          >
            {isRequestingPermission ? (
              <ActivityIndicator size="large" color="#FFFFFF" />
            ) : conversationStatus === 'connected' ? (
              <Ionicons name="stop" size={40} color="white" />
            ) : (
              <Ionicons name="call" size={40} color="white" />
            )}
          </LinearGradient>
        </TouchableOpacity>
      </VoiceAnimationRings>
      
      {/* Right button - Mute */}
      <TouchableOpacity 
        style={styles.secondaryButton}
        onPress={onToggleMute}
        disabled={conversationStatus !== 'connected'}
      >
        <View style={[styles.secondaryButtonCircle, conversationStatus !== 'connected' && styles.buttonDisabled]}>
          <FontAwesome5
            name={micMuted ? "microphone" : "microphone-slash"}
            size={20}
            color={conversationStatus === 'connected' ? (micMuted ? "#4A55A2" : "#FF6B6B") : "#A9A9A9"}
          />
        </View>
        <Text style={[styles.secondaryButtonText, conversationStatus !== 'connected' && styles.buttonTextDisabled]}>
          {micMuted ? "Unmute" : "Mute"}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
  },
  micButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  micButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#7FD6FF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 10,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  secondaryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
  },
  secondaryButtonCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(135, 196, 229, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    color: '#7D7D7D',
    fontSize: 12,
    marginTop: 4,
  },
  buttonDisabled: {
    backgroundColor: 'rgba(200, 200, 200, 0.3)',
  },
  buttonTextDisabled: {
    color: '#A9A9A9',
  },
  micButtonDisabled: {
    opacity: 0.7,
  },
});
