import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface VoiceTabSelectorProps {
  onChatPress: () => void;
  onCallPress?: () => void;
}

export default function VoiceTabSelector({ onChatPress, onCallPress }: VoiceTabSelectorProps) {
  return (
    <View style={styles.tabContainer}>
      <TouchableOpacity 
        style={[styles.tab]} 
        onPress={onChatPress}
      >
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Ionicons 
            name="chatbubble" 
            size={16} 
            color="#FFFFFF" 
            style={{marginRight: 4}} 
          />
          <Text style={[styles.tabText]}>Chat</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity 
        style={[styles.tab, styles.activeTab]}
        onPress={onCallPress}
      >
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Ionicons 
            name="call" 
            size={16} 
            color="#4A55A2" 
            style={{marginRight: 4}} 
          />
          <Text style={[styles.tabText, styles.activeTabText]}>Call</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginTop: 10,
    backgroundColor: '#87C4E5',
    borderRadius: 30,
    padding: 4,
    height: 44,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
    height: 36,
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.15,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  activeTabText: {
    color: '#4A55A2',
  },
});
