import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { reportError, addBreadcrumb } from '@/lib/sentryConfig';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
  onRetry?: () => void;
  onSkipQuestion?: () => void;
  onSaveProgress?: () => void;
  onCompleteManually?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  retryCount: number;
}

export class SelfReflectionErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      retryCount: 0
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('[SelfReflectionErrorBoundary] Error caught:', error);
    console.error('[SelfReflectionErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Add breadcrumb for self-reflection error context
    addBreadcrumb(
      'Self-reflection component error occurred',
      'error',
      {
        component: 'SelfReflectionErrorBoundary',
        errorName: error.name,
        errorMessage: error.message,
        retryCount: this.state.retryCount,
        componentStack: errorInfo.componentStack?.split('\n').slice(0, 3).join('\n')
      }
    );

    // Report error to Sentry with self-reflection-specific context
    reportError(error, {
      component: 'SelfReflectionErrorBoundary',
      action: 'self_reflection_component_render',
      metadata: {
        retryCount: this.state.retryCount,
        errorInfo: {
          componentStack: errorInfo.componentStack,
          errorBoundary: 'SelfReflectionErrorBoundary'
        }
      }
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;
    
    addBreadcrumb(
      'Self-reflection error boundary retry attempted',
      'user_action',
      { 
        component: 'SelfReflectionErrorBoundary',
        retryCount: newRetryCount
      }
    );

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: newRetryCount
    });

    this.props.onRetry?.();
  };

  handleSkipQuestion = () => {
    addBreadcrumb(
      'User skipped question from self-reflection error boundary',
      'user_action',
      { component: 'SelfReflectionErrorBoundary' }
    );

    this.props.onSkipQuestion?.();
  };

  handleSaveProgress = () => {
    addBreadcrumb(
      'User saved progress from self-reflection error boundary',
      'user_action',
      { component: 'SelfReflectionErrorBoundary' }
    );

    this.props.onSaveProgress?.();
  };

  handleCompleteManually = () => {
    addBreadcrumb(
      'User completed self-reflection manually from error boundary',
      'user_action',
      { component: 'SelfReflectionErrorBoundary' }
    );

    this.props.onCompleteManually?.();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI for self-reflection components
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="bulb-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Self-Reflection Bermasalah</Text>
          <Text style={styles.errorMessage}>
            Terjadi kesalahan pada sesi self-reflection. Anda bisa mencoba lagi, melewati pertanyaan ini, atau menyimpan progress yang sudah ada.
          </Text>
          
          {this.state.retryCount < 3 && (
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={this.handleRetry}
            >
              <Text style={styles.retryButtonText}>
                Coba Lagi {this.state.retryCount > 0 && `(${this.state.retryCount + 1}/3)`}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity 
            style={styles.skipButton}
            onPress={this.handleSkipQuestion}
          >
            <Text style={styles.skipButtonText}>Lewati Pertanyaan Ini</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.saveButton}
            onPress={this.handleSaveProgress}
          >
            <Text style={styles.saveButtonText}>Simpan Progress</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.completeButton}
            onPress={this.handleCompleteManually}
          >
            <Text style={styles.completeButtonText}>Selesaikan Manual</Text>
          </TouchableOpacity>

          <View style={styles.helpContainer}>
            <Text style={styles.helpText}>
              🌟 Self-reflection membantu memahami diri lebih baik. Jika ada masalah teknis, Anda bisa melanjutkan nanti atau menyelesaikan secara manual.
            </Text>
          </View>
          
          {__DEV__ && this.state.error && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugTitle}>Debug Info:</Text>
              <Text style={styles.debugText}>
                {this.state.error.name}: {this.state.error.message}
              </Text>
              <Text style={styles.debugText}>
                Retry Count: {this.state.retryCount}
              </Text>
              {this.state.error.stack && (
                <Text style={styles.debugStack}>
                  {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                </Text>
              )}
            </View>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FEFEFE',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#A08CFB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    backgroundColor: '#FFA726',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  skipButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  completeButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 24,
  },
  completeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  helpContainer: {
    backgroundColor: '#E3F2FD',
    padding: 16,
    borderRadius: 10,
    marginBottom: 16,
  },
  helpText: {
    fontSize: 14,
    color: '#1565C0',
    textAlign: 'center',
    lineHeight: 20,
  },
  debugContainer: {
    backgroundColor: '#F5F5F5',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  debugStack: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
});
