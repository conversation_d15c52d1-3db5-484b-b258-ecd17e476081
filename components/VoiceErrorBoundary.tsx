import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { reportError, addBreadcrumb } from '@/lib/sentryConfig';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

export class VoiceErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('[VoiceErrorBoundary] Error caught:', error);
    console.error('[VoiceErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Add breadcrumb for voice error context
    addBreadcrumb(
      'Voice component error occurred',
      'error',
      {
        component: 'VoiceErrorBoundary',
        errorName: error.name,
        errorMessage: error.message,
        componentStack: errorInfo.componentStack?.split('\n').slice(0, 3).join('\n')
      }
    );

    // Report error to Sentry with voice-specific context
    reportError(error, {
      component: 'VoiceErrorBoundary',
      action: 'voice_component_render',
      metadata: {
        errorInfo: {
          componentStack: errorInfo.componentStack,
          errorBoundary: 'VoiceErrorBoundary'
        }
      }
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    addBreadcrumb(
      'Voice error boundary retry attempted',
      'user_action',
      { component: 'VoiceErrorBoundary' }
    );

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI for voice components
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="mic-off" size={48} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Fitur Suara Bermasalah</Text>
          <Text style={styles.errorMessage}>
            Terjadi kesalahan pada fitur suara. Silakan coba lagi atau gunakan fitur chat.
          </Text>
          
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={this.handleRetry}
          >
            <Text style={styles.retryButtonText}>Coba Lagi</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.chatButton}
            onPress={() => {
              addBreadcrumb(
                'User switched to chat from voice error',
                'user_action',
                { component: 'VoiceErrorBoundary' }
              );
              // Navigate to chat - this would need to be implemented based on your navigation
            }}
          >
            <Text style={styles.chatButtonText}>Gunakan Chat</Text>
          </TouchableOpacity>
          
          {__DEV__ && this.state.error && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugTitle}>Debug Info:</Text>
              <Text style={styles.debugText}>
                {this.state.error.name}: {this.state.error.message}
              </Text>
              {this.state.error.stack && (
                <Text style={styles.debugStack}>
                  {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                </Text>
              )}
            </View>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FEFEFE',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#A08CFB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  chatButton: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 24,
  },
  chatButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  debugContainer: {
    backgroundColor: '#F5F5F5',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  debugStack: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
});
