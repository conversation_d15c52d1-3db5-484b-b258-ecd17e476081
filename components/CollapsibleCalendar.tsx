import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import JournalCalendar from '@/components/JournalCalendar';
import JournalDateView from '@/components/JournalDateView';
import type { JournalEntryWithQuestion } from '@/types/journal';

interface CollapsibleCalendarProps {
  currentMonth: Date;
  selectedDate: string | null;
  entryDates: string[];
  entriesForDate: JournalEntryWithQuestion[];
  loading: boolean;
  error: string | null;
  onDateSelect: (date: string) => void;
  onNavigateMonth: (direction: 'prev' | 'next') => void;
  onGoToToday: () => void;
  onShowAllEntries: () => void;
  isDateFilterActive: boolean;
}

const CollapsibleCalendar: React.FC<CollapsibleCalendarProps> = ({
  currentMonth,
  selectedDate,
  entryDates,
  entriesForDate,
  loading,
  error,
  onDateSelect,
  onNavigateMonth,
  onGoToToday,
  onShowAllEntries,
  isDateFilterActive,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const animationValue = useRef(new Animated.Value(0)).current;
  const rotationValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(animationValue, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        useNativeDriver: false, // We're animating height, so can't use native driver
      }),
      Animated.timing(rotationValue, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isExpanded, animationValue, rotationValue]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleDateSelect = (date: string) => {
    onDateSelect(date);
  };

  const handleShowAll = () => {
    onShowAllEntries();
    setIsExpanded(false); // Collapse calendar when showing all entries
  };

  const animatedHeight = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 320], // Reduced height to fit calendar better
  });

  const rotateChevron = rotationValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <View style={styles.container}>
      {/* Header with toggle button */}
      <TouchableOpacity style={styles.header} onPress={toggleExpanded} activeOpacity={0.7}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Ionicons name="calendar" size={20} color="#454459" />
            <Text style={styles.headerTitle}>Kalender</Text>
            {isDateFilterActive && selectedDate && (
              <View style={styles.activeDateBadge}>
                <Text style={styles.activeDateText}>
                  {new Date(selectedDate).toLocaleDateString('id-ID', {
                    day: 'numeric',
                    month: 'short'
                  })}
                </Text>
              </View>
            )}
          </View>
          <Animated.View style={{ transform: [{ rotate: rotateChevron }] }}>
            <Ionicons name="chevron-down" size={20} color="#454459" />
          </Animated.View>
        </View>
      </TouchableOpacity>

      {/* Collapsible calendar content */}
      <Animated.View style={[styles.calendarContent, { height: animatedHeight }]}>
        <View style={styles.calendarWrapper}>
          <JournalCalendar
            currentMonth={currentMonth}
            selectedDate={selectedDate}
            entryDates={entryDates}
            loading={loading}
            onDateSelect={handleDateSelect}
            onNavigateMonth={onNavigateMonth}
            onGoToToday={onGoToToday}
          />
          
          {/* Show All button when date is selected */}
          {isDateFilterActive && (
            <TouchableOpacity style={styles.showAllButton} onPress={handleShowAll}>
              <Ionicons name="list" size={16} color="#FFFFFF" />
              <Text style={styles.showAllButtonText}>Tampilkan Semua Riwayat</Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>

      {/* Selected date entries (shown when date is selected and calendar is expanded) */}
      {isExpanded && isDateFilterActive && selectedDate && (
        <View style={styles.dateEntriesContainer}>
          <JournalDateView
            selectedDate={selectedDate}
            entries={entriesForDate}
            loading={loading}
            error={error}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    backgroundColor: '#FFD572',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#454459',
    marginLeft: 8,
  },
  activeDateBadge: {
    backgroundColor: '#FEBD38',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 12,
  },
  activeDateText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#454459',
  },
  calendarContent: {
    overflow: 'hidden',
  },
  calendarWrapper: {
    padding: 12,
  },
  showAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#A08CFB',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  showAllButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  dateEntriesContainer: {
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    maxHeight: 300,
  },
});

export default CollapsibleCalendar;
