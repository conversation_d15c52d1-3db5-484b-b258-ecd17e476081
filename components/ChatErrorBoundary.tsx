import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { reportError, addBreadcrumb } from '@/lib/sentryConfig';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
  onRetry?: () => void;
  onSwitchToOffline?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  retryCount: number;
}

export class ChatErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      retryCount: 0
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('[ChatErrorBoundary] Error caught:', error);
    console.error('[ChatErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Add breadcrumb for chat error context
    addBreadcrumb(
      'Chat component error occurred',
      'error',
      {
        component: 'ChatErrorBoundary',
        errorName: error.name,
        errorMessage: error.message,
        retryCount: this.state.retryCount,
        componentStack: errorInfo.componentStack?.split('\n').slice(0, 3).join('\n')
      }
    );

    // Report error to Sentry with chat-specific context
    reportError(error, {
      component: 'ChatErrorBoundary',
      action: 'chat_component_render',
      metadata: {
        retryCount: this.state.retryCount,
        errorInfo: {
          componentStack: errorInfo.componentStack,
          errorBoundary: 'ChatErrorBoundary'
        }
      }
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;
    
    addBreadcrumb(
      'Chat error boundary retry attempted',
      'user_action',
      { 
        component: 'ChatErrorBoundary',
        retryCount: newRetryCount
      }
    );

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: newRetryCount
    });

    this.props.onRetry?.();
  };

  handleSwitchToOffline = () => {
    addBreadcrumb(
      'User switched to offline mode from chat error',
      'user_action',
      { component: 'ChatErrorBoundary' }
    );

    this.props.onSwitchToOffline?.();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI for chat components
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="chatbubble-ellipses-outline" size={48} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Chat Bermasalah</Text>
          <Text style={styles.errorMessage}>
            Terjadi kesalahan pada fitur chat. Anda masih bisa menggunakan fitur lain atau mencoba lagi.
          </Text>
          
          {this.state.retryCount < 3 && (
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={this.handleRetry}
            >
              <Text style={styles.retryButtonText}>
                Coba Lagi {this.state.retryCount > 0 && `(${this.state.retryCount + 1}/3)`}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity 
            style={styles.offlineButton}
            onPress={this.handleSwitchToOffline}
          >
            <Text style={styles.offlineButtonText}>Mode Offline</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.voiceButton}
            onPress={() => {
              addBreadcrumb(
                'User switched to voice from chat error',
                'user_action',
                { component: 'ChatErrorBoundary' }
              );
              // Navigate to voice - this would need to be implemented based on your navigation
            }}
          >
            <Text style={styles.voiceButtonText}>Gunakan Suara</Text>
          </TouchableOpacity>
          
          {__DEV__ && this.state.error && (
            <View style={styles.debugContainer}>
              <Text style={styles.debugTitle}>Debug Info:</Text>
              <Text style={styles.debugText}>
                {this.state.error.name}: {this.state.error.message}
              </Text>
              <Text style={styles.debugText}>
                Retry Count: {this.state.retryCount}
              </Text>
              {this.state.error.stack && (
                <Text style={styles.debugStack}>
                  {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                </Text>
              )}
            </View>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FEFEFE',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#A08CFB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  offlineButton: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 12,
  },
  offlineButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  voiceButton: {
    backgroundColor: '#FFD572',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 10,
    marginBottom: 24,
  },
  voiceButtonText: {
    color: '#454459',
    fontSize: 16,
    fontWeight: '600',
  },
  debugContainer: {
    backgroundColor: '#F5F5F5',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    width: '100%',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  debugStack: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
});
