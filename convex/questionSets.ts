import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

/**
 * Create a new question set
 */
export const createQuestionSet = mutation({
  args: {
    name: v.string(),
    version: v.string(),
    description: v.optional(v.string()),
    language: v.string(),
    questions: v.any(), // Array of question objects
    riskRules: v.any(), // Array of risk assessment rules
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const now = Date.now();

    // Create question set
    const questionSetId = await ctx.db.insert("questionSets", {
      name: args.name,
      version: args.version,
      description: args.description,
      isActive: true,
      language: args.language,
      createdAt: now,
      updatedAt: now,
    });

    // Create questions
    for (const question of args.questions) {
      await ctx.db.insert("questions", {
        questionSetId,
        key: question.key,
        type: question.type,
        text: question.text,
        options: question.options,
        scoringMethod: question.scoringMethod,
        scoringConfig: question.scoringConfig,
        order: question.order,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
    }

    // Create risk assessment rules
    for (const rule of args.riskRules) {
      await ctx.db.insert("riskAssessmentRules", {
        questionSetId,
        minScore: rule.minScore,
        maxScore: rule.maxScore,
        riskLevel: rule.riskLevel,
        message: rule.message,
        aiTone: rule.aiTone,
        recommendedActions: rule.recommendedActions,
        requiresImmediateAction: rule.requiresImmediateAction,
        isActive: true,
        createdAt: now,
      });
    }

    return questionSetId;
  },
});

/**
 * Get all question sets
 */
export const getAllQuestionSets = query({
  args: {},
  handler: async (ctx) => {
    const questionSets = await ctx.db
      .query("questionSets")
      .collect();

    return questionSets;
  },
});

/**
 * Seed initial self-reflection data
 */
export const seedInitialData = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if data already exists
    const existing = await ctx.db
      .query("questionSets")
      .first();

    if (existing) {
      return { message: "Data already exists", questionSetId: existing._id };
    }

    const now = Date.now();

    // Create default question set
    const questionSetId = await ctx.db.insert("questionSets", {
      name: "Default Self-Reflection Assessment",
      version: "1.0",
      description: "Initial mental health and wellness assessment",
      isActive: true,
      language: "id",
      createdAt: now,
      updatedAt: now,
    });

    // Default questions based on your existing system
    const questions = [
      {
        key: "mood_today",
        type: "multiple_choice",
        text: {
          id: "Bagaimana perasaan Anda hari ini secara keseluruhan?",
          en: "How are you feeling overall today?"
        },
        options: [
          { value: "very_good", label: { id: "Sangat baik", en: "Very good" }, score: 1 },
          { value: "good", label: { id: "Baik", en: "Good" }, score: 2 },
          { value: "neutral", label: { id: "Biasa saja", en: "Neutral" }, score: 3 },
          { value: "bad", label: { id: "Buruk", en: "Bad" }, score: 4 },
          { value: "very_bad", label: { id: "Sangat buruk", en: "Very bad" }, score: 5 }
        ],
        scoringMethod: "direct",
        scoringConfig: { weight: 1 },
        order: 1
      },
      {
        key: "stress_level",
        type: "scale",
        text: {
          id: "Seberapa stres yang Anda rasakan saat ini? (1 = tidak stres, 5 = sangat stres)",
          en: "How stressed do you feel right now? (1 = not stressed, 5 = very stressed)"
        },
        options: {
          min: 1,
          max: 5,
          labels: {
            1: { id: "Tidak stres", en: "Not stressed" },
            5: { id: "Sangat stres", en: "Very stressed" }
          }
        },
        scoringMethod: "direct",
        scoringConfig: { weight: 1.2 },
        order: 2
      },
      {
        key: "sleep_quality",
        type: "multiple_choice",
        text: {
          id: "Bagaimana kualitas tidur Anda semalam?",
          en: "How was your sleep quality last night?"
        },
        options: [
          { value: "excellent", label: { id: "Sangat baik", en: "Excellent" }, score: 1 },
          { value: "good", label: { id: "Baik", en: "Good" }, score: 2 },
          { value: "fair", label: { id: "Cukup", en: "Fair" }, score: 3 },
          { value: "poor", label: { id: "Buruk", en: "Poor" }, score: 4 },
          { value: "very_poor", label: { id: "Sangat buruk", en: "Very poor" }, score: 5 }
        ],
        scoringMethod: "direct",
        scoringConfig: { weight: 0.8 },
        order: 3
      },
      {
        key: "anxiety_level",
        type: "scale",
        text: {
          id: "Seberapa cemas yang Anda rasakan hari ini? (1 = tidak cemas, 5 = sangat cemas)",
          en: "How anxious do you feel today? (1 = not anxious, 5 = very anxious)"
        },
        options: {
          min: 1,
          max: 5,
          labels: {
            1: { id: "Tidak cemas", en: "Not anxious" },
            5: { id: "Sangat cemas", en: "Very anxious" }
          }
        },
        scoringMethod: "direct",
        scoringConfig: { weight: 1.3 },
        order: 4
      },
      {
        key: "support_need",
        type: "multiple_choice",
        text: {
          id: "Jenis dukungan apa yang paling Anda butuhkan saat ini?",
          en: "What type of support do you need most right now?"
        },
        options: [
          { value: "emotional", label: { id: "Dukungan emosional", en: "Emotional support" }, score: 0 },
          { value: "practical", label: { id: "Bantuan praktis", en: "Practical help" }, score: 0 },
          { value: "professional", label: { id: "Bantuan profesional", en: "Professional help" }, score: 0 },
          { value: "none", label: { id: "Tidak butuh bantuan", en: "No help needed" }, score: 0 }
        ],
        scoringMethod: "none",
        scoringConfig: { weight: 0 },
        order: 5
      }
    ];

    // Create questions
    for (const question of questions) {
      await ctx.db.insert("questions", {
        questionSetId,
        key: question.key,
        type: question.type,
        text: question.text,
        options: question.options,
        scoringMethod: question.scoringMethod,
        scoringConfig: question.scoringConfig,
        order: question.order,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
    }

    // Risk assessment rules
    const riskRules = [
      {
        minScore: 0,
        maxScore: 8,
        riskLevel: "green",
        message: {
          id: "Anda dalam kondisi yang baik! Terus jaga kesehatan mental Anda.",
          en: "You're doing well! Keep taking care of your mental health."
        },
        aiTone: "encouraging",
        recommendedActions: [
          { id: "Lanjutkan rutinitas positif", en: "Continue positive routines" },
          { id: "Jaga pola tidur yang baik", en: "Maintain good sleep patterns" }
        ],
        requiresImmediateAction: false
      },
      {
        minScore: 9,
        maxScore: 15,
        riskLevel: "yellow",
        message: {
          id: "Ada beberapa area yang perlu perhatian. Mari kita bicarakan lebih lanjut.",
          en: "There are some areas that need attention. Let's talk more about it."
        },
        aiTone: "supportive",
        recommendedActions: [
          { id: "Praktikkan teknik relaksasi", en: "Practice relaxation techniques" },
          { id: "Bicarakan dengan teman atau keluarga", en: "Talk to friends or family" }
        ],
        requiresImmediateAction: false
      },
      {
        minScore: 16,
        maxScore: 25,
        riskLevel: "red",
        message: {
          id: "Saya khawatir dengan kondisi Anda. Mari kita cari bantuan yang tepat.",
          en: "I'm concerned about your condition. Let's find the right help."
        },
        aiTone: "caring",
        recommendedActions: [
          { id: "Pertimbangkan bantuan profesional", en: "Consider professional help" },
          { id: "Hubungi layanan kesehatan mental", en: "Contact mental health services" }
        ],
        requiresImmediateAction: true
      }
    ];

    // Create risk assessment rules
    for (const rule of riskRules) {
      await ctx.db.insert("riskAssessmentRules", {
        questionSetId,
        minScore: rule.minScore,
        maxScore: rule.maxScore,
        riskLevel: rule.riskLevel,
        message: rule.message,
        aiTone: rule.aiTone,
        recommendedActions: rule.recommendedActions,
        requiresImmediateAction: rule.requiresImmediateAction,
        isActive: true,
        createdAt: now,
      });
    }

    return { message: "Initial data seeded successfully", questionSetId };
  },
});
