import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";

const schema = defineSchema({
  ...authTables,

  // User profiles to extend the default users table with app-specific data
  userProfiles: defineTable({
    userId: v.id("users"),
    onboardingCompleted: v.boolean(),
    selfReflectionCompleted: v.boolean(),
    selfReflectionSkipped: v.boolean(),
    latestSessionId: v.optional(v.string()),
    latestRiskLevel: v.optional(v.string()),
    latestCompletedAt: v.optional(v.number()),
    preferences: v.any(), // JSON object for user preferences
    role: v.string(), // 'client', 'admin', etc.
    registrationMethod: v.string(), // 'google_oauth', 'email_signup'
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  // Chat threads (existing structure)
  threads: defineTable({
    user_id: v.id("users"),
    title: v.optional(v.string()),
    created_at: v.number(),
    updated_at: v.number(),
  }).index("by_user", ["user_id"]),

  // Chat messages (existing structure)
  messages: defineTable({
    thread_id: v.id("threads"),
    sender_type: v.union(v.literal("user"), v.literal("ai")),
    content: v.string(),
    timestamp: v.number(),
    status: v.union(v.literal("sent"), v.literal("delivered"), v.literal("read")),
    metadata: v.any(),
  }).index("by_thread", ["thread_id"]),

  // Journal entries (existing structure)
  journalEntries: defineTable({
    user_id: v.id("users"),
    question_id: v.id("journalQuestions"),
    answer: v.string(),
    entry_date: v.string(), // ISO date string
    created_at: v.number(),
    updated_at: v.number(),
  }).index("by_user", ["user_id"])
   .index("by_user_date", ["user_id", "entry_date"]),

  // Journal questions (existing structure)
  journalQuestions: defineTable({
    question_text: v.string(),
    question_order: v.number(),
    is_active: v.boolean(),
    created_at: v.number(),
  }).index("by_order", ["question_order"])
   .index("by_active", ["is_active"]),

  // Mood entries (existing structure)
  moodEntries: defineTable({
    user_id: v.id("users"),
    entry_date: v.string(), // ISO date string
    day_of_week: v.string(),
    mood_level: v.number(), // 1-5 scale
    physical_health: v.boolean(),
    sleep_hours: v.number(),
    sleep_quality: v.number(), // 0-10 scale
    stress_level: v.number(), // 1-5 scale
    daily_feeling: v.number(), // 1-4 scale
    created_at: v.number(),
    updated_at: v.number(),
  }).index("by_user", ["user_id"])
   .index("by_user_date", ["user_id", "entry_date"]),

  // Question Sets for self-reflection
  questionSets: defineTable({
    name: v.string(),
    version: v.string(),
    description: v.optional(v.string()),
    isActive: v.boolean(),
    language: v.string(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_active", ["isActive"])
   .index("by_language", ["language"]),

  // Questions for self-reflection
  questions: defineTable({
    questionSetId: v.id("questionSets"),
    key: v.string(),
    type: v.string(), // 'multiple_choice', 'scale', 'boolean', etc.
    text: v.any(), // MultiLanguageText object
    options: v.optional(v.any()), // Question options array
    scoringMethod: v.string(), // 'direct', 'weighted', 'conditional', 'none'
    scoringConfig: v.any(), // Scoring configuration object
    order: v.number(),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_question_set", ["questionSetId"])
   .index("by_question_set_order", ["questionSetId", "order"]),

  // Risk Assessment Rules
  riskAssessmentRules: defineTable({
    questionSetId: v.id("questionSets"),
    minScore: v.number(),
    maxScore: v.number(),
    riskLevel: v.string(), // 'green', 'yellow', 'red', 'emergency'
    message: v.any(), // MultiLanguageText object
    aiTone: v.string(),
    recommendedActions: v.any(), // Array of recommended actions
    requiresImmediateAction: v.boolean(),
    isActive: v.boolean(),
    createdAt: v.number(),
  }).index("by_question_set", ["questionSetId"])
   .index("by_risk_level", ["riskLevel"]),

  // Enhanced self-reflection sessions
  selfReflectionSessions: defineTable({
    userId: v.id("users"),
    questionSetId: v.id("questionSets"),
    responses: v.any(), // JSON object with question responses
    calculatedScores: v.any(), // Individual question scores
    totalScore: v.number(),
    riskLevel: v.string(),
    aiTonePreference: v.optional(v.string()),
    supportPreference: v.optional(v.string()),
    metadata: v.any(), // Additional session data
    completedAt: v.number(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"])
   .index("by_user_date", ["userId", "completedAt"]),

  // Voice credits (existing structure)
  voiceCredits: defineTable({
    user_id: v.id("users"),
    credits: v.number(),
    last_recharge: v.number(),
    created_at: v.number(),
    updated_at: v.number(),
  }).index("by_user", ["user_id"]),
});

export default schema;