import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

/**
 * Get the current user's profile
 */
export const getCurrentUserProfile = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    return await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();
  },
});

/**
 * Get user profile by user ID (for admin use)
 */
export const getUserProfile = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Unauthorized");
    }

    // Only allow users to access their own profile (or add admin check here)
    if (currentUserId !== args.userId) {
      throw new Error("Unauthorized");
    }

    return await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Create a new user profile (called automatically after user registration)
 */
export const createUserProfile = mutation({
  args: {
    userId: v.id("users"),
    onboardingCompleted: v.optional(v.boolean()),
    selfReflectionCompleted: v.optional(v.boolean()),
    selfReflectionSkipped: v.optional(v.boolean()),
    preferences: v.optional(v.any()),
    role: v.optional(v.string()),
    registrationMethod: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId || userId !== args.userId) {
      throw new Error("Unauthorized");
    }

    // Check if profile already exists
    const existing = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existing) {
      return existing._id;
    }

    // Get user data to determine registration method if not provided
    let registrationMethod = args.registrationMethod ?? "unknown";
    if (registrationMethod === "unknown") {
      const user = await ctx.db.get(userId);
      if (user?.email && !user?.image) {
        registrationMethod = "email_signup";
      } else if (user?.image && user?.email) {
        registrationMethod = "google_oauth";
      }
    }

    const now = Date.now();
    return await ctx.db.insert("userProfiles", {
      userId: args.userId,
      onboardingCompleted: args.onboardingCompleted ?? false,
      selfReflectionCompleted: args.selfReflectionCompleted ?? false,
      selfReflectionSkipped: args.selfReflectionSkipped ?? false,
      preferences: args.preferences ?? {},
      role: args.role ?? "client",
      registrationMethod,
      createdAt: now,
      updatedAt: now,
    });
  },
});

/**
 * Update user profile
 */
export const updateUserProfile = mutation({
  args: {
    onboardingCompleted: v.optional(v.boolean()),
    selfReflectionCompleted: v.optional(v.boolean()),
    selfReflectionSkipped: v.optional(v.boolean()),
    latestSessionId: v.optional(v.string()),
    latestRiskLevel: v.optional(v.string()),
    latestCompletedAt: v.optional(v.number()),
    preferences: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const profile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!profile) {
      throw new Error("User profile not found");
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Only update fields that are provided
    if (args.onboardingCompleted !== undefined) {
      updateData.onboardingCompleted = args.onboardingCompleted;
    }
    if (args.selfReflectionCompleted !== undefined) {
      updateData.selfReflectionCompleted = args.selfReflectionCompleted;
    }
    if (args.selfReflectionSkipped !== undefined) {
      updateData.selfReflectionSkipped = args.selfReflectionSkipped;
    }
    if (args.latestSessionId !== undefined) {
      updateData.latestSessionId = args.latestSessionId;
    }
    if (args.latestRiskLevel !== undefined) {
      updateData.latestRiskLevel = args.latestRiskLevel;
    }
    if (args.latestCompletedAt !== undefined) {
      updateData.latestCompletedAt = args.latestCompletedAt;
    }
    if (args.preferences !== undefined) {
      updateData.preferences = args.preferences;
    }

    await ctx.db.patch(profile._id, updateData);
    return profile._id;
  },
});

/**
 * Update user preferences specifically
 */
export const updatePreferences = mutation({
  args: {
    preferences: v.any(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const profile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!profile) {
      throw new Error("User profile not found");
    }

    await ctx.db.patch(profile._id, {
      preferences: args.preferences,
      updatedAt: Date.now(),
    });

    return profile._id;
  },
});

/**
 * Get or create user profile (atomic operation)
 */
export const getOrCreateUserProfile = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Try to get existing profile
    let profile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!profile) {
      // Get user data to determine registration method
      const user = await ctx.db.get(userId);

      // Determine registration method based on available data
      let registrationMethod = "unknown";
      if (user?.email && !user?.image) {
        registrationMethod = "email_signup";
      } else if (user?.image && user?.email) {
        registrationMethod = "google_oauth";
      }

      // Create new profile with explicit defaults for self-reflection flow
      const now = Date.now();
      const profileId = await ctx.db.insert("userProfiles", {
        userId,
        onboardingCompleted: false, // Will be set to true after auth
        selfReflectionCompleted: false, // Must be false for new users
        selfReflectionSkipped: false, // Must be false for new users
        preferences: {},
        role: "client",
        registrationMethod,
        createdAt: now,
        updatedAt: now,
      });

      profile = await ctx.db.get(profileId);
    }

    return profile;
  },
});

/**
 * Debug function to check current user profile
 */
export const debugCurrentUserProfile = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { error: "Not authenticated", userId: null };
    }

    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return {
      userId,
      userProfile,
      hasProfile: !!userProfile,
    };
  },
});

/**
 * Reset user's self-reflection status for testing
 */
export const resetSelfReflectionStatus = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!userProfile) {
      throw new Error("User profile not found");
    }

    await ctx.db.patch(userProfile._id, {
      selfReflectionCompleted: false,
      selfReflectionSkipped: false,
      latestSessionId: undefined,
      latestRiskLevel: undefined,
      latestCompletedAt: undefined,
      updatedAt: Date.now(),
    });

    return { message: "Self-reflection status reset successfully" };
  },
});

/**
 * Reset self-reflection status by user ID (for debugging)
 */
export const resetSelfReflectionByUserId = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!userProfile) {
      return { error: "User profile not found", userId: args.userId };
    }

    await ctx.db.patch(userProfile._id, {
      selfReflectionCompleted: false,
      selfReflectionSkipped: false,
      latestSessionId: undefined,
      latestRiskLevel: undefined,
      latestCompletedAt: undefined,
      updatedAt: Date.now(),
    });

    return {
      message: "Self-reflection status reset successfully",
      userId: args.userId,
      profileId: userProfile._id
    };
  },
});
