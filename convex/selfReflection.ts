import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

/**
 * Get the active question set for self-reflection
 */
export const getActiveQuestionSet = query({
  args: {
    language: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const language = args.language || "id";
    
    // Get active question set
    const questionSet = await ctx.db
      .query("questionSets")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .filter((q) => q.eq(q.field("language"), language))
      .first();

    if (!questionSet) {
      return null;
    }

    // Get questions for this question set
    const questions = await ctx.db
      .query("questions")
      .withIndex("by_question_set_order", (q) => 
        q.eq("questionSetId", questionSet._id)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get risk assessment rules
    const riskRules = await ctx.db
      .query("riskAssessmentRules")
      .withIndex("by_question_set", (q) => q.eq("questionSetId", questionSet._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    return {
      ...questionSet,
      questions: questions.sort((a, b) => a.order - b.order),
      riskAssessmentRules: riskRules.sort((a, b) => a.minScore - b.minScore),
    };
  },
});

/**
 * Save a self-reflection session
 */
export const saveSession = mutation({
  args: {
    questionSetId: v.id("questionSets"),
    responses: v.any(),
    calculatedScores: v.any(),
    totalScore: v.number(),
    riskLevel: v.string(),
    aiTonePreference: v.optional(v.string()),
    supportPreference: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const now = Date.now();
    
    const sessionId = await ctx.db.insert("selfReflectionSessions", {
      userId,
      questionSetId: args.questionSetId,
      responses: args.responses,
      calculatedScores: args.calculatedScores,
      totalScore: args.totalScore,
      riskLevel: args.riskLevel,
      aiTonePreference: args.aiTonePreference,
      supportPreference: args.supportPreference,
      metadata: args.metadata || {},
      completedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Update user profile with latest session info
    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (userProfile) {
      await ctx.db.patch(userProfile._id, {
        selfReflectionCompleted: true,
        latestSessionId: sessionId,
        latestRiskLevel: args.riskLevel,
        latestCompletedAt: now,
        updatedAt: now,
      });
    }

    return sessionId;
  },
});

/**
 * Get user's latest self-reflection session
 */
export const getUserLatestSession = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const session = await ctx.db
      .query("selfReflectionSessions")
      .withIndex("by_user_date", (q) => q.eq("userId", userId))
      .order("desc")
      .first();

    return session;
  },
});

/**
 * Get user's self-reflection history
 */
export const getUserSessions = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    const limit = args.limit || 10;
    
    const sessions = await ctx.db
      .query("selfReflectionSessions")
      .withIndex("by_user_date", (q) => q.eq("userId", userId))
      .order("desc")
      .take(limit);

    return sessions;
  },
});

/**
 * Calculate risk assessment based on score and rules
 */
export const calculateRiskAssessment = query({
  args: {
    questionSetId: v.id("questionSets"),
    totalScore: v.number(),
    maxPossibleScore: v.number(),
  },
  handler: async (ctx, args) => {
    const riskRules = await ctx.db
      .query("riskAssessmentRules")
      .withIndex("by_question_set", (q) => q.eq("questionSetId", args.questionSetId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    if (riskRules.length === 0) {
      // Default risk assessment if no rules found
      const percentage = (args.totalScore / args.maxPossibleScore) * 100;
      let riskLevel = "green";
      let message = "You're doing well!";
      
      if (percentage >= 70) {
        riskLevel = "red";
        message = "Consider seeking professional support.";
      } else if (percentage >= 40) {
        riskLevel = "yellow";
        message = "Some areas may need attention.";
      }

      return {
        level: riskLevel,
        score: args.totalScore,
        maxScore: args.maxPossibleScore,
        percentage,
        message,
        aiTone: riskLevel === "red" ? "supportive" : "encouraging",
        recommendedActions: [],
        requiresImmediateAction: riskLevel === "red",
      };
    }

    // Find matching rule based on score
    const sortedRules = riskRules.sort((a, b) => a.minScore - b.minScore);
    let matchingRule = sortedRules[0]; // Default to lowest rule

    for (const rule of sortedRules) {
      if (args.totalScore >= rule.minScore && args.totalScore <= rule.maxScore) {
        matchingRule = rule;
        break;
      }
    }

    const percentage = (args.totalScore / args.maxPossibleScore) * 100;

    return {
      level: matchingRule.riskLevel,
      score: args.totalScore,
      maxScore: args.maxPossibleScore,
      percentage,
      message: matchingRule.message,
      aiTone: matchingRule.aiTone,
      recommendedActions: matchingRule.recommendedActions || [],
      requiresImmediateAction: matchingRule.requiresImmediateAction,
    };
  },
});

/**
 * Update user profile completion status
 */
export const updateUserProfileCompletion = mutation({
  args: {
    selfReflectionCompleted: v.optional(v.boolean()),
    selfReflectionSkipped: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!userProfile) {
      throw new Error("User profile not found");
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.selfReflectionCompleted !== undefined) {
      updateData.selfReflectionCompleted = args.selfReflectionCompleted;
    }
    if (args.selfReflectionSkipped !== undefined) {
      updateData.selfReflectionSkipped = args.selfReflectionSkipped;
    }

    await ctx.db.patch(userProfile._id, updateData);
    return userProfile._id;
  },
});
